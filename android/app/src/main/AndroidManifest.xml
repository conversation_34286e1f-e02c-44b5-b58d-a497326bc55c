<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <!-- geolocator permission to find region -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Advertising ID -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- Facebook Integration -->
    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <queries>
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>

    <!-- in app browser  -->
    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application android:name="${applicationName}" android:label="${appName}"
        android:icon="@mipmap/ic_launcher">
        <!-- Custom Tabs Service -->
        <!-- <service
            android:name="android.support.customtabs.trusted.TrustedWebActivityService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.support.customtabs.action.CustomTabsService" />
            </intent-filter>
        </service> -->

        <!-- Facebook Integration -->
        <meta-data android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <!-- Google Map -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDEGhaxoooISlODZOuTUrrJuYZXwWwNxT4" />
        <!-- android:value="AIzaSyAJUBSDzIN5UWT1ZCZoOGsKxI70pX_39pY"/> -->

        <!-- Firebase FCM -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="high_importance_channel" />
        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_launcher_foreground_image_resized" />

        <activity
            android:name=".MainActivity" android:exported="true"
            android:launchMode="singleTask" android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Branch URI Scheme -->
            <intent-filter>
                <!-- If utilizing $deeplink_path please explicitly declare your hosts, or utilize a
                wildcard(*) -->
                <data android:scheme="gomama" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <!-- Branch App Links - Live App -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="qyrzv.app.link" />
                <data android:scheme="https" android:host="qyrzv-alternate.app.link" />
            </intent-filter>

            <!-- Branch App Links - Test App -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="qyrzv.test-app.link" />
                <data android:scheme="https" android:host="qyrzv-alternate.test-app.link" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding" android:value="2" />
        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_test_pzp2TmdOPbdIw4cnY1TIxccfvweRUfEN" />
        <!-- todo missing live key -->
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="key_test_pzp2TmdOPbdIw4cnY1TIxccfvweRUfEN" />
        <meta-data
            android:name="io.branch.sdk.TestMode" android:value="true" />
    </application>
</manifest>