<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(FLAVOR_APP_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIconName</key>
		<string>AppIcon</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
		</array>
		<key>CFBundleName</key>
		<string>Go!Mama</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>com.gomama.app.prod</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>gomama</string>
					<string>fb1530224210897902</string>
					<string>com.googleusercontent.apps.266769263225-a1fjv8vs21iolqma8h76c2lq8c5s9r8p</string>
					<string>com.googleusercontent.apps.922025442756-4f32nnutarovarvr5racche3ot8ujc9k</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FacebookAppID</key>
		<string>1530224210897902</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>Go!Mama</string>
		<key>FlutterDeepLinkingEnabled</key>
		<true />
		<key>GIDClientID</key>
		<string>922025442756-4f32nnutarovarvr5racche3ot8ujc9k.apps.googleusercontent.com</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
			<string>fb-messenger-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSCameraUsageDescription</key>
		<string>Require Camera access to take photos for uploading Go!Mama pods suggestions.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Require Location When In Use access to find nearby Go!Mama pods.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Require Location When In Use access to find nearby Go!Mama pods.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Require Photo access to pick photos for uploading Go!Mama pods suggestions.</string>
		<key>NSUserTrackingUsageDescription</key>
		<string>This identifier will be used to enable Facebook login.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
			<string>fetch</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false />
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>branch_key</key>
		<dict>
			<key>live</key>
			<string>key_test_pzp2TmdOPbdIw4cnY1TIxccfvweRUfEN</string>
			<key>test</key>
			<string>key_test_pzp2TmdOPbdIw4cnY1TIxccfvweRUfEN</string>
		</dict>
		<key>branch_universal_link_domains</key>
		<array>
			<string>qyrzv.test-app.link</string>
			<string>qyrzv-alternate.test-app.link</string>
			<string>qyrzv.app.link</string>
			<string>qyrzv-alternate.app.link</string>
		</array>
	</dict>
</plist>