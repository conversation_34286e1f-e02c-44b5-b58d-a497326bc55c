include: package:very_good_analysis/analysis_options.3.0.2.yaml
linter:
  rules:
    public_member_api_docs: false
    avoid_dynamic_calls: false
    lines_longer_than_80_chars: false
analyzer:
  plugins:
    - custom_lint
  exclude: [build/**, lib/**.freezed.dart, lib/**.g.dart]
  errors:
    inference_failure_on_function_return_type: ignore
    inference_failure_on_untyped_parameter: ignore
    inference_failure_on_instance_creation: ignore
    inference_failure_on_function_invocation: ignore
    unused_import: ignore
    use_setters_to_change_properties: ignore
    avoid_positional_boolean_parameters: ignore