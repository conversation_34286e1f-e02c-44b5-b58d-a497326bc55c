import 'dart:async';
import 'dart:convert';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/features/auth/model/app_version_control.dart';
import 'package:gomama/app/features/main/provider/main_providers.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pub_semver/pub_semver.dart';
import 'package:rive/rive.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

Future<String?> getDevice() async {
  final deviceInfo = DeviceInfoPlugin();

  if (kIsWeb) {
    return (await deviceInfo.webBrowserInfo).userAgent;
  }
  switch (defaultTargetPlatform) {
    case TargetPlatform.android:
      return (await deviceInfo.androidInfo).model;
    case TargetPlatform.iOS:
      return (await deviceInfo.iosInfo).utsname.machine;
    // ignore: no_default_cases
    default:
      return '';
  }
}

enum VersionType {
  remote,
  local,
}

Future<AppVersionControl?> getVersion(VersionType type) async {
  Groveman.debug('Getting version');
  if (type == VersionType.remote) {
    final uri = Uri.parse('${Environment.httpUrl}/app-version-controls/latest');

    try {
      final response = await http.get(uri);
      if (response.statusCode == 200) {
        final jsonBody = jsonDecode(response.body) as Map<String, dynamic>;
        final data = jsonBody['data'];

        if (data is Map<String, dynamic>) {
          return AppVersionControl.fromJson(data);
        } else {
          Groveman.warning('getVersion: Unexpected response structure');
          return null;
        }
      } else {
        Groveman.warning('getVersion: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      Groveman.error('getVersion error: $e');
      return null;
    }
  } else if (type == VersionType.local) {
    final packageInfo = await PackageInfo.fromPlatform();
    final localVersion = packageInfo.version;

    return AppVersionControl(
      id: 'client-installed',
      version: localVersion,
      description: 'Installed version',
      publishedBy: 'client-installed',
      addedBy: 'client-installed',
      forceUpdate: false,
    );
  }

  return null;
}

void setupLogging() {
  if (kReleaseMode) {
    // Groveman.plantTree(CrashlyticsTree());
  } else {
    Groveman.plantTree(DebugTree(showColor: true, showEmoji: true));
  }

  FlutterError.onError = (details) {
    Groveman.error(details.exceptionAsString(), stackTrace: details.stack);
  };
}

void setupShopify() {
  Groveman.debug('Setup shopify');
  ShopifyConfig.setConfig(
    storefrontAccessToken: Environment.shopifyAccessToken,
    storeUrl: '${Environment.shopifyStore}.myshopify.com',
    cachePolicy: CachePolicy.noCache,
  );
}

void setupMapbox() {
  Groveman.debug('Setup mapbox');
  const mapboxToken = String.fromEnvironment('PUBLIC_ACCESS_TOKEN');
  MapboxOptions.setAccessToken(mapboxToken);
}

Future<void> setupRive() async {
  await RiveFile.initialize();
}

Future<bool> setupVersion() async {
  Groveman.debug('Getting remote version');
  final remoteVersion = await getVersion(VersionType.remote);
  Groveman.debug('Getting local version');
  final localVersion = await getVersion(VersionType.local);

  var forceUpdate = false;
  Groveman.debug('Checking version');
  if (remoteVersion != null && localVersion != null) {
    if (Version.parse(remoteVersion.version) >
            Version.parse(localVersion.version) &&
        remoteVersion.forceUpdate) {
      forceUpdate = true;
    }
  }

  return forceUpdate;
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  Groveman.info('Handling a background message ${message.messageId}');
}

Future<void> setupFCM() async {
  Groveman.debug('Setup FCM background handler');
  // Set the background messaging handler early on, as a named top-level function
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );
}

Future<void> bootstrap(FutureOr<Widget> Function() builder) async {
  final binding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: binding);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  setupLogging();
  Groveman.debug('Initializing local storage');
  final appStorage = AppStorage();
  await appStorage.initAppStorage();
  Groveman.debug('Initializing secure storage');
  final secureAppStorage = SecureAppStorage();
  await secureAppStorage.initAppStorage();
  final device = await getDevice();
  final forceUpdate = await setupVersion();
  await setupRive();
  setupShopify();
  setupMapbox();
  await setupFCM();

  Groveman.debug('Init Branch SDK');
  await FlutterBranchSdk.init(
    enableLogging: true,
  );

  Groveman.debug('Starting app');
  runApp(
    ProviderScope(
      overrides: [
        appStorageProvider.overrideWithValue(appStorage),
        securedAppStorageProvider.overrideWithValue(secureAppStorage),
        deviceSettingProvider.overrideWithValue(device ?? ''),
        forceUpdateProvider.overrideWithValue(forceUpdate),
      ],
      observers: const [
        // StateLogger(),
      ],
      child: await builder(),
    ),
  );
}
