// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:gomama/app/core/constants/environment.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        if (Environment.firebase == 'dev') {
          return androidDev;
        }

        return android;
      case TargetPlatform.iOS:
        if (Environment.firebase == 'dev') {
          return iosDev;
        }

        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions androidDev = FirebaseOptions(
    apiKey: 'AIzaSyD0DHGR43s6hSuSch8d-5xsxXD_lMOeTyE',
    appId: '1:266769263225:android:eb89dbfcf3e93c492202a8',
    messagingSenderId: '266769263225',
    projectId: 'gomama-dev',
    storageBucket: 'gomama-dev.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBy3nIb5SoIDbATmGWrAg0nYCW0aErS0o8',
    appId: '1:922025442756:android:97bdaaee7ae1ac18573891',
    messagingSenderId: '922025442756',
    projectId: 'gomama-prod',
    databaseURL:
        'https://gomama-prod-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'gomama-prod.appspot.com',
  );

  static const FirebaseOptions iosDev = FirebaseOptions(
    apiKey: 'AIzaSyBs3z6oh7yXjWqxX9lrO_PBto4j9LPqpIY',
    appId: '1:266769263225:ios:784b0fa446c5d0e82202a8',
    messagingSenderId: '266769263225',
    projectId: 'gomama-dev',
    storageBucket: 'gomama-dev.appspot.com',
    androidClientId:
        '266769263225-36ke0srg583p7eb3jm94fsmb0ce6ma9j.apps.googleusercontent.com',
    iosClientId:
        '266769263225-a1fjv8vs21iolqma8h76c2lq8c5s9r8p.apps.googleusercontent.com',
    iosBundleId: 'com.gomama.app.dev',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBAZOvzsqUM_5o6AA9yJjMxCNxXtoe37Mo',
    appId: '1:922025442756:ios:fbe1d073cfd960a5573891',
    messagingSenderId: '922025442756',
    projectId: 'gomama-prod',
    databaseURL:
        'https://gomama-prod-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'gomama-prod.appspot.com',
    androidClientId:
        '922025442756-6ir0rufqg80eq04m44f2mvkd0jv79rhb.apps.googleusercontent.com',
    iosClientId:
        '922025442756-4f32nnutarovarvr5racche3ot8ujc9k.apps.googleusercontent.com',
    iosBundleId: 'com.gomama.app.prod',
  );
}
