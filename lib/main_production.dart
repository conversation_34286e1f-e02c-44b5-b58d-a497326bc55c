import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:gomama/app/app.dart';
import 'package:gomama/bootstrap.dart';
import 'package:gomama/firebase_options.dart';

void main() async {
  await dotenv.load(fileName: '.env.production');
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await bootstrap(() => const App());
}
