import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/network/realtime_service.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/core/theme/app_theme.dart';
import 'package:gomama/l10n/l10n.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class App extends HookConsumerWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize and dispose RealtimeService
    useEffect(
      () {
        final realtimeService = ref.read(realtimeServiceProvider);
        // You can subscribe to messages here if needed globally
        // realtimeService.messages.listen((message) {
        //   // Handle global messages
        // });
        return realtimeService.dispose;
      },
      [],
    );

    final appTheme = ref.watch(appThemeProvider);
    final router = ref.watch(routerProvider);

    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: MaterialApp.router(
        title: 'gomama',
        // TODO(kkcy): add your localization here
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        theme: appTheme.lightTheme,
        darkTheme: appTheme.darkTheme,
        routeInformationParser: router.routeInformationParser,
        routeInformationProvider: router.routeInformationProvider,
        routerDelegate: router.routerDelegate,
        builder: (context, child) => MediaQuery(
          data: MediaQuery.of(context).copyWith(
            boldText: false,
            textScaler: TextScaler.noScaling,
          ),
          child: child!,
        ),
      ),
    );
  }
}
