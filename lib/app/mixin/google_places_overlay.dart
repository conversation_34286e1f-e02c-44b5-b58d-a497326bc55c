import 'package:flutter/material.dart';
import 'package:gomama/app/features/maps/model/google_places.dart';

///
/// use with text form field
/// to search google places
///
mixin GooglePlacesOverlay {
  OverlayEntry? showPredictions(
    BuildContext context,
    List<GooglePlacePrediction> predictions,
    LayerLink layerLink, {
    void Function(int)? onTap,
  }) {
    OverlayEntry? overlayEntry;

    if (context.findRenderObject() != null) {
      final renderBox = context.findRenderObject()! as RenderBox;
      final size = renderBox.size;
      final offset = renderBox.localToGlobal(Offset.zero);

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: offset.dx,
          top: size.height + offset.dy,
          width: size.width,
          child: CompositedTransformFollower(
            showWhenUnlinked: false,
            link: layerLink,
            offset: Offset(0, size.height + 5.0),
            child: Material(
              elevation: 1,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: predictions.length,
                itemBuilder: (BuildContext context, int index) => InkWell(
                  onTap: onTap != null
                      ? () {
                          onTap(index);
                        }
                      : null,
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Text(
                      predictions[index].description!,
                      // style: predictionsStyle,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    if (overlayEntry != null) {
      Overlay.of(context).insert(overlayEntry);
    }

    return overlayEntry;
  }
}
