import 'package:flutter/material.dart';

class CustomColors {
  static const MaterialColor primaries = MaterialColor(0xff5D076F, <int, Color>{
    50: Color(0xffFCE9FF),
    100: Color(0xffE7B3E7),
    200: Color(0xffC672DB),
    300: Color(0xff943FA8),
    400: Color(0xff5D076F),
    500: Color(0xff4b0668),
    600: Color(0xff3e0057),
    700: Color(0xff310046),
    800: Color(0xff240035),
    900: Color(0xff170024),
  });
  static const Color primaryDark = Color(0xff4b0668);
  static const Color primary = Color(0xff5D076F);
  static const Color primaryLight = Color(0xff943FA8);
  static const Color primaryExtraLight = Color(0xffC672DB);
  static const Color secondaryDark = Color(0xffAD7946);
  static const Color secondary = Color(0xffFBDABA);
  static const Color secondaryLight = Color(0xffFFF3DD);
  static const Color secondaryExtraLight = Color(0xffFFFBF4);
  static const Color text = Color(0xff352F36);
  static const Color placeholder = Color(0xff8C8C8C);
  static const Color disableAmenities = Color(0xffEDE6DB);
  static const Color disableTextAmenities = Color(0xffD3B9A1);
  //
  static const Color backgroundGradientLight = Color(0xffB455C8);
  static const Color backgroundGradient = Color(0xff742A83);
  static const Color backgroundGradientDark = Color(0xff490058);
  //
  static const Color secondaryGradientLight = Color(0xffFFF3DD);
  static const Color secondaryGradientDark = Color(0xffFFDDA9);
  //
  static const Color backgroundForm = Color(0xffF8F8F8);
  //
  static const Color divider = Color(0xffFAE3B9);
  //
  static const Color backgroundCream = Color(0xffFFFAF5);
  static const Color backgroundColor = Color(0xFFF7F6FB);
  static const Color red = Color(0xffEB4335);
  // static Color green2 = const Color(0xff127c3c);
  // static Color yellow = const Color(0xfff0cd4d);
  // static Color greyGreen1 = const Color(0xffe1eae1);
  // static Color greyGreen2 = const Color(0xfff5f6f0);
  // static Color greyGreen3 = const Color(0xfff6f9f6);
  // static Color grey = const Color(0xffcccccc);
  // static Color red = const Color(0xffeb5757);
}
