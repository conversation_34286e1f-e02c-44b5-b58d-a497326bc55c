import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/features/listing/model/listing_flags.dart';

class ListingFlagCategoryConverter
    implements JsonConverter<ListingFlagCategory, String> {
  const ListingFlagCategoryConverter();

  @override
  ListingFlagCategory fromJson(String json) {
    switch (json) {
      case 'page_malfunction':
        return const ListingFlagCategory.pageMalfunction();
      case 'duplicate_listing':
        return const ListingFlagCategory.duplicateListing();
      case 'inaccurate_info':
        return const ListingFlagCategory.inaccurateInfo();
      case 'privacy_concern':
        return const ListingFlagCategory.privacyConcern();
      case 'safety_hazard':
        return const ListingFlagCategory.safetyHazard();
      case 'other':
      default:
        return const ListingFlagCategory.other();
    }
  }

  @override
  String toJson(ListingFlagCategory category) => category.value;
}
