import 'dart:math';

class Extension {}

extension DateHelpers on DateTime {
  bool isSameDay(DateTime? other) {
    if (other == null) return false;

    return year == other.year && month == other.month && day == other.day;
  }
}

extension IterableExtension<T> on Iterable<T> {
  Iterable<T> distinctBy(Object Function(T e) getCompareValue) {
    final result = <T>[];
    for (final element in this) {
      if (!result.any((x) => getCompareValue(x) == getCompareValue(element))) {
        result.add(element);
      }
    }

    return result;
  }
}

extension Capitalize on String {
  String capitalize() {
    if (trim().isNotEmpty) {
      final words = removeExtraSpaces()
          .split(' ')
          .map((e) => e[0].toUpperCase() + (e.length > 1 ? e.substring(1) : ''))
          .toList();
      return words.join(' ');
    } else {
      return this;
    }
  }

  String removeExtraSpaces() {
    if (trim().isEmpty) return '';
    return trim().replaceAll(RegExp(' +'), ' ');
  }
}

extension Ordinal on int {
  String toOrdinal() {
    if (this < 0) return toString();

    final specialCases = <String>[
      'first',
      'second',
      'third',
      'fourth',
      'fifth',
      'sixth',
      'seventh',
      'eighth',
      'ninth',
      'tenth',
      'eleventh',
      'twelfth',
      'thirteenth',
      'fourteenth',
      'fifteenth',
      'sixteenth',
      'seventeenth',
      'eighteenth',
      'nineteenth',
      'twentieth',
    ];

    if (this < specialCases.length) {
      return specialCases[this];
    }

    final suffixes = ['th', 'st', 'nd', 'rd'];
    final v = this + 1;

    final suffix = (v % 100 >= 11 && v % 100 <= 13)
        ? 'th'
        : suffixes[(v % 10 < 4) ? (v % 10) : 0];

    return '$v$suffix';
  }
}

double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  const earthRadius = 6371000; // Earth's radius in meters

  // Convert degrees to radians
  final lat1Rad = lat1 * (pi / 180);
  final lon1Rad = lon1 * (pi / 180);
  final lat2Rad = lat2 * (pi / 180);
  final lon2Rad = lon2 * (pi / 180);

  // Haversine formula
  final dLat = lat2Rad - lat1Rad;
  final dLon = lon2Rad - lon1Rad;
  final a = sin(dLat / 2) * sin(dLat / 2) +
      cos(lat1Rad) * cos(lat2Rad) * sin(dLon / 2) * sin(dLon / 2);
  final c = 2 * atan2(sqrt(a), sqrt(1 - a));

  return earthRadius * c; // Distance in meters
}
