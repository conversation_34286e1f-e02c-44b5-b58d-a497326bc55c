/// Flutter icons CustomIcon
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  CustomIcon
///      fonts:
///       - asset: fonts/CustomIcon.ttf
///
/// 
/// * Linearicons Free, Copyright (C) Linearicons.com
///         Author:    Perxis
///         License:   CC BY-SA 4.0 (https://creativecommons.org/licenses/by-sa/4.0/)
///         Homepage:  https://linearicons.com
/// * Material Design Icons, Copyright (C) Google, Inc
///         Author:    Google
///         License:   Apache 2.0 (https://www.apache.org/licenses/LICENSE-2.0)
///         Homepage:  https://design.google.com/icons/
/// * Entypo, Copyright (C) 2012 by <PERSON>
///         Author:    <PERSON>
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://www.entypo.com
/// * Typicons, (c) <PERSON> 2012
///         Author:    <PERSON>
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://typicons.com/
/// * Elusive, Copyright (C) 2013 by Aristeides Stathopoulos
///         Author:    Aristeides Stathopoulos
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://aristeides.com/
/// * Font Awesome 5, Copyright (C) 2016 by Dave Gandy
///         Author:    Dave Gandy
///         License:   SIL (https://github.com/FortAwesome/Font-Awesome/blob/master/LICENSE.txt)
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
/// * Font Awesome 4, Copyright (C) 2016 by Dave Gandy
///         Author:    Dave Gandy
///         License:   SIL ()
///         Homepage:  http://fortawesome.github.com/Font-Awesome/
/// * Octicons, Copyright (C) 2020 by GitHub Inc.
///         Author:    GitHub
///         License:   MIT (http://opensource.org/licenses/mit-license.php)
///         Homepage:  https://primer.style/octicons/
///
import 'package:flutter/widgets.dart';

class CustomIcon {
  CustomIcon._();

  static const _kFontFam = 'CustomIcon';
  static const String? _kFontPkg = null;

  static const IconData gomLogoOnly = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cameraAdd = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData calendarFull = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData close = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData plusCircled = IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData map = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData keyboardArrowDown = IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData keyboardArrowLeft = IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData keyboardArrowRight = IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData keyboardArrowUp = IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData localPhone = IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logout = IconData(0xe812, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData home = IconData(0xe814, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData error = IconData(0xe815, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData flag = IconData(0xe817, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData check = IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData sort = IconData(0xe819, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData image = IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData starEmpty = IconData(0xe820, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData hotAndColdWaterDispenser = IconData(0xe829, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData profile = IconData(0xe831, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData share = IconData(0xe832, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData sink = IconData(0xe833, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData star = IconData(0xe834, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData table = IconData(0xe835, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData whatsapp = IconData(0xe836, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData announcement = IconData(0xe837, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData time = IconData(0xe838, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData termsOfService = IconData(0xe839, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData bin = IconData(0xe83a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData bell = IconData(0xe83c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cart = IconData(0xe83d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData chair = IconData(0xe83e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData curtain = IconData(0xe83f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData diaperChanging = IconData(0xe840, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData disinfect = IconData(0xe841, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData dottedTime = IconData(0xe842, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData faq = IconData(0xe843, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData editProfile = IconData(0xe844, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData female = IconData(0xe845, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData filter = IconData(0xe846, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData globe = IconData(0xe847, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData info = IconData(0xe848, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData location = IconData(0xe849, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData lock = IconData(0xe84a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData male = IconData(0xe84b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData mirror = IconData(0xe84c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData motherWithBaby = IconData(0xe84d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData powerSocket = IconData(0xe84e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData privacyPolicy = IconData(0xe84f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heart = IconData(0xe850, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData gomamaLocationPin = IconData(0xe851, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData paperTowelMachine = IconData(0xe852, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData help = IconData(0xe853, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData minusCircle = IconData(0xf056, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData building = IconData(0xf0f7, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData plus = IconData(0xf303, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData heartEmpty = IconData(0xf327, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData starHalfAlt = IconData(0xf5c0, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData sms = IconData(0xf7cd, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData edit = IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
