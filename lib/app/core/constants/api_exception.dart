import 'dart:convert';

import 'package:gomama/app/core/network/web_providers.dart';

/// Base exception for all exceptions thrown by AppHttpClient.
/// You can create instances of this to create "unknown" error exceptions.
///
/// ```
///   ┌──────────────────────────┐
///   │  AppHttpClientException  │
///   └──────────────────────────┘
///                 ▲
///                 │
///   ┌──────────────────────────┐
///   │   AppNetworkException    │
///   └──────────────────────────┘
///                 ▲
///                 │
/// ┌───────────────────────────────┐
/// │  AppNetworkResponseException  │
/// └───────────────────────────────┘
/// ```
class AppHttpClientException<OriginalException extends Exception>
    implements Exception {
  /// Create a new application http client exception with the specified
  /// underlying [exception].
  AppHttpClientException({required this.exception});

  /// Exception which was caught.
  final OriginalException exception;
}

/// Reason for a network exception.
enum AppNetworkExceptionReason {
  /// A request cancellation is responsible for the exception.
  canceled,

  /// A timeout error is responsible for the exception.
  timedOut,

  /// A response error is responsible for the exception.
  responseError
}

/// Network error.
class AppNetworkException<OriginalException extends Exception>
    extends AppHttpClientException<OriginalException> {
  /// Create a network exception.
  AppNetworkException({
    required this.reason,
    required super.exception,
  });

  /// The reason the network exception ocurred.
  final AppNetworkExceptionReason reason;
}

/// Response exception.
class AppNetworkResponseException<OriginalException extends Exception, DataType>
    extends AppNetworkException<OriginalException> {
  /// Create a new response exception with the specified [statusCode],
  /// original [exception], and response [data].
  AppNetworkResponseException({
    required super.exception,
    this.statusCode,
    this.errorCode,
    this.data,
  }) : super(
          reason: AppNetworkExceptionReason.responseError,
        );

  /// Response data, if any.
  final DataType? data;

  /// HTTP status code, if any.
  final int? statusCode;

  /// Error code, if any.
  final String? errorCode;

  /// True if the response contains data.
  bool get hasData => data != null;

  String? get message => hasData ? (data! as Json)['message'] as String? : null;

  /// If the status code is null, returns false. Otherwise, allows the
  /// given closure [evaluator] to validate the given http integer status code.
  ///
  /// Usage:
  /// ```
  /// final isValid = responseException.validateStatusCode(
  ///   (statusCode) => statusCode >= 200 && statusCode < 300,
  /// );
  /// ```
  bool validateStatusCode(bool Function(int statusCode) evaluator) {
    final statusCode = this.statusCode;
    if (statusCode == null) return false;
    return evaluator(statusCode);
  }

  @override
  String toString() {
    final formattedData = hasData
        ? const JsonEncoder.withIndent('    ').convert(data)
        : 'No data';

    return '''
AppNetworkResponseException {
  statusCode: $statusCode,
  hasData: $hasData,
  data: $formattedData,
  exception: $exception,
  reason: $reason,
  errorCode: $errorCode,
}
        ''';
  }
}
