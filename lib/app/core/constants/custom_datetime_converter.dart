import 'package:freezed_annotation/freezed_annotation.dart';

class CustomDateTimeConverter implements JsonConverter<DateTime?, String?> {
  const CustomDateTimeConverter();

  @override
  DateTime? fromJson(String? json) {
    if (json == null || json.isEmpty) {
      return null;
    }

    return DateTime.parse(json).toLocal();
  }

  @override
  String? toJson(DateTime? dt) {
    if (dt == null) {
      return null;
    }

    return dt.toUtc().toIso8601String();
  }
}

class CustomDateTimeListConverter
    implements JsonConverter<List<DateTime>?, List<dynamic>?> {
  const CustomDateTimeListConverter();

  @override
  List<DateTime>? fromJson(List<dynamic>? json) {
    if (json == null || json.isEmpty) {
      return null;
    }

    return json.map((dynamic item) {
      if (item is String) {
        return DateTime.parse(item).toLocal();
      } else {
        throw FormatException('Expected a String, but got ${item.runtimeType}');
      }
    }).toList();
  }

  @override
  List<String>? toJson(List<DateTime>? dateTimeList) {
    if (dateTimeList == null || dateTimeList.isEmpty) {
      return null;
    }

    return dateTimeList.map((dt) => dt.toUtc().toIso8601String()).toList();
  }
}
