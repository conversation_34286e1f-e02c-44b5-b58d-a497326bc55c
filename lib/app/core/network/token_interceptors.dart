import 'package:dio/dio.dart';

class ApiProviderTokenInterceptor extends QueuedInterceptorsWrapper {
  ApiProviderTokenInterceptor(this.accessToken);
  final String? accessToken;
  // final TokenRepository _tokenRepository;

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // if (accessToken == null || _tokenRepository.tokenHasExpired(token)) {
    //   token = await _tokenRepository.loadAccessToken;
    // }

    // get null on logout
    if (accessToken == null) {
      return handler.next(options);
    }

    options.headers.addAll({'Authorization': 'Bearer ${accessToken!}'});
    return handler.next(options);
  }

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // <-- here you can handle 401 response, which is not related to token expiration, globally to all requests
    return handler.next(err);
  }
}
