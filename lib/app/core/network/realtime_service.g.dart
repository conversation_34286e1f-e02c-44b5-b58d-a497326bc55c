// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'realtime_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$realtimeServiceHash() => r'fac52c2431411d07b6eaf52e03eeff18560169e1';

/// See also [realtimeService].
@ProviderFor(realtimeService)
final realtimeServiceProvider = Provider<RealtimeService>.internal(
  realtimeService,
  name: r'realtimeServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RealtimeServiceRef = ProviderRef<RealtimeService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
