// ignore_for_file: strict_raw_type

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
// import 'package:dio_http_formatter/dio_http_formatter.dart';
import 'package:flutter/foundation.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/utils/dio_logger.dart';
import 'package:groveman/groveman.dart';
// import 'package:http/http.dart' as http;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'web_providers.g.dart';

/// A callback that returns a Dio response, presumably from a Dio method
/// it has called which performs an HTTP request, such as `dio.get()`,
/// `dio.post()`, etc.
typedef HttpLibraryMethod<T> = Future<Response<T>> Function();

/// Function which takes a Dio response object and an exception and returns
/// an optional [AppHttpClientException], optionally mapping the response
/// to a custom exception.
typedef ResponseExceptionMapper = AppNetworkResponseException? Function<T>(
  Response<T>,
  Exception,
);

// shortcut
typedef Json = Map<String, dynamic>;

const kPageLimit = 10;

@Riverpod(keepAlive: true)
WebServices repository(RepositoryRef ref) {
  // watch token in order to reinitalize whenever jwt changes
  // ref.watch(userTokenControllerProvider);
  return WebServices(
    ref,
    // exceptionMapper: <T>(Response<T> response, exception) {
    //   final data = response.data;
    //   if (data != null && data is Json) {
    //     return AppNetworkResponseException(
    //       exception: exception,
    //     );
    //   }
    //   return null;
    // },
  );
}

@Riverpod(keepAlive: true)
WebServices zestRepository(ZestRepositoryRef ref) {
  // watch token in order to reinitalize whenever jwt changes
  // ref.watch(userTokenControllerProvider);
  return WebServices(ref, isZest: true,
      // exceptionMapper: <T>(Response<T> response, exception) {
      //   final data = response.data;
      //   if (data != null && data is Json) {
      //     return AppNetworkResponseException(
      //       exception: exception,
      //     );
      //   }
      //   return null;
      // },
      );
}

// if we want to setup custom exception mapper
@riverpod
WebServices exceptionRepository(
  ExceptionRepositoryRef ref,
  ResponseExceptionMapper mapper,
) {
  return WebServices(
    ref,
    exceptionMapper: mapper,
  );
}

class WebServices {
  WebServices(
    this.ref, {
    this.exceptionMapper,
    this.isZest = false,
  });
  final RepositoryRef ref;
  final ResponseExceptionMapper? exceptionMapper;
  final bool? isZest;

  late final httpClient = Dio(
    BaseOptions(
      baseUrl: isZest == true
          ? Uri(
              scheme: Environment.scheme,
              host: Environment.zestHost,
              port: Environment.zestPort,
              path: Environment.zestVer,
            ).toString()
          : Uri(
              scheme: Environment.scheme,
              host: Environment.host,
              port: Environment.port,
              path: Environment.ver,
            ).toString(),
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      contentType: ContentType.json.toString(),
    ),
  )..interceptors.addAll(
      !kReleaseMode
          ? [
              // LoggerInterceptor(),
              // HttpFormatter(includeResponseBody: false),
            ]
          : [],
    );

  // Map<String, String> get headers {
  //   final headers = {
  //     'Content-Type': 'application/json; charset=UTF-8',
  //   };

  //   final token = ref.read(userTokenControllerProvider).valueOrNull;
  //   if (token != null) {
  //     headers['Authorization'] = 'Bearer $token';
  //   }

  //   Groveman.debug('token', error: token);

  //   return headers;
  // }

  final StreamTransformer<Uint8List, List<int>> uInt8Transformer =
      StreamTransformer.fromHandlers(
    handleData: (data, sink) {
      sink.add(List<int>.from(data));
    },
  );

  // SSE
  Stream<T> sseConnect<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    required T Function(dynamic) fromJson,
  }) {
    // final url = Uri(
    //   scheme: Environment.scheme,
    //   host: Environment.host,
    //   port: Environment.port,
    //   path: '${Environment.ver}$path',
    //   queryParameters: queryParameters,
    // ).toString();

    final stream = Stream.fromFuture(
      httpClient.get<ResponseBody>(
        path,
        queryParameters: queryParameters,
        options: Options(
          responseType: ResponseType.stream,
          headers: {'Accept': 'text/event-stream'},
        ),
      ),
    ).asyncExpand((response) {
      return response.data!.stream
          .transform(uInt8Transformer)
          .transform(const Utf8Decoder())
          .transform(const LineSplitter())
          .where((line) => line.startsWith('data: '))
          .map((line) => line.substring(6))
          .map((dataString) {
        final data = jsonDecode(dataString);
        return fromJson(data);
      });
    }).handleError((error) {
      Groveman.error('stream', error: error);
      throw error as Exception;
      // throw _mapException(error);
      // throw AppHttpClientException(exception: error);
    });

    return stream;
  }

  /// HTTP GET request.
  Future<Response<T>> get<T>(
    String path, {
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  /// HTTP POST request.
  Future<Response<T>> post<T>(
    String path, {
    Json? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  /// HTTP MULTIPART POST request.
  Future<Response<T>> multipartPost<T>(
    String path, {
    FormData? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  /// HTTP MULTIPART PUT request.
  Future<Response<T>> multipartPut<T>(
    String path, {
    FormData? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  /// HTTP PUT request.
  Future<Response<T>> put<T>(
    String path, {
    Json? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  /// HTTP HEAD request.
  Future<Response<T>> head<T>(
    String path, {
    Json? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) {
    return _mapException(
      () => httpClient.head(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      ),
    );
  }

  /// HTTP DELETE request.
  Future<Response<T>> delete<T>(
    String path, {
    Json? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) {
    return _mapException(
      () => httpClient.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      ),
    );
  }

  /// HTTP PATCH request.
  Future<Response<T>> patch<T>(
    String path, {
    Json? data,
    Json? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) {
    return _mapException(
      () => httpClient.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  // Map Dio exceptions (and any other exceptions) to an exception type
  // supported by our application.
  Future<Response<T>> _mapException<T>(HttpLibraryMethod<T> method) async {
    try {
      return await method();
    } on DioException catch (exception) {
      switch (exception.type) {
        case DioExceptionType.cancel:
          throw AppNetworkException(
            reason: AppNetworkExceptionReason.canceled,
            exception: exception,
          );
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.sendTimeout:
          throw AppNetworkException(
            reason: AppNetworkExceptionReason.timedOut,
            exception: exception,
          );
        case DioExceptionType.badResponse:
          // For DioExceptionType.response, we are guaranteed to have a
          // response object present on the exception.
          final response = exception.response;

          // TODO(kkcy): response is! Response<T> is true for some reason
          // if (response == null || response is! Response<T>) {

          if (response == null) {
            // This should never happen, judging by the current source code
            // for Dio.
            // ignore: inference_failure_on_instance_creation
            throw AppNetworkResponseException(exception: exception);
          }

          if (response.data is Json) {
            throw exceptionMapper?.call(response, exception) ??
                AppNetworkResponseException(
                  exception: exception,
                  statusCode: response.statusCode,
                  errorCode: response.data?['code'] as String?,
                  data: response.data,
                );
          }

          throw exceptionMapper?.call(response, exception) ??
              AppNetworkResponseException(
                exception: exception,
                statusCode: response.statusCode,
                errorCode: response.data as String,
                data: response.data,
              );
        case DioExceptionType.connectionError:
        case DioExceptionType.unknown:
        case DioExceptionType.badCertificate:
          throw AppHttpClientException(exception: exception);
      }
    } catch (e) {
      throw AppHttpClientException(
        exception: e is Exception ? e : Exception('Unknown exception ocurred'),
      );
    }
  }

  // Json _returnResponse(http.Response response) {
  //   switch (response.statusCode) {
  //     case 200:
  //     case 201:
  //       return json.decode(response.body) as Json;
  //     case 400:
  //       throw BadRequestException(
  //         response.body,
  //         response.body,
  //       );
  //     case 401:
  //     case 403:
  //       Groveman.warning('401/403', error: response.body);
  //       Groveman.warning('force logout due to unauthorized');
  //       ref.read(authControllerProvider.notifier).logout();

  //       throw UnauthorisedException(
  //         response.body,
  //         response.body,
  //       );
  //     case 404:
  //       throw NotFoundException(
  //         response.body,
  //         response.body,
  //       );
  //     case 422:
  //       throw UnprocessableEntityException(
  //         response.body,
  //         response.body,
  //       );
  //     case 500:
  //       throw UnknownException(
  //         response.body,
  //         response.body,
  //       );
  //     default:
  //       throw FetchDataException(
  //         'Error occured while Communication with Server with StatusCode : ${response.statusCode}\n${response.body}',
  //         response.body,
  //       );
  //   }
  // }

  // Future<Json> _returnStreamedResponse(
  //   http.StreamedResponse response,
  // ) async {
  //   final body = await response.stream.bytesToString();

  //   switch (response.statusCode) {
  //     case 200:
  //     case 201:
  //       return json.decode(body) as Json;
  //     case 400:
  //       throw BadRequestException(
  //         body,
  //         body,
  //       );
  //     case 401:
  //     case 403:
  //       Groveman.warning('401/403', error: body);
  //       Groveman.warning('force logout due to unauthorized');
  //       await ref.read(appProvider).logout();

  //       throw UnauthorisedException(
  //         body,
  //         body,
  //       );
  //     case 404:
  //       throw NotFoundException(
  //         body,
  //         body,
  //       );
  //     case 422:
  //       throw UnprocessableEntityException(
  //         body,
  //         body,
  //       );
  //     case 500:
  //       throw UnknownException(
  //         body,
  //         body,
  //       );
  //     default:
  //       throw FetchDataException(
  //         'Error occured while Communication with Server with StatusCode : ${response.statusCode}\n$body',
  //         body,
  //       );
  //   }
  // }
}
