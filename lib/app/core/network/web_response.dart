import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'web_response.freezed.dart';
part 'web_response.g.dart';

@Freezed(genericArgumentFactories: true)
class PostResponse<T> with _$PostResponse<T> {
  factory PostResponse({
    required T data,
    required bool success,
    String? message,
  }) = _PostResponse;

  factory PostResponse.fromJson(
    Json json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PostResponseFromJson(json, fromJsonT);
}

@freezed
class Pagination with _$Pagination {
  factory Pagination({
    required int total,
    required int perPage,
    required int currentPage,
    required int lastPage,
    required int firstPage,
  }) = _Pagination;

  factory Pagination.fromJson(Json json) => _$PaginationFromJson(json);
}
