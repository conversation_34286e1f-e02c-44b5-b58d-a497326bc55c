// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$repositoryHash() => r'e05abe3a68d66d867b028d04c39946df96b3bc27';

/// See also [repository].
@ProviderFor(repository)
final repositoryProvider = Provider<WebServices>.internal(
  repository,
  name: r'repositoryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$repositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef RepositoryRef = ProviderRef<WebServices>;
String _$zestRepositoryHash() => r'52183b09e3493abb76a958353d5bece572e455e6';

/// See also [zestRepository].
@ProviderFor(zestRepository)
final zestRepositoryProvider = Provider<WebServices>.internal(
  zestRepository,
  name: r'zestRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zestRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ZestRepositoryRef = ProviderRef<WebServices>;
String _$exceptionRepositoryHash() =>
    r'22f95b86b651cfa7156f55801fcb5d8243e3827e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [exceptionRepository].
@ProviderFor(exceptionRepository)
const exceptionRepositoryProvider = ExceptionRepositoryFamily();

/// See also [exceptionRepository].
class ExceptionRepositoryFamily extends Family<WebServices> {
  /// See also [exceptionRepository].
  const ExceptionRepositoryFamily();

  /// See also [exceptionRepository].
  ExceptionRepositoryProvider call(
    AppNetworkResponseException<Exception, dynamic>? Function<T>(
            Response<T>, Exception)
        mapper,
  ) {
    return ExceptionRepositoryProvider(
      mapper,
    );
  }

  @override
  ExceptionRepositoryProvider getProviderOverride(
    covariant ExceptionRepositoryProvider provider,
  ) {
    return call(
      provider.mapper,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'exceptionRepositoryProvider';
}

/// See also [exceptionRepository].
class ExceptionRepositoryProvider extends AutoDisposeProvider<WebServices> {
  /// See also [exceptionRepository].
  ExceptionRepositoryProvider(
    AppNetworkResponseException<Exception, dynamic>? Function<T>(
            Response<T>, Exception)
        mapper,
  ) : this._internal(
          (ref) => exceptionRepository(
            ref as ExceptionRepositoryRef,
            mapper,
          ),
          from: exceptionRepositoryProvider,
          name: r'exceptionRepositoryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$exceptionRepositoryHash,
          dependencies: ExceptionRepositoryFamily._dependencies,
          allTransitiveDependencies:
              ExceptionRepositoryFamily._allTransitiveDependencies,
          mapper: mapper,
        );

  ExceptionRepositoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mapper,
  }) : super.internal();

  final AppNetworkResponseException<Exception, dynamic>? Function<T>(
      Response<T>, Exception) mapper;

  @override
  Override overrideWith(
    WebServices Function(ExceptionRepositoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ExceptionRepositoryProvider._internal(
        (ref) => create(ref as ExceptionRepositoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mapper: mapper,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<WebServices> createElement() {
    return _ExceptionRepositoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ExceptionRepositoryProvider && other.mapper == mapper;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mapper.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ExceptionRepositoryRef on AutoDisposeProviderRef<WebServices> {
  /// The parameter `mapper` of this provider.
  AppNetworkResponseException<Exception, dynamic>? Function<T>(
      Response<T>, Exception) get mapper;
}

class _ExceptionRepositoryProviderElement
    extends AutoDisposeProviderElement<WebServices>
    with ExceptionRepositoryRef {
  _ExceptionRepositoryProviderElement(super.provider);

  @override
  AppNetworkResponseException<Exception, dynamic>? Function<T>(
          Response<T>, Exception)
      get mapper => (origin as ExceptionRepositoryProvider).mapper;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
