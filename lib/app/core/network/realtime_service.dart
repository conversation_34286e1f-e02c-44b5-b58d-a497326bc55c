import 'dart:async';
import 'dart:convert';

import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/mqtt_service.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'realtime_service.g.dart';

enum WebSocketConnectionStatus {
  connecting,
  connected,
  disconnected,
  reconnecting,
}

@Riverpod(keepAlive: true)
RealtimeService realtimeService(RealtimeServiceRef ref) {
  final authController = ref.watch(authControllerProvider);

  return RealtimeService(
    getToken: () async => authController.requireValue.realtimeJwt,
  );
}

class RealtimeService {
  RealtimeService({
    required this.getToken,
  }) {
    _mqttService = MqttService(getToken: getToken);
    _connect();
  }

  final Future<String?> Function() getToken;

  late MqttService _mqttService;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController.broadcast();
  final StreamController<WebSocketConnectionStatus> _statusController =
      StreamController.broadcast();
  StreamSubscription<Map<String, dynamic>>? _messageSubscription;
  StreamSubscription<MqttConnectionStatus>? _statusSubscription;

  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  Stream<WebSocketConnectionStatus> get status => _statusController.stream;

  Future<void> _connect() async {
    // Set up message forwarding from MQTT service to RealtimeService
    _messageSubscription = _mqttService.messages.listen((message) {
      // Transform MQTT topic back to old format for compatibility
      final mqttTopic = message['topic'] as String?;
      if (mqttTopic != null) {
        final oldTopic = _mqttTopicToOldTopic(mqttTopic);
        if (oldTopic != null) {
          final transformedMessage = Map<String, dynamic>.from(message);
          transformedMessage['topic'] = oldTopic;
          _messageController.add(transformedMessage);
        }
      }
    });

    // Set up status forwarding from MQTT service to RealtimeService
    _statusSubscription = _mqttService.status.listen((mqttStatus) {
      final wsStatus = _mqttStatusToWebSocketStatus(mqttStatus);
      _statusController.add(wsStatus);
    });

    // Connect to MQTT broker
    await _mqttService.connect();
  }

  // Helper method to convert old topic format to MQTT topic format
  String _oldTopicToMqttTopic(String oldTopic) {
    // Convert old topic patterns to new MQTT topic structure
    if (oldTopic.startsWith('active-sessions:')) {
      final userId = oldTopic.substring('active-sessions:'.length);
      return 'gomama/users/sessions/$userId';
    } else if (oldTopic.startsWith('active-listings:')) {
      final listingId = oldTopic.substring('active-listings:'.length);
      return 'gomama/listings/status/$listingId';
    }
    // For any other topics, use a generic pattern
    return 'gomama/legacy/$oldTopic';
  }

  // Helper method to convert MQTT topic back to old topic format
  String? _mqttTopicToOldTopic(String mqttTopic) {
    if (mqttTopic.startsWith('gomama/users/sessions/')) {
      final userId = mqttTopic.substring('gomama/users/sessions/'.length);
      return 'active-sessions:$userId';
    } else if (mqttTopic.startsWith('gomama/listings/status/')) {
      final listingId = mqttTopic.substring('gomama/listings/status/'.length);
      return 'active-listings:$listingId';
    } else if (mqttTopic.startsWith('gomama/legacy/')) {
      return mqttTopic.substring('gomama/legacy/'.length);
    }
    return null; // Unknown topic format
  }

  // Helper method to convert MQTT status to WebSocket status
  WebSocketConnectionStatus _mqttStatusToWebSocketStatus(
      MqttConnectionStatus mqttStatus) {
    switch (mqttStatus) {
      case MqttConnectionStatus.connecting:
        return WebSocketConnectionStatus.connecting;
      case MqttConnectionStatus.connected:
        return WebSocketConnectionStatus.connected;
      case MqttConnectionStatus.disconnected:
        return WebSocketConnectionStatus.disconnected;
      case MqttConnectionStatus.reconnecting:
        return WebSocketConnectionStatus.reconnecting;
    }
  }

  void sendMessage(String action, String topic, [Map<String, dynamic>? data]) {
    // For MQTT, we don't need to send subscribe/unsubscribe messages
    // This method is kept for compatibility but doesn't do anything
    Groveman.debug('sendMessage called with action: $action, topic: $topic');
  }

  void subscribeToTopic(String topic) {
    final mqttTopic = _oldTopicToMqttTopic(topic);
    _mqttService.subscribeToTopic(mqttTopic);
    Groveman.info('Subscribed to topic: $topic (MQTT: $mqttTopic)');
  }

  void unsubscribeFromTopic(String topic) {
    final mqttTopic = _oldTopicToMqttTopic(topic);
    _mqttService.unsubscribeFromTopic(mqttTopic);
    Groveman.info('Unsubscribed from topic: $topic (MQTT: $mqttTopic)');
  }

  void dispose() {
    _messageSubscription?.cancel();
    _statusSubscription?.cancel();
    _mqttService.dispose();
    _messageController.close();
    _statusController.close();
    Groveman.info('RealtimeService disposed.');
  }
}
