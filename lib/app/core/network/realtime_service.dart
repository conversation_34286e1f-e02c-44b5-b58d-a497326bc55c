import 'dart:async';
import 'dart:convert';

import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/mqtt_service.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'realtime_service.g.dart';

enum RealtimeConnectionStatus {
  connecting,
  connected,
  disconnected,
  reconnecting,
}

@Riverpod(keepAlive: true)
RealtimeService realtimeService(RealtimeServiceRef ref) {
  final authController = ref.watch(authControllerProvider);

  return RealtimeService(
    getToken: () async => authController.requireValue.realtimeJwt,
  );
}

class RealtimeService {
  RealtimeService({
    required this.getToken,
  }) {
    _mqttService = MqttService(getToken: getToken);
    _connect();
  }

  final Future<String?> Function() getToken;

  late MqttService _mqttService;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController.broadcast();
  final StreamController<RealtimeConnectionStatus> _statusController =
      StreamController.broadcast();
  StreamSubscription<Map<String, dynamic>>? _messageSubscription;
  StreamSubscription<MqttConnectionStatus>? _statusSubscription;

  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  Stream<RealtimeConnectionStatus> get status => _statusController.stream;

  Future<void> _connect() async {
    // Set up message forwarding from MQTT service to RealtimeService
    _messageSubscription = _mqttService.messages.listen((message) {
      _messageController.add(message);
    });

    // Set up status forwarding from MQTT service to RealtimeService
    _statusSubscription = _mqttService.status.listen((mqttStatus) {
      final realtimeStatus = _mqttStatusToRealtimeStatus(mqttStatus);
      _statusController.add(realtimeStatus);
    });

    // Connect to MQTT broker
    await _mqttService.connect();
  }

  // Helper method to convert MQTT status to Realtime status
  RealtimeConnectionStatus _mqttStatusToRealtimeStatus(MqttConnectionStatus mqttStatus) {
    switch (mqttStatus) {
      case MqttConnectionStatus.connecting:
        return RealtimeConnectionStatus.connecting;
      case MqttConnectionStatus.connected:
        return RealtimeConnectionStatus.connected;
      case MqttConnectionStatus.disconnected:
        return RealtimeConnectionStatus.disconnected;
      case MqttConnectionStatus.reconnecting:
        return RealtimeConnectionStatus.reconnecting;
    }
  }

  void subscribeToUserSessions(String userId) {
    final topic = 'gomama/users/sessions/$userId';
    _mqttService.subscribeToTopic(topic);
    Groveman.info('Subscribed to user sessions: $userId');
  }

  void subscribeToUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    _mqttService.subscribeToTopic(topic);
    Groveman.info('Subscribed to user notifications: $userId');
  }

  void subscribeToListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    _mqttService.subscribeToTopic(topic);
    Groveman.info('Subscribed to listing status: $listingId');
  }

  void subscribeToListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    _mqttService.subscribeToTopic(topic);
    Groveman.info('Subscribed to listing availability: $listingId');
  }

  void unsubscribeFromUserSessions(String userId) {
    final topic = 'gomama/users/sessions/$userId';
    _mqttService.unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user sessions: $userId');
  }

  void unsubscribeFromUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    _mqttService.unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user notifications: $userId');
  }

  void unsubscribeFromListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    _mqttService.unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing status: $listingId');
  }

  void unsubscribeFromListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    _mqttService.unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing availability: $listingId');
  }

  void dispose() {
    _messageSubscription?.cancel();
    _statusSubscription?.cancel();
    _mqttService.dispose();
    _messageController.close();
    _statusController.close();
    Groveman.info('RealtimeService disposed.');
  }
}
