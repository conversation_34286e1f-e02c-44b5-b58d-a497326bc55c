import 'dart:convert';

import 'package:gomama/app/core/network/web_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'device_repository.g.dart';

@Riverpod(keepAlive: true)
DeviceRepository deviceRepository(DeviceRepositoryRef ref) =>
    DeviceRepository(ref);

class DeviceRepository {
  DeviceRepository(this.ref);
  final DeviceRepositoryRef ref;

  Future<bool?> create(String deviceToken) async {
    try {
      final response = await ref.read(repositoryProvider).post(
        '/me/devices',
        data: {'device_token': deviceToken},
      );

      return response.data['success'] == true;
    } catch (error, stackTrace) {
      Groveman.error('createDevice', error: error, stackTrace: stackTrace);
      return null;
    }
  }
}
