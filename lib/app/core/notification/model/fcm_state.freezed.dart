// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fcm_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FCMState _$FCMStateFromJson(Map<String, dynamic> json) {
  return _FCMState.fromJson(json);
}

/// @nodoc
mixin _$FCMState {
  String? get fcmToken => throw _privateConstructorUsedError;
  bool get isInitialized => throw _privateConstructorUsedError;
  bool get hasPermission => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FCMStateCopyWith<FCMState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FCMStateCopyWith<$Res> {
  factory $FCMStateCopyWith(FCMState value, $Res Function(FCMState) then) =
      _$FCMStateCopyWithImpl<$Res, FCMState>;
  @useResult
  $Res call({String? fcmToken, bool isInitialized, bool hasPermission});
}

/// @nodoc
class _$FCMStateCopyWithImpl<$Res, $Val extends FCMState>
    implements $FCMStateCopyWith<$Res> {
  _$FCMStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fcmToken = freezed,
    Object? isInitialized = null,
    Object? hasPermission = null,
  }) {
    return _then(_value.copyWith(
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPermission: null == hasPermission
          ? _value.hasPermission
          : hasPermission // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FCMStateImplCopyWith<$Res>
    implements $FCMStateCopyWith<$Res> {
  factory _$$FCMStateImplCopyWith(
          _$FCMStateImpl value, $Res Function(_$FCMStateImpl) then) =
      __$$FCMStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? fcmToken, bool isInitialized, bool hasPermission});
}

/// @nodoc
class __$$FCMStateImplCopyWithImpl<$Res>
    extends _$FCMStateCopyWithImpl<$Res, _$FCMStateImpl>
    implements _$$FCMStateImplCopyWith<$Res> {
  __$$FCMStateImplCopyWithImpl(
      _$FCMStateImpl _value, $Res Function(_$FCMStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fcmToken = freezed,
    Object? isInitialized = null,
    Object? hasPermission = null,
  }) {
    return _then(_$FCMStateImpl(
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPermission: null == hasPermission
          ? _value.hasPermission
          : hasPermission // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FCMStateImpl implements _FCMState {
  const _$FCMStateImpl(
      {this.fcmToken, this.isInitialized = false, this.hasPermission = false});

  factory _$FCMStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$FCMStateImplFromJson(json);

  @override
  final String? fcmToken;
  @override
  @JsonKey()
  final bool isInitialized;
  @override
  @JsonKey()
  final bool hasPermission;

  @override
  String toString() {
    return 'FCMState(fcmToken: $fcmToken, isInitialized: $isInitialized, hasPermission: $hasPermission)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FCMStateImpl &&
            (identical(other.fcmToken, fcmToken) ||
                other.fcmToken == fcmToken) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            (identical(other.hasPermission, hasPermission) ||
                other.hasPermission == hasPermission));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, fcmToken, isInitialized, hasPermission);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FCMStateImplCopyWith<_$FCMStateImpl> get copyWith =>
      __$$FCMStateImplCopyWithImpl<_$FCMStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FCMStateImplToJson(
      this,
    );
  }
}

abstract class _FCMState implements FCMState {
  const factory _FCMState(
      {final String? fcmToken,
      final bool isInitialized,
      final bool hasPermission}) = _$FCMStateImpl;

  factory _FCMState.fromJson(Map<String, dynamic> json) =
      _$FCMStateImpl.fromJson;

  @override
  String? get fcmToken;
  @override
  bool get isInitialized;
  @override
  bool get hasPermission;
  @override
  @JsonKey(ignore: true)
  _$$FCMStateImplCopyWith<_$FCMStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
