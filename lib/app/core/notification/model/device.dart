// TODO: pending backend device endpoint

// import 'package:freezed_annotation/freezed_annotation.dart';

// part 'device.freezed.dart';
// part 'device.g.dart';

// @freezed
// class Device with _$Device {
//   @CustomDateTimeConverter()
//   factory Device({
//     int? id,
//     String? deviceNo,
//     String? model,
//     String? platform,
//     String? token,
//     String? version, // retrieved and used locally only, for remote/local version checking
//     User? user,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//   }) = _Device;

//   factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
// }

// @freezed
// class CreateDevice with _$CreateDevice {
//   @CustomDateTimeConverter()
//   factory CreateDevice({
//     String? deviceNo,
//     String? model,
//     String? platform,
//     String? token,
//   }) = _CreateDevice;

//   factory CreateDevice.fromJson(Map<String, dynamic> json) =>
//       _$CreateDeviceFromJson(json);
// }
