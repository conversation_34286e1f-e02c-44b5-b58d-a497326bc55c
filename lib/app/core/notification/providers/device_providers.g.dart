// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$createDeviceHash() => r'e059423b43a9fb247150b5722ac8ca2483d5d289';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [createDevice].
@ProviderFor(createDevice)
const createDeviceProvider = CreateDeviceFamily();

/// See also [createDevice].
class CreateDeviceFamily extends Family<AsyncValue<bool>> {
  /// See also [createDevice].
  const CreateDeviceFamily();

  /// See also [createDevice].
  CreateDeviceProvider call(
    String deviceToken,
  ) {
    return CreateDeviceProvider(
      deviceToken,
    );
  }

  @override
  CreateDeviceProvider getProviderOverride(
    covariant CreateDeviceProvider provider,
  ) {
    return call(
      provider.deviceToken,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'createDeviceProvider';
}

/// See also [createDevice].
class CreateDeviceProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [createDevice].
  CreateDeviceProvider(
    String deviceToken,
  ) : this._internal(
          (ref) => createDevice(
            ref as CreateDeviceRef,
            deviceToken,
          ),
          from: createDeviceProvider,
          name: r'createDeviceProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$createDeviceHash,
          dependencies: CreateDeviceFamily._dependencies,
          allTransitiveDependencies:
              CreateDeviceFamily._allTransitiveDependencies,
          deviceToken: deviceToken,
        );

  CreateDeviceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.deviceToken,
  }) : super.internal();

  final String deviceToken;

  @override
  Override overrideWith(
    FutureOr<bool> Function(CreateDeviceRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CreateDeviceProvider._internal(
        (ref) => create(ref as CreateDeviceRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        deviceToken: deviceToken,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _CreateDeviceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CreateDeviceProvider && other.deviceToken == deviceToken;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, deviceToken.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CreateDeviceRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `deviceToken` of this provider.
  String get deviceToken;
}

class _CreateDeviceProviderElement
    extends AutoDisposeFutureProviderElement<bool> with CreateDeviceRef {
  _CreateDeviceProviderElement(super.provider);

  @override
  String get deviceToken => (origin as CreateDeviceProvider).deviceToken;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
