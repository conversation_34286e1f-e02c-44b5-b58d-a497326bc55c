// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fcm_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fcmControllerHash() => r'98779cb14c0b4fb4e7a5ba7ce48dcba603fa8789';

/// See also [FcmController].
@ProviderFor(FcmController)
final fcmControllerProvider =
    AutoDisposeAsyncNotifierProvider<FcmController, FCMState>.internal(
  FcmController.new,
  name: r'fcmControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fcmControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FcmController = AutoDisposeAsyncNotifier<FCMState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
