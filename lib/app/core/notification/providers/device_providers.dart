import 'package:gomama/app/core/notification/repository/device_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'device_providers.g.dart';

@riverpod
Future<bool> createDevice(
  CreateDeviceRef ref,
  String deviceToken,
) async {
  try {
    return await ref.read(deviceRepositoryProvider).create(deviceToken) ??
        false;
  } catch (error) {
    Groveman.error('createDevice', error: error);
    rethrow;
  }
}
