import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:gomama/app/core/notification/model/fcm_state.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:groveman/groveman.dart';
import 'package:http/http.dart' as http;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'fcm_providers.g.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
// initialize android channel
const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel', // id
  'High Importance Notifications', // title
  description:
      'This channel is used for important notifications.', // description
  importance: Importance.max,
);

@riverpod
class FcmController extends _$FcmController {
  @override
  Future<FCMState> build() async {
    String? _fcmToken;

    try {
      /// flutterLocalNotificationsPlugin initialize process
      // Initialization Settings for Android
      const initializationSettingsAndroid = AndroidInitializationSettings(
        '@drawable/ic_launcher_foreground_image_resized',
      );
      // Initialization Settings for iOS
      const initializationSettingsIOS = DarwinInitializationSettings();
      // InitializationSettings
      // for initializing settings for both platforms (Android & iOS)
      const initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: processResponse,
        onDidReceiveBackgroundNotificationResponse: processResponse,
      );

      /// Create an Android Notification Channel.
      ///
      /// We use this channel in the `AndroidManifest.xml` file to override the
      /// default FCM channel to enable heads up notifications.
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      /// request notification permission
      final settings = await FirebaseMessaging.instance.requestPermission();
      final hasPermission =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
              settings.authorizationStatus == AuthorizationStatus.provisional;

      if (!hasPermission) {
        return const FCMState();
      }

      // TODO(kkcy): handle ios apn delay
      // For apple platforms, ensure the APNS token is available before making any FCM plugin API calls
      final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      if (apnsToken != null) {
        // APNS token is available, make FCM plugin API requests...
      }

      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions();

      _fcmToken = await FirebaseMessaging.instance.getToken();
      Groveman.info('FCM Token: $_fcmToken');

      // handle on token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen((newFcmToken) {
        // TODO: If necessary send token to application server.
        _fcmToken = newFcmToken;
        // Note: This callback is fired at each app startup and whenever a new
        // token is generated.
      }).onError((error) {
        Groveman.error('onTokenRefresh', error: error);
      });

      // Setup message handlers
      _setupMessageHandlers();

      return FCMState(
        fcmToken: _fcmToken,
        isInitialized: true,
        hasPermission: true,
      );
    } catch (error, stackTrace) {
      Groveman.error(
        'Error initializing FCM',
        error: error,
        stackTrace: stackTrace,
      );
      return const FCMState();
    }
  }

  void _setupMessageHandlers() {
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // app terminated processing
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) async {
      final notification = message?.notification;
      if (message == null || notification == null) return;

      final messageSessionId = message.data.containsKey('session_id')
          ? message.data['session_id'] as String
          : null;
      final messageType = message.data.containsKey('type')
          ? message.data['type'] as String
          : null;

      Groveman.info('getInitialMessage set initial session id');

      if (messageType == 'reminder' &&
          messageSessionId != null &&
          messageSessionId.isNotEmpty == true) {
        ref
            .read(initialSessionIdProvider.notifier)
            .update((state) => messageSessionId);
      }
    }).catchError((error) {
      Groveman.error('getInitialMessage failed', error: error);
    });
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    Groveman.info('_handleForegroundMessage');
    final notification = message.notification;
    if (notification == null) return;

    final messageKey =
        message.data.containsKey('key') ? message.data['key'] as String : null;
    final messageSessionId = message.data.containsKey('session_id')
        ? message.data['session_id'] as String
        : null;
    final messageType = message.data.containsKey('type')
        ? message.data['type'] as String
        : null;

    if (messageType == 'reminder' &&
        messageSessionId != null &&
        messageSessionId.isNotEmpty == true) {
      // intercept notification & trigger ReviewSessionView popup
      final context = routerKey.currentContext;
      if (context != null && context.mounted) {
        // avoid opening popup if already opened
        final sessionOngoing = ref.read(reviewSessionOngoingProvider);
        if (sessionOngoing == true) return;

        Groveman.info('_handleForegroundMessage popup');

        await showCupertinoModalPopup(
          barrierDismissible: false,
          context: context,
          builder: (context) => ReviewSessionView(messageSessionId),
        );
      }

      return;
    }

    // get image URL from notification
    DefaultStyleInformation styleInformation;
    final imageUrl =
        notification.android?.imageUrl ?? message.data['image_url'] as String?;

    if (imageUrl != null) {
      final response = await http.get(Uri.parse(imageUrl));
      final imageBitMap = ByteArrayAndroidBitmap.fromBase64String(
        base64Encode(response.bodyBytes),
      );

      // image information for android
      styleInformation = BigPictureStyleInformation(
        imageBitMap,
        largeIcon: imageBitMap,
        contentTitle: notification.title,
        summaryText: notification.body,
      );
    } else {
      styleInformation = BigTextStyleInformation(
        notification.body ?? '',
      );
    }

    await flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          channel.id,
          channel.name,
          channelDescription: channel.description,
          priority: Priority.max,
          importance: Importance.max,
          styleInformation: styleInformation,
        ),
        // TODO: ios image handling
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          // String? subtitle,
          threadIdentifier: '$messageType $messageKey',
          attachments: imageUrl != null
              ? [DarwinNotificationAttachment(imageUrl)]
              : null,
          interruptionLevel: InterruptionLevel.active,
        ),
      ),
      payload: json.encode(message.data),
    );
  }

  // handle notification click when app is background
  Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    Groveman.info('_handleMessageOpenedApp');
    final notification = message.notification;
    if (notification == null) return;

    final messageSessionId = message.data.containsKey('session_id')
        ? message.data['session_id'] as String
        : null;
    final messageType = message.data.containsKey('type')
        ? message.data['type'] as String
        : null;

    if (messageType == 'reminder' &&
        messageSessionId != null &&
        messageSessionId.isNotEmpty == true) {
      // intercept notification & trigger ReviewSessionView popup
      final context = routerKey.currentContext;
      if (context != null && context.mounted) {
        // avoid opening popup if already opened
        final sessionOngoing = ref.read(reviewSessionOngoingProvider);
        if (sessionOngoing == true) return;

        Groveman.info('processRemoteResponse popup');
        await showCupertinoModalPopup(
          barrierDismissible: false,
          context: context,
          builder: (context) => ReviewSessionView(messageSessionId),
        );
      }
      return;
    }

    // await processRemoteResponse(message.data);
  }
}

// app background processing
Future<void> processRemoteResponse(Map<String, dynamic> response) async {
  Groveman.info('processRemoteResponse');
}

// app foreground processing
Future<void> processResponse(NotificationResponse response) async {
  Groveman.info('processResponse');
}
