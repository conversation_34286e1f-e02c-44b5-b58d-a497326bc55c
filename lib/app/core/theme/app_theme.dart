import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AppTheme {
  /// for getting light theme
  ThemeData get lightTheme {
    return ThemeData(
      fontFamily: 'QuickSand',
      textTheme: const TextTheme(
        titleLarge: TextStyle(
          fontFamily: 'QuickSand',
          color: CustomColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      colorScheme: const ColorScheme.light(
        primary: CustomColors.primaries,
        secondary: CustomColors.secondary,
        outline: CustomColors.primaries,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      iconTheme: const IconThemeData(
        color: CustomColors.primaries,
      ),
      dividerTheme: const DividerThemeData(
        color: CustomColors.divider,
      ),
      cardTheme: const CardTheme(
        color: CustomColors.secondaryLight,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
            const EdgeInsets.symmetric(horizontal: 48, vertical: 16),
          ),
          textStyle: WidgetStateProperty.all<TextStyle>(
            const TextStyle(fontWeight: FontWeight.w700),
          ),
          backgroundColor: WidgetStateProperty.all<Color>(
            CustomColors.secondaryLight,
          ),
          foregroundColor: WidgetStateProperty.all<Color>(
            CustomColors.text,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
            const EdgeInsets.symmetric(horizontal: 28, vertical: 16),
          ),
          textStyle: WidgetStateProperty.all<TextStyle>(
            const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ),
      inputDecorationTheme: const InputDecorationTheme(
        labelStyle: TextStyle(
          color: CustomColors.primaries,
          fontWeight: FontWeight.bold,
        ),
        hintStyle: TextStyle(color: CustomColors.placeholder),
      ),
    );
  }

  /// for getting dark theme
  ThemeData get darkTheme {
    return ThemeData(
      fontFamily: 'QuickSand',
      textTheme: const TextTheme(
        titleLarge: TextStyle(
          fontFamily: 'QuickSand',
          color: CustomColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      colorScheme: const ColorScheme.light(
        primary: CustomColors.primaries,
        secondary: CustomColors.secondary,
        outline: CustomColors.primaries,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      iconTheme: const IconThemeData(
        color: CustomColors.primaries,
      ),
      dividerTheme: const DividerThemeData(
        color: CustomColors.divider,
      ),
      cardTheme: const CardTheme(
        color: CustomColors.secondaryLight,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
            const EdgeInsets.symmetric(horizontal: 48, vertical: 16),
          ),
          textStyle: WidgetStateProperty.all<TextStyle>(
            const TextStyle(fontWeight: FontWeight.w700),
          ),
          backgroundColor: WidgetStateProperty.all<Color>(
            CustomColors.secondaryLight,
          ),
          foregroundColor: WidgetStateProperty.all<Color>(
            CustomColors.text,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
            const EdgeInsets.symmetric(horizontal: 28, vertical: 16),
          ),
          textStyle: WidgetStateProperty.all<TextStyle>(
            const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ),
      inputDecorationTheme: const InputDecorationTheme(
        labelStyle: TextStyle(
          color: CustomColors.primaries,
          fontWeight: FontWeight.bold,
        ),
        hintStyle: TextStyle(color: CustomColors.placeholder),
      ),
    );
  }
}

/// for providing app theme [AppTheme]
final appThemeProvider = Provider<AppTheme>((_) => AppTheme());
