import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/features/about/view/about_view.dart';
import 'package:gomama/app/features/auth/view/about_gomama_view.dart';
import 'package:gomama/app/features/auth/view/auth_view.dart';
import 'package:gomama/app/features/auth/view/frequently_asked_question_view.dart';
import 'package:gomama/app/features/auth/view/privacy_policy_view.dart';
import 'package:gomama/app/features/auth/view/profile_view.dart';
import 'package:gomama/app/features/auth/view/term_service_view.dart';
import 'package:gomama/app/features/commerce/address/view/address_view.dart';
import 'package:gomama/app/features/commerce/cart/view/cart_view.dart';
import 'package:gomama/app/features/commerce/coin/view/daily_coins_view.dart';
import 'package:gomama/app/features/commerce/collections/view/collection_view.dart';
import 'package:gomama/app/features/commerce/commerce_view.dart';
import 'package:gomama/app/features/commerce/help/view/commerce_help_view.dart';
import 'package:gomama/app/features/commerce/orders/view/my_orders_view.dart';
import 'package:gomama/app/features/commerce/orders/view/order_details_view.dart';
import 'package:gomama/app/features/commerce/orders/view/order_placed_view.dart';
import 'package:gomama/app/features/commerce/orders/view/purchase_history_view.dart';
import 'package:gomama/app/features/commerce/price_match/view/my_requests_view.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_view.dart';
import 'package:gomama/app/features/commerce/products/view/my_likes_view.dart';
import 'package:gomama/app/features/commerce/products/view/product_reviews_view.dart';
import 'package:gomama/app/features/commerce/products/view/product_view.dart';
import 'package:gomama/app/features/commerce/search/view/commerce_search_listing_view.dart';
import 'package:gomama/app/features/commerce/search/view/commerce_search_view.dart';
import 'package:gomama/app/features/discover/view/discover_details_view.dart';
import 'package:gomama/app/features/discover/view/discover_view.dart';
import 'package:gomama/app/features/edit_profile/view/delete_account_view.dart';
import 'package:gomama/app/features/edit_profile/view/edit_profile_view.dart';
import 'package:gomama/app/features/edit_profile/view/fill_verification_details.dart';
import 'package:gomama/app/features/edit_profile/view/selfie_form_view.dart';
import 'package:gomama/app/features/explore/view/explore_view.dart';
import 'package:gomama/app/features/favourites/view/favourite_view.dart';
import 'package:gomama/app/features/help/view/help_view.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/view/access_view.dart';
import 'package:gomama/app/features/listing/view/flag_listing_details.dart';
import 'package:gomama/app/features/listing/view/flag_listing_view.dart';
import 'package:gomama/app/features/listing/view/listing_amenities_view.dart';
import 'package:gomama/app/features/listing/view/listing_gallery_photo_view.dart';
import 'package:gomama/app/features/listing/view/listing_reviews_view.dart';
import 'package:gomama/app/features/listing/view/listing_view.dart';
import 'package:gomama/app/features/listing/view/review_listing_view.dart';
import 'package:gomama/app/features/main/view/home_view.dart';
import 'package:gomama/app/features/main/view/onboarding_view.dart';
import 'package:gomama/app/features/main/view/permission_view.dart';
import 'package:gomama/app/features/main/view/splash_view.dart';
import 'package:gomama/app/features/search/view/search_view.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/view/current_session_view.dart';
import 'package:gomama/app/features/session/view/past_session_view.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/features/session/view/session_history_view.dart';
import 'package:gomama/app/features/singpass/model/singpass.dart';
import 'package:gomama/app/features/singpass/provider/singpass_providers.dart';
import 'package:gomama/app/features/singpass/view/singpass_form_view.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:gomama/app/features/suggest/view/suggest_history_details_view.dart';
import 'package:gomama/app/features/suggest/view/suggest_history_view.dart';
import 'package:gomama/app/features/suggest/view/suggest_nursing_facility_view.dart';
import 'package:gomama/app/features/suggest/view/suggest_pod_view.dart';
import 'package:gomama/app/features/suggest/view/suggest_view.dart';
import 'package:gomama/app/features/verification/view/verification_view.dart';

part 'routes.g.dart';

final GlobalKey<NavigatorState> shellNavigatorKey = GlobalKey<NavigatorState>();

// nested routes
@TypedStatefulShellRoute<HomeRoute>(
  branches: [
    TypedStatefulShellBranch<ExploreData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<ExploreRoute>(
          path: ExploreView.routePath,
          name: ExploreView.routeName,
        ),
      ],
    ),
    TypedStatefulShellBranch<SuggestData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<SuggestRoute>(
          path: SuggestView.routePath,
          name: SuggestView.routeName,
        ),
      ],
    ),
    // TypedStatefulShellBranch<AccessData>(
    //   routes: <TypedRoute<RouteData>>[
    //     TypedGoRoute<AccessRoute>(
    //       path: AccessView.routePath,
    //       name: AccessView.routeName,
    //     ),
    //   ],
    // ),
    TypedStatefulShellBranch<CommerceData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<CommerceRoute>(
          path: CommerceView.routePath,
          name: CommerceView.routeName,
          // temp: nest collection & product route here
          routes: [
            TypedGoRoute<CommerceSearchRoute>(
              path: CommerceSearchView.routePath,
              name: CommerceSearchView.routeName,
              routes: [
                TypedGoRoute<CommerceSearchListingRoute>(
                  path: CommerceSearchListingView.routePath,
                  name: CommerceSearchListingView.routeName,
                ),
              ],
            ),
            TypedGoRoute<CollectionRoute>(
              path: CollectionView.routePath,
              name: CollectionView.routeName,
            ),
            TypedGoRoute<ProductRoute>(
              path: ProductView.routePath,
              name: ProductView.routeName,
            ),
            TypedGoRoute<ProductReviewsRoute>(
              path: ProductReviewsView.routePath,
              name: ProductReviewsView.routeName,
            ),
            TypedGoRoute<CartRoute>(
              path: CartView.routePath,
              name: CartView.routeName,
            ),
            TypedGoRoute<MyOrdersRoute>(
              path: MyOrdersView.routePath,
              name: MyOrdersView.routeName,
            ),
            TypedGoRoute<PurchaseHistoryRoute>(
              path: PurchaseHistoryView.routePath,
              name: PurchaseHistoryView.routeName,
            ),
            TypedGoRoute<OrderDetailsRoute>(
              path: OrderDetailsView.routePath,
              name: OrderDetailsView.routeName,
            ),
            TypedGoRoute<PriceMatchRoute>(
              path: PriceMatchView.routePath,
              name: PriceMatchView.routeName,
              routes: [
                TypedGoRoute<PriceMatchRequestRoute>(
                  path: PriceMatchRequestView.routePath,
                  name: PriceMatchRequestView.routeName,
                ),
              ],
            ),
            TypedGoRoute<MyRequestsRoute>(
              path: MyRequestsView.routePath,
              name: MyRequestsView.routeName,
            ),
            TypedGoRoute<AddressRoute>(
              path: AddressView.routePath,
              name: AddressView.routeName,
            ),
            TypedGoRoute<DailyCoinsRoute>(
              path: DailyCoinsView.routePath,
              name: DailyCoinsView.routeName,
            ),
            TypedGoRoute<OrderPlacedRoute>(
              path: OrderPlacedView.routePath,
              name: OrderPlacedView.routeName,
            ),
            TypedGoRoute<CommerceHelpRoute>(
              path: CommerceHelpView.routePath,
              name: CommerceHelpView.routeName,
            ),
            TypedGoRoute<MyLikesRoute>(
              path: MyLikesView.routePath,
              name: MyLikesView.routeName,
            ),
          ],
        ),
      ],
    ),
    TypedStatefulShellBranch<DiscoverData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<DiscoverRoute>(
          path: DiscoverView.routePath,
          name: DiscoverView.routeName,
          routes: [
            TypedGoRoute<DiscoverDetailsRoute>(
              path: DiscoverDetailsView.routePath,
              name: DiscoverDetailsView.routeName,
            ),
          ],
        ),
      ],
    ),
    TypedStatefulShellBranch<ProfileData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<ProfileRoute>(
          path: ProfileView.routePath,
          name: ProfileView.routeName,
          routes: [
            TypedGoRoute<EditProfileRoute>(
              path: EditProfileView.routePath,
              name: EditProfileView.routeName,
            ),
            TypedGoRoute<DeleteAccountRoute>(
              path: DeleteAccountView.routePath,
              name: DeleteAccountView.routeName,
            ),
          ],
        ),
      ],
    ),
  ],
)
class HomeRoute extends StatefulShellRouteData {
  const HomeRoute();

  /// Important note on this redirect function: this isn't reactive.
  /// No redirect will be triggered on a user role change.
  ///
  /// This is currently unsupported.
  // @override
  // FutureOr<String?> redirect(BuildContext context, GoRouterState state) async {
  //   final userRole = await ProviderScope.containerOf(context).read(
  //     permissionsProvider.future,
  //   );

  //   return userRole.map(
  //     admin: (_) => const AdminRoute().location,
  //     user: (_) => const UserRoute().location,
  //     guest: (_) => const GuestRoute().location,
  //     none: (_) => null,
  //   );
  // }

  @override
  Widget builder(
    BuildContext context,
    GoRouterState state,
    StatefulNavigationShell navigationShell,
  ) {
    return navigationShell;
  }

  static const String $restorationScopeId = 'restorationScopeId';

  static Widget $navigatorContainerBuilder(
    BuildContext context,
    StatefulNavigationShell navigationShell,
    List<Widget> children,
  ) {
    return HomeView(
      navigationShell: navigationShell,
      children: children,
    );
  }
}

@TypedGoRoute<SplashRoute>(
  path: SplashView.routePath,
  name: SplashView.routeName,
)
class SplashRoute extends GoRouteData {
  const SplashRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SplashView();
  }
}

@TypedGoRoute<PermissionRoute>(
  path: PermissionView.routePath,
  name: PermissionView.routeName,
)
class PermissionRoute extends GoRouteData {
  const PermissionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PermissionView();
  }
}

@TypedGoRoute<AuthRoute>(
  path: AuthView.routePath,
  name: AuthView.routeName,
)
class AuthRoute extends GoRouteData {
  const AuthRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AuthView();
  }
}

@TypedGoRoute<OnboardingRoute>(
  path: OnboardingView.routePath,
  name: OnboardingView.routeName,
)
class OnboardingRoute extends GoRouteData {
  const OnboardingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const OnboardingView();
  }
}

// @TypedGoRoute<ProfileRoute>(
//   path: ProfileView.routePath,
//   name: ProfileView.routeName,
//   routes: [
//     TypedGoRoute<EditProfileRoute>(
//       path: EditProfileView.routePath,
//       name: EditProfileView.routeName,
//     ),
//   ],
// )

class ProfileData extends StatefulShellBranchData {
  const ProfileData();
}

class ProfileRoute extends GoRouteData {
  const ProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ProfileView();
  }
}

@TypedGoRoute<FavouritesRoute>(
  path: FavouritesView.routePath,
  name: FavouritesView.routeName,
)
class FavouritesRoute extends GoRouteData {
  const FavouritesRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const FavouritesView();
  }
}

@TypedGoRoute<SessionHistoryRoute>(
  path: SessionHistoryView.routePath,
  name: SessionHistoryView.routeName,
)
class SessionHistoryRoute extends GoRouteData {
  const SessionHistoryRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SessionHistoryView();
  }
}

@TypedGoRoute<CurrentSessionRoute>(
  path: CurrentSessionView.routePath,
  name: CurrentSessionView.routeName,
)
class CurrentSessionRoute extends GoRouteData {
  const CurrentSessionRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CurrentSessionView();
  }
}

@TypedGoRoute<ReviewListingRoute>(
  path: ReviewListingView.routePath,
  name: ReviewListingView.routeName,
)
class ReviewListingRoute extends GoRouteData {
  const ReviewListingRoute(
    this.id, {
    this.$extra,
  });
  final String id;
  final Listing? $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ReviewListingView(id, listing: $extra);
  }
}

@TypedGoRoute<ReviewSessionRoute>(
  path: ReviewSessionView.routePath,
  name: ReviewSessionView.routeName,
)
class ReviewSessionRoute extends GoRouteData {
  const ReviewSessionRoute(
    this.id, {
    this.$extra,
  });
  final String id;
  final Session? $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ReviewSessionView(id, initialSession: $extra);
  }
}

@TypedGoRoute<PastSessionRoute>(
  path: PastSessionView.routePath,
  name: PastSessionView.routeName,
)
class PastSessionRoute extends GoRouteData {
  const PastSessionRoute(this.$extra);
  final Session $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PastSessionView(initialSession: $extra);
  }
}

@TypedGoRoute<HelpRoute>(
  path: HelpView.routePath,
  name: HelpView.routeName,
)
class HelpRoute extends GoRouteData {
  const HelpRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const HelpView();
  }
}

@TypedGoRoute<AboutRoute>(
  path: AboutView.routePath,
  name: AboutView.routeName,
)
class AboutRoute extends GoRouteData {
  const AboutRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AboutView();
  }
}

@TypedGoRoute<AboutGomamaRoute>(
  path: AboutGomamaView.routePath,
  name: AboutGomamaView.routeName,
)
class AboutGomamaRoute extends GoRouteData {
  const AboutGomamaRoute(this.$extra);
  final String $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return AboutGomamaView(initialContent: $extra);
  }
}

@TypedGoRoute<PrivacyPolicyRoute>(
  path: PrivacyPolicyView.routePath,
  name: PrivacyPolicyView.routeName,
)
class PrivacyPolicyRoute extends GoRouteData {
  const PrivacyPolicyRoute(this.$extra);
  final String $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PrivacyPolicyView(initialContent: $extra);
  }
}

@TypedGoRoute<TermServiceRoute>(
  path: TermServiceView.routePath,
  name: TermServiceView.routeName,
)
class TermServiceRoute extends GoRouteData {
  const TermServiceRoute(this.$extra);
  final String $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TermServiceView(initialContent: $extra);
  }
}

@TypedGoRoute<FrequentlyAskedQuestionRoute>(
  path: FrequentlyAskedQuestionView.routePath,
  name: FrequentlyAskedQuestionView.routeName,
)
class FrequentlyAskedQuestionRoute extends GoRouteData {
  const FrequentlyAskedQuestionRoute(this.$extra);
  final String $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return FrequentlyAskedQuestionView(initialContent: $extra);
  }
}

class EditProfileRoute extends GoRouteData {
  const EditProfileRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditProfileView();
  }
}

class DeleteAccountRoute extends GoRouteData {
  const DeleteAccountRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const DeleteAccountView();
  }
}

@TypedGoRoute<VerificationRoute>(
  path: VerificationView.routePath,
  name: VerificationView.routeName,
)
class VerificationRoute extends GoRouteData {
  const VerificationRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const VerificationView();
  }
}

@TypedGoRoute<SingpassFormRoute>(
  path: SingpassFormView.routePath,
  name: SingpassFormView.routeName,
)
class SingpassFormRoute extends GoRouteData {
  const SingpassFormRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return Consumer(
      builder: (context, ref, _) {
        final singpassResponse = ref.watch(singpassResponseProvider);
        if (singpassResponse == null) {
          // Handle missing data gracefully
          return const Scaffold(
            body: Center(child: Text('No Singpass data found.')),
          );
        }

        return SingpassFormView(singpassResponse);
      },
    );
  }
}

@TypedGoRoute<SelfieFormRoute>(
  path: SelfieFormView.routePath,
  name: SelfieFormView.routeName,
)
class SelfieFormRoute extends GoRouteData {
  const SelfieFormRoute(this.$photoPath);
  final String $photoPath;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SelfieFormView(photoPath: $photoPath);
  }
}

@TypedGoRoute<FillVerificationDetailsRoute>(
  path: FillVerificationDetailsView.routePath,
  name: FillVerificationDetailsView.routeName,
)
class FillVerificationDetailsRoute extends GoRouteData {
  const FillVerificationDetailsRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const FillVerificationDetailsView();
  }
}

@TypedGoRoute<SearchRoute>(
  path: SearchView.routePath,
  name: SearchView.routeName,
)
class SearchRoute extends GoRouteData {
  const SearchRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SearchView();
  }
}

@TypedGoRoute<ListingRoute>(
  path: ListingView.routePath,
  name: ListingView.routeName,
  routes: [
    // TypedGoRoute<CurrentSessionRoute>(
    //   path: CurrentSessionView.routePath,
    //   name: CurrentSessionView.routeName,
    // ),
    // TypedGoRoute<FlagListingRoute>(
    //   path: FlagListingView.routePath,
    //   name: FlagListingView.routeName,
    //   routes: [
    //     TypedGoRoute<FlagListingDetailsRoute>(
    //       path: FlagListingDetailsView.routePath,
    //       name: FlagListingDetailsView.routeName,
    //     ),
    //   ],
    // ),
    TypedGoRoute<ListingReviewsRoute>(
      path: ListingReviewsView.routePath,
      name: ListingReviewsView.routeName,
    ),
    TypedGoRoute<ListingAmenitiesRoute>(
      path: ListingAmenitiesView.routePath,
      name: ListingAmenitiesView.routeName,
    ),
  ],
)
class ListingRoute extends GoRouteData {
  const ListingRoute(
    this.listingId, {
    this.$extra,
  });
  final String listingId;
  final Listing? $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ListingView(initialListing: $extra, listingId: listingId);
  }
}

@TypedGoRoute<ListingGalleryPhotoViewRoute>(
  path: ListingGalleryPhotoView.routePath,
  name: ListingGalleryPhotoView.routeName,
)
class ListingGalleryPhotoViewRoute extends GoRouteData {
  const ListingGalleryPhotoViewRoute(this.$extra);
  final List<String> $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ListingGalleryPhotoView(initialImageUrls: $extra);
  }
}

@TypedGoRoute<FlagListingRoute>(
  path: FlagListingView.routePath,
  name: FlagListingView.routeName,
  routes: [
    TypedGoRoute<FlagListingDetailsRoute>(
      path: FlagListingDetailsView.routePath,
      name: FlagListingDetailsView.routeName,
    ),
  ],
)
class FlagListingRoute extends GoRouteData {
  const FlagListingRoute(this.$extra);
  final Listing $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return FlagListingView(
      listing: $extra,
    );
  }
}

class FlagListingDetailsRoute extends GoRouteData {
  const FlagListingDetailsRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const FlagListingDetailsView();
  }

  void push(BuildContext context) {}
}

class ListingReviewsRoute extends GoRouteData {
  const ListingReviewsRoute(
    this.listingId,
    this.$extra,
  );
  final String listingId;
  final Listing $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ListingReviewsView(
      listingId: listingId,
      initialListing: $extra,
    );
  }
}

class ListingAmenitiesRoute extends GoRouteData {
  const ListingAmenitiesRoute(
    this.listingId,
    this.$extra,
  );
  final String listingId;
  final Listing $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ListingAmenitiesView(
      listingId: listingId,
      initialListing: $extra,
    );
  }
}

class ExploreData extends StatefulShellBranchData {
  const ExploreData();
}

class ExploreRoute extends GoRouteData {
  const ExploreRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ExploreView();
  }
}

class SuggestData extends StatefulShellBranchData {
  const SuggestData();
}

class SuggestRoute extends GoRouteData {
  const SuggestRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SuggestView();
  }
}

@TypedGoRoute<SuggestPodRoute>(
  path: SuggestPodView.routePath,
  name: SuggestPodView.routeName,
)
class SuggestPodRoute extends GoRouteData {
  const SuggestPodRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SuggestPodView();
  }
}

@TypedGoRoute<SuggestNursingFacilityRoute>(
  path: SuggestNursingFacilityView.routePath,
  name: SuggestNursingFacilityView.routeName,
)
class SuggestNursingFacilityRoute extends GoRouteData {
  const SuggestNursingFacilityRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SuggestNursingFacilityView();
  }
}

@TypedGoRoute<SuggestHistoryRoute>(
  path: SuggestHistoryView.routePath,
  name: SuggestHistoryView.routeName,
)
class SuggestHistoryRoute extends GoRouteData {
  const SuggestHistoryRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SuggestHistoryView();
  }
}

@TypedGoRoute<SuggestHistoryDetailsRoute>(
  path: SuggestHistoryDetailsView.routePath,
  name: SuggestHistoryDetailsView.routeName,
)
class SuggestHistoryDetailsRoute extends GoRouteData {
  const SuggestHistoryDetailsRoute(this.$extra);
  final ListingSuggestion $extra;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SuggestHistoryDetailsView(initialListingSuggestion: $extra);
  }
}

class AccessData extends StatefulShellBranchData {
  const AccessData();
}

class AccessRoute extends GoRouteData {
  const AccessRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AccessView();
  }
}

class CommerceData extends StatefulShellBranchData {
  const CommerceData();
}

class CommerceRoute extends GoRouteData {
  const CommerceRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CommerceView();
  }
}

class DiscoverData extends StatefulShellBranchData {
  const DiscoverData();
}

class DiscoverRoute extends GoRouteData {
  const DiscoverRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const DiscoverView();
  }
}

class DiscoverDetailsRoute extends GoRouteData {
  const DiscoverDetailsRoute(this.eventId);
  final String eventId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DiscoverDetailsView(eventId);
  }
}

// class AdminRoute extends GoRouteData {
//   const AdminRoute();

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return const AdminPage();
//   }
// }

// class UserRoute extends GoRouteData {
//   const UserRoute();

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return const UserPage();
//   }
// }

// class GuestRoute extends GoRouteData {
//   const GuestRoute();

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return const GuestPage();
//   }
// }

/// This route shows how to parameterize a simple page and how to pass a simple query parameter.
// @TypedGoRoute<DetailsRoute>(path: '/details/:id')
// class DetailsRoute extends GoRouteData {
//   const DetailsRoute(this.id, {this.isNuke = false});
//   final int id;
//   final bool isNuke;

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return DetailsPage(
//       id,
//       isNuclearCode: isNuke,
//     );
//   }
// }

// @TypedGoRoute<CollectionRoute>(
//   path: CollectionView.routePath,
//   name: CollectionView.routeName,
// )
class CollectionRoute extends GoRouteData {
  const CollectionRoute(this.id);
  final String id;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return CollectionView(id);
  }
}

class ProductRoute extends GoRouteData {
  const ProductRoute(this.productHandle);
  final String productHandle;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ProductView(productHandle);
  }
}

class CartRoute extends GoRouteData {
  const CartRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CartView();
  }
}

class CommerceSearchRoute extends GoRouteData {
  const CommerceSearchRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CommerceSearchView();
  }
}

class CommerceSearchListingRoute extends GoRouteData {
  const CommerceSearchListingRoute(this.keywords);
  final String keywords;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return CommerceSearchListingView(keywords);
  }
}

class MyOrdersRoute extends GoRouteData {
  const MyOrdersRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MyOrdersView();
  }
}

class PurchaseHistoryRoute extends GoRouteData {
  const PurchaseHistoryRoute(this.initialTab);
  final String initialTab;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PurchaseHistoryView(initialTab: initialTab);
  }
}

class OrderDetailsRoute extends GoRouteData {
  const OrderDetailsRoute(this.orderId, {this.isCompleted = false});
  final String orderId;
  final bool isCompleted;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return OrderDetailsView(
      orderId: orderId,
      isCompleted: isCompleted,
    );
  }
}

class PriceMatchRoute extends GoRouteData {
  const PriceMatchRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PriceMatchView();
  }
}

class PriceMatchRequestRoute extends GoRouteData {
  const PriceMatchRequestRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PriceMatchRequestView();
  }
}

class MyRequestsRoute extends GoRouteData {
  const MyRequestsRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MyRequestsView();
  }
}

class AddressRoute extends GoRouteData {
  const AddressRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AddressView();
  }
}

class DailyCoinsRoute extends GoRouteData {
  const DailyCoinsRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const DailyCoinsView();
  }
}

class OrderPlacedRoute extends GoRouteData {
  const OrderPlacedRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const OrderPlacedView();
  }
}

class CommerceHelpRoute extends GoRouteData {
  const CommerceHelpRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CommerceHelpView();
  }
}

class MyLikesRoute extends GoRouteData {
  const MyLikesRoute();

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MyLikesView();
  }
}

class ProductReviewsRoute extends GoRouteData {
  const ProductReviewsRoute(this.productId);
  final String productId;

  static final GlobalKey<NavigatorState> $parentNavigatorKey = routerKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ProductReviewsView(productId);
  }
}
