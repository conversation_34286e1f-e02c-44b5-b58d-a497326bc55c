// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'routes.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $homeRoute,
      $splashRoute,
      $permissionRoute,
      $authRoute,
      $onboardingRoute,
      $favouritesRoute,
      $sessionHistoryRoute,
      $currentSessionRoute,
      $reviewListingRoute,
      $reviewSessionRoute,
      $pastSessionRoute,
      $helpRoute,
      $aboutRoute,
      $aboutGomamaRoute,
      $privacyPolicyRoute,
      $termServiceRoute,
      $frequentlyAskedQuestionRoute,
      $verificationRoute,
      $singpassFormRoute,
      $selfieFormRoute,
      $fillVerificationDetailsRoute,
      $searchRoute,
      $listingRoute,
      $listingGalleryPhotoViewRoute,
      $flagListingRoute,
      $suggestPodRoute,
      $suggestNursingFacilityRoute,
      $suggestHistoryRoute,
      $suggestHistoryDetailsRoute,
    ];

RouteBase get $homeRoute => StatefulShellRouteData.$route(
      restorationScopeId: HomeRoute.$restorationScopeId,
      navigatorContainerBuilder: HomeRoute.$navigatorContainerBuilder,
      factory: $HomeRouteExtension._fromState,
      branches: [
        StatefulShellBranchData.$branch(
          routes: [
            GoRouteData.$route(
              path: '/explore',
              name: 'explore',
              factory: $ExploreRouteExtension._fromState,
            ),
          ],
        ),
        StatefulShellBranchData.$branch(
          routes: [
            GoRouteData.$route(
              path: '/suggest',
              name: 'suggest',
              factory: $SuggestRouteExtension._fromState,
            ),
          ],
        ),
        StatefulShellBranchData.$branch(
          routes: [
            GoRouteData.$route(
              path: '/commerce',
              name: 'commerce',
              factory: $CommerceRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'commerce-search',
                  name: 'commerce-search',
                  parentNavigatorKey: CommerceSearchRoute.$parentNavigatorKey,
                  factory: $CommerceSearchRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: ':keywords',
                      name: 'commerce-search-listing',
                      parentNavigatorKey:
                          CommerceSearchListingRoute.$parentNavigatorKey,
                      factory: $CommerceSearchListingRouteExtension._fromState,
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'collections/:id',
                  name: 'collection-view',
                  parentNavigatorKey: CollectionRoute.$parentNavigatorKey,
                  factory: $CollectionRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'products/:productHandle',
                  name: 'product-view',
                  parentNavigatorKey: ProductRoute.$parentNavigatorKey,
                  factory: $ProductRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'products/:productId/user-reviews',
                  name: 'user-reviews',
                  parentNavigatorKey: ProductReviewsRoute.$parentNavigatorKey,
                  factory: $ProductReviewsRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'cart',
                  name: 'cart',
                  parentNavigatorKey: CartRoute.$parentNavigatorKey,
                  factory: $CartRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'my-orders',
                  name: 'my-orders',
                  parentNavigatorKey: MyOrdersRoute.$parentNavigatorKey,
                  factory: $MyOrdersRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'history',
                  name: 'purchase-history',
                  parentNavigatorKey: PurchaseHistoryRoute.$parentNavigatorKey,
                  factory: $PurchaseHistoryRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'order-detail/:orderId',
                  name: 'order-detail-view',
                  parentNavigatorKey: OrderDetailsRoute.$parentNavigatorKey,
                  factory: $OrderDetailsRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'price-match',
                  name: 'price-match',
                  parentNavigatorKey: PriceMatchRoute.$parentNavigatorKey,
                  factory: $PriceMatchRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'request',
                      name: 'price-match-request',
                      parentNavigatorKey:
                          PriceMatchRequestRoute.$parentNavigatorKey,
                      factory: $PriceMatchRequestRouteExtension._fromState,
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'my-requests',
                  name: 'my-requests',
                  parentNavigatorKey: MyRequestsRoute.$parentNavigatorKey,
                  factory: $MyRequestsRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'address',
                  name: 'address',
                  parentNavigatorKey: AddressRoute.$parentNavigatorKey,
                  factory: $AddressRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'daily-coins',
                  name: 'daily-coins',
                  parentNavigatorKey: DailyCoinsRoute.$parentNavigatorKey,
                  factory: $DailyCoinsRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'order-placed',
                  name: 'order-placed',
                  parentNavigatorKey: OrderPlacedRoute.$parentNavigatorKey,
                  factory: $OrderPlacedRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'help',
                  name: 'commerce-help',
                  parentNavigatorKey: CommerceHelpRoute.$parentNavigatorKey,
                  factory: $CommerceHelpRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'my-likes',
                  name: 'my-likes',
                  parentNavigatorKey: MyLikesRoute.$parentNavigatorKey,
                  factory: $MyLikesRouteExtension._fromState,
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranchData.$branch(
          routes: [
            GoRouteData.$route(
              path: '/discover',
              name: 'discover',
              factory: $DiscoverRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: ':eventId',
                  name: 'discover-detail',
                  factory: $DiscoverDetailsRouteExtension._fromState,
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranchData.$branch(
          routes: [
            GoRouteData.$route(
              path: '/profile',
              name: 'profile',
              factory: $ProfileRouteExtension._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  name: 'edit-profile',
                  parentNavigatorKey: EditProfileRoute.$parentNavigatorKey,
                  factory: $EditProfileRouteExtension._fromState,
                ),
                GoRouteData.$route(
                  path: 'delete',
                  name: 'delete account',
                  parentNavigatorKey: DeleteAccountRoute.$parentNavigatorKey,
                  factory: $DeleteAccountRouteExtension._fromState,
                ),
              ],
            ),
          ],
        ),
      ],
    );

extension $HomeRouteExtension on HomeRoute {
  static HomeRoute _fromState(GoRouterState state) => const HomeRoute();
}

extension $ExploreRouteExtension on ExploreRoute {
  static ExploreRoute _fromState(GoRouterState state) => const ExploreRoute();

  String get location => GoRouteData.$location(
        '/explore',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $SuggestRouteExtension on SuggestRoute {
  static SuggestRoute _fromState(GoRouterState state) => const SuggestRoute();

  String get location => GoRouteData.$location(
        '/suggest',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CommerceRouteExtension on CommerceRoute {
  static CommerceRoute _fromState(GoRouterState state) => const CommerceRoute();

  String get location => GoRouteData.$location(
        '/commerce',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CommerceSearchRouteExtension on CommerceSearchRoute {
  static CommerceSearchRoute _fromState(GoRouterState state) =>
      const CommerceSearchRoute();

  String get location => GoRouteData.$location(
        '/commerce/commerce-search',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CommerceSearchListingRouteExtension on CommerceSearchListingRoute {
  static CommerceSearchListingRoute _fromState(GoRouterState state) =>
      CommerceSearchListingRoute(
        state.pathParameters['keywords']!,
      );

  String get location => GoRouteData.$location(
        '/commerce/commerce-search/${Uri.encodeComponent(keywords)}',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CollectionRouteExtension on CollectionRoute {
  static CollectionRoute _fromState(GoRouterState state) => CollectionRoute(
        state.pathParameters['id']!,
      );

  String get location => GoRouteData.$location(
        '/commerce/collections/${Uri.encodeComponent(id)}',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ProductRouteExtension on ProductRoute {
  static ProductRoute _fromState(GoRouterState state) => ProductRoute(
        state.pathParameters['productHandle']!,
      );

  String get location => GoRouteData.$location(
        '/commerce/products/${Uri.encodeComponent(productHandle)}',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ProductReviewsRouteExtension on ProductReviewsRoute {
  static ProductReviewsRoute _fromState(GoRouterState state) =>
      ProductReviewsRoute(
        state.pathParameters['productId']!,
      );

  String get location => GoRouteData.$location(
        '/commerce/products/${Uri.encodeComponent(productId)}/user-reviews',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CartRouteExtension on CartRoute {
  static CartRoute _fromState(GoRouterState state) => const CartRoute();

  String get location => GoRouteData.$location(
        '/commerce/cart',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MyOrdersRouteExtension on MyOrdersRoute {
  static MyOrdersRoute _fromState(GoRouterState state) => const MyOrdersRoute();

  String get location => GoRouteData.$location(
        '/commerce/my-orders',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $PurchaseHistoryRouteExtension on PurchaseHistoryRoute {
  static PurchaseHistoryRoute _fromState(GoRouterState state) =>
      PurchaseHistoryRoute(
        state.uri.queryParameters['initial-tab']!,
      );

  String get location => GoRouteData.$location(
        '/commerce/history',
        queryParams: {
          'initial-tab': initialTab,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $OrderDetailsRouteExtension on OrderDetailsRoute {
  static OrderDetailsRoute _fromState(GoRouterState state) => OrderDetailsRoute(
        state.pathParameters['orderId']!,
        isCompleted: _$convertMapValue(
                'is-completed', state.uri.queryParameters, _$boolConverter) ??
            false,
      );

  String get location => GoRouteData.$location(
        '/commerce/order-detail/${Uri.encodeComponent(orderId)}',
        queryParams: {
          if (isCompleted != false) 'is-completed': isCompleted.toString(),
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $PriceMatchRouteExtension on PriceMatchRoute {
  static PriceMatchRoute _fromState(GoRouterState state) =>
      const PriceMatchRoute();

  String get location => GoRouteData.$location(
        '/commerce/price-match',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $PriceMatchRequestRouteExtension on PriceMatchRequestRoute {
  static PriceMatchRequestRoute _fromState(GoRouterState state) =>
      const PriceMatchRequestRoute();

  String get location => GoRouteData.$location(
        '/commerce/price-match/request',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MyRequestsRouteExtension on MyRequestsRoute {
  static MyRequestsRoute _fromState(GoRouterState state) =>
      const MyRequestsRoute();

  String get location => GoRouteData.$location(
        '/commerce/my-requests',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $AddressRouteExtension on AddressRoute {
  static AddressRoute _fromState(GoRouterState state) => const AddressRoute();

  String get location => GoRouteData.$location(
        '/commerce/address',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DailyCoinsRouteExtension on DailyCoinsRoute {
  static DailyCoinsRoute _fromState(GoRouterState state) =>
      const DailyCoinsRoute();

  String get location => GoRouteData.$location(
        '/commerce/daily-coins',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $OrderPlacedRouteExtension on OrderPlacedRoute {
  static OrderPlacedRoute _fromState(GoRouterState state) =>
      const OrderPlacedRoute();

  String get location => GoRouteData.$location(
        '/commerce/order-placed',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CommerceHelpRouteExtension on CommerceHelpRoute {
  static CommerceHelpRoute _fromState(GoRouterState state) =>
      const CommerceHelpRoute();

  String get location => GoRouteData.$location(
        '/commerce/help',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $MyLikesRouteExtension on MyLikesRoute {
  static MyLikesRoute _fromState(GoRouterState state) => const MyLikesRoute();

  String get location => GoRouteData.$location(
        '/commerce/my-likes',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DiscoverRouteExtension on DiscoverRoute {
  static DiscoverRoute _fromState(GoRouterState state) => const DiscoverRoute();

  String get location => GoRouteData.$location(
        '/discover',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DiscoverDetailsRouteExtension on DiscoverDetailsRoute {
  static DiscoverDetailsRoute _fromState(GoRouterState state) =>
      DiscoverDetailsRoute(
        state.pathParameters['eventId']!,
      );

  String get location => GoRouteData.$location(
        '/discover/${Uri.encodeComponent(eventId)}',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ProfileRouteExtension on ProfileRoute {
  static ProfileRoute _fromState(GoRouterState state) => const ProfileRoute();

  String get location => GoRouteData.$location(
        '/profile',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditProfileRouteExtension on EditProfileRoute {
  static EditProfileRoute _fromState(GoRouterState state) =>
      const EditProfileRoute();

  String get location => GoRouteData.$location(
        '/profile/edit',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DeleteAccountRouteExtension on DeleteAccountRoute {
  static DeleteAccountRoute _fromState(GoRouterState state) =>
      const DeleteAccountRoute();

  String get location => GoRouteData.$location(
        '/profile/delete',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

T? _$convertMapValue<T>(
  String key,
  Map<String, String> map,
  T Function(String) converter,
) {
  final value = map[key];
  return value == null ? null : converter(value);
}

bool _$boolConverter(String value) {
  switch (value) {
    case 'true':
      return true;
    case 'false':
      return false;
    default:
      throw UnsupportedError('Cannot convert "$value" into a bool.');
  }
}

RouteBase get $splashRoute => GoRouteData.$route(
      path: '/splash',
      name: 'splash',
      factory: $SplashRouteExtension._fromState,
    );

extension $SplashRouteExtension on SplashRoute {
  static SplashRoute _fromState(GoRouterState state) => const SplashRoute();

  String get location => GoRouteData.$location(
        '/splash',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $permissionRoute => GoRouteData.$route(
      path: '/permission',
      name: 'permission',
      factory: $PermissionRouteExtension._fromState,
    );

extension $PermissionRouteExtension on PermissionRoute {
  static PermissionRoute _fromState(GoRouterState state) =>
      const PermissionRoute();

  String get location => GoRouteData.$location(
        '/permission',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $authRoute => GoRouteData.$route(
      path: '/auth',
      name: 'auth',
      factory: $AuthRouteExtension._fromState,
    );

extension $AuthRouteExtension on AuthRoute {
  static AuthRoute _fromState(GoRouterState state) => const AuthRoute();

  String get location => GoRouteData.$location(
        '/auth',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $onboardingRoute => GoRouteData.$route(
      path: '/onboarding',
      name: 'onboarding',
      factory: $OnboardingRouteExtension._fromState,
    );

extension $OnboardingRouteExtension on OnboardingRoute {
  static OnboardingRoute _fromState(GoRouterState state) =>
      const OnboardingRoute();

  String get location => GoRouteData.$location(
        '/onboarding',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $favouritesRoute => GoRouteData.$route(
      path: '/favourites',
      name: 'favourites',
      parentNavigatorKey: FavouritesRoute.$parentNavigatorKey,
      factory: $FavouritesRouteExtension._fromState,
    );

extension $FavouritesRouteExtension on FavouritesRoute {
  static FavouritesRoute _fromState(GoRouterState state) =>
      const FavouritesRoute();

  String get location => GoRouteData.$location(
        '/favourites',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $sessionHistoryRoute => GoRouteData.$route(
      path: '/history',
      name: 'history',
      parentNavigatorKey: SessionHistoryRoute.$parentNavigatorKey,
      factory: $SessionHistoryRouteExtension._fromState,
    );

extension $SessionHistoryRouteExtension on SessionHistoryRoute {
  static SessionHistoryRoute _fromState(GoRouterState state) =>
      const SessionHistoryRoute();

  String get location => GoRouteData.$location(
        '/history',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $currentSessionRoute => GoRouteData.$route(
      path: '/current-session',
      name: 'current session',
      parentNavigatorKey: CurrentSessionRoute.$parentNavigatorKey,
      factory: $CurrentSessionRouteExtension._fromState,
    );

extension $CurrentSessionRouteExtension on CurrentSessionRoute {
  static CurrentSessionRoute _fromState(GoRouterState state) =>
      const CurrentSessionRoute();

  String get location => GoRouteData.$location(
        '/current-session',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $reviewListingRoute => GoRouteData.$route(
      path: '/listings/:id/submit-reviews',
      name: 'review-listing',
      parentNavigatorKey: ReviewListingRoute.$parentNavigatorKey,
      factory: $ReviewListingRouteExtension._fromState,
    );

extension $ReviewListingRouteExtension on ReviewListingRoute {
  static ReviewListingRoute _fromState(GoRouterState state) =>
      ReviewListingRoute(
        state.pathParameters['id']!,
        $extra: state.extra as Listing?,
      );

  String get location => GoRouteData.$location(
        '/listings/${Uri.encodeComponent(id)}/submit-reviews',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $reviewSessionRoute => GoRouteData.$route(
      path: '/sessions/:id/reviews',
      name: 'review-session',
      parentNavigatorKey: ReviewSessionRoute.$parentNavigatorKey,
      factory: $ReviewSessionRouteExtension._fromState,
    );

extension $ReviewSessionRouteExtension on ReviewSessionRoute {
  static ReviewSessionRoute _fromState(GoRouterState state) =>
      ReviewSessionRoute(
        state.pathParameters['id']!,
        $extra: state.extra as Session?,
      );

  String get location => GoRouteData.$location(
        '/sessions/${Uri.encodeComponent(id)}/reviews',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $pastSessionRoute => GoRouteData.$route(
      path: '/sessions',
      name: 'past-session',
      parentNavigatorKey: PastSessionRoute.$parentNavigatorKey,
      factory: $PastSessionRouteExtension._fromState,
    );

extension $PastSessionRouteExtension on PastSessionRoute {
  static PastSessionRoute _fromState(GoRouterState state) => PastSessionRoute(
        state.extra as Session,
      );

  String get location => GoRouteData.$location(
        '/sessions',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $helpRoute => GoRouteData.$route(
      path: '/help',
      name: 'help',
      parentNavigatorKey: HelpRoute.$parentNavigatorKey,
      factory: $HelpRouteExtension._fromState,
    );

extension $HelpRouteExtension on HelpRoute {
  static HelpRoute _fromState(GoRouterState state) => const HelpRoute();

  String get location => GoRouteData.$location(
        '/help',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $aboutRoute => GoRouteData.$route(
      path: '/about',
      name: 'about',
      parentNavigatorKey: AboutRoute.$parentNavigatorKey,
      factory: $AboutRouteExtension._fromState,
    );

extension $AboutRouteExtension on AboutRoute {
  static AboutRoute _fromState(GoRouterState state) => const AboutRoute();

  String get location => GoRouteData.$location(
        '/about',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $aboutGomamaRoute => GoRouteData.$route(
      path: '/about-gomama',
      name: 'about-gomama',
      parentNavigatorKey: AboutGomamaRoute.$parentNavigatorKey,
      factory: $AboutGomamaRouteExtension._fromState,
    );

extension $AboutGomamaRouteExtension on AboutGomamaRoute {
  static AboutGomamaRoute _fromState(GoRouterState state) => AboutGomamaRoute(
        state.extra as String,
      );

  String get location => GoRouteData.$location(
        '/about-gomama',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $privacyPolicyRoute => GoRouteData.$route(
      path: '/privacy-policy',
      name: 'privacy-policy',
      parentNavigatorKey: PrivacyPolicyRoute.$parentNavigatorKey,
      factory: $PrivacyPolicyRouteExtension._fromState,
    );

extension $PrivacyPolicyRouteExtension on PrivacyPolicyRoute {
  static PrivacyPolicyRoute _fromState(GoRouterState state) =>
      PrivacyPolicyRoute(
        state.extra as String,
      );

  String get location => GoRouteData.$location(
        '/privacy-policy',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $termServiceRoute => GoRouteData.$route(
      path: '/term-service',
      name: 'term-service',
      parentNavigatorKey: TermServiceRoute.$parentNavigatorKey,
      factory: $TermServiceRouteExtension._fromState,
    );

extension $TermServiceRouteExtension on TermServiceRoute {
  static TermServiceRoute _fromState(GoRouterState state) => TermServiceRoute(
        state.extra as String,
      );

  String get location => GoRouteData.$location(
        '/term-service',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $frequentlyAskedQuestionRoute => GoRouteData.$route(
      path: '/frequently-asked-question',
      name: 'frequently-asked-question',
      parentNavigatorKey: FrequentlyAskedQuestionRoute.$parentNavigatorKey,
      factory: $FrequentlyAskedQuestionRouteExtension._fromState,
    );

extension $FrequentlyAskedQuestionRouteExtension
    on FrequentlyAskedQuestionRoute {
  static FrequentlyAskedQuestionRoute _fromState(GoRouterState state) =>
      FrequentlyAskedQuestionRoute(
        state.extra as String,
      );

  String get location => GoRouteData.$location(
        '/frequently-asked-question',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $verificationRoute => GoRouteData.$route(
      path: '/verification',
      name: 'verification',
      parentNavigatorKey: VerificationRoute.$parentNavigatorKey,
      factory: $VerificationRouteExtension._fromState,
    );

extension $VerificationRouteExtension on VerificationRoute {
  static VerificationRoute _fromState(GoRouterState state) =>
      const VerificationRoute();

  String get location => GoRouteData.$location(
        '/verification',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $singpassFormRoute => GoRouteData.$route(
      path: '/singpass-form',
      name: 'singpass form',
      parentNavigatorKey: SingpassFormRoute.$parentNavigatorKey,
      factory: $SingpassFormRouteExtension._fromState,
    );

extension $SingpassFormRouteExtension on SingpassFormRoute {
  static SingpassFormRoute _fromState(GoRouterState state) =>
      const SingpassFormRoute();

  String get location => GoRouteData.$location(
        '/singpass-form',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $selfieFormRoute => GoRouteData.$route(
      path: '/selfie-form',
      name: 'selfie form',
      parentNavigatorKey: SelfieFormRoute.$parentNavigatorKey,
      factory: $SelfieFormRouteExtension._fromState,
    );

extension $SelfieFormRouteExtension on SelfieFormRoute {
  static SelfieFormRoute _fromState(GoRouterState state) => SelfieFormRoute(
        state.uri.queryParameters[r'$photo-path']!,
      );

  String get location => GoRouteData.$location(
        '/selfie-form',
        queryParams: {
          r'$photo-path': $photoPath,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $fillVerificationDetailsRoute => GoRouteData.$route(
      path: '/fill-verification-details',
      name: 'fill verification details',
      parentNavigatorKey: FillVerificationDetailsRoute.$parentNavigatorKey,
      factory: $FillVerificationDetailsRouteExtension._fromState,
    );

extension $FillVerificationDetailsRouteExtension
    on FillVerificationDetailsRoute {
  static FillVerificationDetailsRoute _fromState(GoRouterState state) =>
      const FillVerificationDetailsRoute();

  String get location => GoRouteData.$location(
        '/fill-verification-details',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $searchRoute => GoRouteData.$route(
      path: '/search',
      name: 'search',
      parentNavigatorKey: SearchRoute.$parentNavigatorKey,
      factory: $SearchRouteExtension._fromState,
    );

extension $SearchRouteExtension on SearchRoute {
  static SearchRoute _fromState(GoRouterState state) => const SearchRoute();

  String get location => GoRouteData.$location(
        '/search',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $listingRoute => GoRouteData.$route(
      path: '/listings/:listingId',
      name: 'listing',
      parentNavigatorKey: ListingRoute.$parentNavigatorKey,
      factory: $ListingRouteExtension._fromState,
      routes: [
        GoRouteData.$route(
          path: 'reviews',
          name: 'listingReviews',
          parentNavigatorKey: ListingReviewsRoute.$parentNavigatorKey,
          factory: $ListingReviewsRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: 'amenities',
          name: 'listingAmenities',
          parentNavigatorKey: ListingAmenitiesRoute.$parentNavigatorKey,
          factory: $ListingAmenitiesRouteExtension._fromState,
        ),
      ],
    );

extension $ListingRouteExtension on ListingRoute {
  static ListingRoute _fromState(GoRouterState state) => ListingRoute(
        state.pathParameters['listingId']!,
        $extra: state.extra as Listing?,
      );

  String get location => GoRouteData.$location(
        '/listings/${Uri.encodeComponent(listingId)}',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $ListingReviewsRouteExtension on ListingReviewsRoute {
  static ListingReviewsRoute _fromState(GoRouterState state) =>
      ListingReviewsRoute(
        state.pathParameters['listingId']!,
        state.extra as Listing,
      );

  String get location => GoRouteData.$location(
        '/listings/${Uri.encodeComponent(listingId)}/reviews',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $ListingAmenitiesRouteExtension on ListingAmenitiesRoute {
  static ListingAmenitiesRoute _fromState(GoRouterState state) =>
      ListingAmenitiesRoute(
        state.pathParameters['listingId']!,
        state.extra as Listing,
      );

  String get location => GoRouteData.$location(
        '/listings/${Uri.encodeComponent(listingId)}/amenities',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $listingGalleryPhotoViewRoute => GoRouteData.$route(
      path: '/listing-gallery-photo-view',
      name: 'listing-gallery-photo-view',
      parentNavigatorKey: ListingGalleryPhotoViewRoute.$parentNavigatorKey,
      factory: $ListingGalleryPhotoViewRouteExtension._fromState,
    );

extension $ListingGalleryPhotoViewRouteExtension
    on ListingGalleryPhotoViewRoute {
  static ListingGalleryPhotoViewRoute _fromState(GoRouterState state) =>
      ListingGalleryPhotoViewRoute(
        state.extra as List<String>,
      );

  String get location => GoRouteData.$location(
        '/listing-gallery-photo-view',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

RouteBase get $flagListingRoute => GoRouteData.$route(
      path: '/flag',
      name: 'flag-listing',
      parentNavigatorKey: FlagListingRoute.$parentNavigatorKey,
      factory: $FlagListingRouteExtension._fromState,
      routes: [
        GoRouteData.$route(
          path: 'details',
          name: 'flag listing details',
          parentNavigatorKey: FlagListingDetailsRoute.$parentNavigatorKey,
          factory: $FlagListingDetailsRouteExtension._fromState,
        ),
      ],
    );

extension $FlagListingRouteExtension on FlagListingRoute {
  static FlagListingRoute _fromState(GoRouterState state) => FlagListingRoute(
        state.extra as Listing,
      );

  String get location => GoRouteData.$location(
        '/flag',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $FlagListingDetailsRouteExtension on FlagListingDetailsRoute {
  static FlagListingDetailsRoute _fromState(GoRouterState state) =>
      const FlagListingDetailsRoute();

  String get location => GoRouteData.$location(
        '/flag/details',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $suggestPodRoute => GoRouteData.$route(
      path: '/suggest/gomama',
      name: 'suggest pod',
      parentNavigatorKey: SuggestPodRoute.$parentNavigatorKey,
      factory: $SuggestPodRouteExtension._fromState,
    );

extension $SuggestPodRouteExtension on SuggestPodRoute {
  static SuggestPodRoute _fromState(GoRouterState state) =>
      const SuggestPodRoute();

  String get location => GoRouteData.$location(
        '/suggest/gomama',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $suggestNursingFacilityRoute => GoRouteData.$route(
      path: '/suggest/care',
      name: 'suggest nursing facility',
      parentNavigatorKey: SuggestNursingFacilityRoute.$parentNavigatorKey,
      factory: $SuggestNursingFacilityRouteExtension._fromState,
    );

extension $SuggestNursingFacilityRouteExtension on SuggestNursingFacilityRoute {
  static SuggestNursingFacilityRoute _fromState(GoRouterState state) =>
      const SuggestNursingFacilityRoute();

  String get location => GoRouteData.$location(
        '/suggest/care',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $suggestHistoryRoute => GoRouteData.$route(
      path: '/suggest/history',
      name: 'suggest history',
      parentNavigatorKey: SuggestHistoryRoute.$parentNavigatorKey,
      factory: $SuggestHistoryRouteExtension._fromState,
    );

extension $SuggestHistoryRouteExtension on SuggestHistoryRoute {
  static SuggestHistoryRoute _fromState(GoRouterState state) =>
      const SuggestHistoryRoute();

  String get location => GoRouteData.$location(
        '/suggest/history',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $suggestHistoryDetailsRoute => GoRouteData.$route(
      path: '/suggest/history/details',
      name: 'suggest history details',
      parentNavigatorKey: SuggestHistoryDetailsRoute.$parentNavigatorKey,
      factory: $SuggestHistoryDetailsRouteExtension._fromState,
    );

extension $SuggestHistoryDetailsRouteExtension on SuggestHistoryDetailsRoute {
  static SuggestHistoryDetailsRoute _fromState(GoRouterState state) =>
      SuggestHistoryDetailsRoute(
        state.extra as ListingSuggestion,
      );

  String get location => GoRouteData.$location(
        '/suggest/history/details',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}
