import 'dart:convert';
import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

// Author: <PERSON>
// Date: 28-07-2023
/*
No 3rd party plugin
Purpose: Flutter Dio Logger: Simplifying HTTP Request & Response Logging.
*/

// Define an enum for the different log levels
enum Level { debug, info, warning, error, alien }

// Define a logDebug function that logs messages at the specified level
// log different colors
void logDebug(String message, {Level level = Level.info}) {
  // Define ANSI escape codes for different colors
  const resetColor = '\x1B[0m';
  const redColor = '\x1B[31m'; // Red
  const greenColor = '\x1B[32m'; // Green
  const yellowColor = '\x1B[33m'; // Yellow
  const cyanColor = '\x1B[36m'; // Cyan

  // Get the current time in hours, minutes, and seconds
  final now = DateTime.now();
  final timeString =
      '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

  // Only log messages if the app is running in debug mode
  if (kDebugMode) {
    try {
      String logMessage;
      switch (level) {
        case Level.debug:
          logMessage = '$cyanColor[DEBUG][$timeString] $message$resetColor';
          break;
        case Level.info:
          logMessage = '$greenColor[INFO][$timeString] $message$resetColor';
          break;
        case Level.warning:
          logMessage =
              '$yellowColor[WARNING][$timeString] $message $resetColor';
          break;
        case Level.error:
          logMessage = '$redColor[ERROR][$timeString] $message $resetColor';
          break;
        case Level.alien:
          logMessage = '$redColor[ALIEN][$timeString] $message $resetColor';
          break;
      }
      // Use the DebugPrintCallback to ensure long strings are not truncated
      developer.log(logMessage);
      // debugPrint(logMessage);
    } catch (e) {
      developer.log('dio logger', error: e);
    }
  }
}

// Define an interceptor that logs the requests and responses
class LoggerInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final options = err.requestOptions;
    final requestPath = '${options.baseUrl}${options.path}';

    // Log the error request and error message
    logDebug(
      'onError: ${options.method} request => $requestPath',
      level: Level.error,
    );
    logDebug(
      'onError: ${err.error}, Message: ${err.message}',
      level: Level.debug,
    );

    // Call the super class to continue handling the error
    return super.onError(err, handler);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final requestPath = '${options.baseUrl}${options.path}';

    // Log request details
    logDebug(
      '\n\n\n\n.........................................................................',
    );
    logDebug(
      'onRequest: ${options.method} request => $requestPath',
    );
    logDebug(
      'onRequest: Request Headers => ${options.headers}',
    );
    logDebug(
      'onRequest: Request Data => ${_prettyJsonEncode(options.data)}',
    ); // Log formatted request data

    // Call the super class to continue handling the request
    return super.onRequest(options, handler);
  }

  @override
  // ignore: strict_raw_type
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Log the response status code and data
    logDebug(
      'onResponse: StatusCode: ${response.statusCode}, Data: ${_prettyJsonEncode(response.data)}',
      level: Level.debug,
    ); // Log formatted response data
    logDebug(
      '.........................................................................\n\n\n\n',
    );

    // Call the super class to continue handling the response
    return super.onResponse(response, handler);
  }

  // Helper method to convert data to pretty JSON format
  String _prettyJsonEncode(dynamic data) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      final jsonString = encoder.convert(data);
      return jsonString;
    } catch (e) {
      return data.toString();
    }
  }
}
