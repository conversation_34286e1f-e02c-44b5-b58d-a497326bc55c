// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mediaStateControllerHash() =>
    r'c2b98c4074f3642eb253f12a36760b7d581d9d34';

/// See also [MediaStateController].
@ProviderFor(MediaStateController)
final mediaStateControllerProvider =
    AutoDisposeNotifierProvider<MediaStateController, MediaState>.internal(
  MediaStateController.new,
  name: r'mediaStateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mediaStateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MediaStateController = AutoDisposeNotifier<MediaState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
