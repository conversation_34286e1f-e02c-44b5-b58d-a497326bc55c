// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_media_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileMediaStateControllerHash() =>
    r'497e557c7df00f7a1133e8f2aa38eafac5d9c5d6';

/// See also [ProfileMediaStateController].
@ProviderFor(ProfileMediaStateController)
final profileMediaStateControllerProvider = AutoDisposeNotifierProvider<
    ProfileMediaStateController, ProfileMediaState>.internal(
  ProfileMediaStateController.new,
  name: r'profileMediaStateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileMediaStateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProfileMediaStateController = AutoDisposeNotifier<ProfileMediaState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
