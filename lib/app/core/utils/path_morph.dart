import 'dart:ui';
import 'package:flutter/animation.dart';
import 'package:flutter/rendering.dart';

/// This is a class used to store the sampled path data.
/// In addition to the sampled points from both paths, it stores the
/// indices of points that are at the beginning of a contour.
class SampledPathData {
  SampledPathData() {
    points1 = List<Offset>.empty(growable: true);
    points2 = List<Offset>.empty(growable: true);
    shiftedPoints = List<Offset>.empty(growable: true);
    endIndices = List<int>.empty(growable: true);
  }

  late List<Offset> points1;
  late List<Offset> points2;
  late List<Offset> shiftedPoints;
  late List<int> endIndices;
}

/// This class has all the methods you need to create your morph animations.
class PathMorph {
  /// This method is responsible for sampling both the paths. It generates a
  /// [SampledPathData] object containing all the details required for the
  /// morph animation.
  static SampledPathData samplePaths(
    Path path1,
    Path path2, {
    double precision = 0.01,
  }) {
    final data = SampledPathData();
    var k = 0;
    path1.computeMetrics().forEach((metric) {
      for (var i = 0.0; i < 1.1; i += precision) {
        final tangent = metric.getTangentForOffset(metric.length * i);
        if (tangent == null) continue;
        final position = tangent.position;
        data.points1.add(position);
        data.shiftedPoints.add(position);
      }
    });
    path2.computeMetrics().forEach((metric) {
      data.endIndices.add(k);
      for (var i = 0.0; i < 1.1; i += precision) {
        final tangent = metric.getTangentForOffset(metric.length * i);
        if (tangent == null) continue;
        k += 1;
        data.points2.add(tangent.position);
      }
    });
    return data;
  }

  /// Generates a bunch of animations that are responsible for moving
  /// all the points of paths into the right positions.
  static void generateAnimations(
    AnimationController controller,
    SampledPathData data,
    Function func,
  ) {
    for (var i = 0; i < data.points1.length; i++) {
      final start = data.points1[i];
      final end = data.points2[i];
      final tween = Tween(begin: start, end: end);
      final animation = tween.animate(controller);
      animation.addListener(() {
        func(i, animation.value);
      });
    }
  }

  /// Generates a path using the [SampledPathData] object.
  /// You can use this path while drawing the frames of
  /// the morph animation on your canvas.
  static Path generatePath(SampledPathData data) {
    final p = Path();
    for (var i = 0; i < data.shiftedPoints.length; i++) {
      for (var j = 0; j < data.endIndices.length; j++) {
        if (i == data.endIndices[j]) {
          p.moveTo(data.shiftedPoints[i].dx, data.shiftedPoints[i].dy);
          break;
        }
      }
      p.lineTo(data.shiftedPoints[i].dx, data.shiftedPoints[i].dy);
    }
    return p;
  }
}
