import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'media_provider.freezed.dart';
part 'media_provider.g.dart';

@freezed
class MediaState with _$MediaState {
  factory MediaState({
    @Default([]) List<File> photos,
  }) = _MediaState;

  // factory MediaState.fromJson(Json json) => _$MediaStateFromJson(json);
}

@riverpod
class MediaStateController extends _$MediaStateController {
  @override
  MediaState build() {
    return MediaState();
  }

  void setPhotos(List<File> value) {
    state = state.copyWith(photos: value);
  }

  Future<void> takePhoto() async {
    await Permission.camera.request();

    final _picker = ImagePicker();
    final photo = await _picker.pickImage(
      source: ImageSource.camera,
      preferredCameraDevice: CameraDevice.front,
      maxWidth: 800,
    );

    if (photo != null) {
      final photoFile = File(photo.path);
      state = state.copyWith(photos: [...state.photos, photoFile]);
    }
  }

  Future<void> selectPhoto() async {
    await Permission.photos.request();

    final _picker = ImagePicker();
    final photos = await _picker.pickMultiImage(
      maxWidth: 800,
    );

    if (photos.isNotEmpty) {
      for (final photo in photos) {
        final photoFile = File(photo.path);
        state = state.copyWith(photos: [...state.photos, photoFile]);
      }
    }
  }

  void removePhotoAt(int index) {
    final tempFiles = <File>[
      ...state.photos,
    ]..removeAt(index);
    state = state.copyWith(photos: tempFiles);
  }
}
