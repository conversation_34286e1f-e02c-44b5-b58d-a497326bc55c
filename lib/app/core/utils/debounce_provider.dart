import 'dart:async';
import 'package:dio/dio.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'debounce_provider.g.dart';

/// A utility class for debouncing events.
///
/// This class can be used to debounce events, such as user input events,
/// to prevent too many events from being triggered in a short period of time.
class Debouncer {
  /// Creates a new [Debouncer] instance with the given [delay].
  Debouncer(this.delay);

  /// The delay after which the debounced callback will be called.
  final Duration delay;

  /// The timer used to debounce events.
  Timer? _timer;

  /// Debounces the given [callback] function.
  ///
  /// If the [callback] is called multiple times within the [delay] period,
  /// only the last call will be executed after the [delay] period has elapsed.
  void debounce(void Function() callback) {
    _timer?.cancel();
    _timer = Timer(delay, callback);
  }

  /// Cancels the debounce timer.
  void cancel() {
    Groveman.debug('debounce cancelled');
    _timer?.cancel();
  }
}

// TODO(kkcy): debouncer
/// might need to remove keepAlive and
/// find other way to make sure we are
/// using same instance everytime
@Riverpod(keepAlive: true)
Debouncer debouncer(DebouncerRef ref, Duration duration) {
  Groveman.debug('new debouncer');
  return Debouncer(duration);
}

///
/// Network debounce
///
extension RefExtension on Ref {
  CancelToken cancelToken() {
    final token = CancelToken();
    onDispose(token.cancel);
    return token;
  }

  Future<void> debounce(Duration duration) {
    final completer = Completer<void>();
    final timer = Timer(duration, () {
      if (!completer.isCompleted) {
        completer.complete();
      }
    });

    onDispose(() {
      timer.cancel();
      if (!completer.isCompleted) {
        completer.completeError(StateError('Cancelled'));
      }
    });

    return completer.future;
  }
}
