import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'profile_media_provider.freezed.dart';
part 'profile_media_provider.g.dart';

@freezed
class ProfileMediaState with _$ProfileMediaState {
  factory ProfileMediaState({
    @Default([]) List<File> photos,
  }) = _ProfileMediaState;
}

@riverpod
class ProfileMediaStateController extends _$ProfileMediaStateController {
  @override
  ProfileMediaState build() {
    return ProfileMediaState();
  }

  void setPhotos(List<File> value) {
    state = state.copyWith(photos: value);
  }

  Future<void> takePhoto() async {
    await Permission.camera.request();

    final _picker = ImagePicker();
    final photo = await _picker.pickImage(
      source: ImageSource.camera,
      preferredCameraDevice: CameraDevice.front,
      maxWidth: 800,
    );

    if (photo != null) {
      final photoFile = File(photo.path);
      state = state.copyWith(photos: [...state.photos, photoFile]);
    }
  }

  Future<void> selectPhoto() async {
    await Permission.photos.request();

    final _picker = ImagePicker();
    final photos = await _picker.pickMultiImage(
      maxWidth: 800,
    );

    if (photos.isNotEmpty) {
      for (final photo in photos) {
        final photoFile = File(photo.path);
        state = state.copyWith(photos: [...state.photos, photoFile]);
      }
    }
  }

  void removePhotoAt(int index) {
    final tempFiles = <File>[
      ...state.photos,
    ]..removeAt(index);
    state = state.copyWith(photos: tempFiles);
  }
}
