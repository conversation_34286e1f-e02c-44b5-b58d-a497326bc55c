// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_media_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ListingMediaState {
  List<File> get photos => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ListingMediaStateCopyWith<ListingMediaState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingMediaStateCopyWith<$Res> {
  factory $ListingMediaStateCopyWith(
          ListingMediaState value, $Res Function(ListingMediaState) then) =
      _$ListingMediaStateCopyWithImpl<$Res, ListingMediaState>;
  @useResult
  $Res call({List<File> photos});
}

/// @nodoc
class _$ListingMediaStateCopyWithImpl<$Res, $Val extends ListingMediaState>
    implements $ListingMediaStateCopyWith<$Res> {
  _$ListingMediaStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photos = null,
  }) {
    return _then(_value.copyWith(
      photos: null == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingMediaStateImplCopyWith<$Res>
    implements $ListingMediaStateCopyWith<$Res> {
  factory _$$ListingMediaStateImplCopyWith(_$ListingMediaStateImpl value,
          $Res Function(_$ListingMediaStateImpl) then) =
      __$$ListingMediaStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<File> photos});
}

/// @nodoc
class __$$ListingMediaStateImplCopyWithImpl<$Res>
    extends _$ListingMediaStateCopyWithImpl<$Res, _$ListingMediaStateImpl>
    implements _$$ListingMediaStateImplCopyWith<$Res> {
  __$$ListingMediaStateImplCopyWithImpl(_$ListingMediaStateImpl _value,
      $Res Function(_$ListingMediaStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photos = null,
  }) {
    return _then(_$ListingMediaStateImpl(
      photos: null == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ));
  }
}

/// @nodoc

class _$ListingMediaStateImpl implements _ListingMediaState {
  _$ListingMediaStateImpl({final List<File> photos = const []})
      : _photos = photos;

  final List<File> _photos;
  @override
  @JsonKey()
  List<File> get photos {
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photos);
  }

  @override
  String toString() {
    return 'ListingMediaState(photos: $photos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingMediaStateImpl &&
            const DeepCollectionEquality().equals(other._photos, _photos));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_photos));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingMediaStateImplCopyWith<_$ListingMediaStateImpl> get copyWith =>
      __$$ListingMediaStateImplCopyWithImpl<_$ListingMediaStateImpl>(
          this, _$identity);
}

abstract class _ListingMediaState implements ListingMediaState {
  factory _ListingMediaState({final List<File> photos}) =
      _$ListingMediaStateImpl;

  @override
  List<File> get photos;
  @override
  @JsonKey(ignore: true)
  _$$ListingMediaStateImplCopyWith<_$ListingMediaStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
