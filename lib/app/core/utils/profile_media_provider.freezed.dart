// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_media_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProfileMediaState {
  List<File> get photos => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ProfileMediaStateCopyWith<ProfileMediaState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileMediaStateCopyWith<$Res> {
  factory $ProfileMediaStateCopyWith(
          ProfileMediaState value, $Res Function(ProfileMediaState) then) =
      _$ProfileMediaStateCopyWithImpl<$Res, ProfileMediaState>;
  @useResult
  $Res call({List<File> photos});
}

/// @nodoc
class _$ProfileMediaStateCopyWithImpl<$Res, $Val extends ProfileMediaState>
    implements $ProfileMediaStateCopyWith<$Res> {
  _$ProfileMediaStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photos = null,
  }) {
    return _then(_value.copyWith(
      photos: null == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileMediaStateImplCopyWith<$Res>
    implements $ProfileMediaStateCopyWith<$Res> {
  factory _$$ProfileMediaStateImplCopyWith(_$ProfileMediaStateImpl value,
          $Res Function(_$ProfileMediaStateImpl) then) =
      __$$ProfileMediaStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<File> photos});
}

/// @nodoc
class __$$ProfileMediaStateImplCopyWithImpl<$Res>
    extends _$ProfileMediaStateCopyWithImpl<$Res, _$ProfileMediaStateImpl>
    implements _$$ProfileMediaStateImplCopyWith<$Res> {
  __$$ProfileMediaStateImplCopyWithImpl(_$ProfileMediaStateImpl _value,
      $Res Function(_$ProfileMediaStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photos = null,
  }) {
    return _then(_$ProfileMediaStateImpl(
      photos: null == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ));
  }
}

/// @nodoc

class _$ProfileMediaStateImpl implements _ProfileMediaState {
  _$ProfileMediaStateImpl({final List<File> photos = const []})
      : _photos = photos;

  final List<File> _photos;
  @override
  @JsonKey()
  List<File> get photos {
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_photos);
  }

  @override
  String toString() {
    return 'ProfileMediaState(photos: $photos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileMediaStateImpl &&
            const DeepCollectionEquality().equals(other._photos, _photos));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_photos));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileMediaStateImplCopyWith<_$ProfileMediaStateImpl> get copyWith =>
      __$$ProfileMediaStateImplCopyWithImpl<_$ProfileMediaStateImpl>(
          this, _$identity);
}

abstract class _ProfileMediaState implements ProfileMediaState {
  factory _ProfileMediaState({final List<File> photos}) =
      _$ProfileMediaStateImpl;

  @override
  List<File> get photos;
  @override
  @JsonKey(ignore: true)
  _$$ProfileMediaStateImplCopyWith<_$ProfileMediaStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
