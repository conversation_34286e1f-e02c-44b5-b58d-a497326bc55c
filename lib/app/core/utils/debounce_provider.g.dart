// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'debounce_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$debouncerHash() => r'38a3a80b334b23588be236575d1a52c1d5214fb4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// might need to remove keepAlive and
/// find other way to make sure we are
/// using same instance everytime
///
/// Copied from [debouncer].
@ProviderFor(debouncer)
const debouncerProvider = DebouncerFamily();

/// might need to remove keepAlive and
/// find other way to make sure we are
/// using same instance everytime
///
/// Copied from [debouncer].
class DebouncerFamily extends Family<Debouncer> {
  /// might need to remove keepAlive and
  /// find other way to make sure we are
  /// using same instance everytime
  ///
  /// Copied from [debouncer].
  const DebouncerFamily();

  /// might need to remove keepAlive and
  /// find other way to make sure we are
  /// using same instance everytime
  ///
  /// Copied from [debouncer].
  DebouncerProvider call(
    Duration duration,
  ) {
    return DebouncerProvider(
      duration,
    );
  }

  @override
  DebouncerProvider getProviderOverride(
    covariant DebouncerProvider provider,
  ) {
    return call(
      provider.duration,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'debouncerProvider';
}

/// might need to remove keepAlive and
/// find other way to make sure we are
/// using same instance everytime
///
/// Copied from [debouncer].
class DebouncerProvider extends Provider<Debouncer> {
  /// might need to remove keepAlive and
  /// find other way to make sure we are
  /// using same instance everytime
  ///
  /// Copied from [debouncer].
  DebouncerProvider(
    Duration duration,
  ) : this._internal(
          (ref) => debouncer(
            ref as DebouncerRef,
            duration,
          ),
          from: debouncerProvider,
          name: r'debouncerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$debouncerHash,
          dependencies: DebouncerFamily._dependencies,
          allTransitiveDependencies: DebouncerFamily._allTransitiveDependencies,
          duration: duration,
        );

  DebouncerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.duration,
  }) : super.internal();

  final Duration duration;

  @override
  Override overrideWith(
    Debouncer Function(DebouncerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DebouncerProvider._internal(
        (ref) => create(ref as DebouncerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        duration: duration,
      ),
    );
  }

  @override
  ProviderElement<Debouncer> createElement() {
    return _DebouncerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DebouncerProvider && other.duration == duration;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, duration.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DebouncerRef on ProviderRef<Debouncer> {
  /// The parameter `duration` of this provider.
  Duration get duration;
}

class _DebouncerProviderElement extends ProviderElement<Debouncer>
    with DebouncerRef {
  _DebouncerProviderElement(super.provider);

  @override
  Duration get duration => (origin as DebouncerProvider).duration;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
