// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_media_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingMediaStateControllerHash() =>
    r'129d26e0581c189dc802ef6fc55e114d4eea08b0';

/// See also [ListingMediaStateController].
@ProviderFor(ListingMediaStateController)
final listingMediaStateControllerProvider = AutoDisposeNotifierProvider<
    ListingMediaStateController, ListingMediaState>.internal(
  ListingMediaStateController.new,
  name: r'listingMediaStateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingMediaStateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListingMediaStateController = AutoDisposeNotifier<ListingMediaState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
