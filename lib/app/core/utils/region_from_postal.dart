extension RegionString on String {
  String regionFromPostal() {
    if (startsWith('01') ||
        startsWith('02') ||
        startsWith('03') ||
        startsWith('04') ||
        startsWith('04') ||
        startsWith('05') ||
        startsWith('06') ||
        startsWith('07') ||
        startsWith('08') ||
        startsWith('09') ||
        startsWith('10') ||
        startsWith('14') ||
        startsWith('15') ||
        startsWith('16') ||
        startsWith('17') ||
        startsWith('18') ||
        startsWith('19') ||
        startsWith('20') ||
        startsWith('21') ||
        startsWith('22') ||
        startsWith('23') ||
        startsWith('24') ||
        startsWith('25') ||
        startsWith('26') ||
        startsWith('27') ||
        startsWith('28') ||
        startsWith('29') ||
        startsWith('30') ||
        startsWith('31') ||
        startsWith('32') ||
        startsWith('33') ||
        startsWith('34') ||
        startsWith('35') ||
        startsWith('36') ||
        startsWith('37') ||
        startsWith('38') ||
        startsWith('39') ||
        startsWith('40') ||
        startsWith('41') ||
        startsWith('58') ||
        startsWith('59')) {
      return 'central';
    }

    if (startsWith('11') ||
        startsWith('12') ||
        startsWith('13') ||
        startsWith('60') ||
        startsWith('61') ||
        startsWith('62') ||
        startsWith('63') ||
        startsWith('64') ||
        startsWith('65') ||
        startsWith('66') ||
        startsWith('67') ||
        startsWith('68')) {
      return 'west';
    }

    if (startsWith('42') ||
        startsWith('43') ||
        startsWith('44') ||
        startsWith('45') ||
        startsWith('46') ||
        startsWith('47') ||
        startsWith('48') ||
        startsWith('49') ||
        startsWith('50') ||
        startsWith('51') ||
        startsWith('52') ||
        startsWith('81')) {
      return 'east';
    }

    if (startsWith('53') ||
        startsWith('54') ||
        startsWith('55') ||
        startsWith('56') ||
        startsWith('57') ||
        startsWith('79') ||
        startsWith('80') ||
        startsWith('82')) {
      return 'northeast';
    }

    if (startsWith('69') ||
        startsWith('70') ||
        startsWith('71') ||
        startsWith('72') ||
        startsWith('73') ||
        startsWith('75') ||
        startsWith('76')) {
      return 'north';
    }

    return 'invalid';

    // final postalCode = int.parse(this);

    // if (postalCode >= 10000 && postalCode <= 89999 ||
    //     postalCode >= 100000 && postalCode <= 108999 ||
    //     postalCode >= 120000 && postalCode <= 129999 ||
    //     postalCode >= 130000 && postalCode <= 139999 ||
    //     postalCode >= 150000 && postalCode <= 159999 ||
    //     postalCode >= 200000 && postalCode <= 208999 ||
    //     postalCode >= 98000 && postalCode <= 99999) {
    //   return 'central';
    // } else if (postalCode >= 640000 && postalCode <= 649999 ||
    //     postalCode >= 650000 && postalCode <= 659999 ||
    //     postalCode >= 670000 && postalCode <= 679999) {
    //   return 'west';
    // } else if (postalCode >= 480000 && postalCode <= 489999 ||
    //     postalCode >= 500000 && postalCode <= 529999 ||
    //     postalCode >= 530000 && postalCode <= 539999 ||
    //     postalCode >= 540000 && postalCode <= 549999 ||
    //     postalCode >= 550000 && postalCode <= 559999 ||
    //     postalCode >= 560000 && postalCode <= 569999 ||
    //     postalCode >= 570000 && postalCode <= 579999 ||
    //     postalCode >= 580000 && postalCode <= 589999) {
    //   return 'east';
    // } else if (postalCode >= 760000 && postalCode <= 769999 ||
    //     postalCode >= 770000 && postalCode <= 779999 ||
    //     postalCode >= 780000 && postalCode <= 789999 ||
    //     postalCode >= 800000 && postalCode <= 829999 ||
    //     postalCode >= 830000 && postalCode <= 839999) {
    //   return 'north';
    // } else if (postalCode >= 530000 && postalCode <= 539999 ||
    //     postalCode >= 690000 && postalCode <= 699999 ||
    //     postalCode >= 820000 && postalCode <= 828999) {
    //   return 'northeast';
    // } else {
    //   return 'invalid';
    // }
  }
}
