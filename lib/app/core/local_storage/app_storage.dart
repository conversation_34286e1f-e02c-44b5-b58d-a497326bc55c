import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_storage.g.dart';

class AppStorage {
  // ignore: unused_field, strict_raw_type
  Box? _box;

  /// for initialling app local storage
  Future<void> initAppStorage() async {
    await Hive.initFlutter();
    // TODO(kkcy): add your storage name here
    _box = await Hive.openBox('appStorage');
  }

  // example of storing & getting value

  /// for storing uploaded string value
  // final String _helloWorld = 'helloWorld';

  Future<dynamic> readValue(String key) async {
    return _box?.get(key);
  }

  Future<void> writeValue(String key, dynamic value) async {
    if (_box != null) {
      await _box!.put(key, value);
    }
  }

  /// for clearing all data in box
  Future<void> clearAllData() async {
    await _box?.clear();
  }
}

// to store authentication token
class SecureAppStorage {
  Box<String>? _encryptedBox;
  FlutterSecureStorage secureStorage = const FlutterSecureStorage();

  /// for initialling app local storage
  Future<void> initAppStorage() async {
    await Hive.initFlutter();

    // if key not exists return null
    final encryptionKey = await secureStorage.read(key: 'key');
    if (encryptionKey == null) {
      final key = Hive.generateSecureKey();
      await secureStorage.write(
        key: 'key',
        value: base64UrlEncode(key),
      );
    }

    final key = await secureStorage.read(key: 'key');
    final validEncryptionKey = base64Url.decode(key!);
    _encryptedBox = await Hive.openBox(
      'vaultBox',
      encryptionCipher: HiveAesCipher(validEncryptionKey),
    );
  }

  Future<String?> readValue(String key) async {
    return _encryptedBox?.get(key);
  }

  Future<void> writeValue(String key, String value) async {
    if (_encryptedBox != null) {
      await _encryptedBox!.put(key, value);
    }
  }

  /// for clearing all data in box
  Future<void> clearAllData() async {
    await _encryptedBox?.clear();
  }

  Future<void> removeValue(String key) async {
    return _encryptedBox?.delete(key);
  }
}

@riverpod
AppStorage appStorage(AppStorageRef ref) {
  throw UnimplementedError();
}

@riverpod
SecureAppStorage securedAppStorage(SecuredAppStorageRef ref) {
  return SecureAppStorage();
}
