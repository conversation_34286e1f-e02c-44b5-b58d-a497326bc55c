// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_storage.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appStorageHash() => r'1de73cf52c8f26e3ecdf0934a12b2e6a885dffae';

/// See also [appStorage].
@ProviderFor(appStorage)
final appStorageProvider = AutoDisposeProvider<AppStorage>.internal(
  appStorage,
  name: r'appStorageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$appStorageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AppStorageRef = AutoDisposeProviderRef<AppStorage>;
String _$securedAppStorageHash() => r'725857738b75bb2eb450974ef7578a3df9c0fffd';

/// See also [securedAppStorage].
@ProviderFor(securedAppStorage)
final securedAppStorageProvider =
    AutoDisposeProvider<SecureAppStorage>.internal(
  securedAppStorage,
  name: r'securedAppStorageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$securedAppStorageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SecuredAppStorageRef = AutoDisposeProviderRef<SecureAppStorage>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
