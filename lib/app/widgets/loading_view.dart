import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:rive/rive.dart';

class LoadingView extends ConsumerWidget {
  const LoadingView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const RiveAnimation.asset(
      'assets/rives/goma_running.riv',
      // 'assets/rives/loading_2.riv',
      // 'assets/rives/loading_3.riv',
      fit: BoxFit.contain,
      alignment: Alignment.center,
      placeHolder: CircularProgressIndicator(),
    );
  }
}
