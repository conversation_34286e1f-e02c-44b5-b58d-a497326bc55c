import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';

enum AdaptiveTextButtonState {
  danger,
  primary,
}

class AdaptiveTextButton extends StatelessWidget {
  const AdaptiveTextButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.state = AdaptiveTextButtonState.primary,
  });
  final VoidCallback? onPressed;
  final Widget child;
  final AdaptiveTextButtonState state;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isIOS = theme.platform == TargetPlatform.iOS;

    const dangerColor = Colors.red;
    final normalColor = theme.primaryColor;

    if (isIOS) {
      return CupertinoDialogAction(
        onPressed: onPressed,
        isDestructiveAction: state == AdaptiveTextButtonState.danger,
        child: child,
      );
    } else {
      return TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          textStyle: theme.textTheme.labelLarge,
          side: BorderSide(
            color: state == AdaptiveTextButtonState.danger
                ? dangerColor
                : normalColor,
          ),
          foregroundColor: state == AdaptiveTextButtonState.danger
              ? dangerColor
              : normalColor,
        ),
        child: child,
      );
    }
  }
}
