import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AdaptiveDatePicker {
  Future<dynamic> showDate(
    BuildContext context, {
    required ValueChanged<DateTime> onDateTimeChanged,
    DateTime? initialDateTime,
    int minimumYear = 2022,
    int maximumYear = 2030,
  }) async {
    final theme = Theme.of(context);

    if (theme.platform != TargetPlatform.iOS) {
      return _buildCupertinoDatePicker(
        context,
        onDateTimeChanged: onDateTimeChanged,
        initialDateTime: initialDateTime,
        minimumYear: minimumYear,
        maximumYear: maximumYear,
      );
    } else {
      // Let's use a Material date picker for anything other than IOS,
      // because it doesn't require swipe gestures
      return _buildMaterialDatePicker(
        context,
        onDateTimeChanged: onDateTimeChanged,
        initialDateTime: initialDateTime,
        minimumYear: minimumYear,
        maximumYear: maximumYear,
      );
    }
  }

  Future<void> _buildCupertinoDatePicker(
    BuildContext context, {
    required ValueChanged<DateTime> onDateTimeChanged,
    DateTime? initialDateTime,
    int minimumYear = 2022,
    int maximumYear = 2030,
  }) async {
    final picked = await showCupertinoModalPopup<DateTime>(
      context: context,
      builder: (context) {
        return _CupertinoDatePicker(
          initialDateTime: initialDateTime,
          minimumYear: minimumYear,
          maximumYear: maximumYear,
        );
      },
    );

    if (picked != null && picked != initialDateTime) {
      onDateTimeChanged.call(picked);
    }
  }

  Future<void> _buildMaterialDatePicker(
    BuildContext context, {
    required ValueChanged<DateTime> onDateTimeChanged,
    // required Widget child,
    DateTime? initialDateTime,
    int minimumYear = 2022,
    int maximumYear = 2030,
  }) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: initialDateTime ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2025),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light(),
          child: child!,
        );
      },
    );

    if (picked != null && picked != initialDateTime) {
      onDateTimeChanged.call(picked);
    }
  }
}

class _CupertinoDatePicker extends HookConsumerWidget {
  const _CupertinoDatePicker({
    this.initialDateTime,
    this.minimumYear = 2022,
    this.maximumYear = 2030,
  });

  final DateTime? initialDateTime;
  final int minimumYear;
  final int maximumYear;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(initialDateTime ?? DateTime.now());

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ColoredBox(
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CupertinoButton(
                onPressed: () {
                  context.pop();
                },
                child: const Text('Cancel'),
              ),
              CupertinoButton(
                onPressed: () {
                  context.pop(selectedDate.value);
                },
                child: const Text('Select'),
              ),
            ],
          ),
        ),
        Container(
          height: MediaQuery.of(context).copyWith().size.height / 3,
          color: Colors.white,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.date,
            onDateTimeChanged: (DateTime newDate) {
              selectedDate.value = newDate;
            },
            initialDateTime: initialDateTime,
            minimumYear: minimumYear,
            maximumYear: maximumYear,
            dateOrder: DatePickerDateOrder.dmy,
          ),
        ),
      ],
    );
  }
}
