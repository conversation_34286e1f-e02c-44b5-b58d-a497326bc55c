import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:vector_graphics/vector_graphics.dart';

// const milkBackgroundSvgVec =
//     SvgPicture(AssetBytesLoader('assets/milk_background.svg.vec'));
// final milkBackgroundSvg = SvgPicture.asset(
//   'assets/milk_background.svg.vec',
//   semanticsLabel: 'Milk Background',
// );
// const starsBackgroundSvgVec =
//     SvgPicture(AssetBytesLoader('assets/stars_background.svg.vec'));
// final starsBackgroundSvg = SvgPicture.asset(
//   'assets/stars_background.svg.vec',
//   semanticsLabel: 'Stars Background',
// );

class MilkShape extends CustomPainter {
  MilkShape()
      : _paint = Paint()
          ..style = PaintingStyle.fill
          ..color = const Color(0xffFFF3DD).withOpacity(1),
        _shadowPaint = Paint()
          // ..color = const Color(0xff000000)
          ..color = const Color(0x405D076F)
          // ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 35),
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20),
        _path = Path()
          ..moveTo(562.999, 24.1861)
          ..cubicTo(550.114, 22.6177, 534.474, 31.356, 522.784, 35.598)
          ..cubicTo(502.336, 43.0193, 481.475, 50.3502, 459.731, 52.7666)
          ..cubicTo(410.259, 58.2619, 367.859, 17.9065, 318.634, 45.0245)
          ..cubicTo(297.474, 56.6821, 280.853, 80.0625, 255.489, 82.996)
          ..cubicTo(233.778, 85.507, 215.726, 70.2969, 195.117, 66.411)
          ..cubicTo(174.485, 62.5209, 153.859, 66.6724, 137.09, 79.5595)
          ..cubicTo(123.485, 90.0133, 112.256, 103.348, 98.3496, 113.399)
          ..cubicTo(66.4813, 136.436, 46.3609, 119.867, 14.9633, 107.921)
          ..cubicTo(-5.74796, 100.042, -28.5453, 96.2929, -50.6643, 98.3093)
          ..cubicTo(-77.3837, 100.745, -95.3798, 111.065, -116.016, 127.465)
          ..cubicTo(-135.132, 142.657, -152.609, 145.857, -175.58, 136.278)
          ..cubicTo(-210.702, 121.635, -248.121, 106.429, -284, 129.823)
          ..lineTo(-284, 875)
          ..lineTo(563, 875)
          ..cubicTo(563, 875, 563, 24.1876, 562.999, 24.1876)
          ..lineTo(562.999, 24.1861)
          ..close();

  final Paint _paint, _shadowPaint;
  final Path _path;

  @override
  void paint(Canvas canvas, Size size) {
    // only clip width
    canvas.clipRect(Rect.fromLTWH(0, 0, size.width, double.infinity));

    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    const whiteShadow = BoxShadow(
      blurRadius: 4,
      color: Colors.white,
      offset: Offset(8, 8),
    );
    const colouredShadow = BoxShadow(
      blurRadius: 8,
      color: Color(0xffE1C08F),
      offset: Offset(0, 4),
    );

    final bounds = _path.getBounds();
    final shadowRect = bounds.inflate(whiteShadow.blurRadius);
    final whiteInnerShadow = Paint()
      ..blendMode = BlendMode.srcATop
      ..colorFilter = ColorFilter.mode(whiteShadow.color, BlendMode.srcOut)
      ..imageFilter = ui.ImageFilter.blur(
        sigmaX: whiteShadow.blurSigma,
        sigmaY: whiteShadow.blurSigma,
      );
    final colouredInnerShadow = Paint()
      ..blendMode = BlendMode.srcATop
      ..colorFilter = ColorFilter.mode(colouredShadow.color, BlendMode.srcOut)
      ..imageFilter = ui.ImageFilter.blur(
        sigmaX: colouredShadow.blurSigma,
        sigmaY: colouredShadow.blurSigma,
      );

    offscreenCanvas
      ..translate(0, -60) // move up
      ..save()
      ..translate(4, -4)
      ..drawPath(_path, _shadowPaint) // draw drop shadow
      ..restore()
      ..saveLayer(bounds, Paint()) // save a clean layer
      ..drawPath(_path, _paint) // draw the path
      ..saveLayer(shadowRect, whiteInnerShadow) // draw white inner shadow
      ..translate(
        whiteShadow.offset.dx,
        whiteShadow.offset.dy,
      ) // offset white inner shadow
      ..drawPath(_path, _paint) // draw the path
      ..restore()
      ..saveLayer(
        shadowRect,
        colouredInnerShadow,
      ) // draw coloured inner shadow
      ..translate(
        colouredShadow.offset.dx,
        colouredShadow.offset.dy,
      ) // offset coloured inner shadow
      ..drawPath(_path, _paint) // redraw the path
      ..restore()
      ..restore();

    final picture = recorder.endRecording();
    canvas.drawPicture(picture);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class MilkBackground extends ConsumerWidget {
  const MilkBackground({this.child, super.key});
  final Widget? child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // return FutureBuilder(
    //   future: ScalableImage.fromSvgAsset(
    //     rootBundle,
    //     'assets/milk_background.svg',
    //   ),
    //   builder: (context, snapshot) {
    //     if (snapshot.hasData) {
    //       return ScalableImageWidget(
    //         si: snapshot.data!,
    //       );
    //     }

    //     return const SizedBox.shrink();
    //   },
    // );
    // return DecoratedBox(
    //   decoration: const BoxDecoration(
    //     image: DecorationImage(
    //       image: AssetImage('assets/images/milk_background.png'),
    //       fit: BoxFit.fill,
    //     ),
    //   ),
    //   child: child,
    // );
    return const DecoratedBox(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/milk_background_top.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: SizedBox(height: 120),
    );
    // return CustomPaint(
    //   painter: MilkShape(),
    //   child: child,
    // );
  }
}

class StarsShape extends CustomPainter {
  StarsShape()
      : _starPaint = Paint()
          ..style = PaintingStyle.fill
          ..shader = ui.Gradient.linear(
              const Offset(12 * 4.369450, 12 * 0.5064300), // 12 is star size
              const Offset(12 * 0.9581833, 12 * 0.5064300), // 12 is star size
              [
                const Color(0xffF29BF9).withOpacity(1),
                const Color(0xffEB92F3).withOpacity(1),
                const Color(0xffDA7CE5).withOpacity(1),
                const Color(0xffD575E0).withOpacity(1),
              ],
              [
                0,
                0.18,
                0.46,
                0.54,
              ]),
        _bigCirclePaint = Paint()
          ..style = PaintingStyle.fill
          ..shader = ui.Gradient.linear(
              const Offset(20 * 4.369450, 20 * 0.5064300), // 20 is circle size
              const Offset(20 * 0.9581833, 20 * 0.5064300), // 20 is circle size
              [
                const Color(0xffF29BF9).withOpacity(1),
                const Color(0xffEB92F3).withOpacity(1),
                const Color(0xffDA7CE5).withOpacity(1),
                const Color(0xffD575E0).withOpacity(1),
              ],
              [
                0,
                0.18,
                0.46,
                0.54,
              ])
          ..strokeCap = StrokeCap.round
          ..strokeWidth = 10
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10),
        _smallCirclePaint = Paint()
          ..style = PaintingStyle.fill
          ..shader = ui.Gradient.linear(
              const Offset(8 * 4.369450, 8 * 0.5064300), // 8 is circle size
              const Offset(8 * 0.9581833, 8 * 0.5064300), // 8 is circle size
              [
                const Color(0xffF29BF9).withOpacity(1),
                const Color(0xffEB92F3).withOpacity(1),
                const Color(0xffDA7CE5).withOpacity(1),
                const Color(0xffD575E0).withOpacity(1),
              ],
              [
                0,
                0.18,
                0.46,
                0.54,
              ])
          ..strokeCap = StrokeCap.round
          ..strokeWidth = 4
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10),
        _starPath = Path()
          ..moveTo(16.5594, 8.96341)
          ..lineTo(17.2473, 9.12102)
          ..lineTo(16.5594, 9.27857)
          ..cubicTo(12.8787, 10.1138, 10.0066, 12.9871, 9.17178, 16.6746)
          ..lineTo(9.01427, 17.368)
          ..lineTo(8.85675, 16.6746)
          ..cubicTo(8.02715, 12.9924, 5.14979, 10.1138, 1.46908, 9.27857)
          ..lineTo(0.78125, 9.12102)
          ..lineTo(1.46908, 8.96341)
          ..cubicTo(5.14979, 8.12821, 8.0219, 5.25493, 8.85675, 1.56743)
          ..lineTo(9.01427, 0.874023)
          ..lineTo(9.17178, 1.56743)
          ..cubicTo(10.0014, 5.24968, 12.8787, 8.12821, 16.5594, 8.96341)
          ..close(),
        _bigPointPositions = [
          const Offset(212, 738),
          const Offset(347, 591),
          const Offset(119, 412),
          const Offset(267, 299),
          const Offset(235, 676),
          const Offset(127, 183),
          const Offset(301, 722),
          const Offset(96, 537),
          const Offset(93, 96),
          const Offset(202, 655),
          const Offset(350, 420),
          const Offset(48, 806),
          const Offset(293, 168),
          const Offset(185, 312),
          const Offset(276, 579),
          const Offset(134, 669),
          const Offset(327, 244),
          const Offset(62, 483),
          const Offset(223, 731),
          const Offset(315, 104),
        ],
        _smallPointPositions = [
          const Offset(231, 571),
          const Offset(104, 325),
          const Offset(284, 791),
          const Offset(41, 614),
          const Offset(197, 158),
          const Offset(93, 96),
          const Offset(352, 683),
          const Offset(126, 472),
          const Offset(299, 238),
          const Offset(67, 737),
          const Offset(215, 619),
          const Offset(327, 394),
          const Offset(181, 508),
          const Offset(48, 276),
          const Offset(293, 651),
          const Offset(142, 83),
          const Offset(263, 431),
          const Offset(9, 548),
          const Offset(334, 179),
          const Offset(76, 718),
        ];

  final Paint _starPaint, _bigCirclePaint, _smallCirclePaint;
  final Path _starPath;
  final List<Offset> _bigPointPositions, _smallPointPositions;

  @override
  void paint(ui.Canvas canvas, ui.Size size) {
    canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));

    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    final scale1 = Float64List.fromList(
      [0.8, 0, 0, 0, 0, 0.8, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
    );
    final scale2 = Float64List.fromList(
      [0.6, 0, 0, 0, 0, 0.6, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
    );
    final scaledDownPaths = [
      _starPath,
      _starPath.transform(scale1),
      _starPath.transform(scale2),
    ];
    final relativeX = size.width / 390;
    final relativeY = size.height / 844;

    offscreenCanvas
      ..save()
      ..translate(220 * relativeX, 160 * relativeY)
      ..drawPath(
        scaledDownPaths[0],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(370 * relativeX, 210 * relativeY)
      ..drawPath(
        scaledDownPaths[1],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(70 * relativeX, 280 * relativeY)
      ..drawPath(
        scaledDownPaths[2],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(340 * relativeX, 340 * relativeY)
      ..drawPath(
        scaledDownPaths[0],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(250 * relativeX, 420 * relativeY)
      ..drawPath(
        scaledDownPaths[1],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(110 * relativeX, 540 * relativeY)
      ..drawPath(
        scaledDownPaths[2],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(360 * relativeX, 520 * relativeY)
      ..drawPath(
        scaledDownPaths[0],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(80 * relativeX, 590 * relativeY)
      ..drawPath(
        scaledDownPaths[1],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(100 * relativeX, 680 * relativeY)
      ..drawPath(
        scaledDownPaths[2],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(70 * relativeX, 731 * relativeY)
      ..drawPath(
        scaledDownPaths[0],
        _starPaint,
      )
      ..restore()
      ..save()
      ..translate(69 * relativeX, 790 * relativeY)
      ..drawPath(
        scaledDownPaths[1],
        _starPaint,
      )
      ..restore()
      ..drawPoints(
        ui.PointMode.points,
        _bigPointPositions,
        _bigCirclePaint,
      )
      ..drawPoints(
        ui.PointMode.points,
        _smallPointPositions,
        _smallCirclePaint,
      );

    final picture = recorder.endRecording();
    canvas.drawPicture(picture);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class StarsBackground extends ConsumerWidget {
  const StarsBackground({
    this.child,
    super.key,
    this.isTransparent = false,
  });
  final Widget? child;
  final bool isTransparent;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DecoratedBox(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: const AssetImage('assets/images/stars_background.png'),
          fit: BoxFit.fill,
          colorFilter: isTransparent
              ? ColorFilter.mode(
                  Colors.black.withOpacity(0.8),
                  BlendMode.dstATop,
                )
              : null,
        ),
      ),
      child: child,
    );
    // return CustomPaint(
    //   painter: StarsShape(),
    //   child: child,
    // );
  }
}

class FilterWaveClipper extends CustomClipper<Path> {
  FilterWaveClipper();

  @override
  Path getClip(Size size) {
    final path = Path()
      ..moveTo(size.width * 1.820638, size.height * 0.3198472)
      ..cubicTo(
        size.width * 1.815803,
        size.height * 0.3251588,
        size.width * 1.802531,
        size.height * 0.3291659,
        size.width * 1.794564,
        size.height * 0.3335071,
      )
      ..cubicTo(
        size.width * 1.774069,
        size.height * 0.3446813,
        size.width * 1.759292,
        size.height * 0.3577950,
        size.width * 1.746828,
        size.height * 0.3711813,
      )
      ..cubicTo(
        size.width * 1.709423,
        size.height * 0.4113424,
        size.width * 1.693333,
        size.height * 0.4614076,
        size.width * 1.613428,
        size.height * 0.4894858,
      )
      ..cubicTo(
        size.width * 1.531005,
        size.height * 0.5184479,
        size.width * 1.404300,
        size.height * 0.5125462,
        size.width * 1.332269,
        size.height * 0.5469194,
      )
      ..cubicTo(
        size.width * 1.239959,
        size.height * 0.5909739,
        size.width * 1.317003,
        size.height * 0.7026671,
        size.width * 1.201282,
        size.height * 0.7326896,
      )
      ..cubicTo(
        size.width * 1.108849,
        size.height * 0.7566682,
        size.width * 1.115679,
        size.height * 0.7458531,
        size.width * 1.007692,
        size.height * 0.7753436,
      )
      ..cubicTo(
        size.width * 0.9208179,
        size.height * 0.7855178,
        size.width * 0.9756410,
        size.height * 0.8625592,
        size.width * 0.8730769,
        size.height * 0.8939573,
      )
      ..cubicTo(
        size.width * 0.8541359,
        size.height * 0.9028436,
        size.width * 0.7813513,
        size.height * 0.9118934,
        size.width * 0.7108385,
        size.height * 0.9081754,
      )
      ..cubicTo(
        size.width * 0.5560000,
        size.height * 0.9000118,
        size.width * 0.5943000,
        size.height * 0.9323993,
        size.width * 0.4384615,
        size.height * 0.9786730,
      )
      ..cubicTo(
        size.width * 0.3470795,
        size.height * 1.013791,
        size.width * 0.2287833,
        size.height * 0.9563116,
        size.width * 0.1371795,
        size.height * 0.9899289,
      )
      ..cubicTo(
        size.width * 0.07726154,
        size.height * 1.011918,
        size.width * 0.09035026,
        size.height * 1.021840,
        size.width * 0.03717949,
        size.height * 1.047262,
      )
      ..cubicTo(
        size.width * 0.006207359,
        size.height * 1.062071,
        size.width * -0.04579667,
        size.height * 1.062217,
        size.width * -0.08824410,
        size.height * 1.055929,
      )
      ..cubicTo(
        size.width * -0.1306918,
        size.height * 1.049646,
        size.width * -0.1690021,
        size.height * 1.038159,
        size.width * -0.2119103,
        size.height * 1.032582,
      )
      ..cubicTo(
        size.width * -0.3126026,
        size.height * 1.019485,
        size.width * -0.5372744,
        size.height * 0.9780936,
        size.width * -0.5793385,
        size.height * 1.022344,
      )
      ..cubicTo(
        size.width * -0.5878590,
        size.height * 1.031305,
        size.width * -0.5944359,
        size.height * 1.041390,
        size.width * -0.6113769,
        size.height * 1.047262,
      )
      ..cubicTo(
        size.width * -0.6558744,
        size.height * 1.062688,
        size.width * -0.7115615,
        size.height * 1.036269,
        size.width * -0.7376282,
        size.height * 1.013559,
      )
      ..cubicTo(
        size.width * -0.8941026,
        size.height * 0.8772334,
        size.width * -1.059815,
        size.height * 0.7383685,
        size.width * -1.315385,
        size.height * 0.6390178,
      )
      ..cubicTo(
        size.width * -0.6109692,
        size.height * 0.3490000,
        size.width * -0.2659538,
        size.height * -0.08734822,
        size.width * 0.4384615,
        size.height * -0.3773697,
      )
      ..cubicTo(
        size.width * 0.6167564,
        size.height * -0.2866967,
        size.width * 1.187787,
        size.height * -0.06519455,
        size.width * 1.379192,
        size.height * 0.01958057,
      )
      ..cubicTo(
        size.width * 1.496695,
        size.height * 0.07162441,
        size.width * 1.607149,
        size.height * 0.1279455,
        size.width * 1.694423,
        size.height * 0.1916600,
      )
      ..cubicTo(
        size.width * 1.738038,
        size.height * 0.2234976,
        size.width * 1.775651,
        size.height * 0.2571540,
        size.width * 1.804359,
        size.height * 0.2924491,
      )
      ..cubicTo(
        size.width * 1.809372,
        size.height * 0.2986126,
        size.width * 1.824431,
        size.height * 0.3107299,
        size.width * 1.822213,
        size.height * 0.3173116,
      )
      ..cubicTo(
        size.width * 1.821913,
        size.height * 0.3181908,
        size.width * 1.821374,
        size.height * 0.3190308,
        size.width * 1.820631,
        size.height * 0.3198436,
      )
      ..lineTo(size.width * 1.820638, size.height * 0.3198472)
      ..close();

    return path;
  }

  @override
  bool shouldReclip(FilterWaveClipper oldClipper) => oldClipper != this;
}
