import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/debounce_provider.dart';
import 'package:gomama/app/widgets/shadow_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrandButton<T> extends HookConsumerWidget {
  const BrandButton({
    super.key,
    this.onPressed,
    this.child,
    this.padding,
    this.foregroundColor,
    this.backgroundColor,
    this.shadows = const [
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, 4),
        color: Colors.white,
      ),
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, -2),
        color: CustomColors.secondaryDark,
      ),
    ],
    this.debounced = false,
  });

  factory BrandButton.cta({
    final T Function()? onPressed,
    final Widget? child,
    final Color? foregroundColor = Colors.white,
    final Color? backgroundColor = CustomColors.primary,
    final EdgeInsetsGeometry? padding,
    final List<BoxShadow>? shadows = const [
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, -2),
        color: Color(0xff5A0D69),
      ),
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, 2),
        color: Color(0xCCffffff),
      ),
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, 2),
        color: Color(0xff5A0D69),
      ),
    ],
    final bool? debounced,
  }) =>
      BrandButton(
        onPressed: onPressed,
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        shadows: shadows,
        debounced: debounced,
        child: child,
      );

  factory BrandButton.singpass({
    final T Function()? onPressed,
    final Widget? child,
    final EdgeInsetsGeometry? padding,
    final Color? foregroundColor = Colors.white,
    final Color? backgroundColor = Colors.red,
    final List<BoxShadow>? shadows = const [
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, 4),
        color: Color(0x80ffffff),
      ),
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, -2),
        color: Color(0xff880B00),
      ),
    ],
    final bool? debounced,
  }) =>
      BrandButton(
        onPressed: onPressed,
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        shadows: shadows,
        debounced: debounced,
        child: child,
      );

  factory BrandButton.filled({
    final T Function()? onPressed,
    final Widget? child,
    final Color? foregroundColor = CustomColors.primaryDark,
    final Color? backgroundColor = const Color(0xffE7B3E7),
    final EdgeInsetsGeometry? padding,
    final List<BoxShadow>? shadows = const [],
    final bool? debounced,
  }) =>
      BrandButton(
        onPressed: onPressed,
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        shadows: shadows,
        debounced: debounced,
        child: child,
      );

  factory BrandButton.outlined({
    final T Function()? onPressed,
    final Widget? child,
    final Color? foregroundColor = CustomColors.primary,
    final Color? backgroundColor = CustomColors.primary,
    final EdgeInsetsGeometry? padding,
    final List<BoxShadow>? shadows,
    final bool? debounced,
  }) =>
      BrandButton(
        onPressed: onPressed,
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        shadows: shadows,
        debounced: debounced,
        child: child,
      );

  factory BrandButton.whatsapp({
    final T Function()? onPressed,
    final Widget? child,
    final EdgeInsetsGeometry? padding,
    final Color? foregroundColor = Colors.white,
    final Color? backgroundColor = const Color(0xff25D366),
    final List<BoxShadow>? shadows = const [
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, 4),
        color: Color(0x80ffffff),
      ),
      BoxShadow(
        blurRadius: 4,
        offset: Offset(0, -2),
        color: Color(0xff075E54),
      ),
    ],
    final bool? debounced,
  }) =>
      BrandButton(
        onPressed: onPressed,
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        shadows: shadows,
        debounced: debounced,
        child: child,
      );

  final T Function()? onPressed;
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final Color? foregroundColor;
  final Color? backgroundColor;
  final List<BoxShadow>? shadows;
  final bool? debounced;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // return ElevatedButton(
    //   onPressed: onPressed,
    //   style: ElevatedButton.styleFrom(
    //     padding: padding,
    //   ),
    //   child: Center(child: child),
    // );

    // void onButtonClick() {
    //   // Cancel the previous debounced call if the user continues clicking
    //   if (_debounce?.isActive ?? false) _debounce.cancel();

    //   // Schedule a new debounced call
    //   _debounce = Timer(_debouceDuration, () {
    //     // perform action like form submittion, etc
    //   });
    // }

    final isBusy = useState(false);

    Future<void> onButtonClick() async {
      if (onPressed == null) {
        return;
      }

      if (debounced == true) {
        if (isBusy.value) {
          return;
        }

        isBusy.value = true;

        ref
            .read(debouncerProvider(const Duration(milliseconds: 500)))
            .debounce(() async {
          try {
            final result = onPressed!();
            if (result is Future) {
              // ignore: await_only_futures
              await result;
            }
          } finally {
            isBusy.value = false;
          }
        });
      } else {
        onPressed!();
      }
    }

    if (shadows == null) {
      // should be outlined
      return OutlinedButton(
        onPressed: onButtonClick,
        style: OutlinedButton.styleFrom(
          padding: padding,
          foregroundColor:
              onPressed == null ? CustomColors.placeholder : foregroundColor,
          side: BorderSide(
            color: onPressed == null
                ? CustomColors.placeholder
                : backgroundColor ?? CustomColors.primary,
          ),
        ),
        child: Center(child: child),
      );
    }

    return ElevatedButton(
      onPressed: onButtonClick,
      style: ElevatedButton.styleFrom(
        padding: padding,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
      ),
      child: Center(child: child),
    );

    // return InnerShadow(
    //   shadows: shadows ?? [],
    //   child: ElevatedButton(
    //     onPressed: onButtonClick,
    //     style: ElevatedButton.styleFrom(
    //       padding: padding,
    //       foregroundColor: foregroundColor,
    //       backgroundColor: backgroundColor,
    //     ),
    //     child: Center(child: child),
    //   ),
    // );
  }
}
