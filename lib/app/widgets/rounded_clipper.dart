import 'package:flutter/material.dart';

class RoundedClipper extends CustomClipper<Path> {
  RoundedClipper({
    this.radius = 24,
  });
  final double radius;

  @override
  Path getClip(Size size) {
    final path = Path()
      // Top-left corner
      ..moveTo(0, radius)
      ..quadraticBezierTo(0, 0, radius, 0)
      // Top-right corner
      ..lineTo(size.width - radius, 0)
      ..quadraticBezierTo(size.width, 0, size.width, radius)
      // Bottom-right corner
      ..lineTo(size.width, size.height - radius)
      ..quadraticBezierTo(
        size.width,
        size.height,
        size.width - radius,
        size.height,
      )
      // Bottom-left corner
      ..lineTo(radius, size.height)
      ..quadraticBezierTo(0, size.height, 0, size.height - radius)
      ..close(); // Close the path

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
