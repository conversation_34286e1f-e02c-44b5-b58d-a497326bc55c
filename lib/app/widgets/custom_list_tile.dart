import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CustomListTile extends ConsumerWidget {
  const CustomListTile({
    super.key,
    required this.onTap,
    this.titleString,
    this.leadingIcon,
  });
  final void Function()? onTap;
  final String? titleString;
  final IconData? leadingIcon;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: CustomColors.primaries.shade50,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 10),
        leading: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: CustomColors.primaries.shade100,
          ),
          child: Icon(
            leadingIcon,
            color: Theme.of(context).iconTheme.color,
          ),
        ),
        title: Text(
          titleString ?? '',
          style: const TextStyle(color: CustomColors.primaries),
        ),
        trailing: Icon(
          CustomIcon.keyboardArrowRight,
          color: Theme.of(context).iconTheme.color,
        ),
        onTap: onTap,
      ),
    );
  }
}
