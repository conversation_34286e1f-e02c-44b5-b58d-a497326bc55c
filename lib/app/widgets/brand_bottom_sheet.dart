import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrandBottomSheet extends ConsumerStatefulWidget {
  const BrandBottomSheet({
    super.key,
    required this.minHeight,
    required this.maxHeight,
    this.snapSizes,
    this.slivers = const <Widget>[],
    this.controller,
  });
  final double minHeight;
  final double maxHeight;
  final List<double>? snapSizes;
  final List<Widget> slivers;
  final DraggableScrollableController? controller;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      BrandBottomSheetState();
}

class BrandBottomSheetState extends ConsumerState<BrandBottomSheet> {
  final _sheet = GlobalKey();
  late final DraggableScrollableController _controller;
  late double _minChildSize;
  late double _maxChildSize;
  late List<double> snapSizes;

  DraggableScrollableSheet get sheet =>
      _sheet.currentWidget! as DraggableScrollableSheet;

  bool get isFullyEnclosed {
    return _controller.size == _minChildSize;
  }

  void snapToExtent(double extent) {
    _controller.animateTo(
      extent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? DraggableScrollableController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _minChildSize = widget.minHeight / constraints.maxHeight;
        _maxChildSize =
            (constraints.maxHeight - widget.maxHeight) / constraints.maxHeight;
        snapSizes = [_minChildSize, ...widget.snapSizes ?? []];

        // TODO(kkcy): represent widget.minHeight & widget.maxHeight better
        // final minHeight = widget.minHeight / constraints.maxHeight;
        // final maxHeight =
        //     (constraints.maxHeight - widget.maxHeight) / constraints.maxHeight;

        return DraggableScrollableSheet(
          key: _sheet,
          initialChildSize: _minChildSize,
          minChildSize: _minChildSize,
          maxChildSize: _maxChildSize,
          snap: true,
          snapSizes: snapSizes, // [_minChildSize, ...widget.snapSizes ?? []],
          controller: _controller,
          builder: (BuildContext context, ScrollController scrollController) {
            return DecoratedBox(
              decoration: const BoxDecoration(
                color: CustomColors.secondaryExtraLight,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x40c59a70),
                    blurRadius: 4,
                    offset: Offset(0, -2), // changes position of shadow
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                child: CustomScrollView(
                  controller: scrollController,
                  slivers: widget.slivers,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
