import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BrandScaffold extends ConsumerWidget {
  const BrandScaffold({
    super.key,
    this.child,
    this.children,
    this.title,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.physics,
    this.resizeToAvoidBottomInset = true,
  });
  final Widget? child;
  final List<Widget>? children;
  final Widget? title;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;
  final ScrollPhysics? physics;
  final bool resizeToAvoidBottomInset;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: DecoratedBox(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/backgrounds/stars_background_edit_profile.png',
            ),
            fit: BoxFit.cover,
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CustomColors.backgroundGradientLight,
              CustomColors.backgroundGradient,
              CustomColors.backgroundGradientDark,
            ],
          ),
        ),
        child: Stack(
          children: [
            CustomScrollView(
              physics: physics,
              slivers: [
                SliverPersistentHeader(
                  delegate: BrandAppBar(
                    maxHeight: kToolbarHeight +
                        32 +
                        mediaQuery(context).viewPadding.top,
                    minHeight:
                        kToolbarHeight + mediaQuery(context).viewPadding.top,
                    title: title,
                    actions: actions,
                    automaticallyImplyLeading: automaticallyImplyLeading,
                  ),
                  pinned: true,
                ),
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  sliver: SliverToBoxAdapter(
                    child: Image.asset(
                      'assets/images/goma_planet.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ],
            ),
            if (child != null) child!,
            if (children != null) ...children!,
          ],
        ),
      ),
    );
  }
}
