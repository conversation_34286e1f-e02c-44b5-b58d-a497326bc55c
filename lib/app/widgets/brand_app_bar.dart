import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';

class BrandAppBar extends SliverPersistentHeaderDelegate {
  const BrandAppBar({
    required this.maxHeight,
    required this.minHeight,
    this.child,
    this.title,
    this.actions,
    this.automaticallyImplyLeading = true,
  });
  final double maxHeight;
  final double minHeight;
  final Widget? child;
  final Widget? title;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final adjustedShrinkOffset =
        shrinkOffset > minExtent ? minExtent : shrinkOffset;

    return Stack(
      children: [
        BackgroundWave(
          height: maxHeight,
          minHeight: minHeight,
          shrinkOffset: adjustedShrinkOffset,
        ),
        AppBar(
          title: title,
          centerTitle: false,
          actions: actions,
          automaticallyImplyLeading: automaticallyImplyLeading,
          iconTheme: const IconThemeData(color: CustomColors.primary),
          titleTextStyle: textTheme(context).titleLarge,
        ),
        if (child != null) child!,
      ],
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      oldDelegate.maxExtent != maxExtent || oldDelegate.minExtent != minExtent;
}

class BackgroundWave extends StatelessWidget {
  const BackgroundWave({
    super.key,
    required this.height,
    required this.minHeight,
    required this.shrinkOffset,
  });
  final double height;
  final double minHeight;
  final double shrinkOffset;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: ClipPath(
        clipper: BackgroundWaveClipper(
          shrinkOffset: shrinkOffset,
          height: height,
          minHeight: minHeight,
        ),
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: height,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                CustomColors.secondaryGradientDark,
                CustomColors.secondaryGradientLight,
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class BackgroundWaveClipper extends CustomClipper<Path> {
  BackgroundWaveClipper({
    required this.height,
    required this.minHeight,
    required this.shrinkOffset,
  });
  final double height;
  final double minHeight;
  final double shrinkOffset;

  @override
  Path getClip(Size size) {
    final path = Path();

    // Calculate wave height reduction factor
    final waveHeight = (1 - (shrinkOffset / (height - minHeight))) * 40.0;

    // Ensure the height does not go below minHeight
    final effectiveHeight =
        (height - shrinkOffset < minHeight ? minHeight : height - shrinkOffset);

    path.lineTo(0, waveHeight);

    final controlPoint1 = Offset(size.width / 4, size.height - waveHeight);
    final endPoint1 = Offset(size.width / 2, size.height - waveHeight / 2);
    final controlPoint2 = Offset(size.width * 3 / 4, size.height);
    final endPoint2 = Offset(size.width, size.height - waveHeight);

    path
      ..lineTo(0, effectiveHeight)
      ..quadraticBezierTo(
        controlPoint1.dx,
        controlPoint1.dy,
        endPoint1.dx,
        endPoint1.dy,
      )
      ..quadraticBezierTo(
        controlPoint2.dx,
        controlPoint2.dy,
        endPoint2.dx,
        endPoint2.dy,
      )
      ..lineTo(size.width, 0)
      ..close();

    return path;
  }

  @override
  bool shouldReclip(BackgroundWaveClipper oldClipper) => oldClipper != this;
}
