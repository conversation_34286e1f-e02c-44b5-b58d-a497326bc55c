import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/explore/model/nearby_listing_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/maps/model/map_input.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'explore_providers.g.dart';

@riverpod
class ListingTypeFilters extends _$ListingTypeFilters {
  // all, gomama, care
  @override
  List<String> build() {
    return ['all'];
  }

  void setList(List<String> list) {
    state = [...list];
  }

  void clear() {
    state = [];
  }
}

@riverpod
Future<ListingsResponse> nearbyListingsPages(
  NearbyListingsPagesRef ref,
  NearbyListingsPagination meta,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  if (cancelToken.isCancelled) throw Exception();

  final sorting = ref.watch(listingSortsProvider);
  final amenities = ref.watch(amenityFiltersProvider);
  final types = ref.watch(listingTypeFiltersProvider);
  Groveman
    ..info('amenities selected', error: amenities)
    ..info('types selected', error: types);

  return ref.watch(listingRepositoryProvider).nearbyListings(
        meta.input,
        offset: meta.page,
        cancelToken: cancelToken,
        amenities: amenities,
        types: types,
        sorting: sorting,
      );
}

@riverpod
AsyncValue<int> nearbyListingsCount(
  NearbyListingsCountRef ref,
  NearbyListingsInput? input,
) {
  final meta = NearbyListingsPagination(page: 0, input: input);

  return ref
      .watch(nearbyListingsPagesProvider(meta))
      .whenData((value) => value.meta.total);
}

@riverpod
AsyncValue<Listing> listingAtIndex(
  ListingAtIndexRef ref,
  NearbyListingsOffset query,
) {
  final offsetInPage = query.offset % kPageLimit;

  final meta = NearbyListingsPagination(
    page: query.offset ~/ kPageLimit,
    input: query.input,
  );

  return ref.watch(nearbyListingsPagesProvider(meta)).whenData(
        (value) => value.data[offsetInPage],
      );
}

@riverpod
class ExploreMapPosition extends _$ExploreMapPosition {
  @override
  MapInput build() {
    final position = ref.watch(currentPositionProvider).valueOrNull;

    return position != null
        ? MapInput(
            latitude: position.latitude,
            longitude: position.longitude,
            zoom: 12, // 12 is default zoom
          )
        : MapInput(
            latitude: 1.3521,
            longitude: 103.8198,
            zoom: 12, // 12 is default zoom
          );
  }

  void setPosition(MapInput position) {
    state = position;
  }
}

/// for map markers, without pagination
@riverpod
Future<ListingsResponse> nearbyListingMarkers(
  NearbyListingMarkersRef ref,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  if (cancelToken.isCancelled) throw Exception();

  final amenities = ref.watch(amenityFiltersProvider);
  final types = ref.watch(listingTypeFiltersProvider);
  late final NearbyListingsInput input;

  final position = ref.watch(exploreMapPositionProvider);

  input = NearbyListingsInput(
    lat: position.latitude,
    lon: position.longitude,
    r: getSearchRadius(position.zoom),
  );

  Groveman
    ..info('position', error: position)
    ..info('amenities selected', error: amenities)
    ..info('types selected', error: types);

  return ref.watch(listingRepositoryProvider).nearbyListings(
        input,
        offset: 0,
        limit: 100,
        cancelToken: cancelToken,
        amenities: amenities,
        types: types,
      );
}

/// for map markers, without pagination
@riverpod
Future<ListingsResponse> listingMarkers(
  ListingMarkersRef ref,
) async {
  final amenities = ref.watch(amenityFiltersProvider);
  Groveman.info('amenities selected', error: amenities);

  return ref.watch(listingRepositoryProvider).fetchListings(
        offset: 0,
        limit: 100,
        amenities: amenities,
      );
}

// marker selected
@riverpod
class MarkerSelected extends _$MarkerSelected {
  @override
  Listing? build() {
    return null;
  }

  void setMarker(Listing? marker) {
    state = marker;
  }
}
