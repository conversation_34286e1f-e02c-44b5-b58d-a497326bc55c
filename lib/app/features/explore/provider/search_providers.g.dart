// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchListingsPagesHash() =>
    r'a4797ad08d25a0e005b4cba37aaa665a69d4a269';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [searchListingsPages].
@ProviderFor(searchListingsPages)
const searchListingsPagesProvider = SearchListingsPagesFamily();

/// See also [searchListingsPages].
class SearchListingsPagesFamily extends Family<AsyncValue<ListingsResponse>> {
  /// See also [searchListingsPages].
  const SearchListingsPagesFamily();

  /// See also [searchListingsPages].
  SearchListingsPagesProvider call(
    AllListingsPagination meta,
  ) {
    return SearchListingsPagesProvider(
      meta,
    );
  }

  @override
  SearchListingsPagesProvider getProviderOverride(
    covariant SearchListingsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchListingsPagesProvider';
}

/// See also [searchListingsPages].
class SearchListingsPagesProvider
    extends AutoDisposeFutureProvider<ListingsResponse> {
  /// See also [searchListingsPages].
  SearchListingsPagesProvider(
    AllListingsPagination meta,
  ) : this._internal(
          (ref) => searchListingsPages(
            ref as SearchListingsPagesRef,
            meta,
          ),
          from: searchListingsPagesProvider,
          name: r'searchListingsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$searchListingsPagesHash,
          dependencies: SearchListingsPagesFamily._dependencies,
          allTransitiveDependencies:
              SearchListingsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  SearchListingsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final AllListingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingsResponse> Function(SearchListingsPagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchListingsPagesProvider._internal(
        (ref) => create(ref as SearchListingsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingsResponse> createElement() {
    return _SearchListingsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchListingsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SearchListingsPagesRef on AutoDisposeFutureProviderRef<ListingsResponse> {
  /// The parameter `meta` of this provider.
  AllListingsPagination get meta;
}

class _SearchListingsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingsResponse>
    with SearchListingsPagesRef {
  _SearchListingsPagesProviderElement(super.provider);

  @override
  AllListingsPagination get meta =>
      (origin as SearchListingsPagesProvider).meta;
}

String _$searchListingsCountHash() =>
    r'c6896eeb62d69bde381518aca37322af2b109cac';

/// See also [searchListingsCount].
@ProviderFor(searchListingsCount)
const searchListingsCountProvider = SearchListingsCountFamily();

/// See also [searchListingsCount].
class SearchListingsCountFamily extends Family<AsyncValue<int>> {
  /// See also [searchListingsCount].
  const SearchListingsCountFamily();

  /// See also [searchListingsCount].
  SearchListingsCountProvider call(
    AllListingsInput? input,
  ) {
    return SearchListingsCountProvider(
      input,
    );
  }

  @override
  SearchListingsCountProvider getProviderOverride(
    covariant SearchListingsCountProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchListingsCountProvider';
}

/// See also [searchListingsCount].
class SearchListingsCountProvider extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [searchListingsCount].
  SearchListingsCountProvider(
    AllListingsInput? input,
  ) : this._internal(
          (ref) => searchListingsCount(
            ref as SearchListingsCountRef,
            input,
          ),
          from: searchListingsCountProvider,
          name: r'searchListingsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$searchListingsCountHash,
          dependencies: SearchListingsCountFamily._dependencies,
          allTransitiveDependencies:
              SearchListingsCountFamily._allTransitiveDependencies,
          input: input,
        );

  SearchListingsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final AllListingsInput? input;

  @override
  Override overrideWith(
    AsyncValue<int> Function(SearchListingsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchListingsCountProvider._internal(
        (ref) => create(ref as SearchListingsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _SearchListingsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchListingsCountProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SearchListingsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `input` of this provider.
  AllListingsInput? get input;
}

class _SearchListingsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with SearchListingsCountRef {
  _SearchListingsCountProviderElement(super.provider);

  @override
  AllListingsInput? get input => (origin as SearchListingsCountProvider).input;
}

String _$searchListingAtIndexHash() =>
    r'43995d48df8d0402a5f2a8e45fc903f6540acccd';

/// See also [searchListingAtIndex].
@ProviderFor(searchListingAtIndex)
const searchListingAtIndexProvider = SearchListingAtIndexFamily();

/// See also [searchListingAtIndex].
class SearchListingAtIndexFamily extends Family<AsyncValue<Listing>> {
  /// See also [searchListingAtIndex].
  const SearchListingAtIndexFamily();

  /// See also [searchListingAtIndex].
  SearchListingAtIndexProvider call(
    AllListingsOffset query,
  ) {
    return SearchListingAtIndexProvider(
      query,
    );
  }

  @override
  SearchListingAtIndexProvider getProviderOverride(
    covariant SearchListingAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchListingAtIndexProvider';
}

/// See also [searchListingAtIndex].
class SearchListingAtIndexProvider
    extends AutoDisposeProvider<AsyncValue<Listing>> {
  /// See also [searchListingAtIndex].
  SearchListingAtIndexProvider(
    AllListingsOffset query,
  ) : this._internal(
          (ref) => searchListingAtIndex(
            ref as SearchListingAtIndexRef,
            query,
          ),
          from: searchListingAtIndexProvider,
          name: r'searchListingAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$searchListingAtIndexHash,
          dependencies: SearchListingAtIndexFamily._dependencies,
          allTransitiveDependencies:
              SearchListingAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  SearchListingAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final AllListingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<Listing> Function(SearchListingAtIndexRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SearchListingAtIndexProvider._internal(
        (ref) => create(ref as SearchListingAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<Listing>> createElement() {
    return _SearchListingAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchListingAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SearchListingAtIndexRef on AutoDisposeProviderRef<AsyncValue<Listing>> {
  /// The parameter `query` of this provider.
  AllListingsOffset get query;
}

class _SearchListingAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<Listing>>
    with SearchListingAtIndexRef {
  _SearchListingAtIndexProviderElement(super.provider);

  @override
  AllListingsOffset get query => (origin as SearchListingAtIndexProvider).query;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
