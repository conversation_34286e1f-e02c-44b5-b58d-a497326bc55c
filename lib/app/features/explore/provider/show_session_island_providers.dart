import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';

final showSessionIslandProvider =
    StateNotifierProvider<ShowSessionIslandNotifier, bool>((ref) {
  return ShowSessionIslandNotifier(ref);
});

class ShowSessionIslandNotifier extends StateNotifier<bool> {
  ShowSessionIslandNotifier(this.ref) : super(true) {
    final sessionEvent = ref.watch(activeSessionProvider);

    if (sessionEvent.valueOrNull?.type == 'no_active_session') {
      state = false; // hide session island because no active session
    }
  }
  final Ref ref;

  void toggle() {
    state = !state;
  }

  void setVisibility(bool isVisible) {
    state = isVisible;
  }

  void hide() {
    state = false;
  }

  void show() {
    state = true;
  }

  bool getVisibility() {
    return state;
  }
}
