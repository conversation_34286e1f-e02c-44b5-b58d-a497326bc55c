// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'explore_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$nearbyListingsPagesHash() =>
    r'0071093cf47c5ef8d025293a654b461d7674f40a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [nearbyListingsPages].
@ProviderFor(nearbyListingsPages)
const nearbyListingsPagesProvider = NearbyListingsPagesFamily();

/// See also [nearbyListingsPages].
class NearbyListingsPagesFamily extends Family<AsyncValue<ListingsResponse>> {
  /// See also [nearbyListingsPages].
  const NearbyListingsPagesFamily();

  /// See also [nearbyListingsPages].
  NearbyListingsPagesProvider call(
    NearbyListingsPagination meta,
  ) {
    return NearbyListingsPagesProvider(
      meta,
    );
  }

  @override
  NearbyListingsPagesProvider getProviderOverride(
    covariant NearbyListingsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'nearbyListingsPagesProvider';
}

/// See also [nearbyListingsPages].
class NearbyListingsPagesProvider
    extends AutoDisposeFutureProvider<ListingsResponse> {
  /// See also [nearbyListingsPages].
  NearbyListingsPagesProvider(
    NearbyListingsPagination meta,
  ) : this._internal(
          (ref) => nearbyListingsPages(
            ref as NearbyListingsPagesRef,
            meta,
          ),
          from: nearbyListingsPagesProvider,
          name: r'nearbyListingsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$nearbyListingsPagesHash,
          dependencies: NearbyListingsPagesFamily._dependencies,
          allTransitiveDependencies:
              NearbyListingsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  NearbyListingsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final NearbyListingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingsResponse> Function(NearbyListingsPagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: NearbyListingsPagesProvider._internal(
        (ref) => create(ref as NearbyListingsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingsResponse> createElement() {
    return _NearbyListingsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is NearbyListingsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin NearbyListingsPagesRef on AutoDisposeFutureProviderRef<ListingsResponse> {
  /// The parameter `meta` of this provider.
  NearbyListingsPagination get meta;
}

class _NearbyListingsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingsResponse>
    with NearbyListingsPagesRef {
  _NearbyListingsPagesProviderElement(super.provider);

  @override
  NearbyListingsPagination get meta =>
      (origin as NearbyListingsPagesProvider).meta;
}

String _$nearbyListingsCountHash() =>
    r'd3c5d049d213bdbedf5f01263257b3a160477b69';

/// See also [nearbyListingsCount].
@ProviderFor(nearbyListingsCount)
const nearbyListingsCountProvider = NearbyListingsCountFamily();

/// See also [nearbyListingsCount].
class NearbyListingsCountFamily extends Family<AsyncValue<int>> {
  /// See also [nearbyListingsCount].
  const NearbyListingsCountFamily();

  /// See also [nearbyListingsCount].
  NearbyListingsCountProvider call(
    NearbyListingsInput? input,
  ) {
    return NearbyListingsCountProvider(
      input,
    );
  }

  @override
  NearbyListingsCountProvider getProviderOverride(
    covariant NearbyListingsCountProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'nearbyListingsCountProvider';
}

/// See also [nearbyListingsCount].
class NearbyListingsCountProvider extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [nearbyListingsCount].
  NearbyListingsCountProvider(
    NearbyListingsInput? input,
  ) : this._internal(
          (ref) => nearbyListingsCount(
            ref as NearbyListingsCountRef,
            input,
          ),
          from: nearbyListingsCountProvider,
          name: r'nearbyListingsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$nearbyListingsCountHash,
          dependencies: NearbyListingsCountFamily._dependencies,
          allTransitiveDependencies:
              NearbyListingsCountFamily._allTransitiveDependencies,
          input: input,
        );

  NearbyListingsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final NearbyListingsInput? input;

  @override
  Override overrideWith(
    AsyncValue<int> Function(NearbyListingsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: NearbyListingsCountProvider._internal(
        (ref) => create(ref as NearbyListingsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _NearbyListingsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is NearbyListingsCountProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin NearbyListingsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `input` of this provider.
  NearbyListingsInput? get input;
}

class _NearbyListingsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with NearbyListingsCountRef {
  _NearbyListingsCountProviderElement(super.provider);

  @override
  NearbyListingsInput? get input =>
      (origin as NearbyListingsCountProvider).input;
}

String _$listingAtIndexHash() => r'ca3876c01f48c79f4f3a4531a81ff5661cddfe12';

/// See also [listingAtIndex].
@ProviderFor(listingAtIndex)
const listingAtIndexProvider = ListingAtIndexFamily();

/// See also [listingAtIndex].
class ListingAtIndexFamily extends Family<AsyncValue<Listing>> {
  /// See also [listingAtIndex].
  const ListingAtIndexFamily();

  /// See also [listingAtIndex].
  ListingAtIndexProvider call(
    NearbyListingsOffset query,
  ) {
    return ListingAtIndexProvider(
      query,
    );
  }

  @override
  ListingAtIndexProvider getProviderOverride(
    covariant ListingAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingAtIndexProvider';
}

/// See also [listingAtIndex].
class ListingAtIndexProvider extends AutoDisposeProvider<AsyncValue<Listing>> {
  /// See also [listingAtIndex].
  ListingAtIndexProvider(
    NearbyListingsOffset query,
  ) : this._internal(
          (ref) => listingAtIndex(
            ref as ListingAtIndexRef,
            query,
          ),
          from: listingAtIndexProvider,
          name: r'listingAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingAtIndexHash,
          dependencies: ListingAtIndexFamily._dependencies,
          allTransitiveDependencies:
              ListingAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  ListingAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final NearbyListingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<Listing> Function(ListingAtIndexRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingAtIndexProvider._internal(
        (ref) => create(ref as ListingAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<Listing>> createElement() {
    return _ListingAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingAtIndexRef on AutoDisposeProviderRef<AsyncValue<Listing>> {
  /// The parameter `query` of this provider.
  NearbyListingsOffset get query;
}

class _ListingAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<Listing>>
    with ListingAtIndexRef {
  _ListingAtIndexProviderElement(super.provider);

  @override
  NearbyListingsOffset get query => (origin as ListingAtIndexProvider).query;
}

String _$nearbyListingMarkersHash() =>
    r'75dc9b565d2f75c03aa3016c61f27fb73a377c51';

/// for map markers, without pagination
///
/// Copied from [nearbyListingMarkers].
@ProviderFor(nearbyListingMarkers)
final nearbyListingMarkersProvider =
    AutoDisposeFutureProvider<ListingsResponse>.internal(
  nearbyListingMarkers,
  name: r'nearbyListingMarkersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$nearbyListingMarkersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef NearbyListingMarkersRef
    = AutoDisposeFutureProviderRef<ListingsResponse>;
String _$listingMarkersHash() => r'425652e788db76f0873dd8f2356029c5edabb1d6';

/// for map markers, without pagination
///
/// Copied from [listingMarkers].
@ProviderFor(listingMarkers)
final listingMarkersProvider =
    AutoDisposeFutureProvider<ListingsResponse>.internal(
  listingMarkers,
  name: r'listingMarkersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingMarkersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListingMarkersRef = AutoDisposeFutureProviderRef<ListingsResponse>;
String _$listingTypeFiltersHash() =>
    r'5eaed58d63fd7a8fba63ded47f043d5180c52208';

/// See also [ListingTypeFilters].
@ProviderFor(ListingTypeFilters)
final listingTypeFiltersProvider =
    AutoDisposeNotifierProvider<ListingTypeFilters, List<String>>.internal(
  ListingTypeFilters.new,
  name: r'listingTypeFiltersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingTypeFiltersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListingTypeFilters = AutoDisposeNotifier<List<String>>;
String _$exploreMapPositionHash() =>
    r'44fc3e31e9f468362f0abe48627d3fb07c00694f';

/// See also [ExploreMapPosition].
@ProviderFor(ExploreMapPosition)
final exploreMapPositionProvider =
    AutoDisposeNotifierProvider<ExploreMapPosition, MapInput>.internal(
  ExploreMapPosition.new,
  name: r'exploreMapPositionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$exploreMapPositionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ExploreMapPosition = AutoDisposeNotifier<MapInput>;
String _$markerSelectedHash() => r'351543f29c55e1ad06aeefa629115e25035c7474';

/// See also [MarkerSelected].
@ProviderFor(MarkerSelected)
final markerSelectedProvider =
    AutoDisposeNotifierProvider<MarkerSelected, Listing?>.internal(
  MarkerSelected.new,
  name: r'markerSelectedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$markerSelectedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MarkerSelected = AutoDisposeNotifier<Listing?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
