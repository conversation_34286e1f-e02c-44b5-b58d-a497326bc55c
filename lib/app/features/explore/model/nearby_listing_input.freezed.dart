// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'nearby_listing_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NearbyListingsInput _$NearbyListingsInputFromJson(Map<String, dynamic> json) {
  return _NearbyListingsInput.fromJson(json);
}

/// @nodoc
mixin _$NearbyListingsInput {
  double get lat => throw _privateConstructorUsedError;
  double get lon => throw _privateConstructorUsedError;
  double get r => throw _privateConstructorUsedError;
  double? get deviceLat => throw _privateConstructorUsedError;
  double? get deviceLon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NearbyListingsInputCopyWith<NearbyListingsInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NearbyListingsInputCopyWith<$Res> {
  factory $NearbyListingsInputCopyWith(
          NearbyListingsInput value, $Res Function(NearbyListingsInput) then) =
      _$NearbyListingsInputCopyWithImpl<$Res, NearbyListingsInput>;
  @useResult
  $Res call(
      {double lat, double lon, double r, double? deviceLat, double? deviceLon});
}

/// @nodoc
class _$NearbyListingsInputCopyWithImpl<$Res, $Val extends NearbyListingsInput>
    implements $NearbyListingsInputCopyWith<$Res> {
  _$NearbyListingsInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = null,
    Object? lon = null,
    Object? r = null,
    Object? deviceLat = freezed,
    Object? deviceLon = freezed,
  }) {
    return _then(_value.copyWith(
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
      r: null == r
          ? _value.r
          : r // ignore: cast_nullable_to_non_nullable
              as double,
      deviceLat: freezed == deviceLat
          ? _value.deviceLat
          : deviceLat // ignore: cast_nullable_to_non_nullable
              as double?,
      deviceLon: freezed == deviceLon
          ? _value.deviceLon
          : deviceLon // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NearbyListingsInputImplCopyWith<$Res>
    implements $NearbyListingsInputCopyWith<$Res> {
  factory _$$NearbyListingsInputImplCopyWith(_$NearbyListingsInputImpl value,
          $Res Function(_$NearbyListingsInputImpl) then) =
      __$$NearbyListingsInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double lat, double lon, double r, double? deviceLat, double? deviceLon});
}

/// @nodoc
class __$$NearbyListingsInputImplCopyWithImpl<$Res>
    extends _$NearbyListingsInputCopyWithImpl<$Res, _$NearbyListingsInputImpl>
    implements _$$NearbyListingsInputImplCopyWith<$Res> {
  __$$NearbyListingsInputImplCopyWithImpl(_$NearbyListingsInputImpl _value,
      $Res Function(_$NearbyListingsInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = null,
    Object? lon = null,
    Object? r = null,
    Object? deviceLat = freezed,
    Object? deviceLon = freezed,
  }) {
    return _then(_$NearbyListingsInputImpl(
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
      r: null == r
          ? _value.r
          : r // ignore: cast_nullable_to_non_nullable
              as double,
      deviceLat: freezed == deviceLat
          ? _value.deviceLat
          : deviceLat // ignore: cast_nullable_to_non_nullable
              as double?,
      deviceLon: freezed == deviceLon
          ? _value.deviceLon
          : deviceLon // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NearbyListingsInputImpl implements _NearbyListingsInput {
  const _$NearbyListingsInputImpl(
      {required this.lat,
      required this.lon,
      required this.r,
      this.deviceLat,
      this.deviceLon});

  factory _$NearbyListingsInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$NearbyListingsInputImplFromJson(json);

  @override
  final double lat;
  @override
  final double lon;
  @override
  final double r;
  @override
  final double? deviceLat;
  @override
  final double? deviceLon;

  @override
  String toString() {
    return 'NearbyListingsInput(lat: $lat, lon: $lon, r: $r, deviceLat: $deviceLat, deviceLon: $deviceLon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NearbyListingsInputImpl &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.r, r) || other.r == r) &&
            (identical(other.deviceLat, deviceLat) ||
                other.deviceLat == deviceLat) &&
            (identical(other.deviceLon, deviceLon) ||
                other.deviceLon == deviceLon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lat, lon, r, deviceLat, deviceLon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NearbyListingsInputImplCopyWith<_$NearbyListingsInputImpl> get copyWith =>
      __$$NearbyListingsInputImplCopyWithImpl<_$NearbyListingsInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NearbyListingsInputImplToJson(
      this,
    );
  }
}

abstract class _NearbyListingsInput implements NearbyListingsInput {
  const factory _NearbyListingsInput(
      {required final double lat,
      required final double lon,
      required final double r,
      final double? deviceLat,
      final double? deviceLon}) = _$NearbyListingsInputImpl;

  factory _NearbyListingsInput.fromJson(Map<String, dynamic> json) =
      _$NearbyListingsInputImpl.fromJson;

  @override
  double get lat;
  @override
  double get lon;
  @override
  double get r;
  @override
  double? get deviceLat;
  @override
  double? get deviceLon;
  @override
  @JsonKey(ignore: true)
  _$$NearbyListingsInputImplCopyWith<_$NearbyListingsInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
