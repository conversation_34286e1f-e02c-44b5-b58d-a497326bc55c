import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'nearby_listing_input.freezed.dart';
part 'nearby_listing_input.g.dart';

@freezed
class NearbyListingsInput with _$NearbyListingsInput {
  const factory NearbyListingsInput({
    required double lat,
    required double lon,
    required double r,
    double? deviceLat,
    double? deviceLon,
  }) = _NearbyListingsInput;

  factory NearbyListingsInput.fromJson(Json json) =>
      _$NearbyListingsInputFromJson(json);
}
