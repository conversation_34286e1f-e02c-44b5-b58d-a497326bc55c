import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/provider/favourite_product_providers.dart';
import 'package:gomama/app/features/explore/provider/explore_providers.dart';
import 'package:gomama/app/features/explore/widget/nearest_listing.dart';
import 'package:gomama/app/features/explore/widget/session_island.dart';
import 'package:gomama/app/features/favourites/view/favourite_view.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/listing/widget/listing_card.dart';
import 'package:gomama/app/features/maps/provider/mapbox_map_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/maps/widget/explore_map.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ExploreView extends HookConsumerWidget {
  const ExploreView({super.key});

  static const routeName = 'explore';
  static const routePath = '/explore';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _scaffoldKey = useState(GlobalKey<ScaffoldState>());
    final position = ref.watch(currentPositionProvider);
    final geoJson = ref.watch(listingsGeojsonProvider);
    final initialSessionId = ref.watch(initialSessionIdProvider);

    // initialize favourite product
    ref.watch(favouriteProductControllerProvider);

    // final messages = ref.watch(
    //   sessionsNotifierProvider(
    //     topics: ['active-sessions:e6dfe287-9098-49bf-bbba-7f15979e0df6'],
    //   ),
    // );
    // final hub = ref.watch(hubProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (initialSessionId != null && initialSessionId.isNotEmpty == true) {
            showCupertinoModalPopup(
              barrierDismissible: false,
              context: context,
              builder: (context) => ReviewSessionView(initialSessionId),
            );
          }
        });
        return null;
      },
      [],
    );

    return Scaffold(
      backgroundColor: Colors.transparent,
      key: _scaffoldKey.value,
      // appBar: const PreferredSize(
      //   preferredSize: Size.fromHeight(120),
      //   child: Padding(
      //     padding: EdgeInsets.fromLTRB(31, kTextTabBarHeight, 31, 15),
      //     child: Column(
      //       mainAxisAlignment: MainAxisAlignment.center,
      //       children: [
      //         _Searchbar(),
      //         SizedBox(height: 8),
      //         _Filterbar(),
      //       ],
      //     ),
      //   ),
      // ),
      body: Stack(
        children: [
          geoJson.when(
            loading: () {
              return const Center(
                child: CircularProgressIndicator(),
              );
            },
            error: (error, stackTrace) {
              Groveman.error(
                'geojson',
                error: error,
                stackTrace: stackTrace,
              );

              return const Center(child: Text('Something went wrong'));
            },
            data: (geojson) => position.when(
              loading: () {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
              error: (error, stackTrace) {
                return ExploreMap(
                  1.3521,
                  103.8198,
                  geojson: geojson,
                );
              },
              data: (data) {
                return ExploreMap(
                  data!.latitude,
                  data.longitude,
                  geojson: geojson,
                );
              },
            ),
          ),
          Positioned(
            top: max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
            left: 0,
            right: 0,
            child: Column(
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: _Searchbar(),
                ),
                const SessionIsland(),
                const SizedBox(height: 4),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder: (
                    Widget child,
                    Animation<double> animation,
                  ) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  child: ref.watch(markerSelectedProvider) == null
                      ? const _Filterbar()
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          const _BottomSheet(),
        ],
      ),
    );
  }
}

class _Searchbar extends ConsumerWidget {
  const _Searchbar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Hero(
      tag: 'searchbar',
      child: Material(
        elevation: 4,
        borderRadius: const BorderRadius.all(Radius.circular(12)),
        child: InkWell(
          onTap: () {
            const SearchRoute().push(context);
          },
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          child: Row(
            children: [
              const SizedBox(width: 12),
              const Icon(CustomIcon.location, color: CustomColors.primary),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  ref.watch(markerSelectedProvider)?.name ?? 'Search listings',
                  style: textTheme(context).bodyMedium!.copyWith(
                        color: ref.watch(markerSelectedProvider) != null
                            ? CustomColors.text
                            : CustomColors.placeholder,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                visualDensity: VisualDensity.compact,
                icon: const Icon(CustomIcon.heart),
                onPressed: () {
                  showCupertinoModalPopup(
                    context: context,
                    builder: (context) => const FavouritesView(),
                  );
                },
              ),
              // IconButton(
              //   visualDensity: VisualDensity.compact,
              //   icon: const Icon(Icons.person),
              //   onPressed: () {
              //     showCupertinoModalPopup(
              //       context: context,
              //       builder: (context) => const ProfileView(),
              //     );
              //   },
              // ),
              const SizedBox(width: 4),
            ],
          ),
        ),
      ),
    );
  }
}

enum ListingFilter { all, gomama, care }

const filterIcons = [
  CustomIcon.map,
  CustomIcon.gomamaLocationPin,
  CustomIcon.motherWithBaby,
];

class _Filterbar extends HookConsumerWidget {
  const _Filterbar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(listingTypeFiltersProvider);
    final filtersController = ref.watch(listingTypeFiltersProvider.notifier);

    return Wrap(
      spacing: 5,
      children: ListingFilter.values.map((filter) {
        return FilterChip(
          backgroundColor: CustomColors.secondaryLight,
          selectedColor: CustomColors.primaryLight,
          shape: const StadiumBorder(),
          elevation: 1,
          shadowColor: Colors.black38,
          visualDensity: VisualDensity.compact,
          side: BorderSide.none,
          avatar: Icon(filterIcons[filter.index]),
          iconTheme: filters.contains(filter.name)
              ? const IconThemeData(color: Colors.white)
              : const IconThemeData(color: CustomColors.primary),
          label: Text(filter.name.toUpperCase()),
          labelStyle: filters.contains(filter.name)
              ? const TextStyle(color: Colors.white)
              : const TextStyle(color: CustomColors.primary),
          showCheckmark: false,
          selected: filters.contains(filter.name),
          onSelected: (bool selected) {
            /// NOTE: only single toggle
            filtersController.setList([filter.name]);

            /// NOTE: multiple toggle
            // if (selected) {
            //   if (filter.name != 'all') {
            //     // remove 'all'
            //     filtersController.setList([
            //       ...filters.where((element) => element != 'all'),
            //       filter.name,
            //     ]);
            //   } else {
            //     filtersController.setList(['all']);
            //   }
            // } else {
            //   if (filter.name != 'all') {
            //     var newFilters =
            //         filters.where((element) => element != filter.name);

            //     if (newFilters.isEmpty) {
            //       newFilters = ['all'];
            //     }

            //     filtersController.setList(newFilters.toList());
            //   }
            // }
          },
        );
      }).toList(),
    );
  }
}

class _BottomSheet extends HookConsumerWidget {
  const _BottomSheet();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(listingTypeFiltersProvider);
    final activeSession = ref.watch(activeSessionProvider);
    final bottomSheetKey = useState(GlobalKey<BrandBottomSheetState>());

    ref.listen(markerSelectedProvider, (_, next) {
      final bottomSheetState = bottomSheetKey.value.currentState;

      if (next != null) {
        if (bottomSheetState != null) {
          // Snap to the first snap size above the minimum (index 1)
          bottomSheetState.snapToExtent(bottomSheetState.snapSizes[1]);
        }
      } else {
        if (bottomSheetState != null) {
          // Snap to the first snap size above the minimum (index 1)
          bottomSheetState.snapToExtent(bottomSheetState.snapSizes[0]);
        }
      }
    });

    return BrandBottomSheet(
      key: bottomSheetKey.value,
      minHeight: kBottomNavigationBarHeight + 20,
      maxHeight: max(
            mediaQuery(context).viewPadding.top,
            kToolbarHeight / 2,
          ) +
          128 +
          (activeSession.whenOrNull(
                data: (data) => data != null ? dynamicIslandShrinkedHeight : 0,
              ) ??
              0),
      snapSizes: const [0.5],
      slivers: [
        const SliverPadding(padding: EdgeInsets.only(top: 12)),
        // bottom sheet drag indicator
        SliverToBoxAdapter(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Transform(
                transform: Matrix4.diagonal3Values(12, 4, 1),
                alignment: Alignment.center,
                child: const Icon(
                  Icons.expand_less_rounded,
                  color: Color(0xff975DA0),
                  size: 12,
                ),
              ),
            ],
          ),
        ),
        if (ref.watch(markerSelectedProvider) != null) ...[
          const SliverPadding(padding: EdgeInsets.only(top: 16)),
          SliverToBoxAdapter(
            child: ListingCard(ref.watch(markerSelectedProvider)!),
          ),
        ] else ...[
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(20, 8, 20, 0),
            sliver: SliverToBoxAdapter(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    filters.contains('all')
                        ? 'All Listings'
                        : filters.contains('gomama')
                            ? 'Go!Mama Access'
                            : 'Nursing Rooms (Care)',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  PhysicalModel(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                    elevation: 4,
                    // clipBehavior: Clip.antiAlias,
                    shadowColor: const Color(0x11E1C08F),
                    child: IconButtonTheme(
                      data: const IconButtonThemeData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            Colors.white,
                          ),
                          iconColor: WidgetStatePropertyAll(
                            CustomColors.primary,
                          ),
                          elevation: WidgetStatePropertyAll(2),
                        ),
                      ),
                      child: IconButton.filled(
                        onPressed: () {
                          // showModalBottomSheet(
                          //   context: context,
                          //   isScrollControlled: true,
                          //   backgroundColor: Colors.white,
                          //   builder: (context) {
                          //     // TODO(kkcy): pass in amenities selected to preload
                          //     return const FilterSheet();
                          //   },
                          // );

                          showCupertinoModalPopup(
                            context: context,
                            builder: (context) {
                              return const FilterSheet();
                            },
                          );
                        },
                        icon: const Icon(CustomIcon.filter),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SliverPadding(padding: EdgeInsets.only(top: 16)),
          const NearestListing(),
        ],
        const SliverPadding(padding: EdgeInsets.only(top: 8)),
      ],
    );
  }
}
