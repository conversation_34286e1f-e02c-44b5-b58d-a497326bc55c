// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nearest_listing.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$nearestListingIndexHash() =>
    r'fc811f513697a4a160021bb42e58fa8fe022bc93';

/// See also [_nearestListingIndex].
@ProviderFor(_nearestListingIndex)
final _nearestListingIndexProvider = AutoDisposeProvider<int>.internal(
  _nearestListingIndex,
  name: r'_nearestListingIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$nearestListingIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _NearestListingIndexRef = AutoDisposeProviderRef<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
