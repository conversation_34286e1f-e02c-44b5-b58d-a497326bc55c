import 'dart:math' as math;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/core/utils/use_interval.dart';
import 'package:gomama/app/features/explore/provider/show_session_island_providers.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/current_session_view.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

const animationDuration = Duration(milliseconds: 300);
const dynamicIslandShrinkedWidth = 100;
const dynamicIslandShrinkedHeight = 38.0;
const dynamicIslandShrinkedBorderRadius = 44.0;
const dynamicIslandExpandedHeight = 90.0;
const dynamicIslandExpandedBorderRadius = 12.0;

class SessionIsland extends HookConsumerWidget {
  const SessionIsland({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Keep track of the last valid session
    final lastValidSessionEvent = useState<SessionEvent?>(null);
    final sessionEventAsync = ref.watch(activeSessionProvider);
    final shrinked = useState(true);
    final showIsland = useState(true);
    final endingSessionError = useState<String?>(null);

    // Update lastValidSessionEvent when we get new data
    useEffect(
      () {
        if (sessionEventAsync.value != null &&
            sessionEventAsync.value?.session != null) {
          lastValidSessionEvent.value = sessionEventAsync.value;
        }
        return null;
      },
      [sessionEventAsync.value],
    );

    void toggleIsland() {
      showIsland.value = false;
      shrinked.value = !shrinked.value;
    }

    // Calculate if we should show the island based on current or last valid session and showSessionIslandProvider value
    final shouldShowIsland = (sessionEventAsync.valueOrNull?.session != null ||
            lastValidSessionEvent.value?.session != null) &&
        ref.watch(showSessionIslandProvider);

    if (!shouldShowIsland) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: toggleIsland,
      child: AnimatedContainer(
        onEnd: () => showIsland.value = true,
        margin: const EdgeInsets.only(top: 8),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(
            shrinked.value
                ? dynamicIslandShrinkedBorderRadius
                : dynamicIslandExpandedBorderRadius,
          ),
        ),
        duration: animationDuration,
        curve: Curves.easeInOut,
        width: MediaQuery.of(context).size.width -
            (shrinked.value ? MediaQuery.of(context).size.width * 0.5 : 64),
        height: shouldShowIsland
            ? (shrinked.value
                ? dynamicIslandShrinkedHeight
                : dynamicIslandExpandedHeight)
            : 0,
        child: sessionEventAsync.when(
          error: (error, stackTrace) {
            Groveman.error(
              'sessionEventAsync',
              error: error,
              stackTrace: stackTrace,
            );

            // Show last valid session if available during error
            if (lastValidSessionEvent.value?.session != null) {
              return _buildSessionContent(
                context,
                lastValidSessionEvent.value!.session!,
                lastValidSessionEvent.value?.type ?? '',
                shrinked.value,
                showIsland.value,
                (String message) {
                  endingSessionError.value = message;
                },
                () {
                  endingSessionError.value = null;
                  if (endingSessionError.value == 'No active session found') {
                    ref.read(showSessionIslandProvider.notifier).hide();
                  }
                },
                endingSessionError.value,
              );
            }

            return const SizedBox.shrink();
          },
          loading: () {
            // Show last valid session during loading
            if (lastValidSessionEvent.value?.session != null) {
              return _buildSessionContent(
                context,
                lastValidSessionEvent.value!.session!,
                lastValidSessionEvent.value?.type ?? '',
                shrinked.value,
                showIsland.value,
                (String message) {
                  endingSessionError.value = message;
                },
                () {
                  endingSessionError.value = null;
                  if (endingSessionError.value == 'No active session found') {
                    ref.read(showSessionIslandProvider.notifier).hide();
                  }
                },
                endingSessionError.value,
              );
            }

            return const SizedBox.shrink();
          },
          data: (sessionEvent) {
            final currentSession =
                sessionEvent?.session ?? lastValidSessionEvent.value?.session;
            final currentSessionType = sessionEvent?.type ?? '';

            if (currentSession == null) {
              return const SizedBox.shrink();
            }

            return _buildSessionContent(
              context,
              currentSession,
              currentSessionType,
              shrinked.value,
              showIsland.value,
              (String message) {
                endingSessionError.value = message;
              },
              () {
                endingSessionError.value = null;
                if (endingSessionError.value == 'No active session found') {
                  ref.read(showSessionIslandProvider.notifier).hide();
                }
              },
              endingSessionError.value,
            );
          },
        ),
      ),
    );
  }

  Widget _buildSessionContent(
    BuildContext context,
    Session session,
    String sessionType,
    bool isShrinked,
    bool showIsland,
    Function onError,
    VoidCallback onOk,
    String? endingSessionError,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: (isShrinked ? 10.0 : 15.0),
        vertical: 5,
      ),
      child: AnimatedSwitcher(
        duration: animationDuration,
        child: showIsland
            ? _SessionTimer(
                session,
                sessionType: sessionType,
                isShrinked: isShrinked,
                onError: onError,
                onOk: onOk,
                endingSessionError: endingSessionError,
              )
            : const SizedBox(),
      ),
    );
  }
}

class _SessionTimer extends HookConsumerWidget {
  const _SessionTimer(
    this.session, {
    required this.sessionType,
    required this.isShrinked,
    required this.onError,
    required this.onOk,
    this.endingSessionError,
  });
  final Session session;
  final String sessionType;
  final bool isShrinked;
  final Function onError;
  final VoidCallback onOk;
  final String? endingSessionError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final remainingTime = useState(session.remainingTime);
    final isRunning = useRef(true);
    final lastUpdateTime = useRef(DateTime.now());

    // Compensate for time passed during background
    useEffect(
      () {
        final now = DateTime.now();
        final timeDiff = now.difference(lastUpdateTime.value).inSeconds;
        if (timeDiff > 1) {
          remainingTime.value = math.max(0, remainingTime.value - timeDiff);
        }
        lastUpdateTime.value = now;
        return null;
      },
      [session],
    );

    useInterval(
      () {
        if (remainingTime.value > 0) {
          remainingTime.value--;
          lastUpdateTime.value = DateTime.now();
        } else {
          remainingTime.value = 0;
          isRunning.value = false;
        }
      },
      const Duration(seconds: 1),
    );

    // Update remaining time when session changes
    useEffect(
      () {
        remainingTime.value = session.remainingTime;
        lastUpdateTime.value = DateTime.now();
        return null;
      },
      [session],
    );

    if (isShrinked) {
      return _SessionShrinked(
        time: remainingTime.value,
        sessionType: sessionType,
      );
    }

    return _SessionExpanded(
      session,
      sessionType: sessionType,
      time: remainingTime.value,
      onError: onError,
      onOk: onOk,
      endingSessionError: endingSessionError,
    );
  }
}

class _SessionShrinked extends HookConsumerWidget {
  const _SessionShrinked({
    required this.time,
    required this.sessionType,
  });
  final int time;
  final String sessionType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeRemaining = secondsToMinutesAndSeconds(time);

    return Row(
      children: [
        const Icon(
          CupertinoIcons.timer_fill,
          size: 20,
          color: CustomColors.primaryExtraLight,
        ),
        const Spacer(),
        if (timeRemaining.startsWith('-') ||
            time <= 0 ||
            sessionType == 'session_cleanup' ||
            sessionType == 'entry_expired')
          Text(
            sessionType == 'entry_expired' ? 'Entry Expired' : 'Session Ended',
            // There is a chance user will still stay in pod when session duration already ended, but still ui should show session ended.
            // While Session event type is still active_session instead of session_cleanup.
            style: textTheme(context).labelMedium!.copyWith(
                  color: Colors.white,
                ),
          )
        else
          Row(
            children: [
              Text(
                secondsToMinutesAndSeconds(time),
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 1),
              Transform.translate(
                offset: const Offset(0, -3),
                child: const Text(
                  'MIN',
                  style: TextStyle(
                    color: CustomColors.primaryExtraLight,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }
}

class _SessionExpanded extends HookConsumerWidget {
  const _SessionExpanded(
    this.session, {
    required this.sessionType,
    required this.time,
    required this.onError,
    required this.onOk,
    this.endingSessionError,
  });
  final Session session;
  final String sessionType;
  final int time;
  final Function onError;
  final VoidCallback onOk;
  final String? endingSessionError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeRemaining = secondsToMinutesAndSeconds(time);
    final _isEndingSession = useState(false);

    return Column(
      children: [
        const SizedBox(height: 4),
        Row(
          children: [
            TweenAnimationBuilder<Color?>(
              duration: const Duration(milliseconds: 300),
              tween: ColorTween(
                begin: endingSessionError != null
                    ? CustomColors.red
                    : CustomColors.primaryExtraLight,
                end: endingSessionError != null
                    ? CustomColors.red
                    : CustomColors.primaryExtraLight,
              ),
              builder: (context, color, _) => Icon(
                CupertinoIcons.timer_fill,
                size: 32,
                color: color,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: AnimatedSwitcher(
                duration: animationDuration,
                child: endingSessionError != null
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Expanded(
                            child: Text(
                              endingSessionError!,
                              style: textTheme(context).labelMedium!.copyWith(
                                    color: Colors.white,
                                  ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      )
                    : timeRemaining.startsWith('-') ||
                            time <= 0 ||
                            sessionType == 'session_cleanup' ||
                            sessionType == 'entry_expired'
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                sessionType == 'entry_expired'
                                    ? 'Entry Expired'
                                    : 'Session Ended',
                                style: textTheme(context).labelMedium!.copyWith(
                                      color: Colors.white,
                                    ),
                                textAlign: TextAlign.end,
                              ),
                            ],
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                'Session ends at ${DateFormat('hh:mm aa').format(session.expectedEndedAt!)}',
                                style: textTheme(context).labelMedium!.copyWith(
                                      color: Colors.white,
                                    ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text(
                                    'Time remaining ',
                                    style:
                                        textTheme(context).labelSmall!.copyWith(
                                              color: Colors.white,
                                            ),
                                  ),
                                  Text(
                                    '${secondsToMinutesAndSeconds(time)} minutes',
                                    style: textTheme(context)
                                        .labelSmall!
                                        .copyWith(
                                          color: CustomColors.primaryExtraLight,
                                        ),
                                  ),
                                ],
                              ),
                            ],
                          ),
              ),
            ),
          ],
        ),
        const Spacer(),
        SizedBox(
          height: 30,
          child: AnimatedSwitcher(
            duration: animationDuration,
            child: endingSessionError != null
                ? Row(
                    key: const ValueKey('error_actions'),
                    children: [
                      Expanded(
                        child: BrandButton.singpass(
                          padding: EdgeInsets.zero,
                          onPressed: onOk,
                          child: const Text('OK'),
                        ),
                      ),
                    ],
                  )
                : Row(
                    key: const ValueKey('normal_actions'),
                    children: [
                      if ((timeRemaining.startsWith('-') ||
                              time <= 0 ||
                              sessionType == 'session_cleanup') &&
                          sessionType != 'entry_expired')
                        Expanded(
                          child: _RateButton(
                            session: session,
                            sessionType: sessionType,
                            time: time,
                            isEndingSession: _isEndingSession,
                            onError: onError,
                          ),
                        )
                      else if (sessionType == 'entry_expired')
                        Expanded(
                          child: _CloseButton(
                            session: session,
                            time: time,
                            isEndingSession: _isEndingSession,
                            onError: onError,
                          ),
                        )
                      else ...[
                        // Expanded(
                        //   child: _EndButton(
                        //     session: session,
                        //     isEndingSession: _isEndingSession,
                        //     onError: onError,
                        //   ),
                        // ),
                        // const SizedBox(width: 8),
                        const Expanded(child: SizedBox.shrink()),
                        Expanded(
                          child: BrandButton.filled(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              showCupertinoModalPopup(
                                context: context,
                                builder: (context) {
                                  return const CurrentSessionView();
                                },
                              );
                            },
                            child: const Text('View'),
                          ),
                        ),
                      ],
                    ],
                  ),
          ),
        ),
        const SizedBox(height: 4),
      ],
    );
  }
}

class _CloseButton extends ConsumerWidget {
  const _CloseButton({
    required this.session,
    required this.time,
    required this.isEndingSession,
    required this.onError,
  });
  final Session session;
  final int time;
  final ValueNotifier<bool> isEndingSession;
  final Function onError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BrandButton.filled(
      padding: EdgeInsets.zero,
      onPressed: () {
        // Since it is entry expired, when user click on the "Close" button it should just close the session island view
        ref.read(showSessionIslandProvider.notifier).hide();
      },
      backgroundColor: isEndingSession.value
          ? CustomColors.secondaryLight.withOpacity(0.6)
          : CustomColors.secondaryLight,
      child: const Text('Close'),
    );
  }
}

class _RateButton extends ConsumerWidget {
  const _RateButton({
    required this.session,
    required this.sessionType,
    required this.time,
    required this.isEndingSession,
    required this.onError,
  });
  final Session session;
  final String sessionType;
  final int time;
  final ValueNotifier<bool> isEndingSession;
  final Function onError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeRemaining = secondsToMinutesAndSeconds(time);
    return BrandButton.filled(
      padding: EdgeInsets.zero,
      onPressed: () {
        if (sessionType == 'session_cleanup' ||
            timeRemaining.startsWith('-') ||
            time <= 0) {
          // When time remaining is negative or 00:00, it means duration over but user still in the pod
          // This is to handle the scenario when user might still in pod when the session duration already ended (frontend timer ended but backend actual_ended_at still null because user still in pod)
          // When actual_ended_at is null (still in pod), user should not be able to submit rating
          if (sessionType == 'active_session') {
            onError(
              'Please exit the pod before submit the rating.',
            );
            return;
          }

          if (context.mounted) {
            showCupertinoModalPopup(
              barrierDismissible: false,
              context: context,
              builder: (context) => ReviewSessionView(
                session.id,
                initialSession: session,
              ),
            );
          }
        } else {
          if (isEndingSession.value) {
            return;
          }

          isEndingSession.value = true;

          ref.read(endSessionProvider().future).then(
            (value) {
              if (context.mounted) {
                showCupertinoModalPopup(
                  barrierDismissible: false,
                  context: context,
                  builder: (context) => ReviewSessionView(
                    session.id,
                    initialSession: session,
                  ),
                );
              }
            },
          ).onError((error, stackTrace) {
            isEndingSession.value = false;

            // prompt user
            if (error is AppNetworkResponseException) {
              onError(
                error.message ?? 'Something went wrong',
              );

              return;
            }

            onError('Something went wrong');
          });
        }
      },
      backgroundColor: isEndingSession.value
          ? CustomColors.secondaryLight.withOpacity(0.6)
          : CustomColors.secondaryLight,
      child: const Text('Rate'),
    );
  }
}

// class _EndButton extends ConsumerWidget {
//   const _EndButton({
//     required this.session,
//     required this.isEndingSession,
//     required this.onError,
//   });
//   final Session session;
//   final ValueNotifier<bool> isEndingSession;
//   final Function onError;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return BrandButton.filled(
//       padding: EdgeInsets.zero,
//       onPressed: () {
//         showAdaptiveDialog(
//           context: context,
//           builder: (context) {
//             return AlertDialog.adaptive(
//               title: const Text(
//                 'Are you sure you want\nto end the session?',
//               ),
//               actions: [
//                 AdaptiveTextButton(
//                   onPressed: () {
//                     if (isEndingSession.value) {
//                       return;
//                     }

//                     context.pop();
//                   },
//                   child: const Text('Cancel'),
//                 ),
//                 AdaptiveTextButton(
//                   onPressed: () {
//                     if (isEndingSession.value) {
//                       return;
//                     }

//                     isEndingSession.value = true;

//                     ref.read(endSessionProvider().future).then(
//                       (value) {
//                         if (context.mounted) {
//                           context.pop();

//                           showCupertinoModalPopup(
//                             barrierDismissible: false,
//                             context: context,
//                             builder: (context) => ReviewSessionView(
//                               session.id,
//                               initialSession: session,
//                             ),
//                           );
//                         }
//                       },
//                     ).onError((error, stackTrace) {
//                       isEndingSession.value = false;

//                       // close the confirmation dialog
//                       if (context.mounted) {
//                         context.pop();
//                       }

//                       // prompt user
//                       if (error is AppNetworkResponseException) {
//                         onError(
//                           error.message ?? 'Something went wrong',
//                         );

//                         return;
//                       }

//                       onError('Something went wrong');
//                     });
//                   },
//                   state: AdaptiveTextButtonState.danger,
//                   child: const Text('Yes'),
//                 ),
//               ],
//             );
//           },
//         );

//         // if (_isEndingSession.value) {
//         //   return;
//         // }

//         // _isEndingSession.value = true;

//         // ref.read(endSessionProvider().future).then(
//         //   (value) {
//         //     if (context.mounted) {
//         //       showCupertinoModalPopup(
//         //         barrierDismissible: false,
//         //         context: context,
//         //         builder: (context) => ReviewSessionView(
//         //           session.id,
//         //           initialSession: session,
//         //         ),
//         //       );
//         //     }
//         //   },
//         // ).onError((error, stackTrace) {
//         //   _isEndingSession.value = false;
//         // });
//       },
//       backgroundColor: CustomColors.secondaryLight,
//       child: const Text('End'),
//     );
//   }
// }
