import 'package:flutter/material.dart';
import 'package:gomama/app/core/utils/path_morph.dart';
import 'package:gomama/app/widgets/brand_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AnimatedAuthBackground extends StatefulHookConsumerWidget {
  const AnimatedAuthBackground({super.key});

  @override
  ConsumerState<AnimatedAuthBackground> createState() =>
      _AnimatedAuthBackgroundState();
}

class _AnimatedAuthBackgroundState extends ConsumerState<AnimatedAuthBackground>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late SampledPathData _paths;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _paths = PathMorph.samplePaths(
      homeLayerTwoPathOrigin,
      homeLayerTwoPathDestination,
      precision: 0.001,
    );

    PathMorph.generateAnimations(_controller, _paths, (int i, Offset z) {
      setState(() {
        _paths.shiftedPoints[i] = z;
      });
    });

    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Size(390, 844),
      painter: HomeLayerTwoAnimated(PathMorph.generatePath(_paths)),
    );
  }
}
