import 'package:gomama/app/core/notification/providers/fcm_providers.dart';
import 'package:gomama/app/features/main/model/permission_state.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'permission_providers.g.dart';

@riverpod
Future<PermissionState> permissions(PermissionsRef ref) async {
  // initialize initial session id provider
  ref.watch(initialSessionIdProvider);

  try {
    final locationPermission = await ref.watch(currentPositionProvider.future);
    final notificationPermission =
        await ref.watch(fcmControllerProvider.future);

    return PermissionState(
      hasLocationPermission: locationPermission != null,
      hasNotificationPermission: notificationPermission.hasPermission,
    );
  } catch (e) {
    Groveman.error('permissionsProvider', error: e);
  }

  return const PermissionState();
}
