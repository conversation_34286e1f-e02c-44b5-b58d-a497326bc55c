import 'package:hooks_riverpod/hooks_riverpod.dart';

final initReadyProvider = StateProvider<bool>((ref) => false);
final forceUpdateProvider =
    Provider<bool>((_) => throw UnimplementedError('ForceUpdate'));
final deviceSettingProvider =
    Provider<String>((_) => throw UnimplementedError('DeviceSettings'));

final refreshTokenProvider = StateProvider<bool>((ref) {
  return false;
});

final appProvider = Provider<AppSetting>(
  AppSetting.new,
);

class AppSetting {
  AppSetting(this.ref) {
    _init();
  }

  final ProviderRef<AppSetting> ref;

  Future<void> _init() async {
    Future.delayed(const Duration(seconds: 1), () {
      ref.read(initReadyProvider.notifier).update((state) => true);
    });
  }

  // Future<void> logout() async {
  //   ref.read(userTokenControllerProvider.notifier).logout();
  //   ref.read(userControllerProvider.notifier).logout();
  //   await ref.read(securedAppStorageProvider).clearAllData();
  //   await ref.read(appStorageProvider).clearAllData();
  //   // await ref.read(appStorageProvider).writeValue('onboarding', 'true');
  // }

  // Future<void> refreshToken() async {
  //   try {
  //     final response = await ref.read(authRepositoryProvider).refreshToken();
  //     ref
  //         .read(userTokenControllerProvider.notifier)
  //         .login(response['data']['jwt']['token'] as String);
  //   } catch (e, s) {
  //     Groveman.error('refreshToken', error: e, stackTrace: s);
  //   }
  // }
}
