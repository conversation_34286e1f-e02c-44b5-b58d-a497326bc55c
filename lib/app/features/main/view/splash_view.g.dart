// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'splash_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$splashStateHash() => r'7ce3180f2e9f212612c15ca6d874f85d5cabdae9';

/// See also [SplashState].
@ProviderFor(SplashState)
final splashStateProvider =
    AutoDisposeNotifierProvider<SplashState, bool>.internal(
  SplashState.new,
  name: r'splashStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$splashStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SplashState = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
