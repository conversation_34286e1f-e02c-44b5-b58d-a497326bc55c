import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionView extends HookConsumerWidget {
  const PermissionView({super.key});

  static const routeName = 'permission';
  static const routePath = '/permission';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useOnAppLifecycleStateChange(
      (previous, current) {
        if (current == AppLifecycleState.resumed) {
          // ask for permissions again
          ref.invalidate(currentPositionProvider);
        }
      },
    );

    // Check if notification permission is also needed
    final permissionState = ref.watch(permissionsProvider).valueOrNull;
    final needsNotificationPermission =
        permissionState != null && !permissionState.hasNotificationPermission;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Permission'),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'Please enable location permission to continue using the app',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(height: 8),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'This permission is required for the app to function properly',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () async {
              await openAppSettings();
            },
            child: const Text('Go to Settings'),
          ),
          if (needsNotificationPermission) ...[
            const SizedBox(height: 48),
            const Divider(),
            const SizedBox(height: 16),
            const Text(
              'Enable notification permission to\nreceive important updates',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              onPressed: () async {
                await openAppSettings();
              },
              child: const Text('Enable Notifications'),
            ),
          ],
        ],
      ),
    );
  }
}
