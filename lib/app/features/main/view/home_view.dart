// import 'dart:math' as math;

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/cart/widget/verify_email.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HomeView extends HookConsumerWidget {
  const HomeView({
    required this.navigationShell,
    required this.children,
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('HomeView'));
  // const HomeView(this.child, {super.key});

  static const routeName = 'home';
  static const routePath = '/home';

  /// The navigation shell and container for the branch Navigators.
  final StatefulNavigationShell navigationShell;

  /// The children (branch Navigators) to display in a custom container
  /// ([BranchContainer]).
  final List<Widget> children;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final currentIndex = useState(0);
    final _scaffoldKey = useState(GlobalKey<ScaffoldState>());

    // TODO(kkcy): do we still need to force reset this now that we are using sockets
    // TODO(heng): no need, after commented the life cycle code, the websocket will still send real time value.
    // make sure active session is always alive
    // useOnAppLifecycleStateChange(
    //   (previous, current) {
    //     if (current == AppLifecycleState.resumed) {
    //       /// NOTE: invalidate everytime

    //       // TODO(kkcy): figure out if we can detect
    //       // SSE is disconnected before invalidate
    //       ref.invalidate(activeSessionProvider);
    //     }
    //   },
    // );

    return Scaffold(
      key: _scaffoldKey.value,
      // body: Lottie.asset('assets/water_timer_2.json'),
      // body: Lottie.asset('assets/goma_running_1.json'),
      // body: const RiveAnimation.asset(
      //   'assets/verify.riv',
      //   fit: BoxFit.cover,
      //   alignment: Alignment.bottomCenter,
      // ),
      body: BranchContainer(
        currentIndex: navigationShell.currentIndex,
        children: children,
      ),
      // extendBody: true,
      // floatingActionButton: _ExpandableFab(
      //   children: [
      //     _ActionButton(
      //       onPressed: () => {},
      //       icon: const Icon(Icons.assignment_rounded),
      //       text: Text('Suggest', style: textTheme(context).bodySmall),
      //     ),
      //     _ActionButton(
      //       onPressed: () => {},
      //       icon: const Icon(Icons.shopping_bag),
      //       text: Text('Shop', style: textTheme(context).bodySmall),
      //     ),
      //     _ActionButton(
      //       onPressed: () => {},
      //       icon: const Icon(Icons.public),
      //       text: Text('Discover', style: textTheme(context).bodySmall),
      //     ),
      //   ],
      // ), // FloatingActionButton(
      // floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      // bottomNavigationBar: NavigationBar(
      //   // elevation: 10,
      //   onDestinationSelected: (index) {
      //     _onTap(context, index);
      //   },
      //   backgroundColor: Colors.white,
      //   elevation: 0.1,
      //   shadowColor: Colors.black,
      //   destinations: const [
      //     NavigationDestination(label: 'Explore', icon: Icon(Icons.pin_drop)),
      //     NavigationDestination(label: 'Profile', icon: Icon(Icons.person)),
      //   ],
      // ),
      // bottomNavigationBar: BottomAppBar(
      //   elevation: 0.1,
      //   shadowColor: Colors.black,
      //   color: Colors.white,
      //   shape: const CircularNotchedRectangle(),
      //   notchMargin: 0,
      //   // height: kBottomNavigationBarHeight,
      //   // color: Colors.white,
      //   clipBehavior: Clip.antiAlias,
      //   child: Row(
      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //     children: [
      //       IconButton(
      //         icon: const Icon(Icons.pin_drop),
      //         onPressed: () {
      //           _onTap(context, 0);
      //           // const ExploreRoute().go(context);
      //         },
      //       ),
      //       IconButton(
      //         icon: const Icon(Icons.person),
      //         onPressed: () {
      //           _onTap(context, 1);
      //           // const SuggestRoute().go(context);
      //         },
      //       ),
      //     ],
      //   ),
      // ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: navigationShell.currentIndex,
        type: BottomNavigationBarType.fixed,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        onTap: (index) {
          if (index == 2) {
            final user = ref.read(authControllerProvider).requireValue;
            // check user shopify profile
            if (user.shopifyProfile == null) {
              showEmailVerificationDialog(
                context: context,
                onBack: () {
                  Navigator.of(context).pop();
                },
              );
              return;
            }
          }

          _onTap(context, index);
        },
        elevation: 0.4,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(CustomIcon.location),
            label: 'Explore',
          ),
          BottomNavigationBarItem(
            icon: Icon(CustomIcon.announcement),
            label: 'Suggest',
          ),
          BottomNavigationBarItem(
            icon: Icon(CustomIcon.cart),
            label: 'Shop',
          ),
          BottomNavigationBarItem(
            icon: Icon(CustomIcon.globe),
            label: 'Discover',
          ),
          BottomNavigationBarItem(
            icon: Icon(CustomIcon.profile),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  /// Navigate to the current location of the branch at the provided index when
  /// tapping an item in the BottomNavigationBar.
  void _onTap(BuildContext context, int index) {
    // When navigating to a new branch, it's recommended to use the goBranch
    // method, as doing so makes sure the last navigation state of the
    // Navigator for the branch is restored.
    navigationShell.goBranch(
      index,
      // A common pattern when using bottom navigation bars is to support
      // navigating to the initial location when tapping the item that is
      // already active. This example demonstrates how to support this behavior,
      // using the initialLocation parameter of goBranch.
      initialLocation: index == navigationShell.currentIndex,
    );
  }
}

class BranchContainer extends StatelessWidget {
  const BranchContainer({
    super.key,
    required this.currentIndex,
    required this.children,
  });

  /// The index (in [children]) of the branch Navigator to display.
  final int currentIndex;

  /// The children (branch Navigators) to display in this container.
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: children.mapIndexed(
        (int index, Widget navigator) {
          return Opacity(
            opacity: index == currentIndex ? 1 : 0,
            child: _branchNavigatorWrapper(index, navigator),
          );

          // return AnimatedScale(
          //   scale: index == currentIndex ? 1 : 1.5,
          //   duration: const Duration(milliseconds: 400),
          //   child: AnimatedOpacity(
          //     opacity: index == currentIndex ? 1 : 0,
          //     duration: const Duration(milliseconds: 400),
          //     child: _branchNavigatorWrapper(index, navigator),
          //   ),
          // );
        },
      ).toList(),
    );
  }

  Widget _branchNavigatorWrapper(int index, Widget navigator) => IgnorePointer(
        ignoring: index != currentIndex,
        child: TickerMode(
          enabled: index == currentIndex,
          child: navigator,
        ),
      );
}

// class _ExpandableFab extends HookConsumerWidget {
//   const _ExpandableFab({
//     required this.children,
//   });

//   final List<Widget> children;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final isRotated = useState(false);
//     final controller = useAnimationController(
//       duration: const Duration(milliseconds: 200),
//     );
//     final expandAnimation = useMemoized(
//       () => CurvedAnimation(
//         curve: Curves.fastOutSlowIn,
//         reverseCurve: Curves.easeOutQuad,
//         parent: controller,
//       ),
//     );
//     final rotationAnimation = useMemoized(
//       () => Tween<double>(
//         begin: -30 / 360, // -30 degrees
//         end: 45 / 360, // 45 degrees
//       ).animate(controller),
//     );

//     List<Widget> _buildExpandingActionButtons() {
//       final widgets = <Widget>[];
//       final count = children.length;
//       final step = 180.0 / (count - 1);
//       for (var i = 0, angleInDegrees = 0.0;
//           i < count;
//           i++, angleInDegrees += step) {
//         widgets.add(
//           _ExpandingActionButton(
//             directionInDegrees: angleInDegrees,
//             progress: expandAnimation,
//             child: children[i],
//           ),
//         );
//       }

//       return widgets;
//     }

//     // Toggle rotation
//     void toggleRotation() {
//       isRotated.value = !isRotated.value;
//       if (isRotated.value) {
//         controller.forward();
//       } else {
//         controller.reverse();
//       }
//     }

//     return Stack(
//       alignment: Alignment.bottomCenter,
//       clipBehavior: Clip.none,
//       children: [
//         ClipRRect(
//           borderRadius: const BorderRadius.all(Radius.circular(60)),
//           child: GestureDetector(
//             onTap: toggleRotation,
//             child: SizedBox(
//               height: 120,
//               width: 120,
//               child: Stack(
//                 children: [
//                   Positioned(
//                     top: 16,
//                     left: 0,
//                     right: 0,
//                     child: RotationTransition(
//                       turns: rotationAnimation,
//                       alignment: Alignment.bottomCenter,
//                       child: Image.asset(
//                         'assets/images/fab_interactive.png',
//                         height: 40,
//                       ),
//                     ),
//                   ),
//                   Positioned(
//                     bottom: -10,
//                     child: Image.asset(
//                       'assets/images/fab.png',
//                       width: 120,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         ..._buildExpandingActionButtons(),
//       ],
//     );
//   }
// }

// @immutable
// class _ActionButton extends ConsumerWidget {
//   const _ActionButton({
//     required this.icon,
//     required this.text,
//     this.onPressed,
//   });

//   final VoidCallback? onPressed;
//   final Widget icon;
//   final Widget text;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Material(
//       shape: const CircleBorder(),
//       clipBehavior: Clip.antiAlias,
//       color: Colors.white,
//       elevation: 4,
//       child: InkWell(
//         onTap: onPressed,
//         child: SizedBox(
//           width: 70,
//           height: 70,
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               icon,
//               const SizedBox(height: 4),
//               text,
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// @immutable
// class _ExpandingActionButton extends ConsumerWidget {
//   const _ExpandingActionButton({
//     required this.directionInDegrees,
//     required this.progress,
//     required this.child,
//   });

//   final double directionInDegrees;
//   final Animation<double> progress;
//   final Widget child;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return AnimatedBuilder(
//       animation: progress,
//       builder: (context, child) {
//         final angle = directionInDegrees * progress.value * math.pi / 180;
//         final offsetX = 80 * math.cos(angle);
//         final offsetY = 60 * math.sin(angle);

//         return Positioned(
//           /// NOTE: 30 as button radius
//           right: 30 + offsetX,

//           /// NOTE: 30 as button radius
//           bottom: kBottomNavigationBarHeight + 30 + offsetY,
//           child: child!,
//         );
//       },
//       child: FadeTransition(
//         opacity: progress,
//         child: child,
//       ),
//     );
//   }
// }
