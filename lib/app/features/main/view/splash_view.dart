import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/features/main/provider/main_providers.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:rive/rive.dart' as rive;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'splash_view.g.dart';

// splash screen state
@riverpod
class SplashState extends _$SplashState {
  @override
  bool build() {
    return false;
  }

  // ignore: use_setters_to_change_properties
  void set({
    required bool value,
  }) {
    state = value;
  }
}

class SplashView extends StatefulHookConsumerWidget {
  const SplashView({super.key});

  static const routeName = 'splash';
  static const routePath = '/splash';

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SplashViewState();
}

class _SplashViewState extends ConsumerState<SplashView> {
  rive.RiveFile? _file;
  late rive.SimpleAnimation _controller;
  late bool isForceUpdate;

  @override
  void initState() {
    super.initState();

    isForceUpdate = ref.read(forceUpdateProvider);

    rootBundle.load('assets/rives/splash.riv').then(
      (data) async {
        setState(() {
          _file = rive.RiveFile.import(data);
          _controller = OneShotCustomAnimation(
            'Timeline 1',
            onStop: () {
              if (!isForceUpdate) {
                ref.read(splashStateProvider.notifier).set(value: true);
              } else {
                _showForceUpdateDialog(context);
              }
            },
            onStart: () {},
            autoplay: false,
          );
        });

        // preload static assets
        Groveman.debug('Preloading static assets');
        final _allAsset = [
          'assets/splash.png',
          'assets/backgrounds/gmm-header.png',
          'assets/images/goma_sad.png',
          'assets/images/goma_mascot.png',
          'assets/images/gomama_logo.png',
          'assets/images/apple_logo.png',
          'assets/images/facebook_logo.png',
          'assets/images/google_logo.png',
          'assets/backgrounds/otp_background.png',
          'assets/backgrounds/stars_background_edit_profile.png',
          'assets/backgrounds/milk_background_login_fourth.png',
          'assets/backgrounds/milk_background_login_third.png',
          'assets/backgrounds/milk_background_login_second.png',
          'assets/backgrounds/milk_background_login_first.png',
        ];

        for (final asset in _allAsset) {
          if (context.mounted) {
            await precacheImage(AssetImage(asset), context);
          }
        }

        FlutterNativeSplash.remove();
        _controller.isActive = true;
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_file == null) {
      return const SizedBox.shrink();
      // return Image.asset('assets/splash.png', fit: BoxFit.cover);
    }

    return rive.RiveAnimation.direct(
      _file!,
      fit: BoxFit.cover,
      alignment: Alignment.center,
      controllers: [_controller],
    );
  }

  void _showForceUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog.adaptive(
          title: const Text('Update Required'),
          content: const Text(
            'A new version of the app is available. Please update to continue using the app.',
          ),
          actions: <Widget>[
            AdaptiveTextButton(
              child: const Text('Update'),
              onPressed: () {
                // TODO(ch): Implement update logic here
                Groveman.debug('User tapped update');
              },
            ),
          ],
        );
      },
    );
  }
}

T? _ambiguate<T>(T? value) => value;

class OneShotCustomAnimation extends rive.SimpleAnimation {
  OneShotCustomAnimation(
    super.animationName, {
    super.mix,
    super.autoplay,
    this.onStop,
    this.onStart,
  }) {
    isActiveChanged.addListener(onActiveChanged);
  }

  /// Fires when the animation stops being active
  final VoidCallback? onStop;

  /// Fires when the animation starts being active
  final VoidCallback? onStart;

  /// Dispose of any callback listeners
  @override
  void dispose() {
    isActiveChanged.removeListener(onActiveChanged);
    super.dispose();
  }

  /// Perform tasks when the animation's active state changes
  void onActiveChanged() {
    // Fire any callbacks
    isActive
        ? onStart?.call()
        // onStop can fire while widgets are still drawing
        : _ambiguate(WidgetsBinding.instance)
            ?.addPostFrameCallback((_) => onStop?.call());
  }
}
