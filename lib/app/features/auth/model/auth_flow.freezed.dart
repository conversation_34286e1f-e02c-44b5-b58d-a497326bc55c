// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_flow.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthFlow _$AuthFlowFromJson(Map<String, dynamic> json) {
  return _AuthFlow.fromJson(json);
}

/// @nodoc
mixin _$AuthFlow {
  int get step => throw _privateConstructorUsedError;
  String? get mobileNumberOrEmail => throw _privateConstructorUsedError;
  String? get countryDialCode => throw _privateConstructorUsedError;
  String? get mobileNumber => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  bool? get isLogin => throw _privateConstructorUsedError;
  String? get otp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AuthFlowCopyWith<AuthFlow> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthFlowCopyWith<$Res> {
  factory $AuthFlowCopyWith(AuthFlow value, $Res Function(AuthFlow) then) =
      _$AuthFlowCopyWithImpl<$Res, AuthFlow>;
  @useResult
  $Res call(
      {int step,
      String? mobileNumberOrEmail,
      String? countryDialCode,
      String? mobileNumber,
      String? email,
      bool? isLogin,
      String? otp});
}

/// @nodoc
class _$AuthFlowCopyWithImpl<$Res, $Val extends AuthFlow>
    implements $AuthFlowCopyWith<$Res> {
  _$AuthFlowCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? mobileNumberOrEmail = freezed,
    Object? countryDialCode = freezed,
    Object? mobileNumber = freezed,
    Object? email = freezed,
    Object? isLogin = freezed,
    Object? otp = freezed,
  }) {
    return _then(_value.copyWith(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as int,
      mobileNumberOrEmail: freezed == mobileNumberOrEmail
          ? _value.mobileNumberOrEmail
          : mobileNumberOrEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isLogin: freezed == isLogin
          ? _value.isLogin
          : isLogin // ignore: cast_nullable_to_non_nullable
              as bool?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthFlowImplCopyWith<$Res>
    implements $AuthFlowCopyWith<$Res> {
  factory _$$AuthFlowImplCopyWith(
          _$AuthFlowImpl value, $Res Function(_$AuthFlowImpl) then) =
      __$$AuthFlowImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int step,
      String? mobileNumberOrEmail,
      String? countryDialCode,
      String? mobileNumber,
      String? email,
      bool? isLogin,
      String? otp});
}

/// @nodoc
class __$$AuthFlowImplCopyWithImpl<$Res>
    extends _$AuthFlowCopyWithImpl<$Res, _$AuthFlowImpl>
    implements _$$AuthFlowImplCopyWith<$Res> {
  __$$AuthFlowImplCopyWithImpl(
      _$AuthFlowImpl _value, $Res Function(_$AuthFlowImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? mobileNumberOrEmail = freezed,
    Object? countryDialCode = freezed,
    Object? mobileNumber = freezed,
    Object? email = freezed,
    Object? isLogin = freezed,
    Object? otp = freezed,
  }) {
    return _then(_$AuthFlowImpl(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as int,
      mobileNumberOrEmail: freezed == mobileNumberOrEmail
          ? _value.mobileNumberOrEmail
          : mobileNumberOrEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isLogin: freezed == isLogin
          ? _value.isLogin
          : isLogin // ignore: cast_nullable_to_non_nullable
              as bool?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthFlowImpl implements _AuthFlow {
  _$AuthFlowImpl(
      {required this.step,
      this.mobileNumberOrEmail,
      this.countryDialCode,
      this.mobileNumber,
      this.email,
      this.isLogin,
      this.otp});

  factory _$AuthFlowImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthFlowImplFromJson(json);

  @override
  final int step;
  @override
  final String? mobileNumberOrEmail;
  @override
  final String? countryDialCode;
  @override
  final String? mobileNumber;
  @override
  final String? email;
  @override
  final bool? isLogin;
  @override
  final String? otp;

  @override
  String toString() {
    return 'AuthFlow(step: $step, mobileNumberOrEmail: $mobileNumberOrEmail, countryDialCode: $countryDialCode, mobileNumber: $mobileNumber, email: $email, isLogin: $isLogin, otp: $otp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFlowImpl &&
            (identical(other.step, step) || other.step == step) &&
            (identical(other.mobileNumberOrEmail, mobileNumberOrEmail) ||
                other.mobileNumberOrEmail == mobileNumberOrEmail) &&
            (identical(other.countryDialCode, countryDialCode) ||
                other.countryDialCode == countryDialCode) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isLogin, isLogin) || other.isLogin == isLogin) &&
            (identical(other.otp, otp) || other.otp == otp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, step, mobileNumberOrEmail,
      countryDialCode, mobileNumber, email, isLogin, otp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthFlowImplCopyWith<_$AuthFlowImpl> get copyWith =>
      __$$AuthFlowImplCopyWithImpl<_$AuthFlowImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthFlowImplToJson(
      this,
    );
  }
}

abstract class _AuthFlow implements AuthFlow {
  factory _AuthFlow(
      {required final int step,
      final String? mobileNumberOrEmail,
      final String? countryDialCode,
      final String? mobileNumber,
      final String? email,
      final bool? isLogin,
      final String? otp}) = _$AuthFlowImpl;

  factory _AuthFlow.fromJson(Map<String, dynamic> json) =
      _$AuthFlowImpl.fromJson;

  @override
  int get step;
  @override
  String? get mobileNumberOrEmail;
  @override
  String? get countryDialCode;
  @override
  String? get mobileNumber;
  @override
  String? get email;
  @override
  bool? get isLogin;
  @override
  String? get otp;
  @override
  @JsonKey(ignore: true)
  _$$AuthFlowImplCopyWith<_$AuthFlowImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
