import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'auth_flow.freezed.dart';
part 'auth_flow.g.dart';

@freezed
class AuthFlow with _$AuthFlow {
  factory AuthFlow({
    required int step,
    String? mobileNumberOrEmail,
    String? countryDialCode,
    String? mobileNumber,
    String? email,
    bool? isLogin,
    String? otp,
  }) = _AuthFlow;

  factory AuthFlow.fromJson(Json json) => _$AuthFlowFromJson(json);
}
