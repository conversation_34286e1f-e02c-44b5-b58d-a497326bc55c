// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileImpl _$$ProfileImplFromJson(Map<String, dynamic> json) =>
    _$ProfileImpl(
      id: (json['id'] as num).toInt(),
      userId: json['user_id'] as String,
      socialType: $enumDecode(_$SocialTypeEnumMap, json['social_type']),
      socialId: json['social_id'] as String?,
      email: json['email'] as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      photoUrl: json['photo_url'] as String?,
      username: json['username'] as String?,
      password: json['password'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$ProfileImplToJson(_$ProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'social_type': _$SocialTypeEnumMap[instance.socialType]!,
      'social_id': instance.socialId,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'photo_url': instance.photoUrl,
      'username': instance.username,
      'password': instance.password,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$SocialTypeEnumMap = {
  SocialType.google: 'google',
  SocialType.facebook: 'facebook',
  SocialType.apple: 'apple',
  SocialType.shopify: 'shopify',
};
