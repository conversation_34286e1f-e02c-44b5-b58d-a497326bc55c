// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OtpResponse _$OtpResponseFromJson(Map<String, dynamic> json) {
  return _OtpResponse.fromJson(json);
}

/// @nodoc
mixin _$OtpResponse {
  bool get isLogin => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OtpResponseCopyWith<OtpResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtpResponseCopyWith<$Res> {
  factory $OtpResponseCopyWith(
          OtpResponse value, $Res Function(OtpResponse) then) =
      _$OtpResponseCopyWithImpl<$Res, OtpResponse>;
  @useResult
  $Res call({bool isLogin});
}

/// @nodoc
class _$OtpResponseCopyWithImpl<$Res, $Val extends OtpResponse>
    implements $OtpResponseCopyWith<$Res> {
  _$OtpResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLogin = null,
  }) {
    return _then(_value.copyWith(
      isLogin: null == isLogin
          ? _value.isLogin
          : isLogin // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtpResponseImplCopyWith<$Res>
    implements $OtpResponseCopyWith<$Res> {
  factory _$$OtpResponseImplCopyWith(
          _$OtpResponseImpl value, $Res Function(_$OtpResponseImpl) then) =
      __$$OtpResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLogin});
}

/// @nodoc
class __$$OtpResponseImplCopyWithImpl<$Res>
    extends _$OtpResponseCopyWithImpl<$Res, _$OtpResponseImpl>
    implements _$$OtpResponseImplCopyWith<$Res> {
  __$$OtpResponseImplCopyWithImpl(
      _$OtpResponseImpl _value, $Res Function(_$OtpResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLogin = null,
  }) {
    return _then(_$OtpResponseImpl(
      isLogin: null == isLogin
          ? _value.isLogin
          : isLogin // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OtpResponseImpl implements _OtpResponse {
  _$OtpResponseImpl({required this.isLogin});

  factory _$OtpResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtpResponseImplFromJson(json);

  @override
  final bool isLogin;

  @override
  String toString() {
    return 'OtpResponse(isLogin: $isLogin)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpResponseImpl &&
            (identical(other.isLogin, isLogin) || other.isLogin == isLogin));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, isLogin);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpResponseImplCopyWith<_$OtpResponseImpl> get copyWith =>
      __$$OtpResponseImplCopyWithImpl<_$OtpResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtpResponseImplToJson(
      this,
    );
  }
}

abstract class _OtpResponse implements OtpResponse {
  factory _OtpResponse({required final bool isLogin}) = _$OtpResponseImpl;

  factory _OtpResponse.fromJson(Map<String, dynamic> json) =
      _$OtpResponseImpl.fromJson;

  @override
  bool get isLogin;
  @override
  @JsonKey(ignore: true)
  _$$OtpResponseImplCopyWith<_$OtpResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RequestOtpInput _$RequestOtpInputFromJson(Map<String, dynamic> json) {
  return _RequestOtpInput.fromJson(json);
}

/// @nodoc
mixin _$RequestOtpInput {
  String? get loginAccount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RequestOtpInputCopyWith<RequestOtpInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RequestOtpInputCopyWith<$Res> {
  factory $RequestOtpInputCopyWith(
          RequestOtpInput value, $Res Function(RequestOtpInput) then) =
      _$RequestOtpInputCopyWithImpl<$Res, RequestOtpInput>;
  @useResult
  $Res call({String? loginAccount});
}

/// @nodoc
class _$RequestOtpInputCopyWithImpl<$Res, $Val extends RequestOtpInput>
    implements $RequestOtpInputCopyWith<$Res> {
  _$RequestOtpInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginAccount = freezed,
  }) {
    return _then(_value.copyWith(
      loginAccount: freezed == loginAccount
          ? _value.loginAccount
          : loginAccount // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RequestOtpInputImplCopyWith<$Res>
    implements $RequestOtpInputCopyWith<$Res> {
  factory _$$RequestOtpInputImplCopyWith(_$RequestOtpInputImpl value,
          $Res Function(_$RequestOtpInputImpl) then) =
      __$$RequestOtpInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? loginAccount});
}

/// @nodoc
class __$$RequestOtpInputImplCopyWithImpl<$Res>
    extends _$RequestOtpInputCopyWithImpl<$Res, _$RequestOtpInputImpl>
    implements _$$RequestOtpInputImplCopyWith<$Res> {
  __$$RequestOtpInputImplCopyWithImpl(
      _$RequestOtpInputImpl _value, $Res Function(_$RequestOtpInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginAccount = freezed,
  }) {
    return _then(_$RequestOtpInputImpl(
      loginAccount: freezed == loginAccount
          ? _value.loginAccount
          : loginAccount // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RequestOtpInputImpl implements _RequestOtpInput {
  _$RequestOtpInputImpl({this.loginAccount});

  factory _$RequestOtpInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$RequestOtpInputImplFromJson(json);

  @override
  final String? loginAccount;

  @override
  String toString() {
    return 'RequestOtpInput(loginAccount: $loginAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestOtpInputImpl &&
            (identical(other.loginAccount, loginAccount) ||
                other.loginAccount == loginAccount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, loginAccount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RequestOtpInputImplCopyWith<_$RequestOtpInputImpl> get copyWith =>
      __$$RequestOtpInputImplCopyWithImpl<_$RequestOtpInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RequestOtpInputImplToJson(
      this,
    );
  }
}

abstract class _RequestOtpInput implements RequestOtpInput {
  factory _RequestOtpInput({final String? loginAccount}) =
      _$RequestOtpInputImpl;

  factory _RequestOtpInput.fromJson(Map<String, dynamic> json) =
      _$RequestOtpInputImpl.fromJson;

  @override
  String? get loginAccount;
  @override
  @JsonKey(ignore: true)
  _$$RequestOtpInputImplCopyWith<_$RequestOtpInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifyRegisterOtpInput _$VerifyRegisterOtpInputFromJson(
    Map<String, dynamic> json) {
  return _VerifyRegisterOtpInput.fromJson(json);
}

/// @nodoc
mixin _$VerifyRegisterOtpInput {
  String? get registerAccount => throw _privateConstructorUsedError;
  String? get otp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VerifyRegisterOtpInputCopyWith<VerifyRegisterOtpInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyRegisterOtpInputCopyWith<$Res> {
  factory $VerifyRegisterOtpInputCopyWith(VerifyRegisterOtpInput value,
          $Res Function(VerifyRegisterOtpInput) then) =
      _$VerifyRegisterOtpInputCopyWithImpl<$Res, VerifyRegisterOtpInput>;
  @useResult
  $Res call({String? registerAccount, String? otp});
}

/// @nodoc
class _$VerifyRegisterOtpInputCopyWithImpl<$Res,
        $Val extends VerifyRegisterOtpInput>
    implements $VerifyRegisterOtpInputCopyWith<$Res> {
  _$VerifyRegisterOtpInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? registerAccount = freezed,
    Object? otp = freezed,
  }) {
    return _then(_value.copyWith(
      registerAccount: freezed == registerAccount
          ? _value.registerAccount
          : registerAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyRegisterOtpInputImplCopyWith<$Res>
    implements $VerifyRegisterOtpInputCopyWith<$Res> {
  factory _$$VerifyRegisterOtpInputImplCopyWith(
          _$VerifyRegisterOtpInputImpl value,
          $Res Function(_$VerifyRegisterOtpInputImpl) then) =
      __$$VerifyRegisterOtpInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? registerAccount, String? otp});
}

/// @nodoc
class __$$VerifyRegisterOtpInputImplCopyWithImpl<$Res>
    extends _$VerifyRegisterOtpInputCopyWithImpl<$Res,
        _$VerifyRegisterOtpInputImpl>
    implements _$$VerifyRegisterOtpInputImplCopyWith<$Res> {
  __$$VerifyRegisterOtpInputImplCopyWithImpl(
      _$VerifyRegisterOtpInputImpl _value,
      $Res Function(_$VerifyRegisterOtpInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? registerAccount = freezed,
    Object? otp = freezed,
  }) {
    return _then(_$VerifyRegisterOtpInputImpl(
      registerAccount: freezed == registerAccount
          ? _value.registerAccount
          : registerAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyRegisterOtpInputImpl implements _VerifyRegisterOtpInput {
  _$VerifyRegisterOtpInputImpl({this.registerAccount, this.otp});

  factory _$VerifyRegisterOtpInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyRegisterOtpInputImplFromJson(json);

  @override
  final String? registerAccount;
  @override
  final String? otp;

  @override
  String toString() {
    return 'VerifyRegisterOtpInput(registerAccount: $registerAccount, otp: $otp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyRegisterOtpInputImpl &&
            (identical(other.registerAccount, registerAccount) ||
                other.registerAccount == registerAccount) &&
            (identical(other.otp, otp) || other.otp == otp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, registerAccount, otp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyRegisterOtpInputImplCopyWith<_$VerifyRegisterOtpInputImpl>
      get copyWith => __$$VerifyRegisterOtpInputImplCopyWithImpl<
          _$VerifyRegisterOtpInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyRegisterOtpInputImplToJson(
      this,
    );
  }
}

abstract class _VerifyRegisterOtpInput implements VerifyRegisterOtpInput {
  factory _VerifyRegisterOtpInput(
      {final String? registerAccount,
      final String? otp}) = _$VerifyRegisterOtpInputImpl;

  factory _VerifyRegisterOtpInput.fromJson(Map<String, dynamic> json) =
      _$VerifyRegisterOtpInputImpl.fromJson;

  @override
  String? get registerAccount;
  @override
  String? get otp;
  @override
  @JsonKey(ignore: true)
  _$$VerifyRegisterOtpInputImplCopyWith<_$VerifyRegisterOtpInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

RequestEmailOrPhoneUpdateOtpInput _$RequestEmailOrPhoneUpdateOtpInputFromJson(
    Map<String, dynamic> json) {
  return _RequestEmailOrPhoneUpdateOtpInput.fromJson(json);
}

/// @nodoc
mixin _$RequestEmailOrPhoneUpdateOtpInput {
  String? get mobileOrEmail => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RequestEmailOrPhoneUpdateOtpInputCopyWith<RequestEmailOrPhoneUpdateOtpInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RequestEmailOrPhoneUpdateOtpInputCopyWith<$Res> {
  factory $RequestEmailOrPhoneUpdateOtpInputCopyWith(
          RequestEmailOrPhoneUpdateOtpInput value,
          $Res Function(RequestEmailOrPhoneUpdateOtpInput) then) =
      _$RequestEmailOrPhoneUpdateOtpInputCopyWithImpl<$Res,
          RequestEmailOrPhoneUpdateOtpInput>;
  @useResult
  $Res call({String? mobileOrEmail});
}

/// @nodoc
class _$RequestEmailOrPhoneUpdateOtpInputCopyWithImpl<$Res,
        $Val extends RequestEmailOrPhoneUpdateOtpInput>
    implements $RequestEmailOrPhoneUpdateOtpInputCopyWith<$Res> {
  _$RequestEmailOrPhoneUpdateOtpInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobileOrEmail = freezed,
  }) {
    return _then(_value.copyWith(
      mobileOrEmail: freezed == mobileOrEmail
          ? _value.mobileOrEmail
          : mobileOrEmail // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RequestEmailOrPhoneUpdateOtpInputImplCopyWith<$Res>
    implements $RequestEmailOrPhoneUpdateOtpInputCopyWith<$Res> {
  factory _$$RequestEmailOrPhoneUpdateOtpInputImplCopyWith(
          _$RequestEmailOrPhoneUpdateOtpInputImpl value,
          $Res Function(_$RequestEmailOrPhoneUpdateOtpInputImpl) then) =
      __$$RequestEmailOrPhoneUpdateOtpInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mobileOrEmail});
}

/// @nodoc
class __$$RequestEmailOrPhoneUpdateOtpInputImplCopyWithImpl<$Res>
    extends _$RequestEmailOrPhoneUpdateOtpInputCopyWithImpl<$Res,
        _$RequestEmailOrPhoneUpdateOtpInputImpl>
    implements _$$RequestEmailOrPhoneUpdateOtpInputImplCopyWith<$Res> {
  __$$RequestEmailOrPhoneUpdateOtpInputImplCopyWithImpl(
      _$RequestEmailOrPhoneUpdateOtpInputImpl _value,
      $Res Function(_$RequestEmailOrPhoneUpdateOtpInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobileOrEmail = freezed,
  }) {
    return _then(_$RequestEmailOrPhoneUpdateOtpInputImpl(
      mobileOrEmail: freezed == mobileOrEmail
          ? _value.mobileOrEmail
          : mobileOrEmail // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RequestEmailOrPhoneUpdateOtpInputImpl
    implements _RequestEmailOrPhoneUpdateOtpInput {
  _$RequestEmailOrPhoneUpdateOtpInputImpl({this.mobileOrEmail});

  factory _$RequestEmailOrPhoneUpdateOtpInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$RequestEmailOrPhoneUpdateOtpInputImplFromJson(json);

  @override
  final String? mobileOrEmail;

  @override
  String toString() {
    return 'RequestEmailOrPhoneUpdateOtpInput(mobileOrEmail: $mobileOrEmail)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestEmailOrPhoneUpdateOtpInputImpl &&
            (identical(other.mobileOrEmail, mobileOrEmail) ||
                other.mobileOrEmail == mobileOrEmail));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mobileOrEmail);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RequestEmailOrPhoneUpdateOtpInputImplCopyWith<
          _$RequestEmailOrPhoneUpdateOtpInputImpl>
      get copyWith => __$$RequestEmailOrPhoneUpdateOtpInputImplCopyWithImpl<
          _$RequestEmailOrPhoneUpdateOtpInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RequestEmailOrPhoneUpdateOtpInputImplToJson(
      this,
    );
  }
}

abstract class _RequestEmailOrPhoneUpdateOtpInput
    implements RequestEmailOrPhoneUpdateOtpInput {
  factory _RequestEmailOrPhoneUpdateOtpInput({final String? mobileOrEmail}) =
      _$RequestEmailOrPhoneUpdateOtpInputImpl;

  factory _RequestEmailOrPhoneUpdateOtpInput.fromJson(
          Map<String, dynamic> json) =
      _$RequestEmailOrPhoneUpdateOtpInputImpl.fromJson;

  @override
  String? get mobileOrEmail;
  @override
  @JsonKey(ignore: true)
  _$$RequestEmailOrPhoneUpdateOtpInputImplCopyWith<
          _$RequestEmailOrPhoneUpdateOtpInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

VerifyUpdateEmailOrPhoneOtpInput _$VerifyUpdateEmailOrPhoneOtpInputFromJson(
    Map<String, dynamic> json) {
  return _VerifyUpdateEmailOrPhoneOtpInput.fromJson(json);
}

/// @nodoc
mixin _$VerifyUpdateEmailOrPhoneOtpInput {
  String? get countryDialCode => throw _privateConstructorUsedError;
  String? get newMobileNumber => throw _privateConstructorUsedError;
  String? get newEmail => throw _privateConstructorUsedError;
  String? get otp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VerifyUpdateEmailOrPhoneOtpInputCopyWith<VerifyUpdateEmailOrPhoneOtpInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyUpdateEmailOrPhoneOtpInputCopyWith<$Res> {
  factory $VerifyUpdateEmailOrPhoneOtpInputCopyWith(
          VerifyUpdateEmailOrPhoneOtpInput value,
          $Res Function(VerifyUpdateEmailOrPhoneOtpInput) then) =
      _$VerifyUpdateEmailOrPhoneOtpInputCopyWithImpl<$Res,
          VerifyUpdateEmailOrPhoneOtpInput>;
  @useResult
  $Res call(
      {String? countryDialCode,
      String? newMobileNumber,
      String? newEmail,
      String? otp});
}

/// @nodoc
class _$VerifyUpdateEmailOrPhoneOtpInputCopyWithImpl<$Res,
        $Val extends VerifyUpdateEmailOrPhoneOtpInput>
    implements $VerifyUpdateEmailOrPhoneOtpInputCopyWith<$Res> {
  _$VerifyUpdateEmailOrPhoneOtpInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryDialCode = freezed,
    Object? newMobileNumber = freezed,
    Object? newEmail = freezed,
    Object? otp = freezed,
  }) {
    return _then(_value.copyWith(
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      newMobileNumber: freezed == newMobileNumber
          ? _value.newMobileNumber
          : newMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      newEmail: freezed == newEmail
          ? _value.newEmail
          : newEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWith<$Res>
    implements $VerifyUpdateEmailOrPhoneOtpInputCopyWith<$Res> {
  factory _$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWith(
          _$VerifyUpdateEmailOrPhoneOtpInputImpl value,
          $Res Function(_$VerifyUpdateEmailOrPhoneOtpInputImpl) then) =
      __$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? countryDialCode,
      String? newMobileNumber,
      String? newEmail,
      String? otp});
}

/// @nodoc
class __$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWithImpl<$Res>
    extends _$VerifyUpdateEmailOrPhoneOtpInputCopyWithImpl<$Res,
        _$VerifyUpdateEmailOrPhoneOtpInputImpl>
    implements _$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWith<$Res> {
  __$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWithImpl(
      _$VerifyUpdateEmailOrPhoneOtpInputImpl _value,
      $Res Function(_$VerifyUpdateEmailOrPhoneOtpInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryDialCode = freezed,
    Object? newMobileNumber = freezed,
    Object? newEmail = freezed,
    Object? otp = freezed,
  }) {
    return _then(_$VerifyUpdateEmailOrPhoneOtpInputImpl(
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      newMobileNumber: freezed == newMobileNumber
          ? _value.newMobileNumber
          : newMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      newEmail: freezed == newEmail
          ? _value.newEmail
          : newEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyUpdateEmailOrPhoneOtpInputImpl
    implements _VerifyUpdateEmailOrPhoneOtpInput {
  _$VerifyUpdateEmailOrPhoneOtpInputImpl(
      {this.countryDialCode, this.newMobileNumber, this.newEmail, this.otp});

  factory _$VerifyUpdateEmailOrPhoneOtpInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$VerifyUpdateEmailOrPhoneOtpInputImplFromJson(json);

  @override
  final String? countryDialCode;
  @override
  final String? newMobileNumber;
  @override
  final String? newEmail;
  @override
  final String? otp;

  @override
  String toString() {
    return 'VerifyUpdateEmailOrPhoneOtpInput(countryDialCode: $countryDialCode, newMobileNumber: $newMobileNumber, newEmail: $newEmail, otp: $otp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyUpdateEmailOrPhoneOtpInputImpl &&
            (identical(other.countryDialCode, countryDialCode) ||
                other.countryDialCode == countryDialCode) &&
            (identical(other.newMobileNumber, newMobileNumber) ||
                other.newMobileNumber == newMobileNumber) &&
            (identical(other.newEmail, newEmail) ||
                other.newEmail == newEmail) &&
            (identical(other.otp, otp) || other.otp == otp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, countryDialCode, newMobileNumber, newEmail, otp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWith<
          _$VerifyUpdateEmailOrPhoneOtpInputImpl>
      get copyWith => __$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWithImpl<
          _$VerifyUpdateEmailOrPhoneOtpInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyUpdateEmailOrPhoneOtpInputImplToJson(
      this,
    );
  }
}

abstract class _VerifyUpdateEmailOrPhoneOtpInput
    implements VerifyUpdateEmailOrPhoneOtpInput {
  factory _VerifyUpdateEmailOrPhoneOtpInput(
      {final String? countryDialCode,
      final String? newMobileNumber,
      final String? newEmail,
      final String? otp}) = _$VerifyUpdateEmailOrPhoneOtpInputImpl;

  factory _VerifyUpdateEmailOrPhoneOtpInput.fromJson(
          Map<String, dynamic> json) =
      _$VerifyUpdateEmailOrPhoneOtpInputImpl.fromJson;

  @override
  String? get countryDialCode;
  @override
  String? get newMobileNumber;
  @override
  String? get newEmail;
  @override
  String? get otp;
  @override
  @JsonKey(ignore: true)
  _$$VerifyUpdateEmailOrPhoneOtpInputImplCopyWith<
          _$VerifyUpdateEmailOrPhoneOtpInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
