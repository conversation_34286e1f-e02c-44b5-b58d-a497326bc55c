// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PartialUserImpl _$$PartialUserImplFromJson(Map<String, dynamic> json) =>
    _$PartialUserImpl(
      id: json['id'] as String?,
      username: json['username'] as String?,
      fullName: json['full_name'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      emailAddress: json['email_address'] as String?,
      photoUrl: json['photo_url'] as String?,
      gender: json['gender'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      fullMobileNumber: json['full_mobile_number'] as String?,
      authProvider: json['auth_provider'] as String?,
      token: json['token'] as String?,
      realtimeJwt: json['realtime_jwt'] as String?,
      birthday:
          const CustomDateTimeConverter().fromJson(json['birthday'] as String?),
      childrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['children_birthdays'] as List?),
      isMobileNumberVerified: json['is_mobile_number_verified'] as bool?,
      isEmailAddressVerified: json['is_email_address_verified'] as bool?,
      isGomamaVerified: json['is_gomama_verified'] as bool?,
      isSingpassVerified: json['is_singpass_verified'] as bool?,
      isAdminVerified: json['is_admin_verified'] as bool?,
      latestVerifySelfieFailCount:
          (json['latest_verify_selfie_fail_count'] as num?)?.toInt(),
      profiles: (json['profiles'] as List<dynamic>?)
          ?.map((e) => Profile.fromJson(e as Map<String, dynamic>))
          .toList(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$PartialUserImplToJson(_$PartialUserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'full_name': instance.fullName,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'email_address': instance.emailAddress,
      'photo_url': instance.photoUrl,
      'gender': instance.gender,
      'mobile_number': instance.mobileNumber,
      'full_mobile_number': instance.fullMobileNumber,
      'auth_provider': instance.authProvider,
      'token': instance.token,
      'realtime_jwt': instance.realtimeJwt,
      'birthday': const CustomDateTimeConverter().toJson(instance.birthday),
      'children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.childrenBirthdays),
      'is_mobile_number_verified': instance.isMobileNumberVerified,
      'is_email_address_verified': instance.isEmailAddressVerified,
      'is_gomama_verified': instance.isGomamaVerified,
      'is_singpass_verified': instance.isSingpassVerified,
      'is_admin_verified': instance.isAdminVerified,
      'latest_verify_selfie_fail_count': instance.latestVerifySelfieFailCount,
      'profiles': instance.profiles?.map((e) => e.toJson()).toList(),
      'runtimeType': instance.$type,
    };

_$SignedInImpl _$$SignedInImplFromJson(Map<String, dynamic> json) =>
    _$SignedInImpl(
      id: json['id'] as String,
      username: json['username'] as String?,
      fullName: json['full_name'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      emailAddress: json['email_address'] as String?,
      photoUrl: json['photo_url'] as String?,
      gender: json['gender'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      fullMobileNumber: json['full_mobile_number'] as String?,
      authProvider: json['auth_provider'] as String?,
      token: json['token'] as String,
      realtimeJwt: json['realtime_jwt'] as String?,
      birthday:
          const CustomDateTimeConverter().fromJson(json['birthday'] as String?),
      childrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['children_birthdays'] as List?),
      isMobileNumberVerified: json['is_mobile_number_verified'] as bool?,
      isEmailAddressVerified: json['is_email_address_verified'] as bool?,
      isGomamaVerified: json['is_gomama_verified'] as bool?,
      isSingpassVerified: json['is_singpass_verified'] as bool?,
      isAdminVerified: json['is_admin_verified'] as bool?,
      latestVerifySelfieFailCount:
          (json['latest_verify_selfie_fail_count'] as num).toInt(),
      profiles: (json['profiles'] as List<dynamic>?)
          ?.map((e) => Profile.fromJson(e as Map<String, dynamic>))
          .toList(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$SignedInImplToJson(_$SignedInImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'full_name': instance.fullName,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'email_address': instance.emailAddress,
      'photo_url': instance.photoUrl,
      'gender': instance.gender,
      'mobile_number': instance.mobileNumber,
      'full_mobile_number': instance.fullMobileNumber,
      'auth_provider': instance.authProvider,
      'token': instance.token,
      'realtime_jwt': instance.realtimeJwt,
      'birthday': const CustomDateTimeConverter().toJson(instance.birthday),
      'children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.childrenBirthdays),
      'is_mobile_number_verified': instance.isMobileNumberVerified,
      'is_email_address_verified': instance.isEmailAddressVerified,
      'is_gomama_verified': instance.isGomamaVerified,
      'is_singpass_verified': instance.isSingpassVerified,
      'is_admin_verified': instance.isAdminVerified,
      'latest_verify_selfie_fail_count': instance.latestVerifySelfieFailCount,
      'profiles': instance.profiles?.map((e) => e.toJson()).toList(),
      'runtimeType': instance.$type,
    };

_$SignedOutImpl _$$SignedOutImplFromJson(Map<String, dynamic> json) =>
    _$SignedOutImpl(
      id: json['id'] as String?,
      username: json['username'] as String?,
      fullName: json['full_name'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      emailAddress: json['email_address'] as String?,
      photoUrl: json['photo_url'] as String?,
      gender: json['gender'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      fullMobileNumber: json['full_mobile_number'] as String?,
      authProvider: json['auth_provider'] as String?,
      token: json['token'] as String?,
      realtimeJwt: json['realtime_jwt'] as String?,
      birthday:
          const CustomDateTimeConverter().fromJson(json['birthday'] as String?),
      childrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['children_birthdays'] as List?),
      isMobileNumberVerified: json['is_mobile_number_verified'] as bool?,
      isEmailAddressVerified: json['is_email_address_verified'] as bool?,
      isGomamaVerified: json['is_gomama_verified'] as bool?,
      isSingpassVerified: json['is_singpass_verified'] as bool?,
      isAdminVerified: json['is_admin_verified'] as bool?,
      latestVerifySelfieFailCount:
          (json['latest_verify_selfie_fail_count'] as num?)?.toInt(),
      profiles: (json['profiles'] as List<dynamic>?)
          ?.map((e) => Profile.fromJson(e as Map<String, dynamic>))
          .toList(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$SignedOutImplToJson(_$SignedOutImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'full_name': instance.fullName,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'email_address': instance.emailAddress,
      'photo_url': instance.photoUrl,
      'gender': instance.gender,
      'mobile_number': instance.mobileNumber,
      'full_mobile_number': instance.fullMobileNumber,
      'auth_provider': instance.authProvider,
      'token': instance.token,
      'realtime_jwt': instance.realtimeJwt,
      'birthday': const CustomDateTimeConverter().toJson(instance.birthday),
      'children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.childrenBirthdays),
      'is_mobile_number_verified': instance.isMobileNumberVerified,
      'is_email_address_verified': instance.isEmailAddressVerified,
      'is_gomama_verified': instance.isGomamaVerified,
      'is_singpass_verified': instance.isSingpassVerified,
      'is_admin_verified': instance.isAdminVerified,
      'latest_verify_selfie_fail_count': instance.latestVerifySelfieFailCount,
      'profiles': instance.profiles?.map((e) => e.toJson()).toList(),
      'runtimeType': instance.$type,
    };

_$UpdateUserInputImpl _$$UpdateUserInputImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateUserInputImpl(
      username: json['username'] as String?,
      lat: (json['lat'] as num?)?.toDouble(),
      lon: (json['lon'] as num?)?.toDouble(),
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      gender: json['gender'] as String?,
      birthday:
          const CustomDateTimeConverter().fromJson(json['birthday'] as String?),
      childrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['children_birthdays'] as List?),
      mobileNumber: json['mobile_number'] as String?,
      companyName: json['company_name'] as String?,
      nationality: json['nationality'] as String?,
      password: json['password'] as String?,
      resetPasswordOtp: json['reset_password_otp'] as String?,
    );

Map<String, dynamic> _$$UpdateUserInputImplToJson(
        _$UpdateUserInputImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'lat': instance.lat,
      'lon': instance.lon,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'gender': instance.gender,
      'birthday': const CustomDateTimeConverter().toJson(instance.birthday),
      'children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.childrenBirthdays),
      'mobile_number': instance.mobileNumber,
      'company_name': instance.companyName,
      'nationality': instance.nationality,
      'password': instance.password,
      'reset_password_otp': instance.resetPasswordOtp,
    };

_$CreateUserInputImpl _$$CreateUserInputImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateUserInputImpl(
      username: json['username'] as String,
      countryDialCode: json['country_dial_code'] as String?,
      newMobileNumber: json['new_mobile_number'] as String?,
      newEmail: json['new_email'] as String?,
      longitude: (json['longitude'] as num?)?.toDouble(),
      latitude: (json['latitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$CreateUserInputImplToJson(
        _$CreateUserInputImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'country_dial_code': instance.countryDialCode,
      'new_mobile_number': instance.newMobileNumber,
      'new_email': instance.newEmail,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
    };
