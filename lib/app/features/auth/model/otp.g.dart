// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OtpResponseImpl _$$OtpResponseImplFromJson(Map<String, dynamic> json) =>
    _$OtpResponseImpl(
      isLogin: json['is_login'] as bool,
    );

Map<String, dynamic> _$$OtpResponseImplToJson(_$OtpResponseImpl instance) =>
    <String, dynamic>{
      'is_login': instance.isLogin,
    };

_$RequestOtpInputImpl _$$RequestOtpInputImplFromJson(
        Map<String, dynamic> json) =>
    _$RequestOtpInputImpl(
      loginAccount: json['login_account'] as String?,
    );

Map<String, dynamic> _$$RequestOtpInputImplToJson(
        _$RequestOtpInputImpl instance) =>
    <String, dynamic>{
      'login_account': instance.loginAccount,
    };

_$VerifyRegisterOtpInputImpl _$$VerifyRegisterOtpInputImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifyRegisterOtpInputImpl(
      registerAccount: json['register_account'] as String?,
      otp: json['otp'] as String?,
    );

Map<String, dynamic> _$$VerifyRegisterOtpInputImplToJson(
        _$VerifyRegisterOtpInputImpl instance) =>
    <String, dynamic>{
      'register_account': instance.registerAccount,
      'otp': instance.otp,
    };

_$RequestEmailOrPhoneUpdateOtpInputImpl
    _$$RequestEmailOrPhoneUpdateOtpInputImplFromJson(
            Map<String, dynamic> json) =>
        _$RequestEmailOrPhoneUpdateOtpInputImpl(
          mobileOrEmail: json['mobile_or_email'] as String?,
        );

Map<String, dynamic> _$$RequestEmailOrPhoneUpdateOtpInputImplToJson(
        _$RequestEmailOrPhoneUpdateOtpInputImpl instance) =>
    <String, dynamic>{
      'mobile_or_email': instance.mobileOrEmail,
    };

_$VerifyUpdateEmailOrPhoneOtpInputImpl
    _$$VerifyUpdateEmailOrPhoneOtpInputImplFromJson(
            Map<String, dynamic> json) =>
        _$VerifyUpdateEmailOrPhoneOtpInputImpl(
          countryDialCode: json['country_dial_code'] as String?,
          newMobileNumber: json['new_mobile_number'] as String?,
          newEmail: json['new_email'] as String?,
          otp: json['otp'] as String?,
        );

Map<String, dynamic> _$$VerifyUpdateEmailOrPhoneOtpInputImplToJson(
        _$VerifyUpdateEmailOrPhoneOtpInputImpl instance) =>
    <String, dynamic>{
      'country_dial_code': instance.countryDialCode,
      'new_mobile_number': instance.newMobileNumber,
      'new_email': instance.newEmail,
      'otp': instance.otp,
    };
