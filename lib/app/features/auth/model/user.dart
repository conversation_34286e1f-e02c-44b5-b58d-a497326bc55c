// ignore_for_file: invalid_annotation_target

import 'dart:io';
import 'package:collection/collection.dart';

import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/profile.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
sealed class User with _$User {
  factory User.fromJson(Json json) => _$UserFromJson(json);

  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  const factory User.partial({
    String? id,
    String? username,
    String? fullName,
    String? firstName,
    String? lastName,
    String? emailAddress,
    String? photoUrl,
    String? gender,
    String? mobileNumber,
    String? fullMobileNumber,
    String? authProvider,
    String? token,
    String? realtimeJwt,
    DateTime? birthday,
    List<DateTime>? childrenBirthdays,
    bool? isMobileNumberVerified,
    bool? isEmailAddressVerified,
    bool? isGomamaVerified,
    bool? isSingpassVerified,
    bool? isAdminVerified,
    int? latestVerifySelfieFailCount,
    List<Profile>? profiles,
  }) = PartialUser;

  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  const factory User.signedIn({
    required String id,
    String? username,
    String? fullName,
    String? firstName,
    String? lastName,
    String? emailAddress,
    String? photoUrl,
    String? gender,
    String? mobileNumber,
    String? fullMobileNumber,
    String? authProvider,
    required String token,
    String? realtimeJwt,
    DateTime? birthday,
    List<DateTime>? childrenBirthdays,
    bool? isMobileNumberVerified,
    bool? isEmailAddressVerified,
    bool? isGomamaVerified,
    bool? isSingpassVerified,
    bool? isAdminVerified,
    required int latestVerifySelfieFailCount,
    List<Profile>? profiles,
  }) = SignedIn;

  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  const factory User.signedOut({
    String? id,
    String? username,
    String? fullName,
    String? firstName,
    String? lastName,
    String? emailAddress,
    String? photoUrl,
    String? gender,
    String? mobileNumber,
    String? fullMobileNumber,
    String? authProvider,
    String? token,
    String? realtimeJwt,
    DateTime? birthday,
    List<DateTime>? childrenBirthdays,
    bool? isMobileNumberVerified,
    bool? isEmailAddressVerified,
    bool? isGomamaVerified,
    bool? isSingpassVerified,
    bool? isAdminVerified,
    int? latestVerifySelfieFailCount,
    List<Profile>? profiles,
  }) = SignedOut;

  const User._();

  bool get isAuth => switch (this) {
        PartialUser() => true,
        SignedIn() => true,
        SignedOut() => false,
      };

  // bool get isVerified => switch (this) {
  //       PartialUser(
  //         isGomamaVerified: final isGomamaVerified,
  //         isSingpassVerified: final isSingpassVerified
  //       ) =>
  //         isGomamaVerified == true || isSingpassVerified == true,
  //       SignedIn(
  //         isGomamaVerified: final isGomamaVerified,
  //         isSingpassVerified: final isSingpassVerified
  //       ) =>
  //         isGomamaVerified == true || isSingpassVerified == true,
  //       SignedOut() => false,
  //     };

  bool get isVerified =>
      (isGomamaVerified ?? false) ||
      (isSingpassVerified ?? false) ||
      (isAdminVerified ?? false);

  bool get hasValidChild =>
      childrenBirthdays != null &&
      childrenBirthdays!.isNotEmpty &&
      childrenBirthdays!.any((birthday) {
        final now = DateTime.now();
        final threeYearsAgo = now.subtract(const Duration(days: 365 * 3));
        return birthday.isAfter(threeYearsAgo);
      });

  Profile? get shopifyProfile => profiles?.firstWhereIndexedOrNull(
        (index, profile) => profile.socialType == SocialType.shopify,
      );
}

@freezed
class UpdateUserInput with _$UpdateUserInput {
  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  factory UpdateUserInput({
    String? username,
    double? lat,
    double? lon,
    String? firstName,
    String? lastName,
    @JsonKey(includeFromJson: false) File? profileImage,
    String? gender,
    DateTime? birthday,
    List<DateTime>? childrenBirthdays,
    String? mobileNumber,
    String? companyName,
    String? nationality,
    String? password,
    String? resetPasswordOtp,
  }) = _UpdateUserInput;

  factory UpdateUserInput.fromJson(Json json) =>
      _$UpdateUserInputFromJson(json);
}

@freezed
class CreateUserInput with _$CreateUserInput {
  @Assert(
    'newEmail != null || (countryDialCode != null && newMobileNumber != null)',
    'Either newEmail or both countryDialCode and newMobileNumber must be provided',
  )
  factory CreateUserInput({
    required String username,
    String? countryDialCode,
    String? newMobileNumber,
    String? newEmail,
    double? longitude,
    double? latitude,
  }) = _CreateUserInput;

  factory CreateUserInput.fromJson(Map<String, dynamic> json) =>
      _$CreateUserInputFromJson(json);

  // Factory constructors for specific cases
  factory CreateUserInput.withEmail({
    required String username,
    required String newEmail,
    double? longitude,
    double? latitude,
  }) {
    return CreateUserInput(
      username: username,
      newEmail: newEmail,
      longitude: longitude,
      latitude: latitude,
    );
  }

  factory CreateUserInput.withPhone({
    required String username,
    required String countryDialCode,
    required String newMobileNumber,
    double? longitude,
    double? latitude,
  }) {
    return CreateUserInput(
      username: username,
      countryDialCode: countryDialCode,
      newMobileNumber: newMobileNumber,
      longitude: longitude,
      latitude: latitude,
    );
  }
}
