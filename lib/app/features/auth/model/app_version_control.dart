import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/constants/custom_integer_bool_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'app_version_control.freezed.dart';
part 'app_version_control.g.dart';

@freezed
class AppVersionControl with _$AppVersionControl {
  @CustomDateTimeConverter()
  factory AppVersionControl({
    required String id,
    required String version,
    required String description,
    required String publishedBy,
    required String addedBy,
    required bool forceUpdate,
    DateTime? publishedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _AppVersionControl;

  factory AppVersionControl.fromJson(Map<String, dynamic> json) =>
      _$AppVersionControlFromJson(json);
}

@freezed
class LatestAppVersionControlResponse with _$LatestAppVersionControlResponse {
  const factory LatestAppVersionControlResponse({
    required AppVersionControl data,
    String? message,
    required bool success,
  }) = _LatestAppVersionControlResponse;

  factory LatestAppVersionControlResponse.fromJson(Json json) =>
      _$LatestAppVersionControlResponseFromJson(json);
}
