import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/constants/custom_integer_bool_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'info_content_management.freezed.dart';
part 'info_content_management.g.dart';

@freezed
class InfoContentManagement with _$InfoContentManagement {
  factory InfoContentManagement({
    required int id,
    required String pageName,
    required String content,
    required String slug,
  }) = _InfoContentManagement;

  factory InfoContentManagement.fromJson(Map<String, dynamic> json) =>
      _$InfoContentManagementFromJson(json);
}
