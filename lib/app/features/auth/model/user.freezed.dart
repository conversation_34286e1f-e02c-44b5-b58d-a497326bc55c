// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'partial':
      return PartialUser.fromJson(json);
    case 'signedIn':
      return SignedIn.fromJson(json);
    case 'signedOut':
      return SignedOut.fromJson(json);

    default:
      throw CheckedFromJsonException(json, 'runtimeType', 'User',
          'Invalid union type "${json['runtimeType']}"!');
  }
}

/// @nodoc
mixin _$User {
  String? get id => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  String? get fullName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get emailAddress => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get mobileNumber => throw _privateConstructorUsedError;
  String? get fullMobileNumber => throw _privateConstructorUsedError;
  String? get authProvider => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  String? get realtimeJwt => throw _privateConstructorUsedError;
  DateTime? get birthday => throw _privateConstructorUsedError;
  List<DateTime>? get childrenBirthdays => throw _privateConstructorUsedError;
  bool? get isMobileNumberVerified => throw _privateConstructorUsedError;
  bool? get isEmailAddressVerified => throw _privateConstructorUsedError;
  bool? get isGomamaVerified => throw _privateConstructorUsedError;
  bool? get isSingpassVerified => throw _privateConstructorUsedError;
  bool? get isAdminVerified => throw _privateConstructorUsedError;
  int? get latestVerifySelfieFailCount => throw _privateConstructorUsedError;
  List<Profile>? get profiles => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        partial,
    required TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedIn,
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedOut,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult? Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PartialUser value) partial,
    required TResult Function(SignedIn value) signedIn,
    required TResult Function(SignedOut value) signedOut,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PartialUser value)? partial,
    TResult? Function(SignedIn value)? signedIn,
    TResult? Function(SignedOut value)? signedOut,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PartialUser value)? partial,
    TResult Function(SignedIn value)? signedIn,
    TResult Function(SignedOut value)? signedOut,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {String id,
      String? username,
      String? fullName,
      String? firstName,
      String? lastName,
      String? emailAddress,
      String? photoUrl,
      String? gender,
      String? mobileNumber,
      String? fullMobileNumber,
      String? authProvider,
      String token,
      String? realtimeJwt,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      bool? isMobileNumberVerified,
      bool? isEmailAddressVerified,
      bool? isGomamaVerified,
      bool? isSingpassVerified,
      bool? isAdminVerified,
      int latestVerifySelfieFailCount,
      List<Profile>? profiles});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? emailAddress = freezed,
    Object? photoUrl = freezed,
    Object? gender = freezed,
    Object? mobileNumber = freezed,
    Object? fullMobileNumber = freezed,
    Object? authProvider = freezed,
    Object? token = null,
    Object? realtimeJwt = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? isMobileNumberVerified = freezed,
    Object? isEmailAddressVerified = freezed,
    Object? isGomamaVerified = freezed,
    Object? isSingpassVerified = freezed,
    Object? isAdminVerified = freezed,
    Object? latestVerifySelfieFailCount = null,
    Object? profiles = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id!
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      fullMobileNumber: freezed == fullMobileNumber
          ? _value.fullMobileNumber
          : fullMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      authProvider: freezed == authProvider
          ? _value.authProvider
          : authProvider // ignore: cast_nullable_to_non_nullable
              as String?,
      token: null == token
          ? _value.token!
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value.childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      isMobileNumberVerified: freezed == isMobileNumberVerified
          ? _value.isMobileNumberVerified
          : isMobileNumberVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailAddressVerified: freezed == isEmailAddressVerified
          ? _value.isEmailAddressVerified
          : isEmailAddressVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isGomamaVerified: freezed == isGomamaVerified
          ? _value.isGomamaVerified
          : isGomamaVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSingpassVerified: freezed == isSingpassVerified
          ? _value.isSingpassVerified
          : isSingpassVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdminVerified: freezed == isAdminVerified
          ? _value.isAdminVerified
          : isAdminVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      latestVerifySelfieFailCount: null == latestVerifySelfieFailCount
          ? _value.latestVerifySelfieFailCount!
          : latestVerifySelfieFailCount // ignore: cast_nullable_to_non_nullable
              as int,
      profiles: freezed == profiles
          ? _value.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<Profile>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PartialUserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$PartialUserImplCopyWith(
          _$PartialUserImpl value, $Res Function(_$PartialUserImpl) then) =
      __$$PartialUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? username,
      String? fullName,
      String? firstName,
      String? lastName,
      String? emailAddress,
      String? photoUrl,
      String? gender,
      String? mobileNumber,
      String? fullMobileNumber,
      String? authProvider,
      String? token,
      String? realtimeJwt,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      bool? isMobileNumberVerified,
      bool? isEmailAddressVerified,
      bool? isGomamaVerified,
      bool? isSingpassVerified,
      bool? isAdminVerified,
      int? latestVerifySelfieFailCount,
      List<Profile>? profiles});
}

/// @nodoc
class __$$PartialUserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$PartialUserImpl>
    implements _$$PartialUserImplCopyWith<$Res> {
  __$$PartialUserImplCopyWithImpl(
      _$PartialUserImpl _value, $Res Function(_$PartialUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? emailAddress = freezed,
    Object? photoUrl = freezed,
    Object? gender = freezed,
    Object? mobileNumber = freezed,
    Object? fullMobileNumber = freezed,
    Object? authProvider = freezed,
    Object? token = freezed,
    Object? realtimeJwt = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? isMobileNumberVerified = freezed,
    Object? isEmailAddressVerified = freezed,
    Object? isGomamaVerified = freezed,
    Object? isSingpassVerified = freezed,
    Object? isAdminVerified = freezed,
    Object? latestVerifySelfieFailCount = freezed,
    Object? profiles = freezed,
  }) {
    return _then(_$PartialUserImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      fullMobileNumber: freezed == fullMobileNumber
          ? _value.fullMobileNumber
          : fullMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      authProvider: freezed == authProvider
          ? _value.authProvider
          : authProvider // ignore: cast_nullable_to_non_nullable
              as String?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      isMobileNumberVerified: freezed == isMobileNumberVerified
          ? _value.isMobileNumberVerified
          : isMobileNumberVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailAddressVerified: freezed == isEmailAddressVerified
          ? _value.isEmailAddressVerified
          : isEmailAddressVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isGomamaVerified: freezed == isGomamaVerified
          ? _value.isGomamaVerified
          : isGomamaVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSingpassVerified: freezed == isSingpassVerified
          ? _value.isSingpassVerified
          : isSingpassVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdminVerified: freezed == isAdminVerified
          ? _value.isAdminVerified
          : isAdminVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      latestVerifySelfieFailCount: freezed == latestVerifySelfieFailCount
          ? _value.latestVerifySelfieFailCount
          : latestVerifySelfieFailCount // ignore: cast_nullable_to_non_nullable
              as int?,
      profiles: freezed == profiles
          ? _value._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<Profile>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$PartialUserImpl extends PartialUser {
  const _$PartialUserImpl(
      {this.id,
      this.username,
      this.fullName,
      this.firstName,
      this.lastName,
      this.emailAddress,
      this.photoUrl,
      this.gender,
      this.mobileNumber,
      this.fullMobileNumber,
      this.authProvider,
      this.token,
      this.realtimeJwt,
      this.birthday,
      final List<DateTime>? childrenBirthdays,
      this.isMobileNumberVerified,
      this.isEmailAddressVerified,
      this.isGomamaVerified,
      this.isSingpassVerified,
      this.isAdminVerified,
      this.latestVerifySelfieFailCount,
      final List<Profile>? profiles,
      final String? $type})
      : _childrenBirthdays = childrenBirthdays,
        _profiles = profiles,
        $type = $type ?? 'partial',
        super._();

  factory _$PartialUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$PartialUserImplFromJson(json);

  @override
  final String? id;
  @override
  final String? username;
  @override
  final String? fullName;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? emailAddress;
  @override
  final String? photoUrl;
  @override
  final String? gender;
  @override
  final String? mobileNumber;
  @override
  final String? fullMobileNumber;
  @override
  final String? authProvider;
  @override
  final String? token;
  @override
  final String? realtimeJwt;
  @override
  final DateTime? birthday;
  final List<DateTime>? _childrenBirthdays;
  @override
  List<DateTime>? get childrenBirthdays {
    final value = _childrenBirthdays;
    if (value == null) return null;
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isMobileNumberVerified;
  @override
  final bool? isEmailAddressVerified;
  @override
  final bool? isGomamaVerified;
  @override
  final bool? isSingpassVerified;
  @override
  final bool? isAdminVerified;
  @override
  final int? latestVerifySelfieFailCount;
  final List<Profile>? _profiles;
  @override
  List<Profile>? get profiles {
    final value = _profiles;
    if (value == null) return null;
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'User.partial(id: $id, username: $username, fullName: $fullName, firstName: $firstName, lastName: $lastName, emailAddress: $emailAddress, photoUrl: $photoUrl, gender: $gender, mobileNumber: $mobileNumber, fullMobileNumber: $fullMobileNumber, authProvider: $authProvider, token: $token, realtimeJwt: $realtimeJwt, birthday: $birthday, childrenBirthdays: $childrenBirthdays, isMobileNumberVerified: $isMobileNumberVerified, isEmailAddressVerified: $isEmailAddressVerified, isGomamaVerified: $isGomamaVerified, isSingpassVerified: $isSingpassVerified, isAdminVerified: $isAdminVerified, latestVerifySelfieFailCount: $latestVerifySelfieFailCount, profiles: $profiles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PartialUserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.fullMobileNumber, fullMobileNumber) ||
                other.fullMobileNumber == fullMobileNumber) &&
            (identical(other.authProvider, authProvider) ||
                other.authProvider == authProvider) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.realtimeJwt, realtimeJwt) ||
                other.realtimeJwt == realtimeJwt) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.isMobileNumberVerified, isMobileNumberVerified) ||
                other.isMobileNumberVerified == isMobileNumberVerified) &&
            (identical(other.isEmailAddressVerified, isEmailAddressVerified) ||
                other.isEmailAddressVerified == isEmailAddressVerified) &&
            (identical(other.isGomamaVerified, isGomamaVerified) ||
                other.isGomamaVerified == isGomamaVerified) &&
            (identical(other.isSingpassVerified, isSingpassVerified) ||
                other.isSingpassVerified == isSingpassVerified) &&
            (identical(other.isAdminVerified, isAdminVerified) ||
                other.isAdminVerified == isAdminVerified) &&
            (identical(other.latestVerifySelfieFailCount,
                    latestVerifySelfieFailCount) ||
                other.latestVerifySelfieFailCount ==
                    latestVerifySelfieFailCount) &&
            const DeepCollectionEquality().equals(other._profiles, _profiles));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        const DeepCollectionEquality().hash(_childrenBirthdays),
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        const DeepCollectionEquality().hash(_profiles)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PartialUserImplCopyWith<_$PartialUserImpl> get copyWith =>
      __$$PartialUserImplCopyWithImpl<_$PartialUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        partial,
    required TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedIn,
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedOut,
  }) {
    return partial(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult? Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
  }) {
    return partial?.call(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
    required TResult orElse(),
  }) {
    if (partial != null) {
      return partial(
          id,
          username,
          fullName,
          firstName,
          lastName,
          emailAddress,
          photoUrl,
          gender,
          mobileNumber,
          fullMobileNumber,
          authProvider,
          token,
          realtimeJwt,
          birthday,
          childrenBirthdays,
          isMobileNumberVerified,
          isEmailAddressVerified,
          isGomamaVerified,
          isSingpassVerified,
          isAdminVerified,
          latestVerifySelfieFailCount,
          profiles);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PartialUser value) partial,
    required TResult Function(SignedIn value) signedIn,
    required TResult Function(SignedOut value) signedOut,
  }) {
    return partial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PartialUser value)? partial,
    TResult? Function(SignedIn value)? signedIn,
    TResult? Function(SignedOut value)? signedOut,
  }) {
    return partial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PartialUser value)? partial,
    TResult Function(SignedIn value)? signedIn,
    TResult Function(SignedOut value)? signedOut,
    required TResult orElse(),
  }) {
    if (partial != null) {
      return partial(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$PartialUserImplToJson(
      this,
    );
  }
}

abstract class PartialUser extends User {
  const factory PartialUser(
      {final String? id,
      final String? username,
      final String? fullName,
      final String? firstName,
      final String? lastName,
      final String? emailAddress,
      final String? photoUrl,
      final String? gender,
      final String? mobileNumber,
      final String? fullMobileNumber,
      final String? authProvider,
      final String? token,
      final String? realtimeJwt,
      final DateTime? birthday,
      final List<DateTime>? childrenBirthdays,
      final bool? isMobileNumberVerified,
      final bool? isEmailAddressVerified,
      final bool? isGomamaVerified,
      final bool? isSingpassVerified,
      final bool? isAdminVerified,
      final int? latestVerifySelfieFailCount,
      final List<Profile>? profiles}) = _$PartialUserImpl;
  const PartialUser._() : super._();

  factory PartialUser.fromJson(Map<String, dynamic> json) =
      _$PartialUserImpl.fromJson;

  @override
  String? get id;
  @override
  String? get username;
  @override
  String? get fullName;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get emailAddress;
  @override
  String? get photoUrl;
  @override
  String? get gender;
  @override
  String? get mobileNumber;
  @override
  String? get fullMobileNumber;
  @override
  String? get authProvider;
  @override
  String? get token;
  @override
  String? get realtimeJwt;
  @override
  DateTime? get birthday;
  @override
  List<DateTime>? get childrenBirthdays;
  @override
  bool? get isMobileNumberVerified;
  @override
  bool? get isEmailAddressVerified;
  @override
  bool? get isGomamaVerified;
  @override
  bool? get isSingpassVerified;
  @override
  bool? get isAdminVerified;
  @override
  int? get latestVerifySelfieFailCount;
  @override
  List<Profile>? get profiles;
  @override
  @JsonKey(ignore: true)
  _$$PartialUserImplCopyWith<_$PartialUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignedInImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$SignedInImplCopyWith(
          _$SignedInImpl value, $Res Function(_$SignedInImpl) then) =
      __$$SignedInImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? username,
      String? fullName,
      String? firstName,
      String? lastName,
      String? emailAddress,
      String? photoUrl,
      String? gender,
      String? mobileNumber,
      String? fullMobileNumber,
      String? authProvider,
      String token,
      String? realtimeJwt,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      bool? isMobileNumberVerified,
      bool? isEmailAddressVerified,
      bool? isGomamaVerified,
      bool? isSingpassVerified,
      bool? isAdminVerified,
      int latestVerifySelfieFailCount,
      List<Profile>? profiles});
}

/// @nodoc
class __$$SignedInImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$SignedInImpl>
    implements _$$SignedInImplCopyWith<$Res> {
  __$$SignedInImplCopyWithImpl(
      _$SignedInImpl _value, $Res Function(_$SignedInImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? emailAddress = freezed,
    Object? photoUrl = freezed,
    Object? gender = freezed,
    Object? mobileNumber = freezed,
    Object? fullMobileNumber = freezed,
    Object? authProvider = freezed,
    Object? token = null,
    Object? realtimeJwt = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? isMobileNumberVerified = freezed,
    Object? isEmailAddressVerified = freezed,
    Object? isGomamaVerified = freezed,
    Object? isSingpassVerified = freezed,
    Object? isAdminVerified = freezed,
    Object? latestVerifySelfieFailCount = null,
    Object? profiles = freezed,
  }) {
    return _then(_$SignedInImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      fullMobileNumber: freezed == fullMobileNumber
          ? _value.fullMobileNumber
          : fullMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      authProvider: freezed == authProvider
          ? _value.authProvider
          : authProvider // ignore: cast_nullable_to_non_nullable
              as String?,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      isMobileNumberVerified: freezed == isMobileNumberVerified
          ? _value.isMobileNumberVerified
          : isMobileNumberVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailAddressVerified: freezed == isEmailAddressVerified
          ? _value.isEmailAddressVerified
          : isEmailAddressVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isGomamaVerified: freezed == isGomamaVerified
          ? _value.isGomamaVerified
          : isGomamaVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSingpassVerified: freezed == isSingpassVerified
          ? _value.isSingpassVerified
          : isSingpassVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdminVerified: freezed == isAdminVerified
          ? _value.isAdminVerified
          : isAdminVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      latestVerifySelfieFailCount: null == latestVerifySelfieFailCount
          ? _value.latestVerifySelfieFailCount
          : latestVerifySelfieFailCount // ignore: cast_nullable_to_non_nullable
              as int,
      profiles: freezed == profiles
          ? _value._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<Profile>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$SignedInImpl extends SignedIn {
  const _$SignedInImpl(
      {required this.id,
      this.username,
      this.fullName,
      this.firstName,
      this.lastName,
      this.emailAddress,
      this.photoUrl,
      this.gender,
      this.mobileNumber,
      this.fullMobileNumber,
      this.authProvider,
      required this.token,
      this.realtimeJwt,
      this.birthday,
      final List<DateTime>? childrenBirthdays,
      this.isMobileNumberVerified,
      this.isEmailAddressVerified,
      this.isGomamaVerified,
      this.isSingpassVerified,
      this.isAdminVerified,
      required this.latestVerifySelfieFailCount,
      final List<Profile>? profiles,
      final String? $type})
      : _childrenBirthdays = childrenBirthdays,
        _profiles = profiles,
        $type = $type ?? 'signedIn',
        super._();

  factory _$SignedInImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignedInImplFromJson(json);

  @override
  final String id;
  @override
  final String? username;
  @override
  final String? fullName;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? emailAddress;
  @override
  final String? photoUrl;
  @override
  final String? gender;
  @override
  final String? mobileNumber;
  @override
  final String? fullMobileNumber;
  @override
  final String? authProvider;
  @override
  final String token;
  @override
  final String? realtimeJwt;
  @override
  final DateTime? birthday;
  final List<DateTime>? _childrenBirthdays;
  @override
  List<DateTime>? get childrenBirthdays {
    final value = _childrenBirthdays;
    if (value == null) return null;
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isMobileNumberVerified;
  @override
  final bool? isEmailAddressVerified;
  @override
  final bool? isGomamaVerified;
  @override
  final bool? isSingpassVerified;
  @override
  final bool? isAdminVerified;
  @override
  final int latestVerifySelfieFailCount;
  final List<Profile>? _profiles;
  @override
  List<Profile>? get profiles {
    final value = _profiles;
    if (value == null) return null;
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'User.signedIn(id: $id, username: $username, fullName: $fullName, firstName: $firstName, lastName: $lastName, emailAddress: $emailAddress, photoUrl: $photoUrl, gender: $gender, mobileNumber: $mobileNumber, fullMobileNumber: $fullMobileNumber, authProvider: $authProvider, token: $token, realtimeJwt: $realtimeJwt, birthday: $birthday, childrenBirthdays: $childrenBirthdays, isMobileNumberVerified: $isMobileNumberVerified, isEmailAddressVerified: $isEmailAddressVerified, isGomamaVerified: $isGomamaVerified, isSingpassVerified: $isSingpassVerified, isAdminVerified: $isAdminVerified, latestVerifySelfieFailCount: $latestVerifySelfieFailCount, profiles: $profiles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignedInImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.fullMobileNumber, fullMobileNumber) ||
                other.fullMobileNumber == fullMobileNumber) &&
            (identical(other.authProvider, authProvider) ||
                other.authProvider == authProvider) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.realtimeJwt, realtimeJwt) ||
                other.realtimeJwt == realtimeJwt) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.isMobileNumberVerified, isMobileNumberVerified) ||
                other.isMobileNumberVerified == isMobileNumberVerified) &&
            (identical(other.isEmailAddressVerified, isEmailAddressVerified) ||
                other.isEmailAddressVerified == isEmailAddressVerified) &&
            (identical(other.isGomamaVerified, isGomamaVerified) ||
                other.isGomamaVerified == isGomamaVerified) &&
            (identical(other.isSingpassVerified, isSingpassVerified) ||
                other.isSingpassVerified == isSingpassVerified) &&
            (identical(other.isAdminVerified, isAdminVerified) ||
                other.isAdminVerified == isAdminVerified) &&
            (identical(other.latestVerifySelfieFailCount,
                    latestVerifySelfieFailCount) ||
                other.latestVerifySelfieFailCount ==
                    latestVerifySelfieFailCount) &&
            const DeepCollectionEquality().equals(other._profiles, _profiles));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        const DeepCollectionEquality().hash(_childrenBirthdays),
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        const DeepCollectionEquality().hash(_profiles)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SignedInImplCopyWith<_$SignedInImpl> get copyWith =>
      __$$SignedInImplCopyWithImpl<_$SignedInImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        partial,
    required TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedIn,
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedOut,
  }) {
    return signedIn(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult? Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
  }) {
    return signedIn?.call(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
    required TResult orElse(),
  }) {
    if (signedIn != null) {
      return signedIn(
          id,
          username,
          fullName,
          firstName,
          lastName,
          emailAddress,
          photoUrl,
          gender,
          mobileNumber,
          fullMobileNumber,
          authProvider,
          token,
          realtimeJwt,
          birthday,
          childrenBirthdays,
          isMobileNumberVerified,
          isEmailAddressVerified,
          isGomamaVerified,
          isSingpassVerified,
          isAdminVerified,
          latestVerifySelfieFailCount,
          profiles);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PartialUser value) partial,
    required TResult Function(SignedIn value) signedIn,
    required TResult Function(SignedOut value) signedOut,
  }) {
    return signedIn(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PartialUser value)? partial,
    TResult? Function(SignedIn value)? signedIn,
    TResult? Function(SignedOut value)? signedOut,
  }) {
    return signedIn?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PartialUser value)? partial,
    TResult Function(SignedIn value)? signedIn,
    TResult Function(SignedOut value)? signedOut,
    required TResult orElse(),
  }) {
    if (signedIn != null) {
      return signedIn(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$SignedInImplToJson(
      this,
    );
  }
}

abstract class SignedIn extends User {
  const factory SignedIn(
      {required final String id,
      final String? username,
      final String? fullName,
      final String? firstName,
      final String? lastName,
      final String? emailAddress,
      final String? photoUrl,
      final String? gender,
      final String? mobileNumber,
      final String? fullMobileNumber,
      final String? authProvider,
      required final String token,
      final String? realtimeJwt,
      final DateTime? birthday,
      final List<DateTime>? childrenBirthdays,
      final bool? isMobileNumberVerified,
      final bool? isEmailAddressVerified,
      final bool? isGomamaVerified,
      final bool? isSingpassVerified,
      final bool? isAdminVerified,
      required final int latestVerifySelfieFailCount,
      final List<Profile>? profiles}) = _$SignedInImpl;
  const SignedIn._() : super._();

  factory SignedIn.fromJson(Map<String, dynamic> json) =
      _$SignedInImpl.fromJson;

  @override
  String get id;
  @override
  String? get username;
  @override
  String? get fullName;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get emailAddress;
  @override
  String? get photoUrl;
  @override
  String? get gender;
  @override
  String? get mobileNumber;
  @override
  String? get fullMobileNumber;
  @override
  String? get authProvider;
  @override
  String get token;
  @override
  String? get realtimeJwt;
  @override
  DateTime? get birthday;
  @override
  List<DateTime>? get childrenBirthdays;
  @override
  bool? get isMobileNumberVerified;
  @override
  bool? get isEmailAddressVerified;
  @override
  bool? get isGomamaVerified;
  @override
  bool? get isSingpassVerified;
  @override
  bool? get isAdminVerified;
  @override
  int get latestVerifySelfieFailCount;
  @override
  List<Profile>? get profiles;
  @override
  @JsonKey(ignore: true)
  _$$SignedInImplCopyWith<_$SignedInImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignedOutImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$SignedOutImplCopyWith(
          _$SignedOutImpl value, $Res Function(_$SignedOutImpl) then) =
      __$$SignedOutImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? username,
      String? fullName,
      String? firstName,
      String? lastName,
      String? emailAddress,
      String? photoUrl,
      String? gender,
      String? mobileNumber,
      String? fullMobileNumber,
      String? authProvider,
      String? token,
      String? realtimeJwt,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      bool? isMobileNumberVerified,
      bool? isEmailAddressVerified,
      bool? isGomamaVerified,
      bool? isSingpassVerified,
      bool? isAdminVerified,
      int? latestVerifySelfieFailCount,
      List<Profile>? profiles});
}

/// @nodoc
class __$$SignedOutImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$SignedOutImpl>
    implements _$$SignedOutImplCopyWith<$Res> {
  __$$SignedOutImplCopyWithImpl(
      _$SignedOutImpl _value, $Res Function(_$SignedOutImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? emailAddress = freezed,
    Object? photoUrl = freezed,
    Object? gender = freezed,
    Object? mobileNumber = freezed,
    Object? fullMobileNumber = freezed,
    Object? authProvider = freezed,
    Object? token = freezed,
    Object? realtimeJwt = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? isMobileNumberVerified = freezed,
    Object? isEmailAddressVerified = freezed,
    Object? isGomamaVerified = freezed,
    Object? isSingpassVerified = freezed,
    Object? isAdminVerified = freezed,
    Object? latestVerifySelfieFailCount = freezed,
    Object? profiles = freezed,
  }) {
    return _then(_$SignedOutImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      emailAddress: freezed == emailAddress
          ? _value.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      fullMobileNumber: freezed == fullMobileNumber
          ? _value.fullMobileNumber
          : fullMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      authProvider: freezed == authProvider
          ? _value.authProvider
          : authProvider // ignore: cast_nullable_to_non_nullable
              as String?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      isMobileNumberVerified: freezed == isMobileNumberVerified
          ? _value.isMobileNumberVerified
          : isMobileNumberVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailAddressVerified: freezed == isEmailAddressVerified
          ? _value.isEmailAddressVerified
          : isEmailAddressVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isGomamaVerified: freezed == isGomamaVerified
          ? _value.isGomamaVerified
          : isGomamaVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSingpassVerified: freezed == isSingpassVerified
          ? _value.isSingpassVerified
          : isSingpassVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdminVerified: freezed == isAdminVerified
          ? _value.isAdminVerified
          : isAdminVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      latestVerifySelfieFailCount: freezed == latestVerifySelfieFailCount
          ? _value.latestVerifySelfieFailCount
          : latestVerifySelfieFailCount // ignore: cast_nullable_to_non_nullable
              as int?,
      profiles: freezed == profiles
          ? _value._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<Profile>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$SignedOutImpl extends SignedOut {
  const _$SignedOutImpl(
      {this.id,
      this.username,
      this.fullName,
      this.firstName,
      this.lastName,
      this.emailAddress,
      this.photoUrl,
      this.gender,
      this.mobileNumber,
      this.fullMobileNumber,
      this.authProvider,
      this.token,
      this.realtimeJwt,
      this.birthday,
      final List<DateTime>? childrenBirthdays,
      this.isMobileNumberVerified,
      this.isEmailAddressVerified,
      this.isGomamaVerified,
      this.isSingpassVerified,
      this.isAdminVerified,
      this.latestVerifySelfieFailCount,
      final List<Profile>? profiles,
      final String? $type})
      : _childrenBirthdays = childrenBirthdays,
        _profiles = profiles,
        $type = $type ?? 'signedOut',
        super._();

  factory _$SignedOutImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignedOutImplFromJson(json);

  @override
  final String? id;
  @override
  final String? username;
  @override
  final String? fullName;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? emailAddress;
  @override
  final String? photoUrl;
  @override
  final String? gender;
  @override
  final String? mobileNumber;
  @override
  final String? fullMobileNumber;
  @override
  final String? authProvider;
  @override
  final String? token;
  @override
  final String? realtimeJwt;
  @override
  final DateTime? birthday;
  final List<DateTime>? _childrenBirthdays;
  @override
  List<DateTime>? get childrenBirthdays {
    final value = _childrenBirthdays;
    if (value == null) return null;
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isMobileNumberVerified;
  @override
  final bool? isEmailAddressVerified;
  @override
  final bool? isGomamaVerified;
  @override
  final bool? isSingpassVerified;
  @override
  final bool? isAdminVerified;
  @override
  final int? latestVerifySelfieFailCount;
  final List<Profile>? _profiles;
  @override
  List<Profile>? get profiles {
    final value = _profiles;
    if (value == null) return null;
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'User.signedOut(id: $id, username: $username, fullName: $fullName, firstName: $firstName, lastName: $lastName, emailAddress: $emailAddress, photoUrl: $photoUrl, gender: $gender, mobileNumber: $mobileNumber, fullMobileNumber: $fullMobileNumber, authProvider: $authProvider, token: $token, realtimeJwt: $realtimeJwt, birthday: $birthday, childrenBirthdays: $childrenBirthdays, isMobileNumberVerified: $isMobileNumberVerified, isEmailAddressVerified: $isEmailAddressVerified, isGomamaVerified: $isGomamaVerified, isSingpassVerified: $isSingpassVerified, isAdminVerified: $isAdminVerified, latestVerifySelfieFailCount: $latestVerifySelfieFailCount, profiles: $profiles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignedOutImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.fullMobileNumber, fullMobileNumber) ||
                other.fullMobileNumber == fullMobileNumber) &&
            (identical(other.authProvider, authProvider) ||
                other.authProvider == authProvider) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.realtimeJwt, realtimeJwt) ||
                other.realtimeJwt == realtimeJwt) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.isMobileNumberVerified, isMobileNumberVerified) ||
                other.isMobileNumberVerified == isMobileNumberVerified) &&
            (identical(other.isEmailAddressVerified, isEmailAddressVerified) ||
                other.isEmailAddressVerified == isEmailAddressVerified) &&
            (identical(other.isGomamaVerified, isGomamaVerified) ||
                other.isGomamaVerified == isGomamaVerified) &&
            (identical(other.isSingpassVerified, isSingpassVerified) ||
                other.isSingpassVerified == isSingpassVerified) &&
            (identical(other.isAdminVerified, isAdminVerified) ||
                other.isAdminVerified == isAdminVerified) &&
            (identical(other.latestVerifySelfieFailCount,
                    latestVerifySelfieFailCount) ||
                other.latestVerifySelfieFailCount ==
                    latestVerifySelfieFailCount) &&
            const DeepCollectionEquality().equals(other._profiles, _profiles));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        const DeepCollectionEquality().hash(_childrenBirthdays),
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        const DeepCollectionEquality().hash(_profiles)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SignedOutImplCopyWith<_$SignedOutImpl> get copyWith =>
      __$$SignedOutImplCopyWithImpl<_$SignedOutImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        partial,
    required TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedIn,
    required TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)
        signedOut,
  }) {
    return signedOut(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult? Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult? Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
  }) {
    return signedOut?.call(
        id,
        username,
        fullName,
        firstName,
        lastName,
        emailAddress,
        photoUrl,
        gender,
        mobileNumber,
        fullMobileNumber,
        authProvider,
        token,
        realtimeJwt,
        birthday,
        childrenBirthdays,
        isMobileNumberVerified,
        isEmailAddressVerified,
        isGomamaVerified,
        isSingpassVerified,
        isAdminVerified,
        latestVerifySelfieFailCount,
        profiles);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        partial,
    TResult Function(
            String id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedIn,
    TResult Function(
            String? id,
            String? username,
            String? fullName,
            String? firstName,
            String? lastName,
            String? emailAddress,
            String? photoUrl,
            String? gender,
            String? mobileNumber,
            String? fullMobileNumber,
            String? authProvider,
            String? token,
            String? realtimeJwt,
            DateTime? birthday,
            List<DateTime>? childrenBirthdays,
            bool? isMobileNumberVerified,
            bool? isEmailAddressVerified,
            bool? isGomamaVerified,
            bool? isSingpassVerified,
            bool? isAdminVerified,
            int? latestVerifySelfieFailCount,
            List<Profile>? profiles)?
        signedOut,
    required TResult orElse(),
  }) {
    if (signedOut != null) {
      return signedOut(
          id,
          username,
          fullName,
          firstName,
          lastName,
          emailAddress,
          photoUrl,
          gender,
          mobileNumber,
          fullMobileNumber,
          authProvider,
          token,
          realtimeJwt,
          birthday,
          childrenBirthdays,
          isMobileNumberVerified,
          isEmailAddressVerified,
          isGomamaVerified,
          isSingpassVerified,
          isAdminVerified,
          latestVerifySelfieFailCount,
          profiles);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PartialUser value) partial,
    required TResult Function(SignedIn value) signedIn,
    required TResult Function(SignedOut value) signedOut,
  }) {
    return signedOut(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PartialUser value)? partial,
    TResult? Function(SignedIn value)? signedIn,
    TResult? Function(SignedOut value)? signedOut,
  }) {
    return signedOut?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PartialUser value)? partial,
    TResult Function(SignedIn value)? signedIn,
    TResult Function(SignedOut value)? signedOut,
    required TResult orElse(),
  }) {
    if (signedOut != null) {
      return signedOut(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$SignedOutImplToJson(
      this,
    );
  }
}

abstract class SignedOut extends User {
  const factory SignedOut(
      {final String? id,
      final String? username,
      final String? fullName,
      final String? firstName,
      final String? lastName,
      final String? emailAddress,
      final String? photoUrl,
      final String? gender,
      final String? mobileNumber,
      final String? fullMobileNumber,
      final String? authProvider,
      final String? token,
      final String? realtimeJwt,
      final DateTime? birthday,
      final List<DateTime>? childrenBirthdays,
      final bool? isMobileNumberVerified,
      final bool? isEmailAddressVerified,
      final bool? isGomamaVerified,
      final bool? isSingpassVerified,
      final bool? isAdminVerified,
      final int? latestVerifySelfieFailCount,
      final List<Profile>? profiles}) = _$SignedOutImpl;
  const SignedOut._() : super._();

  factory SignedOut.fromJson(Map<String, dynamic> json) =
      _$SignedOutImpl.fromJson;

  @override
  String? get id;
  @override
  String? get username;
  @override
  String? get fullName;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get emailAddress;
  @override
  String? get photoUrl;
  @override
  String? get gender;
  @override
  String? get mobileNumber;
  @override
  String? get fullMobileNumber;
  @override
  String? get authProvider;
  @override
  String? get token;
  @override
  String? get realtimeJwt;
  @override
  DateTime? get birthday;
  @override
  List<DateTime>? get childrenBirthdays;
  @override
  bool? get isMobileNumberVerified;
  @override
  bool? get isEmailAddressVerified;
  @override
  bool? get isGomamaVerified;
  @override
  bool? get isSingpassVerified;
  @override
  bool? get isAdminVerified;
  @override
  int? get latestVerifySelfieFailCount;
  @override
  List<Profile>? get profiles;
  @override
  @JsonKey(ignore: true)
  _$$SignedOutImplCopyWith<_$SignedOutImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateUserInput _$UpdateUserInputFromJson(Map<String, dynamic> json) {
  return _UpdateUserInput.fromJson(json);
}

/// @nodoc
mixin _$UpdateUserInput {
  String? get username => throw _privateConstructorUsedError;
  double? get lat => throw _privateConstructorUsedError;
  double? get lon => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false)
  File? get profileImage => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  DateTime? get birthday => throw _privateConstructorUsedError;
  List<DateTime>? get childrenBirthdays => throw _privateConstructorUsedError;
  String? get mobileNumber => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;
  String? get nationality => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get resetPasswordOtp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UpdateUserInputCopyWith<UpdateUserInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateUserInputCopyWith<$Res> {
  factory $UpdateUserInputCopyWith(
          UpdateUserInput value, $Res Function(UpdateUserInput) then) =
      _$UpdateUserInputCopyWithImpl<$Res, UpdateUserInput>;
  @useResult
  $Res call(
      {String? username,
      double? lat,
      double? lon,
      String? firstName,
      String? lastName,
      @JsonKey(includeFromJson: false) File? profileImage,
      String? gender,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      String? mobileNumber,
      String? companyName,
      String? nationality,
      String? password,
      String? resetPasswordOtp});
}

/// @nodoc
class _$UpdateUserInputCopyWithImpl<$Res, $Val extends UpdateUserInput>
    implements $UpdateUserInputCopyWith<$Res> {
  _$UpdateUserInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? lat = freezed,
    Object? lon = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? profileImage = freezed,
    Object? gender = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? mobileNumber = freezed,
    Object? companyName = freezed,
    Object? nationality = freezed,
    Object? password = freezed,
    Object? resetPasswordOtp = freezed,
  }) {
    return _then(_value.copyWith(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      lon: freezed == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as File?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value.childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      nationality: freezed == nationality
          ? _value.nationality
          : nationality // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      resetPasswordOtp: freezed == resetPasswordOtp
          ? _value.resetPasswordOtp
          : resetPasswordOtp // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateUserInputImplCopyWith<$Res>
    implements $UpdateUserInputCopyWith<$Res> {
  factory _$$UpdateUserInputImplCopyWith(_$UpdateUserInputImpl value,
          $Res Function(_$UpdateUserInputImpl) then) =
      __$$UpdateUserInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? username,
      double? lat,
      double? lon,
      String? firstName,
      String? lastName,
      @JsonKey(includeFromJson: false) File? profileImage,
      String? gender,
      DateTime? birthday,
      List<DateTime>? childrenBirthdays,
      String? mobileNumber,
      String? companyName,
      String? nationality,
      String? password,
      String? resetPasswordOtp});
}

/// @nodoc
class __$$UpdateUserInputImplCopyWithImpl<$Res>
    extends _$UpdateUserInputCopyWithImpl<$Res, _$UpdateUserInputImpl>
    implements _$$UpdateUserInputImplCopyWith<$Res> {
  __$$UpdateUserInputImplCopyWithImpl(
      _$UpdateUserInputImpl _value, $Res Function(_$UpdateUserInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = freezed,
    Object? lat = freezed,
    Object? lon = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? profileImage = freezed,
    Object? gender = freezed,
    Object? birthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? mobileNumber = freezed,
    Object? companyName = freezed,
    Object? nationality = freezed,
    Object? password = freezed,
    Object? resetPasswordOtp = freezed,
  }) {
    return _then(_$UpdateUserInputImpl(
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      lon: freezed == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as File?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      nationality: freezed == nationality
          ? _value.nationality
          : nationality // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      resetPasswordOtp: freezed == resetPasswordOtp
          ? _value.resetPasswordOtp
          : resetPasswordOtp // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$UpdateUserInputImpl implements _UpdateUserInput {
  _$UpdateUserInputImpl(
      {this.username,
      this.lat,
      this.lon,
      this.firstName,
      this.lastName,
      @JsonKey(includeFromJson: false) this.profileImage,
      this.gender,
      this.birthday,
      final List<DateTime>? childrenBirthdays,
      this.mobileNumber,
      this.companyName,
      this.nationality,
      this.password,
      this.resetPasswordOtp})
      : _childrenBirthdays = childrenBirthdays;

  factory _$UpdateUserInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateUserInputImplFromJson(json);

  @override
  final String? username;
  @override
  final double? lat;
  @override
  final double? lon;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  @JsonKey(includeFromJson: false)
  final File? profileImage;
  @override
  final String? gender;
  @override
  final DateTime? birthday;
  final List<DateTime>? _childrenBirthdays;
  @override
  List<DateTime>? get childrenBirthdays {
    final value = _childrenBirthdays;
    if (value == null) return null;
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? mobileNumber;
  @override
  final String? companyName;
  @override
  final String? nationality;
  @override
  final String? password;
  @override
  final String? resetPasswordOtp;

  @override
  String toString() {
    return 'UpdateUserInput(username: $username, lat: $lat, lon: $lon, firstName: $firstName, lastName: $lastName, profileImage: $profileImage, gender: $gender, birthday: $birthday, childrenBirthdays: $childrenBirthdays, mobileNumber: $mobileNumber, companyName: $companyName, nationality: $nationality, password: $password, resetPasswordOtp: $resetPasswordOtp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateUserInputImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.nationality, nationality) ||
                other.nationality == nationality) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.resetPasswordOtp, resetPasswordOtp) ||
                other.resetPasswordOtp == resetPasswordOtp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      username,
      lat,
      lon,
      firstName,
      lastName,
      profileImage,
      gender,
      birthday,
      const DeepCollectionEquality().hash(_childrenBirthdays),
      mobileNumber,
      companyName,
      nationality,
      password,
      resetPasswordOtp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateUserInputImplCopyWith<_$UpdateUserInputImpl> get copyWith =>
      __$$UpdateUserInputImplCopyWithImpl<_$UpdateUserInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateUserInputImplToJson(
      this,
    );
  }
}

abstract class _UpdateUserInput implements UpdateUserInput {
  factory _UpdateUserInput(
      {final String? username,
      final double? lat,
      final double? lon,
      final String? firstName,
      final String? lastName,
      @JsonKey(includeFromJson: false) final File? profileImage,
      final String? gender,
      final DateTime? birthday,
      final List<DateTime>? childrenBirthdays,
      final String? mobileNumber,
      final String? companyName,
      final String? nationality,
      final String? password,
      final String? resetPasswordOtp}) = _$UpdateUserInputImpl;

  factory _UpdateUserInput.fromJson(Map<String, dynamic> json) =
      _$UpdateUserInputImpl.fromJson;

  @override
  String? get username;
  @override
  double? get lat;
  @override
  double? get lon;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  @JsonKey(includeFromJson: false)
  File? get profileImage;
  @override
  String? get gender;
  @override
  DateTime? get birthday;
  @override
  List<DateTime>? get childrenBirthdays;
  @override
  String? get mobileNumber;
  @override
  String? get companyName;
  @override
  String? get nationality;
  @override
  String? get password;
  @override
  String? get resetPasswordOtp;
  @override
  @JsonKey(ignore: true)
  _$$UpdateUserInputImplCopyWith<_$UpdateUserInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateUserInput _$CreateUserInputFromJson(Map<String, dynamic> json) {
  return _CreateUserInput.fromJson(json);
}

/// @nodoc
mixin _$CreateUserInput {
  String get username => throw _privateConstructorUsedError;
  String? get countryDialCode => throw _privateConstructorUsedError;
  String? get newMobileNumber => throw _privateConstructorUsedError;
  String? get newEmail => throw _privateConstructorUsedError;
  double? get longitude => throw _privateConstructorUsedError;
  double? get latitude => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateUserInputCopyWith<CreateUserInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateUserInputCopyWith<$Res> {
  factory $CreateUserInputCopyWith(
          CreateUserInput value, $Res Function(CreateUserInput) then) =
      _$CreateUserInputCopyWithImpl<$Res, CreateUserInput>;
  @useResult
  $Res call(
      {String username,
      String? countryDialCode,
      String? newMobileNumber,
      String? newEmail,
      double? longitude,
      double? latitude});
}

/// @nodoc
class _$CreateUserInputCopyWithImpl<$Res, $Val extends CreateUserInput>
    implements $CreateUserInputCopyWith<$Res> {
  _$CreateUserInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? countryDialCode = freezed,
    Object? newMobileNumber = freezed,
    Object? newEmail = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
  }) {
    return _then(_value.copyWith(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      newMobileNumber: freezed == newMobileNumber
          ? _value.newMobileNumber
          : newMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      newEmail: freezed == newEmail
          ? _value.newEmail
          : newEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateUserInputImplCopyWith<$Res>
    implements $CreateUserInputCopyWith<$Res> {
  factory _$$CreateUserInputImplCopyWith(_$CreateUserInputImpl value,
          $Res Function(_$CreateUserInputImpl) then) =
      __$$CreateUserInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String username,
      String? countryDialCode,
      String? newMobileNumber,
      String? newEmail,
      double? longitude,
      double? latitude});
}

/// @nodoc
class __$$CreateUserInputImplCopyWithImpl<$Res>
    extends _$CreateUserInputCopyWithImpl<$Res, _$CreateUserInputImpl>
    implements _$$CreateUserInputImplCopyWith<$Res> {
  __$$CreateUserInputImplCopyWithImpl(
      _$CreateUserInputImpl _value, $Res Function(_$CreateUserInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? countryDialCode = freezed,
    Object? newMobileNumber = freezed,
    Object? newEmail = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
  }) {
    return _then(_$CreateUserInputImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      countryDialCode: freezed == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String?,
      newMobileNumber: freezed == newMobileNumber
          ? _value.newMobileNumber
          : newMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      newEmail: freezed == newEmail
          ? _value.newEmail
          : newEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateUserInputImpl implements _CreateUserInput {
  _$CreateUserInputImpl(
      {required this.username,
      this.countryDialCode,
      this.newMobileNumber,
      this.newEmail,
      this.longitude,
      this.latitude})
      : assert(
            newEmail != null ||
                (countryDialCode != null && newMobileNumber != null),
            'Either newEmail or both countryDialCode and newMobileNumber must be provided');

  factory _$CreateUserInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateUserInputImplFromJson(json);

  @override
  final String username;
  @override
  final String? countryDialCode;
  @override
  final String? newMobileNumber;
  @override
  final String? newEmail;
  @override
  final double? longitude;
  @override
  final double? latitude;

  @override
  String toString() {
    return 'CreateUserInput(username: $username, countryDialCode: $countryDialCode, newMobileNumber: $newMobileNumber, newEmail: $newEmail, longitude: $longitude, latitude: $latitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateUserInputImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.countryDialCode, countryDialCode) ||
                other.countryDialCode == countryDialCode) &&
            (identical(other.newMobileNumber, newMobileNumber) ||
                other.newMobileNumber == newMobileNumber) &&
            (identical(other.newEmail, newEmail) ||
                other.newEmail == newEmail) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, username, countryDialCode,
      newMobileNumber, newEmail, longitude, latitude);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateUserInputImplCopyWith<_$CreateUserInputImpl> get copyWith =>
      __$$CreateUserInputImplCopyWithImpl<_$CreateUserInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateUserInputImplToJson(
      this,
    );
  }
}

abstract class _CreateUserInput implements CreateUserInput {
  factory _CreateUserInput(
      {required final String username,
      final String? countryDialCode,
      final String? newMobileNumber,
      final String? newEmail,
      final double? longitude,
      final double? latitude}) = _$CreateUserInputImpl;

  factory _CreateUserInput.fromJson(Map<String, dynamic> json) =
      _$CreateUserInputImpl.fromJson;

  @override
  String get username;
  @override
  String? get countryDialCode;
  @override
  String? get newMobileNumber;
  @override
  String? get newEmail;
  @override
  double? get longitude;
  @override
  double? get latitude;
  @override
  @JsonKey(ignore: true)
  _$$CreateUserInputImplCopyWith<_$CreateUserInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
