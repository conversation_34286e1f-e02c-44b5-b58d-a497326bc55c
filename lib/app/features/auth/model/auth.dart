import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/user.dart';

part 'auth.freezed.dart';
part 'auth.g.dart';

@freezed
class AuthResponse with _$AuthResponse {
  factory AuthResponse({
    required PartialUser user,
    required AuthToken token,
  }) = _AuthResponse;

  factory AuthResponse.fromJson(Json json) => _$AuthResponseFromJson(json);
}

@freezed
class AuthToken with _$AuthToken {
  @CustomDateTimeConverter()
  factory AuthToken({
    required String token,
    String? realtimeJwt,
    DateTime? expiredAt,
  }) = _AuthToken;

  factory AuthToken.fromJson(Json json) => _$AuthTokenFromJson(json);
}

@freezed
class AuthInput with _$AuthInput {
  factory AuthInput({
    String? loginAccount,
    String? password,
    String? otp,
    // google & facebook login
    String? accessToken,
    // apple login
    String? authorizationCode,
    String? lastName,
    String? firstName,
    String? email,
  }) = _AuthInput;

  factory AuthInput.fromJson(Json json) => _$AuthInputFromJson(json);
}

@freezed
class RegisterInput with _$RegisterInput {
  factory RegisterInput({
    String? email,
    String? username,
    String? password,
    String? passwordConfirmation,
    String? authCode,
  }) = _RegisterInput;

  factory RegisterInput.fromJson(Json json) => _$RegisterInputFromJson(json);
}

@freezed
class VerifyCodeInput with _$VerifyCodeInput {
  factory VerifyCodeInput({
    String? email,
    String? code,
  }) = _VerifyCodeInput;

  factory VerifyCodeInput.fromJson(Json json) =>
      _$VerifyCodeInputFromJson(json);
}

@freezed
class ChangePasswordInput with _$ChangePasswordInput {
  factory ChangePasswordInput({
    String? password,
    String? newPassword,
    String? newPasswordConfirmation,
  }) = _ChangePasswordInput;

  factory ChangePasswordInput.fromJson(Json json) =>
      _$ChangePasswordInputFromJson(json);
}

@freezed
class ForgotPasswordInput with _$ForgotPasswordInput {
  factory ForgotPasswordInput({
    String? email,
    String? password,
    String? passwordConfirmation,
    String? authCode,
  }) = _ForgotPasswordInput;

  factory ForgotPasswordInput.fromJson(Json json) =>
      _$ForgotPasswordInputFromJson(json);
}

// Exceptions
enum LoginException implements Exception {
  appTrackingTransparencyNotAuthorized,
}
