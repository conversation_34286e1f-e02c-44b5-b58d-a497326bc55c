// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthResponseImpl _$$AuthResponseImplFromJson(Map<String, dynamic> json) =>
    _$AuthResponseImpl(
      user: PartialUser.fromJson(json['user'] as Map<String, dynamic>),
      token: AuthToken.fromJson(json['token'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AuthResponseImplToJson(_$AuthResponseImpl instance) =>
    <String, dynamic>{
      'user': instance.user.toJson(),
      'token': instance.token.toJson(),
    };

_$AuthTokenImpl _$$AuthTokenImplFromJson(Map<String, dynamic> json) =>
    _$AuthTokenImpl(
      token: json['token'] as String,
      realtimeJwt: json['realtime_jwt'] as String?,
      expiredAt: const CustomDateTimeConverter()
          .fromJson(json['expired_at'] as String?),
    );

Map<String, dynamic> _$$AuthTokenImplToJson(_$AuthTokenImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'realtime_jwt': instance.realtimeJwt,
      'expired_at': const CustomDateTimeConverter().toJson(instance.expiredAt),
    };

_$AuthInputImpl _$$AuthInputImplFromJson(Map<String, dynamic> json) =>
    _$AuthInputImpl(
      loginAccount: json['login_account'] as String?,
      password: json['password'] as String?,
      otp: json['otp'] as String?,
      accessToken: json['access_token'] as String?,
      authorizationCode: json['authorization_code'] as String?,
      lastName: json['last_name'] as String?,
      firstName: json['first_name'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$$AuthInputImplToJson(_$AuthInputImpl instance) =>
    <String, dynamic>{
      'login_account': instance.loginAccount,
      'password': instance.password,
      'otp': instance.otp,
      'access_token': instance.accessToken,
      'authorization_code': instance.authorizationCode,
      'last_name': instance.lastName,
      'first_name': instance.firstName,
      'email': instance.email,
    };

_$RegisterInputImpl _$$RegisterInputImplFromJson(Map<String, dynamic> json) =>
    _$RegisterInputImpl(
      email: json['email'] as String?,
      username: json['username'] as String?,
      password: json['password'] as String?,
      passwordConfirmation: json['password_confirmation'] as String?,
      authCode: json['auth_code'] as String?,
    );

Map<String, dynamic> _$$RegisterInputImplToJson(_$RegisterInputImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'username': instance.username,
      'password': instance.password,
      'password_confirmation': instance.passwordConfirmation,
      'auth_code': instance.authCode,
    };

_$VerifyCodeInputImpl _$$VerifyCodeInputImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifyCodeInputImpl(
      email: json['email'] as String?,
      code: json['code'] as String?,
    );

Map<String, dynamic> _$$VerifyCodeInputImplToJson(
        _$VerifyCodeInputImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'code': instance.code,
    };

_$ChangePasswordInputImpl _$$ChangePasswordInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ChangePasswordInputImpl(
      password: json['password'] as String?,
      newPassword: json['new_password'] as String?,
      newPasswordConfirmation: json['new_password_confirmation'] as String?,
    );

Map<String, dynamic> _$$ChangePasswordInputImplToJson(
        _$ChangePasswordInputImpl instance) =>
    <String, dynamic>{
      'password': instance.password,
      'new_password': instance.newPassword,
      'new_password_confirmation': instance.newPasswordConfirmation,
    };

_$ForgotPasswordInputImpl _$$ForgotPasswordInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ForgotPasswordInputImpl(
      email: json['email'] as String?,
      password: json['password'] as String?,
      passwordConfirmation: json['password_confirmation'] as String?,
      authCode: json['auth_code'] as String?,
    );

Map<String, dynamic> _$$ForgotPasswordInputImplToJson(
        _$ForgotPasswordInputImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'password_confirmation': instance.passwordConfirmation,
      'auth_code': instance.authCode,
    };
