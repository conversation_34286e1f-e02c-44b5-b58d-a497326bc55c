// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';

part 'profile.freezed.dart';
part 'profile.g.dart';

enum SocialType { google, facebook, apple, shopify }

@freezed
class Profile with _$Profile {
  @CustomDateTimeConverter()
  const factory Profile({
    required int id,
    required String userId,
    required SocialType socialType,
    String? socialId,
    required String email,
    String? firstName,
    String? lastName,
    String? photoUrl,
    String? username,
    String? password,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Profile;

  factory Profile.fromJson(Map<String, dynamic> json) =>
      _$ProfileFromJson(json);
}
