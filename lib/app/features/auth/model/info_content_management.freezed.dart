// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'info_content_management.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InfoContentManagement _$InfoContentManagementFromJson(
    Map<String, dynamic> json) {
  return _InfoContentManagement.fromJson(json);
}

/// @nodoc
mixin _$InfoContentManagement {
  int get id => throw _privateConstructorUsedError;
  String get pageName => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String get slug => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InfoContentManagementCopyWith<InfoContentManagement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InfoContentManagementCopyWith<$Res> {
  factory $InfoContentManagementCopyWith(InfoContentManagement value,
          $Res Function(InfoContentManagement) then) =
      _$InfoContentManagementCopyWithImpl<$Res, InfoContentManagement>;
  @useResult
  $Res call({int id, String pageName, String content, String slug});
}

/// @nodoc
class _$InfoContentManagementCopyWithImpl<$Res,
        $Val extends InfoContentManagement>
    implements $InfoContentManagementCopyWith<$Res> {
  _$InfoContentManagementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pageName = null,
    Object? content = null,
    Object? slug = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      pageName: null == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InfoContentManagementImplCopyWith<$Res>
    implements $InfoContentManagementCopyWith<$Res> {
  factory _$$InfoContentManagementImplCopyWith(
          _$InfoContentManagementImpl value,
          $Res Function(_$InfoContentManagementImpl) then) =
      __$$InfoContentManagementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String pageName, String content, String slug});
}

/// @nodoc
class __$$InfoContentManagementImplCopyWithImpl<$Res>
    extends _$InfoContentManagementCopyWithImpl<$Res,
        _$InfoContentManagementImpl>
    implements _$$InfoContentManagementImplCopyWith<$Res> {
  __$$InfoContentManagementImplCopyWithImpl(_$InfoContentManagementImpl _value,
      $Res Function(_$InfoContentManagementImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pageName = null,
    Object? content = null,
    Object? slug = null,
  }) {
    return _then(_$InfoContentManagementImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      pageName: null == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InfoContentManagementImpl implements _InfoContentManagement {
  _$InfoContentManagementImpl(
      {required this.id,
      required this.pageName,
      required this.content,
      required this.slug});

  factory _$InfoContentManagementImpl.fromJson(Map<String, dynamic> json) =>
      _$$InfoContentManagementImplFromJson(json);

  @override
  final int id;
  @override
  final String pageName;
  @override
  final String content;
  @override
  final String slug;

  @override
  String toString() {
    return 'InfoContentManagement(id: $id, pageName: $pageName, content: $content, slug: $slug)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InfoContentManagementImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.pageName, pageName) ||
                other.pageName == pageName) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.slug, slug) || other.slug == slug));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, pageName, content, slug);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InfoContentManagementImplCopyWith<_$InfoContentManagementImpl>
      get copyWith => __$$InfoContentManagementImplCopyWithImpl<
          _$InfoContentManagementImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InfoContentManagementImplToJson(
      this,
    );
  }
}

abstract class _InfoContentManagement implements InfoContentManagement {
  factory _InfoContentManagement(
      {required final int id,
      required final String pageName,
      required final String content,
      required final String slug}) = _$InfoContentManagementImpl;

  factory _InfoContentManagement.fromJson(Map<String, dynamic> json) =
      _$InfoContentManagementImpl.fromJson;

  @override
  int get id;
  @override
  String get pageName;
  @override
  String get content;
  @override
  String get slug;
  @override
  @JsonKey(ignore: true)
  _$$InfoContentManagementImplCopyWith<_$InfoContentManagementImpl>
      get copyWith => throw _privateConstructorUsedError;
}
