// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_version_control.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppVersionControlImpl _$$AppVersionControlImplFromJson(
        Map<String, dynamic> json) =>
    _$AppVersionControlImpl(
      id: json['id'] as String,
      version: json['version'] as String,
      description: json['description'] as String,
      publishedBy: json['published_by'] as String,
      addedBy: json['added_by'] as String,
      forceUpdate: json['force_update'] as bool,
      publishedAt: const CustomDateTimeConverter()
          .fromJson(json['published_at'] as String?),
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$AppVersionControlImplToJson(
        _$AppVersionControlImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'version': instance.version,
      'description': instance.description,
      'published_by': instance.publishedBy,
      'added_by': instance.addedBy,
      'force_update': instance.forceUpdate,
      'published_at':
          const CustomDateTimeConverter().toJson(instance.publishedAt),
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$LatestAppVersionControlResponseImpl
    _$$LatestAppVersionControlResponseImplFromJson(Map<String, dynamic> json) =>
        _$LatestAppVersionControlResponseImpl(
          data:
              AppVersionControl.fromJson(json['data'] as Map<String, dynamic>),
          message: json['message'] as String?,
          success: json['success'] as bool,
        );

Map<String, dynamic> _$$LatestAppVersionControlResponseImplToJson(
        _$LatestAppVersionControlResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.toJson(),
      'message': instance.message,
      'success': instance.success,
    };
