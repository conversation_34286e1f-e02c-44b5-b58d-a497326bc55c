import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/user.dart';

part 'otp.freezed.dart';
part 'otp.g.dart';

@freezed
class OtpResponse with _$OtpResponse {
  factory OtpResponse({
    required bool isLogin,
  }) = _OtpResponse;

  factory OtpResponse.fromJson(Json json) => _$OtpResponseFromJson(json);
}

@freezed
class RequestOtpInput with _$RequestOtpInput {
  factory RequestOtpInput({
    String? loginAccount,
  }) = _RequestOtpInput;

  factory RequestOtpInput.fromJson(Json json) =>
      _$RequestOtpInputFromJson(json);
}

@freezed
class VerifyRegisterOtpInput with _$VerifyRegisterOtpInput {
  factory VerifyRegisterOtpInput({
    String? registerAccount,
    String? otp,
  }) = _VerifyRegisterOtpInput;

  factory VerifyRegisterOtpInput.fromJson(Json json) =>
      _$VerifyRegisterOtpInputFromJson(json);
}

@freezed
class RequestEmailOrPhoneUpdateOtpInput
    with _$RequestEmailOrPhoneUpdateOtpInput {
  factory RequestEmailOrPhoneUpdateOtpInput({
    String? mobileOrEmail,
  }) = _RequestEmailOrPhoneUpdateOtpInput;

  factory RequestEmailOrPhoneUpdateOtpInput.fromJson(Json json) =>
      _$RequestEmailOrPhoneUpdateOtpInputFromJson(json);
}

@freezed
class VerifyUpdateEmailOrPhoneOtpInput with _$VerifyUpdateEmailOrPhoneOtpInput {
  factory VerifyUpdateEmailOrPhoneOtpInput({
    String? countryDialCode,
    String? newMobileNumber,
    String? newEmail,
    String? otp,
  }) = _VerifyUpdateEmailOrPhoneOtpInput;

  factory VerifyUpdateEmailOrPhoneOtpInput.fromJson(Json json) =>
      _$VerifyUpdateEmailOrPhoneOtpInputFromJson(json);
}
