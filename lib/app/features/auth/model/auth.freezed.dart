// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) {
  return _AuthResponse.fromJson(json);
}

/// @nodoc
mixin _$AuthResponse {
  PartialUser get user => throw _privateConstructorUsedError;
  AuthToken get token => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AuthResponseCopyWith<AuthResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthResponseCopyWith<$Res> {
  factory $AuthResponseCopyWith(
          AuthResponse value, $Res Function(AuthResponse) then) =
      _$AuthResponseCopyWithImpl<$Res, AuthResponse>;
  @useResult
  $Res call({PartialUser user, AuthToken token});

  $AuthTokenCopyWith<$Res> get token;
}

/// @nodoc
class _$AuthResponseCopyWithImpl<$Res, $Val extends AuthResponse>
    implements $AuthResponseCopyWith<$Res> {
  _$AuthResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? token = null,
  }) {
    return _then(_value.copyWith(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as PartialUser,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as AuthToken,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AuthTokenCopyWith<$Res> get token {
    return $AuthTokenCopyWith<$Res>(_value.token, (value) {
      return _then(_value.copyWith(token: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthResponseImplCopyWith<$Res>
    implements $AuthResponseCopyWith<$Res> {
  factory _$$AuthResponseImplCopyWith(
          _$AuthResponseImpl value, $Res Function(_$AuthResponseImpl) then) =
      __$$AuthResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({PartialUser user, AuthToken token});

  @override
  $AuthTokenCopyWith<$Res> get token;
}

/// @nodoc
class __$$AuthResponseImplCopyWithImpl<$Res>
    extends _$AuthResponseCopyWithImpl<$Res, _$AuthResponseImpl>
    implements _$$AuthResponseImplCopyWith<$Res> {
  __$$AuthResponseImplCopyWithImpl(
      _$AuthResponseImpl _value, $Res Function(_$AuthResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? token = null,
  }) {
    return _then(_$AuthResponseImpl(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as PartialUser,
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as AuthToken,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthResponseImpl implements _AuthResponse {
  _$AuthResponseImpl({required this.user, required this.token});

  factory _$AuthResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthResponseImplFromJson(json);

  @override
  final PartialUser user;
  @override
  final AuthToken token;

  @override
  String toString() {
    return 'AuthResponse(user: $user, token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthResponseImpl &&
            const DeepCollectionEquality().equals(other.user, user) &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(user), token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthResponseImplCopyWith<_$AuthResponseImpl> get copyWith =>
      __$$AuthResponseImplCopyWithImpl<_$AuthResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthResponseImplToJson(
      this,
    );
  }
}

abstract class _AuthResponse implements AuthResponse {
  factory _AuthResponse(
      {required final PartialUser user,
      required final AuthToken token}) = _$AuthResponseImpl;

  factory _AuthResponse.fromJson(Map<String, dynamic> json) =
      _$AuthResponseImpl.fromJson;

  @override
  PartialUser get user;
  @override
  AuthToken get token;
  @override
  @JsonKey(ignore: true)
  _$$AuthResponseImplCopyWith<_$AuthResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AuthToken _$AuthTokenFromJson(Map<String, dynamic> json) {
  return _AuthToken.fromJson(json);
}

/// @nodoc
mixin _$AuthToken {
  String get token => throw _privateConstructorUsedError;
  String? get realtimeJwt => throw _privateConstructorUsedError;
  DateTime? get expiredAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AuthTokenCopyWith<AuthToken> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthTokenCopyWith<$Res> {
  factory $AuthTokenCopyWith(AuthToken value, $Res Function(AuthToken) then) =
      _$AuthTokenCopyWithImpl<$Res, AuthToken>;
  @useResult
  $Res call({String token, String? realtimeJwt, DateTime? expiredAt});
}

/// @nodoc
class _$AuthTokenCopyWithImpl<$Res, $Val extends AuthToken>
    implements $AuthTokenCopyWith<$Res> {
  _$AuthTokenCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? realtimeJwt = freezed,
    Object? expiredAt = freezed,
  }) {
    return _then(_value.copyWith(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredAt: freezed == expiredAt
          ? _value.expiredAt
          : expiredAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthTokenImplCopyWith<$Res>
    implements $AuthTokenCopyWith<$Res> {
  factory _$$AuthTokenImplCopyWith(
          _$AuthTokenImpl value, $Res Function(_$AuthTokenImpl) then) =
      __$$AuthTokenImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String token, String? realtimeJwt, DateTime? expiredAt});
}

/// @nodoc
class __$$AuthTokenImplCopyWithImpl<$Res>
    extends _$AuthTokenCopyWithImpl<$Res, _$AuthTokenImpl>
    implements _$$AuthTokenImplCopyWith<$Res> {
  __$$AuthTokenImplCopyWithImpl(
      _$AuthTokenImpl _value, $Res Function(_$AuthTokenImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? realtimeJwt = freezed,
    Object? expiredAt = freezed,
  }) {
    return _then(_$AuthTokenImpl(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      realtimeJwt: freezed == realtimeJwt
          ? _value.realtimeJwt
          : realtimeJwt // ignore: cast_nullable_to_non_nullable
              as String?,
      expiredAt: freezed == expiredAt
          ? _value.expiredAt
          : expiredAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$AuthTokenImpl implements _AuthToken {
  _$AuthTokenImpl({required this.token, this.realtimeJwt, this.expiredAt});

  factory _$AuthTokenImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthTokenImplFromJson(json);

  @override
  final String token;
  @override
  final String? realtimeJwt;
  @override
  final DateTime? expiredAt;

  @override
  String toString() {
    return 'AuthToken(token: $token, realtimeJwt: $realtimeJwt, expiredAt: $expiredAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthTokenImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.realtimeJwt, realtimeJwt) ||
                other.realtimeJwt == realtimeJwt) &&
            (identical(other.expiredAt, expiredAt) ||
                other.expiredAt == expiredAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, token, realtimeJwt, expiredAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthTokenImplCopyWith<_$AuthTokenImpl> get copyWith =>
      __$$AuthTokenImplCopyWithImpl<_$AuthTokenImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthTokenImplToJson(
      this,
    );
  }
}

abstract class _AuthToken implements AuthToken {
  factory _AuthToken(
      {required final String token,
      final String? realtimeJwt,
      final DateTime? expiredAt}) = _$AuthTokenImpl;

  factory _AuthToken.fromJson(Map<String, dynamic> json) =
      _$AuthTokenImpl.fromJson;

  @override
  String get token;
  @override
  String? get realtimeJwt;
  @override
  DateTime? get expiredAt;
  @override
  @JsonKey(ignore: true)
  _$$AuthTokenImplCopyWith<_$AuthTokenImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AuthInput _$AuthInputFromJson(Map<String, dynamic> json) {
  return _AuthInput.fromJson(json);
}

/// @nodoc
mixin _$AuthInput {
  String? get loginAccount => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get otp =>
      throw _privateConstructorUsedError; // google & facebook login
  String? get accessToken => throw _privateConstructorUsedError; // apple login
  String? get authorizationCode => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AuthInputCopyWith<AuthInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthInputCopyWith<$Res> {
  factory $AuthInputCopyWith(AuthInput value, $Res Function(AuthInput) then) =
      _$AuthInputCopyWithImpl<$Res, AuthInput>;
  @useResult
  $Res call(
      {String? loginAccount,
      String? password,
      String? otp,
      String? accessToken,
      String? authorizationCode,
      String? lastName,
      String? firstName,
      String? email});
}

/// @nodoc
class _$AuthInputCopyWithImpl<$Res, $Val extends AuthInput>
    implements $AuthInputCopyWith<$Res> {
  _$AuthInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginAccount = freezed,
    Object? password = freezed,
    Object? otp = freezed,
    Object? accessToken = freezed,
    Object? authorizationCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? email = freezed,
  }) {
    return _then(_value.copyWith(
      loginAccount: freezed == loginAccount
          ? _value.loginAccount
          : loginAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationCode: freezed == authorizationCode
          ? _value.authorizationCode
          : authorizationCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthInputImplCopyWith<$Res>
    implements $AuthInputCopyWith<$Res> {
  factory _$$AuthInputImplCopyWith(
          _$AuthInputImpl value, $Res Function(_$AuthInputImpl) then) =
      __$$AuthInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? loginAccount,
      String? password,
      String? otp,
      String? accessToken,
      String? authorizationCode,
      String? lastName,
      String? firstName,
      String? email});
}

/// @nodoc
class __$$AuthInputImplCopyWithImpl<$Res>
    extends _$AuthInputCopyWithImpl<$Res, _$AuthInputImpl>
    implements _$$AuthInputImplCopyWith<$Res> {
  __$$AuthInputImplCopyWithImpl(
      _$AuthInputImpl _value, $Res Function(_$AuthInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginAccount = freezed,
    Object? password = freezed,
    Object? otp = freezed,
    Object? accessToken = freezed,
    Object? authorizationCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? email = freezed,
  }) {
    return _then(_$AuthInputImpl(
      loginAccount: freezed == loginAccount
          ? _value.loginAccount
          : loginAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationCode: freezed == authorizationCode
          ? _value.authorizationCode
          : authorizationCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthInputImpl implements _AuthInput {
  _$AuthInputImpl(
      {this.loginAccount,
      this.password,
      this.otp,
      this.accessToken,
      this.authorizationCode,
      this.lastName,
      this.firstName,
      this.email});

  factory _$AuthInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthInputImplFromJson(json);

  @override
  final String? loginAccount;
  @override
  final String? password;
  @override
  final String? otp;
// google & facebook login
  @override
  final String? accessToken;
// apple login
  @override
  final String? authorizationCode;
  @override
  final String? lastName;
  @override
  final String? firstName;
  @override
  final String? email;

  @override
  String toString() {
    return 'AuthInput(loginAccount: $loginAccount, password: $password, otp: $otp, accessToken: $accessToken, authorizationCode: $authorizationCode, lastName: $lastName, firstName: $firstName, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthInputImpl &&
            (identical(other.loginAccount, loginAccount) ||
                other.loginAccount == loginAccount) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.authorizationCode, authorizationCode) ||
                other.authorizationCode == authorizationCode) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, loginAccount, password, otp,
      accessToken, authorizationCode, lastName, firstName, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthInputImplCopyWith<_$AuthInputImpl> get copyWith =>
      __$$AuthInputImplCopyWithImpl<_$AuthInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthInputImplToJson(
      this,
    );
  }
}

abstract class _AuthInput implements AuthInput {
  factory _AuthInput(
      {final String? loginAccount,
      final String? password,
      final String? otp,
      final String? accessToken,
      final String? authorizationCode,
      final String? lastName,
      final String? firstName,
      final String? email}) = _$AuthInputImpl;

  factory _AuthInput.fromJson(Map<String, dynamic> json) =
      _$AuthInputImpl.fromJson;

  @override
  String? get loginAccount;
  @override
  String? get password;
  @override
  String? get otp;
  @override // google & facebook login
  String? get accessToken;
  @override // apple login
  String? get authorizationCode;
  @override
  String? get lastName;
  @override
  String? get firstName;
  @override
  String? get email;
  @override
  @JsonKey(ignore: true)
  _$$AuthInputImplCopyWith<_$AuthInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RegisterInput _$RegisterInputFromJson(Map<String, dynamic> json) {
  return _RegisterInput.fromJson(json);
}

/// @nodoc
mixin _$RegisterInput {
  String? get email => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get passwordConfirmation => throw _privateConstructorUsedError;
  String? get authCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RegisterInputCopyWith<RegisterInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterInputCopyWith<$Res> {
  factory $RegisterInputCopyWith(
          RegisterInput value, $Res Function(RegisterInput) then) =
      _$RegisterInputCopyWithImpl<$Res, RegisterInput>;
  @useResult
  $Res call(
      {String? email,
      String? username,
      String? password,
      String? passwordConfirmation,
      String? authCode});
}

/// @nodoc
class _$RegisterInputCopyWithImpl<$Res, $Val extends RegisterInput>
    implements $RegisterInputCopyWith<$Res> {
  _$RegisterInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? username = freezed,
    Object? password = freezed,
    Object? passwordConfirmation = freezed,
    Object? authCode = freezed,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordConfirmation: freezed == passwordConfirmation
          ? _value.passwordConfirmation
          : passwordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
      authCode: freezed == authCode
          ? _value.authCode
          : authCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterInputImplCopyWith<$Res>
    implements $RegisterInputCopyWith<$Res> {
  factory _$$RegisterInputImplCopyWith(
          _$RegisterInputImpl value, $Res Function(_$RegisterInputImpl) then) =
      __$$RegisterInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? email,
      String? username,
      String? password,
      String? passwordConfirmation,
      String? authCode});
}

/// @nodoc
class __$$RegisterInputImplCopyWithImpl<$Res>
    extends _$RegisterInputCopyWithImpl<$Res, _$RegisterInputImpl>
    implements _$$RegisterInputImplCopyWith<$Res> {
  __$$RegisterInputImplCopyWithImpl(
      _$RegisterInputImpl _value, $Res Function(_$RegisterInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? username = freezed,
    Object? password = freezed,
    Object? passwordConfirmation = freezed,
    Object? authCode = freezed,
  }) {
    return _then(_$RegisterInputImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordConfirmation: freezed == passwordConfirmation
          ? _value.passwordConfirmation
          : passwordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
      authCode: freezed == authCode
          ? _value.authCode
          : authCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegisterInputImpl implements _RegisterInput {
  _$RegisterInputImpl(
      {this.email,
      this.username,
      this.password,
      this.passwordConfirmation,
      this.authCode});

  factory _$RegisterInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterInputImplFromJson(json);

  @override
  final String? email;
  @override
  final String? username;
  @override
  final String? password;
  @override
  final String? passwordConfirmation;
  @override
  final String? authCode;

  @override
  String toString() {
    return 'RegisterInput(email: $email, username: $username, password: $password, passwordConfirmation: $passwordConfirmation, authCode: $authCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterInputImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.passwordConfirmation, passwordConfirmation) ||
                other.passwordConfirmation == passwordConfirmation) &&
            (identical(other.authCode, authCode) ||
                other.authCode == authCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, email, username, password, passwordConfirmation, authCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterInputImplCopyWith<_$RegisterInputImpl> get copyWith =>
      __$$RegisterInputImplCopyWithImpl<_$RegisterInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterInputImplToJson(
      this,
    );
  }
}

abstract class _RegisterInput implements RegisterInput {
  factory _RegisterInput(
      {final String? email,
      final String? username,
      final String? password,
      final String? passwordConfirmation,
      final String? authCode}) = _$RegisterInputImpl;

  factory _RegisterInput.fromJson(Map<String, dynamic> json) =
      _$RegisterInputImpl.fromJson;

  @override
  String? get email;
  @override
  String? get username;
  @override
  String? get password;
  @override
  String? get passwordConfirmation;
  @override
  String? get authCode;
  @override
  @JsonKey(ignore: true)
  _$$RegisterInputImplCopyWith<_$RegisterInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifyCodeInput _$VerifyCodeInputFromJson(Map<String, dynamic> json) {
  return _VerifyCodeInput.fromJson(json);
}

/// @nodoc
mixin _$VerifyCodeInput {
  String? get email => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VerifyCodeInputCopyWith<VerifyCodeInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyCodeInputCopyWith<$Res> {
  factory $VerifyCodeInputCopyWith(
          VerifyCodeInput value, $Res Function(VerifyCodeInput) then) =
      _$VerifyCodeInputCopyWithImpl<$Res, VerifyCodeInput>;
  @useResult
  $Res call({String? email, String? code});
}

/// @nodoc
class _$VerifyCodeInputCopyWithImpl<$Res, $Val extends VerifyCodeInput>
    implements $VerifyCodeInputCopyWith<$Res> {
  _$VerifyCodeInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? code = freezed,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyCodeInputImplCopyWith<$Res>
    implements $VerifyCodeInputCopyWith<$Res> {
  factory _$$VerifyCodeInputImplCopyWith(_$VerifyCodeInputImpl value,
          $Res Function(_$VerifyCodeInputImpl) then) =
      __$$VerifyCodeInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? email, String? code});
}

/// @nodoc
class __$$VerifyCodeInputImplCopyWithImpl<$Res>
    extends _$VerifyCodeInputCopyWithImpl<$Res, _$VerifyCodeInputImpl>
    implements _$$VerifyCodeInputImplCopyWith<$Res> {
  __$$VerifyCodeInputImplCopyWithImpl(
      _$VerifyCodeInputImpl _value, $Res Function(_$VerifyCodeInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? code = freezed,
  }) {
    return _then(_$VerifyCodeInputImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyCodeInputImpl implements _VerifyCodeInput {
  _$VerifyCodeInputImpl({this.email, this.code});

  factory _$VerifyCodeInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyCodeInputImplFromJson(json);

  @override
  final String? email;
  @override
  final String? code;

  @override
  String toString() {
    return 'VerifyCodeInput(email: $email, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyCodeInputImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.code, code) || other.code == code));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, email, code);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyCodeInputImplCopyWith<_$VerifyCodeInputImpl> get copyWith =>
      __$$VerifyCodeInputImplCopyWithImpl<_$VerifyCodeInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyCodeInputImplToJson(
      this,
    );
  }
}

abstract class _VerifyCodeInput implements VerifyCodeInput {
  factory _VerifyCodeInput({final String? email, final String? code}) =
      _$VerifyCodeInputImpl;

  factory _VerifyCodeInput.fromJson(Map<String, dynamic> json) =
      _$VerifyCodeInputImpl.fromJson;

  @override
  String? get email;
  @override
  String? get code;
  @override
  @JsonKey(ignore: true)
  _$$VerifyCodeInputImplCopyWith<_$VerifyCodeInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChangePasswordInput _$ChangePasswordInputFromJson(Map<String, dynamic> json) {
  return _ChangePasswordInput.fromJson(json);
}

/// @nodoc
mixin _$ChangePasswordInput {
  String? get password => throw _privateConstructorUsedError;
  String? get newPassword => throw _privateConstructorUsedError;
  String? get newPasswordConfirmation => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChangePasswordInputCopyWith<ChangePasswordInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChangePasswordInputCopyWith<$Res> {
  factory $ChangePasswordInputCopyWith(
          ChangePasswordInput value, $Res Function(ChangePasswordInput) then) =
      _$ChangePasswordInputCopyWithImpl<$Res, ChangePasswordInput>;
  @useResult
  $Res call(
      {String? password, String? newPassword, String? newPasswordConfirmation});
}

/// @nodoc
class _$ChangePasswordInputCopyWithImpl<$Res, $Val extends ChangePasswordInput>
    implements $ChangePasswordInputCopyWith<$Res> {
  _$ChangePasswordInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = freezed,
    Object? newPassword = freezed,
    Object? newPasswordConfirmation = freezed,
  }) {
    return _then(_value.copyWith(
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      newPassword: freezed == newPassword
          ? _value.newPassword
          : newPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      newPasswordConfirmation: freezed == newPasswordConfirmation
          ? _value.newPasswordConfirmation
          : newPasswordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChangePasswordInputImplCopyWith<$Res>
    implements $ChangePasswordInputCopyWith<$Res> {
  factory _$$ChangePasswordInputImplCopyWith(_$ChangePasswordInputImpl value,
          $Res Function(_$ChangePasswordInputImpl) then) =
      __$$ChangePasswordInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? password, String? newPassword, String? newPasswordConfirmation});
}

/// @nodoc
class __$$ChangePasswordInputImplCopyWithImpl<$Res>
    extends _$ChangePasswordInputCopyWithImpl<$Res, _$ChangePasswordInputImpl>
    implements _$$ChangePasswordInputImplCopyWith<$Res> {
  __$$ChangePasswordInputImplCopyWithImpl(_$ChangePasswordInputImpl _value,
      $Res Function(_$ChangePasswordInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = freezed,
    Object? newPassword = freezed,
    Object? newPasswordConfirmation = freezed,
  }) {
    return _then(_$ChangePasswordInputImpl(
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      newPassword: freezed == newPassword
          ? _value.newPassword
          : newPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      newPasswordConfirmation: freezed == newPasswordConfirmation
          ? _value.newPasswordConfirmation
          : newPasswordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChangePasswordInputImpl implements _ChangePasswordInput {
  _$ChangePasswordInputImpl(
      {this.password, this.newPassword, this.newPasswordConfirmation});

  factory _$ChangePasswordInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChangePasswordInputImplFromJson(json);

  @override
  final String? password;
  @override
  final String? newPassword;
  @override
  final String? newPasswordConfirmation;

  @override
  String toString() {
    return 'ChangePasswordInput(password: $password, newPassword: $newPassword, newPasswordConfirmation: $newPasswordConfirmation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePasswordInputImpl &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.newPassword, newPassword) ||
                other.newPassword == newPassword) &&
            (identical(
                    other.newPasswordConfirmation, newPasswordConfirmation) ||
                other.newPasswordConfirmation == newPasswordConfirmation));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, password, newPassword, newPasswordConfirmation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePasswordInputImplCopyWith<_$ChangePasswordInputImpl> get copyWith =>
      __$$ChangePasswordInputImplCopyWithImpl<_$ChangePasswordInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChangePasswordInputImplToJson(
      this,
    );
  }
}

abstract class _ChangePasswordInput implements ChangePasswordInput {
  factory _ChangePasswordInput(
      {final String? password,
      final String? newPassword,
      final String? newPasswordConfirmation}) = _$ChangePasswordInputImpl;

  factory _ChangePasswordInput.fromJson(Map<String, dynamic> json) =
      _$ChangePasswordInputImpl.fromJson;

  @override
  String? get password;
  @override
  String? get newPassword;
  @override
  String? get newPasswordConfirmation;
  @override
  @JsonKey(ignore: true)
  _$$ChangePasswordInputImplCopyWith<_$ChangePasswordInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ForgotPasswordInput _$ForgotPasswordInputFromJson(Map<String, dynamic> json) {
  return _ForgotPasswordInput.fromJson(json);
}

/// @nodoc
mixin _$ForgotPasswordInput {
  String? get email => throw _privateConstructorUsedError;
  String? get password => throw _privateConstructorUsedError;
  String? get passwordConfirmation => throw _privateConstructorUsedError;
  String? get authCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ForgotPasswordInputCopyWith<ForgotPasswordInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForgotPasswordInputCopyWith<$Res> {
  factory $ForgotPasswordInputCopyWith(
          ForgotPasswordInput value, $Res Function(ForgotPasswordInput) then) =
      _$ForgotPasswordInputCopyWithImpl<$Res, ForgotPasswordInput>;
  @useResult
  $Res call(
      {String? email,
      String? password,
      String? passwordConfirmation,
      String? authCode});
}

/// @nodoc
class _$ForgotPasswordInputCopyWithImpl<$Res, $Val extends ForgotPasswordInput>
    implements $ForgotPasswordInputCopyWith<$Res> {
  _$ForgotPasswordInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? password = freezed,
    Object? passwordConfirmation = freezed,
    Object? authCode = freezed,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordConfirmation: freezed == passwordConfirmation
          ? _value.passwordConfirmation
          : passwordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
      authCode: freezed == authCode
          ? _value.authCode
          : authCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ForgotPasswordInputImplCopyWith<$Res>
    implements $ForgotPasswordInputCopyWith<$Res> {
  factory _$$ForgotPasswordInputImplCopyWith(_$ForgotPasswordInputImpl value,
          $Res Function(_$ForgotPasswordInputImpl) then) =
      __$$ForgotPasswordInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? email,
      String? password,
      String? passwordConfirmation,
      String? authCode});
}

/// @nodoc
class __$$ForgotPasswordInputImplCopyWithImpl<$Res>
    extends _$ForgotPasswordInputCopyWithImpl<$Res, _$ForgotPasswordInputImpl>
    implements _$$ForgotPasswordInputImplCopyWith<$Res> {
  __$$ForgotPasswordInputImplCopyWithImpl(_$ForgotPasswordInputImpl _value,
      $Res Function(_$ForgotPasswordInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? password = freezed,
    Object? passwordConfirmation = freezed,
    Object? authCode = freezed,
  }) {
    return _then(_$ForgotPasswordInputImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      password: freezed == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordConfirmation: freezed == passwordConfirmation
          ? _value.passwordConfirmation
          : passwordConfirmation // ignore: cast_nullable_to_non_nullable
              as String?,
      authCode: freezed == authCode
          ? _value.authCode
          : authCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ForgotPasswordInputImpl implements _ForgotPasswordInput {
  _$ForgotPasswordInputImpl(
      {this.email, this.password, this.passwordConfirmation, this.authCode});

  factory _$ForgotPasswordInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ForgotPasswordInputImplFromJson(json);

  @override
  final String? email;
  @override
  final String? password;
  @override
  final String? passwordConfirmation;
  @override
  final String? authCode;

  @override
  String toString() {
    return 'ForgotPasswordInput(email: $email, password: $password, passwordConfirmation: $passwordConfirmation, authCode: $authCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForgotPasswordInputImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.passwordConfirmation, passwordConfirmation) ||
                other.passwordConfirmation == passwordConfirmation) &&
            (identical(other.authCode, authCode) ||
                other.authCode == authCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, email, password, passwordConfirmation, authCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ForgotPasswordInputImplCopyWith<_$ForgotPasswordInputImpl> get copyWith =>
      __$$ForgotPasswordInputImplCopyWithImpl<_$ForgotPasswordInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ForgotPasswordInputImplToJson(
      this,
    );
  }
}

abstract class _ForgotPasswordInput implements ForgotPasswordInput {
  factory _ForgotPasswordInput(
      {final String? email,
      final String? password,
      final String? passwordConfirmation,
      final String? authCode}) = _$ForgotPasswordInputImpl;

  factory _ForgotPasswordInput.fromJson(Map<String, dynamic> json) =
      _$ForgotPasswordInputImpl.fromJson;

  @override
  String? get email;
  @override
  String? get password;
  @override
  String? get passwordConfirmation;
  @override
  String? get authCode;
  @override
  @JsonKey(ignore: true)
  _$$ForgotPasswordInputImplCopyWith<_$ForgotPasswordInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
