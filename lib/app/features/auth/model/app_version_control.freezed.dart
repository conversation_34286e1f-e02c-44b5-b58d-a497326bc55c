// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_version_control.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppVersionControl _$AppVersionControlFromJson(Map<String, dynamic> json) {
  return _AppVersionControl.fromJson(json);
}

/// @nodoc
mixin _$AppVersionControl {
  String get id => throw _privateConstructorUsedError;
  String get version => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get publishedBy => throw _privateConstructorUsedError;
  String get addedBy => throw _privateConstructorUsedError;
  bool get forceUpdate => throw _privateConstructorUsedError;
  DateTime? get publishedAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppVersionControlCopyWith<AppVersionControl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppVersionControlCopyWith<$Res> {
  factory $AppVersionControlCopyWith(
          AppVersionControl value, $Res Function(AppVersionControl) then) =
      _$AppVersionControlCopyWithImpl<$Res, AppVersionControl>;
  @useResult
  $Res call(
      {String id,
      String version,
      String description,
      String publishedBy,
      String addedBy,
      bool forceUpdate,
      DateTime? publishedAt,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$AppVersionControlCopyWithImpl<$Res, $Val extends AppVersionControl>
    implements $AppVersionControlCopyWith<$Res> {
  _$AppVersionControlCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? version = null,
    Object? description = null,
    Object? publishedBy = null,
    Object? addedBy = null,
    Object? forceUpdate = null,
    Object? publishedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      publishedBy: null == publishedBy
          ? _value.publishedBy
          : publishedBy // ignore: cast_nullable_to_non_nullable
              as String,
      addedBy: null == addedBy
          ? _value.addedBy
          : addedBy // ignore: cast_nullable_to_non_nullable
              as String,
      forceUpdate: null == forceUpdate
          ? _value.forceUpdate
          : forceUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
      publishedAt: freezed == publishedAt
          ? _value.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppVersionControlImplCopyWith<$Res>
    implements $AppVersionControlCopyWith<$Res> {
  factory _$$AppVersionControlImplCopyWith(_$AppVersionControlImpl value,
          $Res Function(_$AppVersionControlImpl) then) =
      __$$AppVersionControlImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String version,
      String description,
      String publishedBy,
      String addedBy,
      bool forceUpdate,
      DateTime? publishedAt,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$AppVersionControlImplCopyWithImpl<$Res>
    extends _$AppVersionControlCopyWithImpl<$Res, _$AppVersionControlImpl>
    implements _$$AppVersionControlImplCopyWith<$Res> {
  __$$AppVersionControlImplCopyWithImpl(_$AppVersionControlImpl _value,
      $Res Function(_$AppVersionControlImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? version = null,
    Object? description = null,
    Object? publishedBy = null,
    Object? addedBy = null,
    Object? forceUpdate = null,
    Object? publishedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$AppVersionControlImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      publishedBy: null == publishedBy
          ? _value.publishedBy
          : publishedBy // ignore: cast_nullable_to_non_nullable
              as String,
      addedBy: null == addedBy
          ? _value.addedBy
          : addedBy // ignore: cast_nullable_to_non_nullable
              as String,
      forceUpdate: null == forceUpdate
          ? _value.forceUpdate
          : forceUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
      publishedAt: freezed == publishedAt
          ? _value.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$AppVersionControlImpl implements _AppVersionControl {
  _$AppVersionControlImpl(
      {required this.id,
      required this.version,
      required this.description,
      required this.publishedBy,
      required this.addedBy,
      required this.forceUpdate,
      this.publishedAt,
      this.createdAt,
      this.updatedAt});

  factory _$AppVersionControlImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppVersionControlImplFromJson(json);

  @override
  final String id;
  @override
  final String version;
  @override
  final String description;
  @override
  final String publishedBy;
  @override
  final String addedBy;
  @override
  final bool forceUpdate;
  @override
  final DateTime? publishedAt;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'AppVersionControl(id: $id, version: $version, description: $description, publishedBy: $publishedBy, addedBy: $addedBy, forceUpdate: $forceUpdate, publishedAt: $publishedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppVersionControlImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.publishedBy, publishedBy) ||
                other.publishedBy == publishedBy) &&
            (identical(other.addedBy, addedBy) || other.addedBy == addedBy) &&
            (identical(other.forceUpdate, forceUpdate) ||
                other.forceUpdate == forceUpdate) &&
            (identical(other.publishedAt, publishedAt) ||
                other.publishedAt == publishedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, version, description,
      publishedBy, addedBy, forceUpdate, publishedAt, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppVersionControlImplCopyWith<_$AppVersionControlImpl> get copyWith =>
      __$$AppVersionControlImplCopyWithImpl<_$AppVersionControlImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppVersionControlImplToJson(
      this,
    );
  }
}

abstract class _AppVersionControl implements AppVersionControl {
  factory _AppVersionControl(
      {required final String id,
      required final String version,
      required final String description,
      required final String publishedBy,
      required final String addedBy,
      required final bool forceUpdate,
      final DateTime? publishedAt,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$AppVersionControlImpl;

  factory _AppVersionControl.fromJson(Map<String, dynamic> json) =
      _$AppVersionControlImpl.fromJson;

  @override
  String get id;
  @override
  String get version;
  @override
  String get description;
  @override
  String get publishedBy;
  @override
  String get addedBy;
  @override
  bool get forceUpdate;
  @override
  DateTime? get publishedAt;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$AppVersionControlImplCopyWith<_$AppVersionControlImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LatestAppVersionControlResponse _$LatestAppVersionControlResponseFromJson(
    Map<String, dynamic> json) {
  return _LatestAppVersionControlResponse.fromJson(json);
}

/// @nodoc
mixin _$LatestAppVersionControlResponse {
  AppVersionControl get data => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  bool get success => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LatestAppVersionControlResponseCopyWith<LatestAppVersionControlResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LatestAppVersionControlResponseCopyWith<$Res> {
  factory $LatestAppVersionControlResponseCopyWith(
          LatestAppVersionControlResponse value,
          $Res Function(LatestAppVersionControlResponse) then) =
      _$LatestAppVersionControlResponseCopyWithImpl<$Res,
          LatestAppVersionControlResponse>;
  @useResult
  $Res call({AppVersionControl data, String? message, bool success});

  $AppVersionControlCopyWith<$Res> get data;
}

/// @nodoc
class _$LatestAppVersionControlResponseCopyWithImpl<$Res,
        $Val extends LatestAppVersionControlResponse>
    implements $LatestAppVersionControlResponseCopyWith<$Res> {
  _$LatestAppVersionControlResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? message = freezed,
    Object? success = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as AppVersionControl,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AppVersionControlCopyWith<$Res> get data {
    return $AppVersionControlCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LatestAppVersionControlResponseImplCopyWith<$Res>
    implements $LatestAppVersionControlResponseCopyWith<$Res> {
  factory _$$LatestAppVersionControlResponseImplCopyWith(
          _$LatestAppVersionControlResponseImpl value,
          $Res Function(_$LatestAppVersionControlResponseImpl) then) =
      __$$LatestAppVersionControlResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppVersionControl data, String? message, bool success});

  @override
  $AppVersionControlCopyWith<$Res> get data;
}

/// @nodoc
class __$$LatestAppVersionControlResponseImplCopyWithImpl<$Res>
    extends _$LatestAppVersionControlResponseCopyWithImpl<$Res,
        _$LatestAppVersionControlResponseImpl>
    implements _$$LatestAppVersionControlResponseImplCopyWith<$Res> {
  __$$LatestAppVersionControlResponseImplCopyWithImpl(
      _$LatestAppVersionControlResponseImpl _value,
      $Res Function(_$LatestAppVersionControlResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? message = freezed,
    Object? success = null,
  }) {
    return _then(_$LatestAppVersionControlResponseImpl(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as AppVersionControl,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LatestAppVersionControlResponseImpl
    implements _LatestAppVersionControlResponse {
  const _$LatestAppVersionControlResponseImpl(
      {required this.data, this.message, required this.success});

  factory _$LatestAppVersionControlResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$LatestAppVersionControlResponseImplFromJson(json);

  @override
  final AppVersionControl data;
  @override
  final String? message;
  @override
  final bool success;

  @override
  String toString() {
    return 'LatestAppVersionControlResponse(data: $data, message: $message, success: $success)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LatestAppVersionControlResponseImpl &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.success, success) || other.success == success));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, data, message, success);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LatestAppVersionControlResponseImplCopyWith<
          _$LatestAppVersionControlResponseImpl>
      get copyWith => __$$LatestAppVersionControlResponseImplCopyWithImpl<
          _$LatestAppVersionControlResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LatestAppVersionControlResponseImplToJson(
      this,
    );
  }
}

abstract class _LatestAppVersionControlResponse
    implements LatestAppVersionControlResponse {
  const factory _LatestAppVersionControlResponse(
      {required final AppVersionControl data,
      final String? message,
      required final bool success}) = _$LatestAppVersionControlResponseImpl;

  factory _LatestAppVersionControlResponse.fromJson(Map<String, dynamic> json) =
      _$LatestAppVersionControlResponseImpl.fromJson;

  @override
  AppVersionControl get data;
  @override
  String? get message;
  @override
  bool get success;
  @override
  @JsonKey(ignore: true)
  _$$LatestAppVersionControlResponseImplCopyWith<
          _$LatestAppVersionControlResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
