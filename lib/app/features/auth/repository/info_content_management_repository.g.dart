// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_content_management_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$infoContentManagementRepositoryHash() =>
    r'f723bac1b05414526f0d95d7f2388ef99ed66497';

/// See also [infoContentManagementRepository].
@ProviderFor(infoContentManagementRepository)
final infoContentManagementRepositoryProvider =
    Provider<InfoContentManagementRepository>.internal(
  infoContentManagementRepository,
  name: r'infoContentManagementRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$infoContentManagementRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef InfoContentManagementRepositoryRef
    = ProviderRef<InfoContentManagementRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
