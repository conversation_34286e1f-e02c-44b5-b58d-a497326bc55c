import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/auth.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/coin/model/commerce_coin.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/orders/model/order_review_request.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:gomama/app/features/edit_profile/model/selfie_verification.dart';
import 'package:gomama/app/features/edit_profile/model/singpass_verification.dart';
import 'package:gomama/app/features/singpass/model/singpass.dart';
import 'package:groveman/groveman.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_repository.g.dart';

@Riverpod(keepAlive: true)
UserRepository userRepository(UserRepositoryRef ref) => UserRepository(ref);

class UserRepository {
  UserRepository(this.ref);
  final UserRepositoryRef ref;

  Future<List<User>> findUsers({
    Json? queryParameters,
  }) async {
    try {
      final response = await ref.watch(repositoryProvider).get<Json>(
            '/users',
            queryParameters: queryParameters,
          );

      return (response.data! as List<dynamic>)
          .map((e) {
            return PartialUser.fromJson(e as Json);
          })
          .toList(growable: false)
          .cast<User>();
    } catch (error) {
      Groveman.warning('findUsers', error: error);
      rethrow;
    }
  }

  Future<User> findUser(
    String id, {
    Map<String, String>? queryParameters,
  }) async {
    try {
      final response = await ref.watch(repositoryProvider).get<Json>(
            '/users/$id',
            queryParameters: queryParameters,
          );

      return PartialUser.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('findUser', error: error);
      rethrow;
    }
  }

  Future<User?> searchUsername(
    String username,
  ) async {
    try {
      final response = await ref.watch(repositoryProvider).get<Json>(
            '/users/search-username/$username',
          );

      if (response.data!['success'] == false) {
        return null;
      }

      return PartialUser.fromJson(response.data!['data'] as Json);
    } catch (error) {
      Groveman.warning('searchUsername', error: error);
      rethrow;
    }
  }

  Future<User> fetchMyself() async {
    try {
      final response = await ref.watch(repositoryProvider).get<Json>('/me');

      return PartialUser.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchMyself', error: error);
      rethrow;
    }
  }

  Future<String> fetchAppVersion() async {
    try {
      final response =
          await ref.watch(repositoryProvider).get<Json>('/app_version');

      return response.data!['app_version'] as String;
    } catch (error) {
      Groveman.warning('fetchAppVersion', error: error);
      rethrow;
    }
  }

  Future<bool> deleteAccount(String reason) async {
    try {
      final response = await ref.read(repositoryProvider).delete<Json>(
        '/me',
        data: {
          'reason': reason,
        },
      );
      return response.data!['success'] == true;
    } catch (error) {
      Groveman.warning('deleteAccount', error: error);
      rethrow;
    }
  }

  Future<PostResponse<User>> gomamaVerify(SelfieVerificationInput input) async {
    try {
      final formData = FormData();

      formData.fields
        ..add(MapEntry('email', input.email))
        ..add(MapEntry('first_name', input.firstName))
        ..add(MapEntry('last_name', input.lastName))
        ..add(MapEntry('fin_or_passport', input.finOrPassport))
        ..add(
          MapEntry('birthday', input.birthday.toIso8601String()),
        )
        ..add(MapEntry('mobile_number', input.mobileNumber))
        ..add(MapEntry('gender', input.gender));

      for (var i = 0; i < input.childrenBirthdays.length; i++) {
        formData.fields.add(
          MapEntry(
            'children_birthdays[$i]',
            input.childrenBirthdays[i].toIso8601String(),
          ),
        );
      }

      if (input.userSelfieFile != null) {
        final selfieFile = input.userSelfieFile;
        final filename = path.basename(selfieFile!.path);

        formData.files
          ..add(
            MapEntry(
              'user_selfie_file',
              await MultipartFile.fromFile(
                selfieFile.path,
                filename: filename,
                contentType: MediaType(
                  'image',
                  path.extension(filename).toLowerCase().replaceAll('.', ''),
                ),
              ),
            ),
          )
          ..add(
            MapEntry(
              'user_selfie_file_copy',
              await MultipartFile.fromFile(
                selfieFile.path,
                filename: filename,
                contentType: MediaType(
                  'image',
                  path.extension(filename).toLowerCase().replaceAll('.', ''),
                ),
              ),
            ),
          );
      }

      final response = await ref.watch(repositoryProvider).multipartPost<Json>(
            '/me/gomama-verify',
            data: formData,
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {
          return PartialUser.fromJson(json! as Json);
        },
      );
    } catch (error) {
      Groveman.warning('gomamaVerify', error: error);
      rethrow;
    }
  }

  Future<PostResponse<User>> gomamaVerifySelfieFailCountAdd() async {
    try {
      final response = await ref
          .read(repositoryProvider)
          .post<Json>('/me/gomama-verify/add-selfie-fail-count');

      return PostResponse.fromJson(
        response.data!,
        (json) => PartialUser.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('gomamaVerifySelfieFailCountAdd', error: error);
      rethrow;
    }
  }

  Future<String> singpassInitialize() async {
    try {
      final response = await ref
          .watch(repositoryProvider)
          .get('/singpassV3/myinfo/authorize');

      return response.data!['data'] as String; // singpass url
    } catch (error) {
      Groveman.warning('singpassInitialize', error: error);
      rethrow;
    }
  }

  Future<SingpassProcessResponse> singpassProcess(
    SingpassProcessInput data,
  ) async {
    try {
      final response = await ref.watch(repositoryProvider).post<Json>(
            '/singpassV3/myinfo/process',
            data: data.toJson(),
          );

      return SingpassProcessResponse.fromJson(response.data!['data'] as Json);
    } catch (error) {
      Groveman.warning('singpassProcess', error: error);
      rethrow;
    }
  }

  Future<PostResponse<User>> singpassVerify(
    SingpassVerificationInput data,
  ) async {
    try {
      final response = await ref.watch(repositoryProvider).post<Json>(
            '/me/singpass-verify',
            data: data.toJson(),
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {
          return PartialUser.fromJson(json! as Json);
        },
      );
    } catch (error) {
      Groveman.warning('singpassVerify', error: error);
      rethrow;
    }
  }

  Future<PostResponse<User>> updateUser(UpdateUserInput input) async {
    final formData = FormData.fromMap({
      ...input.toJson(),
    });

    if (input.profileImage != null) {
      formData.files.addAll(
        {
          'profile_image': await MultipartFile.fromFile(
            input.profileImage!.path,
          ),
        }.entries.toList(),
      );
    }

    try {
      final response = await ref
          .watch(repositoryProvider)
          .multipartPut<Json>('/users', data: formData);

      return PostResponse.fromJson(
        response.data!,
        (json) => PartialUser.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('updateUser', error: error);
      rethrow;
    }
  }

  Future<PostResponse<AuthResponse>> createUser(CreateUserInput input) async {
    try {
      final response = await ref
          .watch(repositoryProvider)
          .post<Json>('/users', data: input.toJson());

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return PostResponse.fromJson(
        response.data!,
        (json) => authResponse,
      );
    } catch (error) {
      Groveman.warning('createUser', error: error);
      rethrow;
    }
  }
}
