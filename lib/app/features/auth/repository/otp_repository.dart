import 'dart:convert';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/otp.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:groveman/groveman.dart';
import 'package:http/http.dart' as http;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_repository.g.dart';

@Riverpod(keepAlive: true)
OtpRepository otpRepository(OtpRepositoryRef ref) => OtpRepository(ref);

class OtpRepository {
  OtpRepository(this.ref);
  final OtpRepositoryRef ref;

  Future<PostResponse<OtpResponse>> requestOtp(RequestOtpInput input) async {
    final response = await ref.read(repositoryProvider).post<Json>(
          '/otp/register-or-login',
          data: input.toJson(),
        );
    try {
      return PostResponse.fromJson(
        response.data!,
        (json) => OtpResponse.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('requestOtp', error: error);
      rethrow;
    }
  }

  Future<PostResponse<void>> validateRegisterOtp(
    VerifyRegisterOtpInput input,
  ) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/otp/verify-registration-otp',
            data: input.toJson(),
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {},
      );
    } catch (error) {
      Groveman.warning('validateRegisterOtp', error: error);
      rethrow;
    }
  }

  Future<PostResponse<void>> requestUpdateEmailOrPhoneOtp(
    RequestEmailOrPhoneUpdateOtpInput input,
  ) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/otp/update-email-or-phone',
            data: input.toJson(),
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {},
      );
    } catch (error) {
      Groveman.warning('requestUpdateEmailOrPhoneOtp', error: error);
      rethrow;
    }
  }

  Future<PostResponse<User>> validateUpdateEmailOrPhoneOtp(
    VerifyUpdateEmailOrPhoneOtpInput input,
  ) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/otp/verify-update-email-or-phone',
            data: input.toJson(),
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {
          return PartialUser.fromJson(json! as Json);
        },
      );
    } catch (error) {
      Groveman.warning('validateUpdateEmailOrPhoneOtp', error: error);
      rethrow;
    }
  }
}
