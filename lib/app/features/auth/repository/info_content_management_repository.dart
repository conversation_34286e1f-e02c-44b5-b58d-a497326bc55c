import 'dart:convert';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/info_content_management.dart';
import 'package:gomama/app/features/auth/model/otp.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:groveman/groveman.dart';
import 'package:http/http.dart' as http;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'info_content_management_repository.g.dart';

@Riverpod(keepAlive: true)
InfoContentManagementRepository infoContentManagementRepository(
        InfoContentManagementRepositoryRef ref) =>
    InfoContentManagementRepository(ref);

class InfoContentManagementRepository {
  InfoContentManagementRepository(this.ref);
  final InfoContentManagementRepositoryRef ref;

  Future<PostResponse<InfoContentManagement>> getInfoContent(
      String slug,) async {
    final response =
        await ref.read(repositoryProvider).get<Json>('/cms/info-contents/$slug');
    try {
      return PostResponse.fromJson(
        response.data!,
        (json) => InfoContentManagement.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('getInfoContent', error: error);
      rethrow;
    }
  }
}
