// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpRepositoryHash() => r'ba724dee17d4dec288056fa0c3be7ad67ca4aaf6';

/// See also [otpRepository].
@ProviderFor(otpRepository)
final otpRepositoryProvider = Provider<OtpRepository>.internal(
  otpRepository,
  name: r'otpRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$otpRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OtpRepositoryRef = ProviderRef<OtpRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
