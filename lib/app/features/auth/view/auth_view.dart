import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:gomama/app/features/auth/widget/auth_form.dart';
import 'package:gomama/app/features/auth/widget/auth_otp_form.dart';
import 'package:gomama/app/features/auth/widget/profile_form.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

const duration = Duration(milliseconds: 400);

class AuthView extends HookConsumerWidget {
  const AuthView({super.key});

  static const routeName = 'auth';
  static const routePath = '/auth';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlow = ref.watch(authFlowControllerProvider);
    final authFlowController = ref.watch(authFlowControllerProvider.notifier);
    final controllerOne = useAnimationController();
    final controllerTwo = useAnimationController();
    final controllerThree = useAnimationController();

    useEffect(
      () {
        void animateToStep(int step) {
          if (step == 0) {
            controllerOne.animateTo(
              0,
              duration: duration,
              curve: Curves.ease,
            );
            controllerTwo.animateTo(
              0,
              duration: duration,
              curve: Curves.ease,
            );
            controllerThree.animateTo(
              0,
              duration: duration,
              curve: Curves.ease,
            );
          } else if (step == 1) {
            controllerOne.animateTo(
              1 / 2,
              duration: duration,
              curve: Curves.ease,
            );
            controllerTwo.animateTo(
              1 / 2,
              duration: duration,
              curve: Curves.ease,
            );
            controllerThree.animateTo(
              1 / 2,
              duration: duration,
              curve: Curves.ease,
            );
          } else if (step == 2) {
            controllerOne.animateTo(
              2 / 2,
              duration: duration,
              curve: Curves.ease,
            );
            controllerTwo.animateTo(
              2 / 2,
              duration: duration,
              curve: Curves.ease,
            );
            controllerThree.animateTo(
              2 / 2,
              duration: duration,
              curve: Curves.ease,
            );
          }
        }

        animateToStep(authFlow.step);

        return;
      },
      [authFlow.step],
    );

    return Scaffold(
      backgroundColor: CustomColors.primary,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Stack(
          children: [
            SizedBox(
              height: mediaQuery(context).size.height,
              width: mediaQuery(context).size.width,
            ),
            Positioned.fill(
              child: Image.asset(
                'assets/backgrounds/stars_background_edit_profile.png',
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: controllerThree.drive(
                  TweenSequence<Offset>([
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.1),
                        end: const Offset(0, -0.2),
                      ),
                      weight: 1,
                    ),
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.2),
                        end: const Offset(0, -0.3),
                      ),
                      weight: 1,
                    ),
                  ]),
                ),
                child: Image.asset(
                  'assets/backgrounds/milk_background_login_fourth.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: controllerTwo.drive(
                  TweenSequence<Offset>([
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.05),
                        end: const Offset(0, -0.2),
                      ),
                      weight: 1,
                    ),
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.2),
                        end: const Offset(0, -0.8),
                      ),
                      weight: 1,
                    ),
                  ]),
                ),
                child: Image.asset(
                  'assets/backgrounds/milk_background_login_third.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: controllerOne.drive(
                  TweenSequence<Offset>([
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: Offset.zero,
                        end: const Offset(0, -0.8),
                      ),
                      weight: 1,
                    ),
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.8),
                        end: const Offset(0, -0.9),
                      ),
                      weight: 1,
                    ),
                  ]),
                ),
                child: Image.asset(
                  'assets/backgrounds/milk_background_login_second.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: controllerOne.drive(
                  TweenSequence<Offset>([
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.1),
                        end: const Offset(0, -0.6),
                      ),
                      weight: 1,
                    ),
                    TweenSequenceItem(
                      tween: Tween<Offset>(
                        begin: const Offset(0, -0.6),
                        end: const Offset(0, -0.8),
                      ),
                      weight: 1,
                    ),
                  ]),
                ),
                child: Image.asset(
                  'assets/backgrounds/milk_background_login_first.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const _AuthBody(),
            if (authFlow.step > 0)
              Positioned(
                top: kToolbarHeight,
                left: 16,
                child: BackButton(
                  onPressed: () {
                    authFlowController.setStep(authFlow.step - 1);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _AuthBody extends ConsumerWidget {
  const _AuthBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlow = ref.watch(authFlowControllerProvider);

    switch (authFlow.step) {
      case 0:
        return const AuthForm();
      case 1:
        return const AuthOtpForm();
      case 2:
        return const ProfileForm();
      default:
        // error
        return const SizedBox.shrink();
    }
  }
}
