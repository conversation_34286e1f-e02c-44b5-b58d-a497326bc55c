import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/info_content_management_providers.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AboutGomamaView extends HookConsumerWidget {
  const AboutGomamaView({
    required this.initialContent,
    super.key,
  });

  final String initialContent;

  static const routeName = 'about-gomama';
  static const routePath = '/about-gomama';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BrandScaffold(
      title: const Text('About Go!Mama'),
      child: BrandBottomSheet(
        minHeight: mediaQuery(context).size.height -
            (max(
                  mediaQuery(context).viewPadding.top,
                  kToolbarHeight / 2,
                ) +
                100),
        maxHeight: max(
              mediaQuery(context).viewPadding.top,
              kToolbarHeight / 2,
            ) +
            100,
        slivers: [
          _AboutGomama(initialContent),
        ],
      ),
    );
  }
}

class _AboutGomama extends ConsumerWidget {
  const _AboutGomama(this.infoContent);
  final String infoContent;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contentAsync = ref.watch(getInfoContentProvider('about-gomama'));

    return contentAsync.when(
      error: (error, stackTrace) {
        Groveman.error('AboutGomamaView', error: error, stackTrace: stackTrace);

        return SliverFillRemaining(
          child: Center(
            child: Column(
              children: [
                const SizedBox(height: 64),
                Image.asset(
                  'assets/images/goma_sad.png',
                  height: 160,
                ),
                const Text('Unable to retrieve info, please try again later'),
              ],
            ),
          ),
        );
      },
      loading: () => const SliverFillRemaining(
        child: Center(
          child: SizedBox(
            width: 160,
            height: 160,
            child: LoadingView(),
          ),
        ),
      ),
      data: (infoContent) {
        return SliverList.list(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: HtmlWidget(
                infoContent,
              ),
            ),
            const SizedBox(height: 48),
          ],
        );
      },
    );
  }
}
