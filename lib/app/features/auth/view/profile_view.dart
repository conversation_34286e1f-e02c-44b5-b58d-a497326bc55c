import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/profile_media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/suggest/widget/add_profile_photo_dialog.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:gomama/app/widgets/brand_list_tile.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';

final clientAppVersionProvider = FutureProvider<String>((ref) async {
  final packageInfo = await PackageInfo.fromPlatform();

  return packageInfo.version;
});

class ProfileView extends ConsumerWidget {
  const ProfileView({super.key});

  static const routeName = 'profile';
  static const routePath = '/profile';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // return DecoratedBox(
    //   decoration: const BoxDecoration(
    //     gradient: LinearGradient(
    //       colors: [
    //         CustomColors.backgroundGradientLight,
    //         CustomColors.backgroundGradient,
    //         CustomColors.backgroundGradientDark,
    //       ],
    //       begin: Alignment.topCenter,
    //       end: Alignment.bottomCenter,
    //     ),
    //   ),
    //   child: StarsBackground(
    //     child: Material(
    //       color: Colors.transparent,
    //       child: Stack(
    //         children: [
    //           const Positioned(
    //             top: 120,
    //             left: 0,
    //             right: 0,
    //             child: Row(
    //               children: [
    //                 SizedBox(width: 32),
    //                 CircleAvatar(radius: 50),
    //                 SizedBox(width: 16),
    //                 Expanded(
    //                   child: Column(
    //                     crossAxisAlignment: CrossAxisAlignment.start,
    //                     children: [
    //                       Text(
    //                         'Hi, User!',
    //                         style: TextStyle(
    //                           fontSize: 24,
    //                           color: Colors.white,
    //                           fontWeight: FontWeight.w700,
    //                         ),
    //                       ),
    //                       _VerificationButton(),
    //                     ],
    //                   ),
    //                 ),
    //                 SizedBox(width: 32),
    //               ],
    //             ),
    //           ),
    //           const Positioned.fill(
    //             top: 270,
    //             child: RepaintBoundary(
    //               child: MilkBackground(),
    //             ),
    //           ),
    //           const Padding(
    //             padding: EdgeInsets.symmetric(horizontal: 16),
    //             child: Column(
    //               children: [
    //                 SizedBox(height: 320),
    //                 _MyAccount(),
    //                 _MoreInfo(),
    //                 // _MyAccount(),
    //                 // _MoreInfo(),
    //                 // _MyAccount(),
    //                 // _MoreInfo(),
    //                 // _MyAccount(),
    //                 // _MoreInfo(),
    //                 // _MyAccount(),
    //                 // _MoreInfo(),
    //               ],
    //             ),
    //           ),
    //           Positioned(
    //             top: kTextTabBarHeight,
    //             left: 16,
    //             child: Row(
    //               children: [
    //                 IconButton(
    //                   onPressed: () {
    //                     // use native pop because this is cupertino modal popup
    //                     Navigator.of(context).pop();
    //                   },
    //                   color: Colors.white,
    //                   icon: const Icon(Icons.home),
    //                 ),
    //                 const SizedBox(width: 16),
    //                 Text(
    //                   'Profile',
    //                   style: textTheme(context)
    //                       .titleLarge!
    //                       .copyWith(color: Colors.white),
    //                 ),
    //               ],
    //             ),
    //           ),
    //         ],
    //       ),
    //     ),
    //   ),
    // );

    // return const StarsBackground(
    //   child: Scaffold(
    //     backgroundColor: Colors.transparent,
    //     body: MilkBackground(
    //       child: Padding(
    //         padding: EdgeInsets.symmetric(horizontal: 16),
    //         // child: _Body(),
    //         child: SizedBox.expand(),
    //       ),
    //     ),
    //   ),
    // );

    return StarsBackground(
      child: MediaQuery.removePadding(
        context: context,
        // removeTop: true,
        removeBottom: true,
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              SliverOverlapAbsorber(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
                sliver: const SliverSafeArea(
                  top: false,
                  sliver: SliverAppBar(
                    centerTitle: false,
                    automaticallyImplyLeading: false,
                    foregroundColor: Colors.white,
                    collapsedHeight: kToolbarHeight,
                    primary: false,
                    expandedHeight: 128 + kToolbarHeight,
                    pinned: true,
                    scrolledUnderElevation: 0,
                    backgroundColor: Colors.transparent,
                    flexibleSpace: FlexibleSpaceBar(
                      collapseMode: CollapseMode.pin,
                      background: _ProfileHero(),
                    ),
                  ),
                ),
              ),
            ],
            // body: const MilkBackground(
            //   child: Padding(
            //     padding: EdgeInsets.symmetric(horizontal: 32),
            //     child: _Body(),
            //     // child: SizedBox.expand(),
            //   ),
            // ),
            body: const Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                MilkBackground(),
                Expanded(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: CustomColors.secondaryLight,
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 32),
                      child: _Body(),
                    ),
                  ),
                ),
              ],
            ),
            // body: const _Body(),
          ),
          // body: const RepaintBoundary(
          //   child: MilkBackground(
          //     child: Padding(
          //       padding: EdgeInsets.symmetric(horizontal: 16),
          //       child: _Body(),
          //     ),
          //   ),
          // ),
        ),
        // body: ListView(
        //   physics: const ClampingScrollPhysics(),
        //   children: const [
        //     MilkBackground(
        //       child: Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         // child: _Body(),
        //         child: Column(
        //           children: [
        //             // Container(color: Colors.blue, height: 300, width: 300),
        //             SizedBox(height: 300),
        //             _MyAccount(),
        //             _MyAccount(),
        //             _MyAccount(),
        //             _MyAccount(),
        //             _MoreInfo(),
        //             SizedBox(height: 160),
        //           ],
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
      ),
    );
  }
}

class _ProfileHero extends ConsumerWidget {
  const _ProfileHero();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // TODO: Need to fix not using listen else it will auto call updateUser to update user profile image
    ref.listen(profileMediaStateControllerProvider, (previous, next) {
      if (next.photos.isNotEmpty) {
        final input = UpdateUserInput(
          profileImage: next.photos[0],
        );
        ref.read(updateUserProvider(input));
        Groveman.info('Profile media length: ', error: next.photos.length);
        next.photos.clear();
        Groveman.info('Profile media length after clear: ',
            error: next.photos.length);
      }
    });

    final user = ref.watch(authControllerProvider).requireValue;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Row(
          children: [
            const SizedBox(width: 32),
            GestureDetector(
              onTap: () {
                // TODO(ch): integrate upload user.photoUrl
                showDialog<void>(
                  context: context,
                  builder: (BuildContext context) {
                    return const AddProfilePhotoDialog();
                  },
                );
              },
              child: Stack(
                children: [
                  Container(
                    width: 96,
                    height: 96,
                    decoration: BoxDecoration(
                      color: CustomColors.primary.withAlpha(200),
                      shape: BoxShape.circle,
                    ),
                    clipBehavior: Clip.hardEdge,
                    child: user.photoUrl?.isNotEmpty == true
                        ? CachedNetworkImage(
                            imageUrl: user.photoUrl ?? '',
                            fit: BoxFit.cover,
                            placeholder: (context, url) => const Center(
                              child: Icon(
                                CustomIcon.cameraAdd,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                            errorWidget: (context, url, error) => const Center(
                              child: Icon(
                                CustomIcon.cameraAdd,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                          )
                        : const Icon(
                            CustomIcon.cameraAdd,
                            color: Colors.white,
                            size: 32,
                          ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FittedBox(
                    fit: BoxFit.fitWidth,
                    child: Text(
                      'Hi, ${user.username ?? user.firstName ?? user.lastName ?? 'user'}!',
                      style: const TextStyle(
                        fontSize: 24,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _VerificationButton(isVerified: user.isVerified),
                ],
              ),
            ),
            const SizedBox(width: 32),
          ],
        ),
      ],
    );
  }
}

class _VerificationButton extends ConsumerWidget {
  const _VerificationButton({required this.isVerified});
  final bool isVerified;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: double.infinity,
      height: 72,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: PhysicalModel(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              elevation: 8,
              shadowColor: Colors.black54,
              child: Material(
                color: isVerified ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(20),
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: isVerified
                      ? null
                      : () {
                          const VerificationRoute().push(context);
                        },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 10),
                        const Text(
                          'Go!Mama Pass',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          isVerified
                              ? 'Verified' // if need to update your details, such as you have a newborn.'
                              : 'Verify for Access',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 3,
            child: IgnorePointer(
              child: Image.asset(
                'assets/images/goma_mascot.png',
                height: 60,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Body extends ConsumerWidget {
  const _Body();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CustomScrollView(
      physics: const ClampingScrollPhysics(),
      slivers: <Widget>[
        SliverOverlapInjector(
          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
            context,
          ),
        ),
        const SliverList(
          delegate: SliverChildListDelegate.fixed(
            [
              // TODO(kkcy): find scroll controller to dynamic this height
              // SizedBox(height: 100),
              _MyAccount(),
              _MoreInfo(),
            ],
          ),
        ),
      ],
    );
  }
}

class _MyAccount extends ConsumerWidget {
  const _MyAccount();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Material(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Account settings',
            style: textTheme(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          BrandListTileContainer(
            child: Column(
              children: ListTile.divideTiles(
                color: Colors.black26,
                tiles: [
                  BrandListTile(
                    leadingIcon: CustomIcon.editProfile,
                    titleString: 'Edit Profile',
                    onTap: () {
                      const EditProfileRoute().push(context);
                    },
                  ),
                  BrandListTile(
                    leadingIcon: CustomIcon.time,
                    titleString: 'Session History',
                    onTap: () {
                      const SessionHistoryRoute().push(context);
                    },
                  ),
                ],
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

class _MoreInfo extends ConsumerWidget {
  const _MoreInfo();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localAppVersion = ref.watch(clientAppVersionProvider);

    return Material(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Text(
            'More info and support',
            style: textTheme(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          BrandListTileContainer(
            child: Column(
              children: ListTile.divideTiles(
                color: Colors.black26,
                tiles: [
                  BrandExpansionTile(
                    leadingIcon: CustomIcon.info,
                    titleString: 'About',
                    children: [
                      BrandListTile(
                        leadingIcon: CustomIcon.gomLogoOnly,
                        titleString: 'About Go!Mama',
                        tileColor: CustomColors.secondaryExtraLight,
                        onTap: () {
                          const AboutGomamaRoute('').push(context);
                        },
                      ),
                      BrandListTile(
                        leadingIcon: CustomIcon.privacyPolicy,
                        titleString: 'Privacy Policy',
                        tileColor: CustomColors.secondaryExtraLight,
                        onTap: () {
                          const PrivacyPolicyRoute('').push(context);
                        },
                      ),
                      BrandListTile(
                        leadingIcon: CustomIcon.termsOfService,
                        titleString: 'Terms of Service',
                        tileColor: CustomColors.secondaryExtraLight,
                        onTap: () {
                          const TermServiceRoute('').push(context);
                        },
                      ),
                    ],
                  ),
                  BrandExpansionTile(
                    leadingIcon: CustomIcon.help,
                    titleString: 'Help',
                    children: [
                      BrandListTile(
                        leadingIcon: CustomIcon.faq,
                        titleString: 'FAQs',
                        tileColor: CustomColors.secondaryExtraLight,
                        onTap: () {
                          const FrequentlyAskedQuestionRoute('').push(context);
                        },
                      ),
                      BrandListTile(
                        leadingIcon: CustomIcon.whatsapp,
                        titleString: 'WhatsApp',
                        tileColor: CustomColors.secondaryExtraLight,
                        onTap: () {
                          ref.read(
                            sendWhatsAppMessageProvider(
                              'Hi GO!MAMA, I need some help on ',
                              Environment.gomamaWhatsappPhone,
                            ),
                          );
                        },
                      ),
                      // BrandListTile(
                      //   leadingIcon: CustomIcon.help,
                      //   titleString: 'Feedback',
                      //   tileColor: CustomColors.secondaryExtraLight,
                      //   onTap:() {
                      //     print('hello');
                      //   },
                      // ),
                    ],
                  ),
                  // BrandListTile(
                  //   leadingIcon: Icons.delete,
                  //   titleString: 'Delete Account',
                  //   onTap: () {
                  //     showDialog<void>(
                  //       context: context,
                  //       builder: (BuildContext context) {
                  //         return AlertDialog(
                  //           title: const Text(
                  //             'Deleting your account is permanent',
                  //             textAlign: TextAlign.center,
                  //             style: TextStyle(
                  //               color: CustomColors.primary,
                  //             ),
                  //           ),
                  //           content: IntrinsicHeight(
                  //             child: Row(
                  //               mainAxisAlignment: MainAxisAlignment.center,
                  //               children: [
                  //                 TextButton(
                  //                   style: TextButton.styleFrom(
                  //                     side: const BorderSide(
                  //                       color: CustomColors.primary,
                  //                     ),
                  //                     foregroundColor: CustomColors.primary,
                  //                   ),
                  //                   onPressed: () {
                  //                     Navigator.of(context).pop();
                  //                   },
                  //                   child: const Text('Cancel'),
                  //                 ),
                  //                 const SizedBox(
                  //                   width: 24,
                  //                 ),
                  //                 TextButton(
                  //                   style: TextButton.styleFrom(
                  //                     backgroundColor: CustomColors.primary,
                  //                     foregroundColor: Colors.white,
                  //                   ),
                  //                   onPressed: () {},
                  //                   child: const Text('Delete'),
                  //                 ),
                  //               ],
                  //             ),
                  //           ),
                  //         );
                  //       },
                  //     );
                  //   },
                  // ),
                  BrandListTile(
                    leadingIcon: CustomIcon.logout,
                    titleString: 'Logout',
                    color: CustomColors.red,
                    onTap: () {
                      showDialog<void>(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog.adaptive(
                            title: const Text(
                              'Are you sure\nyou want to logout?',
                              textAlign: TextAlign.center,
                            ),
                            actions: [
                              AdaptiveTextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: const Text('Cancel'),
                              ),
                              AdaptiveTextButton(
                                onPressed: () {
                                  ref
                                      .read(
                                        authControllerProvider.notifier,
                                      )
                                      .logout();
                                },
                                state: AdaptiveTextButtonState.danger,
                                child: const Text('Logout'),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  ),
                ],
              ).toList(),
            ),
          ),
          const SizedBox(height: 16),
          localAppVersion
                  .whenData(
                    (val) => Center(child: Text('Client Version: $val')),
                  )
                  .asData
                  ?.valueOrNull ??
              const SizedBox.shrink(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
