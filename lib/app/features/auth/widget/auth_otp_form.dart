import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/custom_validators.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:gomama/app/features/auth/widget/countdown_timer.dart';
import 'package:gomama/app/features/auth/widget/otp_field.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AuthOtpForm extends HookConsumerWidget {
  const AuthOtpForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlow = ref.watch(authFlowControllerProvider);
    final authFlowController = ref.watch(authFlowControllerProvider.notifier);

    if (authFlow.mobileNumberOrEmail == null) {
      return const SizedBox.shrink();
    }

    final isMobileNumber = isValidMobileNumber(authFlow.mobileNumberOrEmail!);
    final errorText = useState<String?>(null);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Center(
        child: Column(
          children: [
            const SizedBox(height: 80),
            Image.asset(
              'assets/images/otp.png',
              width: 120,
            ),
            const SizedBox(height: 20),
            Text(
              'Verify OTP', // for ${authFlow.isLogin == true ? 'Login' : 'Registration & Auto Login'} ${errorText.value}',
              style: textTheme(context).titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'We sent a verification code to',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: CustomColors.primary,
              ),
            ),
            Text(
              isMobileNumber
                  ? authFlow.mobileNumberOrEmail!.replaceAllMapped(
                      RegExp(r'(\d{4})'),
                      (match) => '${match[0]} ',
                    )
                  : authFlow.mobileNumberOrEmail ?? '',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: CustomColors.primary,
              ),
            ),
            if (isMobileNumber)
              const Column(
                children: [
                  SizedBox(height: 12),
                  Text(
                    'If you did not receive any message after sometime, please ensure your number is correct with correct country code, and try again.',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            const SizedBox(height: 36),
            OtpField(
              errorText: errorText.value,
              onCompleted: (otp) async {
                errorText.value = null;

                if (authFlow.isLogin == true) {
                  try {
                    await ref
                        .read(authControllerProvider.notifier)
                        .loginWithOtp(authFlow.mobileNumberOrEmail!, otp);
                  } catch (e, s) {
                    Groveman.error(
                      'OtpField onComplete',
                      error: e,
                      stackTrace: s,
                    );

                    errorText.value = 'Invalid OTP';
                  }
                } else {
                  try {
                    await authFlowController.validateRegisterOtp(otp);
                  } catch (e, s) {
                    Groveman.error(
                      'OtpField onComplete',
                      error: e,
                      stackTrace: s,
                    );

                    errorText.value = 'Invalid OTP';
                  }
                }
              },
            ),
            const SizedBox(height: 20),
            CountdownTimerWidget(
              onResend: () async {
                await authFlowController.requestOtp(
                    authFlow.email,
                    authFlow.mobileNumber,
                    authFlow.countryDialCode,
                    authFlowController.isSubmittedWithEmail());
              },
            ),
          ],
        ),
      ),
    );
  }
}
