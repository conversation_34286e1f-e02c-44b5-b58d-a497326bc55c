import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:pinput/pinput.dart';

class OtpField extends StatefulWidget {
  const OtpField({
    super.key,
    this.errorText,
    required this.onCompleted,
  });

  final String? errorText;
  final void Function(String pin) onCompleted;

  @override
  State<OtpField> createState() => _OtpFieldState();

  @override
  String toStringShort() => 'Rounded With Cursor';
}

class _OtpFieldState extends State<OtpField> {
  final pinController = TextEditingController();
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const borderColor = CustomColors.primary;

    final defaultPinTheme = PinTheme(
      width: 40,
      height: 50,
      textStyle: textTheme(context).bodyLarge!.copyWith(
            fontWeight: FontWeight.bold,
          ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Color(0x33470A54), // #470A5433 in ARGB format
            offset: Offset(0, 2), // x and y offset
            blurRadius: 4, // blur radius
          ),
        ],
      ),
    );

    return Form(
      key: formKey,
      child: Column(
        children: [
          Directionality(
            textDirection: TextDirection.ltr,
            child: Pinput(
              length: 6,
              controller: pinController,
              focusNode: focusNode,
              // androidSmsAutofillMethod:
              //     AndroidSmsAutofillMethod.smsUserConsentApi,
              // listenForMultipleSmsOnAndroid: true,
              defaultPinTheme: defaultPinTheme,
              // validator: (value) {
              //   return value == dummyOtp ? null : 'Invalid OTP Code';
              // },
              errorText: widget.errorText,
              forceErrorState: widget.errorText != null,
              hapticFeedbackType: HapticFeedbackType.lightImpact,
              onCompleted: widget.onCompleted,
              cursor: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    margin: const EdgeInsets.only(bottom: 9),
                    width: 22,
                    height: 1,
                    color: borderColor,
                  ),
                ],
              ),
              errorPinTheme: defaultPinTheme.copyWith(
                textStyle: textTheme(context).bodyLarge!.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.redAccent,
                    ),
              ),
              errorTextStyle: const TextStyle(
                color: Colors.redAccent,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
