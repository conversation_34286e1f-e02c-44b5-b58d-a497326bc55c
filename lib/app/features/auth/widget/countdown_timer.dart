import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';

class CountdownTimerWidget extends StatefulWidget {
  const CountdownTimerWidget({
    required this.onResend,
    super.key,
  });

  final Function()? onResend;

  @override
  State<CountdownTimerWidget> createState() => _CountdownTimerWidgetState();
}

class _CountdownTimerWidgetState extends State<CountdownTimerWidget> {
  int totalSeconds = 60;
  bool busy = false;
  late Timer timer;

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (totalSeconds > 0) {
        setState(() {
          totalSeconds--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;

    if (totalSeconds <= 0) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        child: BrandButton.outlined(
          onPressed: busy
              ? null
              : () async {
                  setState(() {
                    busy = true;
                  });

                  await widget.onResend?.call();

                  setState(() {
                    totalSeconds = 60;
                    busy = false;
                    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
                      if (totalSeconds > 0) {
                        setState(() {
                          totalSeconds--;
                        });
                      } else {
                        timer.cancel();
                      }
                    });
                  });
                },
          padding: EdgeInsets.zero,
          child: const Text('Resend'),
        ),
      );
    }

    return RichText(
      text: TextSpan(
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
        ),
        children: [
          const TextSpan(text: 'Resend code in '),
          TextSpan(
            text: '$minutes:${seconds.toString().padLeft(2, '0')}',
            style: const TextStyle(
              color: CustomColors.primaries,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }
}
