import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProfileForm extends HookConsumerWidget {
  const ProfileForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlowController = ref.watch(authFlowControllerProvider.notifier);

    final _formKey = useState(GlobalKey<FormBuilderState>());
    final _isBusy = useState<bool>(false);
    final _errorMsg = useState<String?>(null);

    return FormBuilder(
      key: _formKey.value,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          children: [
            const SizedBox(height: 80),
            Image.asset(
              'assets/images/user.png',
              width: 120,
            ),
            const SizedBox(height: 20),
            const Text(
              'Your username',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),
            Material(
              elevation: 2,
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: FormBuilderTextField(
                  name: 'username',
                  autocorrect: false,
                  maxLength: 12,
                  decoration: InputDecoration(
                    hintText: 'Username',
                    hintStyle: textTheme(context)
                        .bodyMedium!
                        .copyWith(color: CustomColors.placeholder),
                    contentPadding: EdgeInsets.zero,
                    isCollapsed: true,
                    border: InputBorder.none,
                    alignLabelWithHint: false,
                    errorText: _errorMsg.value,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your username';
                    }

                    if (value.length < 3) {
                      return 'Minimum 3 characters long';
                    }

                    return null;
                  },
                ),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: CustomColors.primary,
                  foregroundColor: Colors.white,
                ),
                onPressed: () async {
                  // Validate and save the form values
                  final success =
                      _formKey.value.currentState?.saveAndValidate();

                  if (success != true || _isBusy.value) {
                    return;
                  }

                  _errorMsg.value = null;

                  final username =
                      _formKey.value.currentState?.value['username'].toString();

                  final isUsernameTaken =
                      await authFlowController.checkUsernameExist(username!);

                  if (isUsernameTaken) {
                    _errorMsg.value = 'The username has been taken';

                    return;
                  }

                  _isBusy.value = true;

                  try {
                    if (authFlowController.isSubmittedWithEmail()) {
                      await authFlowController.createUserWithEmail(username);
                    } else {
                      await authFlowController.createUserWithPhone(username);
                    }
                  } catch (e) {
                    _errorMsg.value = e.toString();
                  } finally {
                    _isBusy.value = false;
                  }
                },
                child: Text(_isBusy.value ? 'Loading...' : 'Continue'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
