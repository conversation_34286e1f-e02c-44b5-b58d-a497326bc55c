// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authFlowControllerHash() =>
    r'ccbb8200868edf2823742051867b55a7334ebe5c';

/// See also [AuthFlowController].
@ProviderFor(AuthFlowController)
final authFlowControllerProvider =
    AutoDisposeNotifierProvider<AuthFlowController, AuthFlow>.internal(
  AuthFlowController.new,
  name: r'authFlowControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authFlowControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AuthFlowController = AutoDisposeNotifier<AuthFlow>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
