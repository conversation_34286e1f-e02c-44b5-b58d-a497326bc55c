// import 'dart:async';

// import 'package:gomama/app/core/local_storage/app_storage.dart';
// import 'package:gomama/app/features/auth/model/user.dart';
// import 'package:gomama/app/features/auth/repository/user_repository.dart';
// import 'package:gomama/app/features/main/providers/main_providers.dart';

// import 'package:groveman/groveman.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/repository/user_repository.dart';
import 'package:gomama/app/features/edit_profile/model/selfie_verification.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_providers.g.dart';

@riverpod
class SingleUser extends _$SingleUser {
  @override
  FutureOr<User?> build(String username) async {
    return ref.watch(userRepositoryProvider).searchUsername(username);
  }
}

@riverpod
Future<bool> updateUser(
  UpdateUserRef ref,
  UpdateUserInput input,
) async {
  final response = await ref.watch(userRepositoryProvider).updateUser(input);

  if (response.success) {
    ref.read(authControllerProvider.notifier).setUser(response.data);
  }

  return response.success;
}

@riverpod
Future<void> createUser(
  CreateUserRef ref,
  CreateUserInput input,
) async {
  final response = await ref.watch(userRepositoryProvider).createUser(input);

  /// NOTE: to automate login flow
  if (response.success) {
    await ref.read(authControllerProvider.notifier).loginWithToken(
          response.data.token.token,
          response.data.token.realtimeJwt,
        );
  }
}

@riverpod
Future<bool> deleteUser(
  DeleteUserRef ref,
  String reason,
) async {
  final response = await ref.watch(userRepositoryProvider).deleteAccount(reason);

  return response;
}

@riverpod
Future<void> addSelfieFailCount(
  AddSelfieFailCountRef ref,
) async {
  final response =
      await ref.watch(userRepositoryProvider).gomamaVerifySelfieFailCountAdd();

  /// NOTE: to automate login flow
  if (response.success) {
    ref.read(authControllerProvider.notifier).setUser(response.data);
  }
}
