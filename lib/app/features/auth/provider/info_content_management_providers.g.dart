// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_content_management_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getInfoContentHash() => r'9889c8ae04611caaa076d83b6807100bd240d25b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getInfoContent].
@ProviderFor(getInfoContent)
const getInfoContentProvider = GetInfoContentFamily();

/// See also [getInfoContent].
class GetInfoContentFamily extends Family<AsyncValue<String>> {
  /// See also [getInfoContent].
  const GetInfoContentFamily();

  /// See also [getInfoContent].
  GetInfoContentProvider call(
    String slug,
  ) {
    return GetInfoContentProvider(
      slug,
    );
  }

  @override
  GetInfoContentProvider getProviderOverride(
    covariant GetInfoContentProvider provider,
  ) {
    return call(
      provider.slug,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getInfoContentProvider';
}

/// See also [getInfoContent].
class GetInfoContentProvider extends AutoDisposeFutureProvider<String> {
  /// See also [getInfoContent].
  GetInfoContentProvider(
    String slug,
  ) : this._internal(
          (ref) => getInfoContent(
            ref as GetInfoContentRef,
            slug,
          ),
          from: getInfoContentProvider,
          name: r'getInfoContentProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getInfoContentHash,
          dependencies: GetInfoContentFamily._dependencies,
          allTransitiveDependencies:
              GetInfoContentFamily._allTransitiveDependencies,
          slug: slug,
        );

  GetInfoContentProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.slug,
  }) : super.internal();

  final String slug;

  @override
  Override overrideWith(
    FutureOr<String> Function(GetInfoContentRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetInfoContentProvider._internal(
        (ref) => create(ref as GetInfoContentRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        slug: slug,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<String> createElement() {
    return _GetInfoContentProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetInfoContentProvider && other.slug == slug;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, slug.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetInfoContentRef on AutoDisposeFutureProviderRef<String> {
  /// The parameter `slug` of this provider.
  String get slug;
}

class _GetInfoContentProviderElement
    extends AutoDisposeFutureProviderElement<String> with GetInfoContentRef {
  _GetInfoContentProviderElement(super.provider);

  @override
  String get slug => (origin as GetInfoContentProvider).slug;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
