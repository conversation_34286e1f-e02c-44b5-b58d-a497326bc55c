import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/auth_flow.dart';
import 'package:gomama/app/features/auth/model/otp.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/auth/repository/otp_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_providers.g.dart';

@riverpod
class AuthFlowController extends _$AuthFlowController {
  @override
  AuthFlow build() {
    return AuthFlow(step: 0);
  }

  void setStep(int step) {
    state = state.copyWith(step: step);
  }

  void setMobileNumberNCountryDialCode(
      String? mobileNumber, String? countryDialCode) {
    state = state.copyWith(
        mobileNumber: mobileNumber, countryDialCode: countryDialCode);
  }

  void setEmail(String? email) {
    state = state.copyWith(email: email);
  }

  void setmobileNumberOrEmail(String mobileNumberOrEmail) {
    state = state.copyWith(mobileNumberOrEmail: mobileNumberOrEmail);
  }

  void setIsLogin(bool isLogin) {
    state = state.copyWith(isLogin: isLogin);
  }

  void setOtp(String otp) {
    state = state.copyWith(otp: otp);
  }

  bool isSubmittedWithEmail() {
    return state.email != null;
  }

  Future<void> requestOtp(String? email, String? mobileNumber,
      String? countryDialCode, bool isEmail) async {
    if (isEmail) {
      setMobileNumberNCountryDialCode(null, null);
      setEmail(email!);
      setmobileNumberOrEmail(email);
    } else {
      setEmail(null);
      setMobileNumberNCountryDialCode(mobileNumber!, countryDialCode!);
      setmobileNumberOrEmail(countryDialCode + mobileNumber);
    }

    final response = await ref.watch(otpRepositoryProvider).requestOtp(
          RequestOtpInput(
            loginAccount: isEmail ? email : (countryDialCode! + mobileNumber!),
          ),
        );

    setIsLogin(response.data.isLogin);
    setStep(1);
  }

  Future<void> validateRegisterOtp(String otp) async {
    final response = await ref.watch(otpRepositoryProvider).validateRegisterOtp(
          VerifyRegisterOtpInput(
            registerAccount: state.mobileNumberOrEmail,
            otp: otp,
          ),
        );

    if (response.success) {
      setStep(2);
    } else {
      // error
      setStep(9);
    }
  }

  Future<bool> checkUsernameExist(String username) async {
    try {
      final userAsync = await ref.read(singleUserProvider(username).future);

      return userAsync != null;
    } catch (error) {
      if (error is DioException && error.response?.statusCode == 404) {
        // If it's a 404 error, it means the username doesn't exist
        return false;
      } else {
        Groveman.warning('isUsernameExist', error: error);
        // For any other errors, log it and return false
        return false;
      }
    }
  }

  Future<void> createUserWithEmail(String username) async {
    final input = CreateUserInput.withEmail(
      newEmail: state.email!,
      username: username,
    );

    return ref.read(createUserProvider(input));
  }

  Future<void> createUserWithPhone(String username) async {
    final input = CreateUserInput.withPhone(
      countryDialCode: state.countryDialCode!,
      newMobileNumber: state.mobileNumber!,
      username: username,
    );

    return ref.read(createUserProvider(input));
  }
}
