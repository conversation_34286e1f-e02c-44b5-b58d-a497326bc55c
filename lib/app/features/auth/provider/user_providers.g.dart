// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateUserHash() => r'a6a2245256603ea6f1bf09b0d73fe4ce4cc6bb5b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [updateUser].
@ProviderFor(updateUser)
const updateUserProvider = UpdateUserFamily();

/// See also [updateUser].
class UpdateUserFamily extends Family<AsyncValue<bool>> {
  /// See also [updateUser].
  const UpdateUserFamily();

  /// See also [updateUser].
  UpdateUserProvider call(
    UpdateUserInput input,
  ) {
    return UpdateUserProvider(
      input,
    );
  }

  @override
  UpdateUserProvider getProviderOverride(
    covariant UpdateUserProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'updateUserProvider';
}

/// See also [updateUser].
class UpdateUserProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [updateUser].
  UpdateUserProvider(
    UpdateUserInput input,
  ) : this._internal(
          (ref) => updateUser(
            ref as UpdateUserRef,
            input,
          ),
          from: updateUserProvider,
          name: r'updateUserProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$updateUserHash,
          dependencies: UpdateUserFamily._dependencies,
          allTransitiveDependencies:
              UpdateUserFamily._allTransitiveDependencies,
          input: input,
        );

  UpdateUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final UpdateUserInput input;

  @override
  Override overrideWith(
    FutureOr<bool> Function(UpdateUserRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateUserProvider._internal(
        (ref) => create(ref as UpdateUserRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _UpdateUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateUserProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateUserRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `input` of this provider.
  UpdateUserInput get input;
}

class _UpdateUserProviderElement extends AutoDisposeFutureProviderElement<bool>
    with UpdateUserRef {
  _UpdateUserProviderElement(super.provider);

  @override
  UpdateUserInput get input => (origin as UpdateUserProvider).input;
}

String _$createUserHash() => r'05407e71e6b9886fa73585379300c2b98aff632b';

/// See also [createUser].
@ProviderFor(createUser)
const createUserProvider = CreateUserFamily();

/// See also [createUser].
class CreateUserFamily extends Family<AsyncValue<void>> {
  /// See also [createUser].
  const CreateUserFamily();

  /// See also [createUser].
  CreateUserProvider call(
    CreateUserInput input,
  ) {
    return CreateUserProvider(
      input,
    );
  }

  @override
  CreateUserProvider getProviderOverride(
    covariant CreateUserProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'createUserProvider';
}

/// See also [createUser].
class CreateUserProvider extends AutoDisposeFutureProvider<void> {
  /// See also [createUser].
  CreateUserProvider(
    CreateUserInput input,
  ) : this._internal(
          (ref) => createUser(
            ref as CreateUserRef,
            input,
          ),
          from: createUserProvider,
          name: r'createUserProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$createUserHash,
          dependencies: CreateUserFamily._dependencies,
          allTransitiveDependencies:
              CreateUserFamily._allTransitiveDependencies,
          input: input,
        );

  CreateUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final CreateUserInput input;

  @override
  Override overrideWith(
    FutureOr<void> Function(CreateUserRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CreateUserProvider._internal(
        (ref) => create(ref as CreateUserRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _CreateUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CreateUserProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CreateUserRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `input` of this provider.
  CreateUserInput get input;
}

class _CreateUserProviderElement extends AutoDisposeFutureProviderElement<void>
    with CreateUserRef {
  _CreateUserProviderElement(super.provider);

  @override
  CreateUserInput get input => (origin as CreateUserProvider).input;
}

String _$deleteUserHash() => r'3542f190f6709ec3c27548990cd574d3da444fce';

/// See also [deleteUser].
@ProviderFor(deleteUser)
const deleteUserProvider = DeleteUserFamily();

/// See also [deleteUser].
class DeleteUserFamily extends Family<AsyncValue<bool>> {
  /// See also [deleteUser].
  const DeleteUserFamily();

  /// See also [deleteUser].
  DeleteUserProvider call(
    String reason,
  ) {
    return DeleteUserProvider(
      reason,
    );
  }

  @override
  DeleteUserProvider getProviderOverride(
    covariant DeleteUserProvider provider,
  ) {
    return call(
      provider.reason,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'deleteUserProvider';
}

/// See also [deleteUser].
class DeleteUserProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [deleteUser].
  DeleteUserProvider(
    String reason,
  ) : this._internal(
          (ref) => deleteUser(
            ref as DeleteUserRef,
            reason,
          ),
          from: deleteUserProvider,
          name: r'deleteUserProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$deleteUserHash,
          dependencies: DeleteUserFamily._dependencies,
          allTransitiveDependencies:
              DeleteUserFamily._allTransitiveDependencies,
          reason: reason,
        );

  DeleteUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.reason,
  }) : super.internal();

  final String reason;

  @override
  Override overrideWith(
    FutureOr<bool> Function(DeleteUserRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeleteUserProvider._internal(
        (ref) => create(ref as DeleteUserRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        reason: reason,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _DeleteUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DeleteUserProvider && other.reason == reason;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, reason.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeleteUserRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `reason` of this provider.
  String get reason;
}

class _DeleteUserProviderElement extends AutoDisposeFutureProviderElement<bool>
    with DeleteUserRef {
  _DeleteUserProviderElement(super.provider);

  @override
  String get reason => (origin as DeleteUserProvider).reason;
}

String _$addSelfieFailCountHash() =>
    r'd8d190e6d8ffe11e2fd50b011dcd09595555ce3a';

/// See also [addSelfieFailCount].
@ProviderFor(addSelfieFailCount)
final addSelfieFailCountProvider = AutoDisposeFutureProvider<void>.internal(
  addSelfieFailCount,
  name: r'addSelfieFailCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addSelfieFailCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AddSelfieFailCountRef = AutoDisposeFutureProviderRef<void>;
String _$singleUserHash() => r'43fb1ec61829a3f2dcc5ce29a0fe0e5566908728';

abstract class _$SingleUser extends BuildlessAutoDisposeAsyncNotifier<User?> {
  late final String username;

  FutureOr<User?> build(
    String username,
  );
}

/// See also [SingleUser].
@ProviderFor(SingleUser)
const singleUserProvider = SingleUserFamily();

/// See also [SingleUser].
class SingleUserFamily extends Family<AsyncValue<User?>> {
  /// See also [SingleUser].
  const SingleUserFamily();

  /// See also [SingleUser].
  SingleUserProvider call(
    String username,
  ) {
    return SingleUserProvider(
      username,
    );
  }

  @override
  SingleUserProvider getProviderOverride(
    covariant SingleUserProvider provider,
  ) {
    return call(
      provider.username,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'singleUserProvider';
}

/// See also [SingleUser].
class SingleUserProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SingleUser, User?> {
  /// See also [SingleUser].
  SingleUserProvider(
    String username,
  ) : this._internal(
          () => SingleUser()..username = username,
          from: singleUserProvider,
          name: r'singleUserProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$singleUserHash,
          dependencies: SingleUserFamily._dependencies,
          allTransitiveDependencies:
              SingleUserFamily._allTransitiveDependencies,
          username: username,
        );

  SingleUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.username,
  }) : super.internal();

  final String username;

  @override
  FutureOr<User?> runNotifierBuild(
    covariant SingleUser notifier,
  ) {
    return notifier.build(
      username,
    );
  }

  @override
  Override overrideWith(SingleUser Function() create) {
    return ProviderOverride(
      origin: this,
      override: SingleUserProvider._internal(
        () => create()..username = username,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        username: username,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SingleUser, User?> createElement() {
    return _SingleUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SingleUserProvider && other.username == username;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, username.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleUserRef on AutoDisposeAsyncNotifierProviderRef<User?> {
  /// The parameter `username` of this provider.
  String get username;
}

class _SingleUserProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SingleUser, User?>
    with SingleUserRef {
  _SingleUserProviderElement(super.provider);

  @override
  String get username => (origin as SingleUserProvider).username;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
