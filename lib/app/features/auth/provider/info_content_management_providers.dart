import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/info_content_management.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/repository/info_content_management_repository.dart';
import 'package:gomama/app/features/auth/repository/user_repository.dart';
import 'package:gomama/app/features/edit_profile/model/selfie_verification.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'info_content_management_providers.g.dart';

@riverpod
Future<String> getInfoContent(
  GetInfoContentRef ref,
  String slug,
) async {
  final response = await ref
      .watch(infoContentManagementRepositoryProvider)
      .getInfoContent(slug);

  return response.data.content;
}
