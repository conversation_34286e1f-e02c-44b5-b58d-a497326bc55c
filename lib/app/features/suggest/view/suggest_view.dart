import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SuggestView extends HookConsumerWidget {
  const SuggestView({super.key});

  static const routeName = 'suggest';
  static const routePath = '/suggest';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: const Text('Suggest'),
              actions: [
                FilledButton.icon(
                  icon: const Icon(
                    CustomIcon.dottedTime,
                    color: CustomColors.primary,
                  ),
                  style: FilledButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: CustomColors.primary,
                    minimumSize: Size.zero,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                  ),
                  label: const Text('History'),
                  onPressed: () {
                    const SuggestHistoryRoute().push(context);
                  },
                ),
                const SizedBox(width: 16),
              ],
            ),
            pinned: true,
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            sliver: SliverList.list(
              children: const [
                PodSuggestionCard(),
                SizedBox(height: 24),
                CareSuggestionCard(),
                SizedBox(height: 36),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class PodSuggestionCard extends ConsumerWidget {
  const PodSuggestionCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: AspectRatio(
        aspectRatio: 328 / 216,
        child: DecoratedBox(
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/images/pod_suggestion.png'),
              fit: BoxFit.fitWidth,
            ),
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                CustomColors.primaries.shade100,
                CustomColors.primaries.shade50,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                const SuggestPodRoute().push(context);
              },
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: FractionallySizedBox(
                    widthFactor: 0.60,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Suggest Go!Mama Pod's location",
                          style: textTheme(context).titleSmall,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Need a private space to breastfeed or pump?',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CareSuggestionCard extends ConsumerWidget {
  const CareSuggestionCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: AspectRatio(
        aspectRatio: 328 / 216,
        child: DecoratedBox(
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/images/care_suggestion.png'),
              fit: BoxFit.fitHeight,
              alignment: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            gradient: const LinearGradient(
              colors: [
                CustomColors.secondary,
                CustomColors.secondaryLight,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                const SuggestNursingFacilityRoute().push(context);
              },
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: FractionallySizedBox(
                    widthFactor: 0.50,
                    child: Column(
                      children: [
                        Text(
                          'Add an Existing Nursing Facility',
                          style: textTheme(context).titleSmall,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          "Did you see an existing nursing facility that's not in our listing? Share it here!",
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
