import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/debounce_provider.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/maps/provider/google_places_providers.dart';
import 'package:gomama/app/features/maps/provider/mapbox_map_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/maps/widget/suggest_map.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/features/suggest/widget/add_photo_dialog.dart';
import 'package:gomama/app/mixin/google_places_overlay.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// UI utility providers
final _textEditingControllerProvider = Provider.autoDispose
    .family<TextEditingController, String>((ref, controllerKey) {
  return TextEditingController();
});

final stepIndexProvider = StateProvider.autoDispose<int>((ref) => 0);
final stepIndexNotifierProvider =
    Provider.autoDispose<ValueNotifier<int>>((ref) {
  final notifier = ValueNotifier<int>(0);

  ref.listen(stepIndexProvider, (_, next) {
    notifier.value = next;
  });

  return notifier;
});
// UI utility providers

class SuggestPodView extends HookConsumerWidget {
  const SuggestPodView({super.key});

  static const routeName = 'suggest pod';
  static const routePath = '/suggest/gomama';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepNotifier = ref.watch(stepIndexNotifierProvider);

    final addressController =
        ref.watch(_textEditingControllerProvider('address'));
    final noteController = ref.watch(_textEditingControllerProvider('note'));
    final photos = ref.watch(
      mediaStateControllerProvider.select((value) => value.photos),
    );
    // TODO(kkcy): get the position ready
    ref
      ..watch(currentPositionProvider)
      ..watch(_textEditingControllerProvider('region'))
      ..watch(mapboxMapStateControllerProvider);

    final address = useValueListenable(addressController);
    final note = useValueListenable(noteController);
    final currentStep = useValueListenable(stepNotifier);

    final isValidForNext = useMemoized(
      () {
        switch (currentStep) {
          case 0:
            return address.text.isNotEmpty;
          case 1:
            return address.text.isNotEmpty;
          case 2:
            return photos.isNotEmpty;
          case 3:
            return note.text.isNotEmpty;
          default:
            return true;
        }
      },
      [currentStep, address, photos, note],
    );

    Future<void> onSubmit() async {
      late double latitude, longitude;

      latitude = ref.read(mapboxMapStateControllerProvider).latitude;
      longitude = ref.read(mapboxMapStateControllerProvider).longitude;

      final input = ListingSuggestionInput.podSuggestionInput(
        region: ref.read(_textEditingControllerProvider('region')).text,
        fullAddress: ref.read(_textEditingControllerProvider('address')).text,
        latitude: latitude,
        longitude: longitude,
        mainImageFile: photos.first,
        subImageFiles: photos.length > 1 ? photos.skip(1).toList() : [],
        note: ref.read(_textEditingControllerProvider('note')).text,
      );

      Groveman.debug('suggest nursing facility', error: input);

      final response = await ref.read(
        suggestListingProvider(input).future,
      );

      if (response.success) {
        final dialogContext = context; // Capture the context

        if (context.mounted) {
          await showDialog<void>(
            context: dialogContext, // Use the captured context
            builder: (BuildContext context) {
              return _SubmittedDialog(response.data.id);
            },
          ).then((value) {
            if (context.mounted) {
              // Redirect to SuggestRoute view when the dialog is closed
              const SuggestRoute().go(context);
            }
          });
        }
      }
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: const Text('Suggest A Pod!'),
            ),
            pinned: true,
          ),
          SliverToBoxAdapter(
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: Stepper(
                physics: const NeverScrollableScrollPhysics(),
                currentStep: currentStep,
                onStepCancel: () {
                  if (currentStep > 0) {
                    ref.read(stepIndexProvider.notifier).state--;
                  }
                },
                onStepContinue: () {
                  if (currentStep < 3) {
                    ref.read(stepIndexProvider.notifier).state++;
                  }
                },
                controlsBuilder:
                    (BuildContext context, ControlsDetails details) {
                  return Column(
                    children: [
                      Row(
                        children: <Widget>[
                          if (ref.read(stepIndexProvider.notifier).state != 0)
                            TextButton(
                              onPressed: details.onStepCancel,
                              child: const Text('Back'),
                            ),
                          const Spacer(),
                          if (ref.read(stepIndexProvider.notifier).state < 3)
                            Consumer(
                              builder: (context, ref, child) {
                                // ref.watch()
                                return TextButton(
                                  onPressed: !isValidForNext
                                      ? null
                                      : () {
                                          details.onStepContinue!();
                                        },
                                  child: const Text('Next'),
                                );
                              },
                            ),
                          if (ref.read(stepIndexProvider.notifier).state == 3)
                            FilledButton(
                              onPressed: !isValidForNext ? null : onSubmit,
                              child: const Text('Submit'),
                            ),
                        ],
                      ),
                    ],
                  );
                },
                steps: <Step>[
                  Step(
                    isActive: 0 <= currentStep,
                    title: const Text('Location'),
                    content: const _LocationStep(),
                  ),
                  Step(
                    isActive: 1 <= currentStep,
                    title: const Text('Address'),
                    content: const _AddressStep(),
                  ),
                  Step(
                    isActive: 2 <= currentStep,
                    title: const Text('Photos'),
                    content: const _PhotoStep(),
                  ),
                  Step(
                    isActive: 3 <= currentStep,
                    title: const Text('Submission'),
                    content: const _SubmissionStep(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SubmittedDialog extends ConsumerWidget {
  const _SubmittedDialog(this.referenceCode);
  final String referenceCode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog.adaptive(
      title: const Text(
        'Go!Mama values your suggestion',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: CustomColors.primary,
        ),
      ),
      content: IntrinsicHeight(
        child: Column(
          children: [
            const SizedBox(height: 12),
            const Text('We are working hard on it.'),
            const Text('Stay tuned, it will come true!'),
            const SizedBox(height: 12),
            Text(
              'Reference code: $referenceCode',
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ],
        ),
      ),
    );
  }
}

class _LocationStep extends HookConsumerWidget {
  const _LocationStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressController = useValueListenable(
      ref.watch(_textEditingControllerProvider('address')),
    );

    return FilledButton(
      onPressed: () {
        showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return const _PinMapDialog();
          },
        );
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (addressController.text.isEmpty)
            const Icon(CustomIcon.location)
          else
            const Icon(CustomIcon.check),
          const SizedBox(width: 8),
          const Text('Pin on map'),
        ],
      ),
    );
  }
}

class _PinMapDialog extends ConsumerWidget {
  const _PinMapDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressProvider = ref.read(_textEditingControllerProvider('address'));
    void onUpdateAddress(String address) {
      addressProvider.text = address;
    }

    return AlertDialog(
      insetPadding: const EdgeInsets.all(24),
      title: const Text(
        'Pin on map',
        style: TextStyle(
          color: CustomColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Builder(
        builder: (context) {
          return SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Search to place location marker on the map.'),
                const SizedBox(height: 16),
                const _AutocompleteTextView(),
                const SizedBox(height: 16),
                Expanded(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Builder(
                      builder: (context) {
                        return SuggestMap(onUpdateAddress);
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
      actions: <Widget>[
        SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (addressProvider.text.isNotEmpty) {
                ref.read(stepIndexProvider.notifier).state++;
              }
            },
            child: const Text('Ok'),
          ),
        ),
      ],
    );
  }
}

class _AutocompleteTextView extends HookConsumerWidget
    with GooglePlacesOverlay {
  const _AutocompleteTextView();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _layerLink = useState(LayerLink());
    final _overlayEntry = useState<OverlayEntry?>(null);

    final googlePlacesAutocompleteController = ref.watch(
      googlePlacesAutocompleteControllerProvider.notifier,
    );

    // final addressController = useValueListenable(
    //   ref.watch(_textEditingControllerProvider('address')),
    // );
    // final _controller = useTextEditingController(text: addressController.text);
    final _controller = ref.watch(_textEditingControllerProvider('address'));

    Future<void> _pinCurrentLocation() async {
      // get current location
      final position = ref.read(currentPositionProvider).requireValue;
      if (position != null) {
        // set coordinate
        await ref
            .read(mapboxMapStateControllerProvider.notifier)
            .moveLocationAtMap(position.latitude, position.longitude);
      }
    }

    ref.listen(
      googlePlacesAutocompleteControllerProvider
          .select((value) => value.allPredictions),
      (_, predictions) {
        if (predictions.isNotEmpty) {
          _overlayEntry.value?.remove();
          _overlayEntry.value = showPredictions(
            context,
            predictions,
            _layerLink.value,
            // TODO(kkcy): show loading perhaps
            onTap: (int index) async {
              if (index < predictions.length) {
                ref
                    .read(debouncerProvider(const Duration(milliseconds: 150)))
                    .debounce(() async {
                  final detail = await ref.read(
                    getPlaceDetailsFromPlaceIdProvider(
                      predictions[index].placeId!,
                    ).future,
                  );

                  if (detail == null) {
                    return;
                  }

                  await ref
                      .read(mapboxMapStateControllerProvider.notifier)
                      .moveLocationAtMap(detail.latitude, detail.longitude);

                  // update address
                  ref.read(_textEditingControllerProvider('address')).text =
                      detail.address;

                  // update region
                  if (detail.region != null) {
                    ref.read(_textEditingControllerProvider('region')).text =
                        detail.region!;
                  }

                  // onTap clear overlay
                  googlePlacesAutocompleteController.removeOverlay();
                  _overlayEntry.value?.remove();
                  _overlayEntry.value = null;
                });
              }
            },
          );
        } else {
          _overlayEntry.value?.remove();
          _overlayEntry.value = null;
        }
      },
    );

    return CompositedTransformTarget(
      link: _layerLink.value,
      child: TextFormField(
        controller: _controller,
        onChanged: googlePlacesAutocompleteController.textChanged,
        autovalidateMode: AutovalidateMode.disabled,
        autocorrect: false,
        cursorColor: CustomColors.primary,
        decoration: InputDecoration(
          prefixIcon: const Icon(CustomIcon.location),
          // center on self button
          suffixIcon: GestureDetector(
            onTap: _pinCurrentLocation,
            child: const Icon(
              Icons.my_location,
              size: 24,
            ),
          ),
          hintText: 'Enter the Address',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          border: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(16),
            gapPadding: 0,
          ),
          contentPadding: EdgeInsets.zero,
          fillColor: Colors.grey.shade200,
          filled: true,
        ),
      ),
    );
  }
}

class _AddressStep extends ConsumerWidget {
  const _AddressStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      controller: ref.read(_textEditingControllerProvider('address')),
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        prefixIcon: const Icon(CustomIcon.building),
        hintText: 'Floor level (if applicable)',
        hintStyle: TextStyle(color: Colors.grey.shade500),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        contentPadding: EdgeInsets.zero,
        fillColor: Colors.grey.shade200,
        filled: true,
      ),
    );
  }
}

class _PhotoStep extends ConsumerWidget {
  const _PhotoStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photos =
        ref.watch(mediaStateControllerProvider.select((value) => value.photos));
    final mediaStateController =
        ref.watch(mediaStateControllerProvider.notifier);

    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          if (photos.isNotEmpty) ...[
            SizedBox(
              height: 150,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, index) => const SizedBox(width: 4),
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.file(photos[index]),
                      ),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                          child: InkWell(
                            onTap: () {
                              mediaStateController.removePhotoAt(index);
                            },
                            child: const Icon(
                              CustomIcon.close,
                              size: 12,
                              color: CustomColors.primary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
                itemCount: photos.length,
              ),
            ),
            const SizedBox(height: 8),
          ],
          FilledButton(
            onPressed: () {
              showCupertinoModalPopup<void>(
                context: context,
                builder: (BuildContext context) {
                  return const AddPhotoDialog();
                },
              );
            },
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(CustomIcon.cameraAdd),
                SizedBox(width: 8),
                Text('Add a photo'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SubmissionStep extends ConsumerWidget {
  const _SubmissionStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      controller: ref.watch(_textEditingControllerProvider('note')),
      maxLines: 7,
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        hintText: "Tell us why you'd like a Nursing Pod here.",
        hintStyle: TextStyle(color: Colors.grey.shade500),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        fillColor: Colors.grey.shade200,
        filled: true,
      ),
    );
  }
}
