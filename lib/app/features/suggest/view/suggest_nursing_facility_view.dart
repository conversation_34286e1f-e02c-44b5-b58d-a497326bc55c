// ignore_for_file: use_setters_to_change_properties
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/debounce_provider.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/maps/provider/google_places_providers.dart';
import 'package:gomama/app/features/maps/provider/mapbox_map_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/maps/widget/suggest_map.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/features/suggest/widget/add_photo_dialog.dart';
import 'package:gomama/app/mixin/google_places_overlay.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// UI utility providers
final _textEditingControllerProvider = Provider.autoDispose
    .family<TextEditingController, String>((ref, controllerKey) {
  return TextEditingController();
});

final stepIndexProvider = StateProvider.autoDispose<int>((ref) => 0);
final stepIndexNotifierProvider =
    Provider.autoDispose<ValueNotifier<int>>((ref) {
  final notifier = ValueNotifier<int>(0);

  ref.listen(stepIndexProvider, (_, next) {
    notifier.value = next;
  });

  return notifier;
});

final selectedAmenitiesProvider =
    StateProvider.autoDispose<List<Amenity>>((ref) => []);
final selectedAmenitiesNotifierProvider =
    Provider.autoDispose<ValueNotifier<List<Amenity>>>((ref) {
  final notifier = ValueNotifier<List<Amenity>>([]);

  ref.listen(selectedAmenitiesProvider, (_, next) {
    notifier.value = next;
  });

  return notifier;
});
// UI utility providers

class SuggestNursingFacilityView extends HookConsumerWidget {
  const SuggestNursingFacilityView({super.key});

  static const routeName = 'suggest nursing facility';
  static const routePath = '/suggest/care';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepNotifier = ref.watch(stepIndexNotifierProvider);
    final amenitiesSelectedNotifier =
        ref.watch(selectedAmenitiesNotifierProvider);

    final addressController =
        ref.watch(_textEditingControllerProvider('address'));
    final postalCodeController =
        ref.watch(_textEditingControllerProvider('postal_code'));
    final noteController = ref.watch(_textEditingControllerProvider('note'));
    final companyNameController =
        ref.watch(_textEditingControllerProvider('company_name'));
    final contactNumberController =
        ref.watch(_textEditingControllerProvider('contact_number'));
    final countryDialCodeController =
        ref.watch(_textEditingControllerProvider('country_dial_code'));
    final openingHoursController =
        ref.watch(_textEditingControllerProvider('opening_hours'));
    final photos = ref.watch(
      mediaStateControllerProvider.select((value) => value.photos),
    );

    ref
      ..watch(_textEditingControllerProvider('address_name'))
      ..watch(_textEditingControllerProvider('description'))
      ..watch(_textEditingControllerProvider('region'))
      // ..watch(_textEditingControllerProvider('company_address')) // this might be redundant
      ..watch(_textEditingControllerProvider('postal_code'))
      ..watch(_textEditingControllerProvider('opening_hours'))
      // TODO(kkcy): get the position ready
      ..watch(currentPositionProvider)
      ..watch(mapboxMapStateControllerProvider);

    // final havePrivateFeedingRoom = useState(false);
    // final selectedRoomsLockable = useState('Lockable');
    // final selectedPrivateRoomAmenities = useState<List<String>>([]);
    // final privateRoomAmenitiesList = [
    //   'Power Socket',
    //   'Table Top',
    //   'Seat',
    //   'Fan',
    //   'Proper Lighting',
    // ];

    final address = useValueListenable(addressController);
    final postalCode = useValueListenable(postalCodeController);
    final note = useValueListenable(noteController);
    final companyName = useValueListenable(companyNameController);
    final contactNumber = useValueListenable(contactNumberController);
    final countryDialCode = useValueListenable(countryDialCodeController);
    final openingHours = useValueListenable(openingHoursController);
    final currentStep = useValueListenable(stepNotifier);
    final currentAmenities = useValueListenable(amenitiesSelectedNotifier);

    final isValidForNext = useMemoized(
      () {
        switch (currentStep) {
          case 0:
            return address.text.isNotEmpty;
          case 1:
            return address.text.isNotEmpty;
          case 2:
            return currentAmenities.isNotEmpty;
          case 3:
            return photos.isNotEmpty;
          case 4:
          default:
            return true;
        }
      },
      [
        currentStep,
        address,
        postalCode,
        currentAmenities,
        companyName,
        contactNumber,
        countryDialCode,
        openingHours,
        photos,
        note,
      ],
    );

    Future<void> onSubmit() async {
      late double latitude, longitude;

      latitude = ref.read(mapboxMapStateControllerProvider).latitude;
      longitude = ref.read(mapboxMapStateControllerProvider).longitude;

      final input = ListingSuggestionInput.nursingFacilitySuggestionInput(
        region: ref.read(_textEditingControllerProvider('region')).text,
        fullAddress: ref.read(_textEditingControllerProvider('address')).text,
        addressName:
            ref.read(_textEditingControllerProvider('address_name')).text,
        description:
            ref.read(_textEditingControllerProvider('description')).text,
        note: ref.read(_textEditingControllerProvider('note')).text,
        amenities: currentAmenities.map((amenity) => amenity.slug).toList(),
        companyName:
            ref.read(_textEditingControllerProvider('company_name')).text,
        postalCode:
            ref.read(_textEditingControllerProvider('postal_code')).text,
        contactNumber:
            ref.read(_textEditingControllerProvider('contact_number')).text,
        countryDialCode:
            ref.read(_textEditingControllerProvider('country_dial_code')).text,
        openingHours:
            ref.read(_textEditingControllerProvider('opening_hours')).text,
        latitude: latitude,
        longitude: longitude,
        mainImageFile: photos.first,
        subImageFiles: photos.length > 1 ? photos.skip(1).toList() : [],
      );

      Groveman.debug('suggest nursing facility', error: input);

      final response = await ref.read(
        suggestListingProvider(input).future,
      );

      if (response.success) {
        final dialogContext = context; // Capture the context

        if (context.mounted) {
          await showDialog<void>(
            context: dialogContext, // Use the captured context
            builder: (BuildContext context) {
              return _SubmittedDialog(response.data.id);
            },
          ).then((value) {
            if (context.mounted) {
              // Redirect to SuggestRoute view when the dialog is closed
              const SuggestRoute().go(context);
            }
          });
        }

        // Reset to step 0 after submit
        ref.read(stepIndexProvider.notifier).state = 0;
      }
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: const Text('Suggest A Nursing Facility!'),
            ),
            pinned: true,
          ),
          SliverToBoxAdapter(
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: Stepper(
                physics: const NeverScrollableScrollPhysics(),
                currentStep: ref.read(stepIndexProvider.notifier).state,
                // onStepTapped: (step) {
                //   ref.read(stepIndexProvider.notifier).state = step;
                // },
                onStepCancel: () {
                  if (ref.read(stepIndexProvider.notifier).state > 0) {
                    ref.read(stepIndexProvider.notifier).state--;
                  }
                },
                onStepContinue: () {
                  if (ref.read(stepIndexProvider.notifier).state < 5) {
                    ref.read(stepIndexProvider.notifier).state++;
                  }
                },
                controlsBuilder:
                    (BuildContext context, ControlsDetails details) {
                  return Column(
                    children: [
                      Row(
                        children: <Widget>[
                          if (ref.read(stepIndexProvider.notifier).state != 0)
                            TextButton(
                              onPressed: details.onStepCancel,
                              child: const Text('Back'),
                            ),
                          const Spacer(),
                          if (ref.read(stepIndexProvider.notifier).state < 5)
                            TextButton(
                              onPressed: !isValidForNext
                                  ? null
                                  : () {
                                      details.onStepContinue!();
                                    },
                              child: const Text('Next'),
                            ),
                          if (ref.read(stepIndexProvider.notifier).state == 5)
                            FilledButton(
                              onPressed: !isValidForNext ? null : onSubmit,
                              child: const Text('Submit'),
                            ),
                        ],
                      ),
                    ],
                  );
                },
                steps: <Step>[
                  Step(
                    isActive: 0 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Location'),
                    content: const _LocationStep(),
                  ),
                  Step(
                    isActive: 1 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Address'),
                    content: const _AddressStep(),
                  ),
                  Step(
                    isActive: 2 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Amenities'),
                    content: _AmenitiesStep(
                      (
                        Amenity amenity, {
                        bool isSelected = false,
                      }) {
                        ref.read(selectedAmenitiesProvider.notifier).state =
                            isSelected
                                ? [
                                    ...ref
                                        .read(
                                            selectedAmenitiesProvider.notifier)
                                        .state,
                                    amenity,
                                  ]
                                : ref
                                    .read(selectedAmenitiesProvider.notifier)
                                    .state
                                    .where((amenityEle) =>
                                        amenityEle.slug != amenity.slug)
                                    .toList();
                      },
                    ),
                  ),
                  Step(
                    isActive: 3 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Photos'),
                    content: const _PhotoStep(),
                  ),
                  Step(
                    isActive: 4 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Facility Information'),
                    content: const _FacilityInformationStep(),
                  ),
                  Step(
                    isActive: 5 <= ref.read(stepIndexProvider.notifier).state,
                    title: const Text('Submission'),
                    content: const _SubmissionStep(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SubmittedDialog extends ConsumerWidget {
  const _SubmittedDialog(this.referenceCode);
  final String referenceCode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AlertDialog.adaptive(
      title: const Text(
        'Go!Mama values your suggestion',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: CustomColors.primary,
        ),
      ),
      content: IntrinsicHeight(
        child: Column(
          children: [
            const SizedBox(height: 12),
            const Text('We are working hard on it.'),
            const Text('Stay tuned, it will come true!'),
            const SizedBox(height: 12),
            Text(
              'Reference code: $referenceCode',
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ],
        ),
      ),
    );
  }
}

class _LocationStep extends HookConsumerWidget {
  const _LocationStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressController = useValueListenable(
      ref.watch(_textEditingControllerProvider('address')),
    );

    return FilledButton(
      onPressed: () {
        showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return const _PinMapDialog();
          },
        );
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (addressController.text.isEmpty)
            const Icon(CustomIcon.location)
          else
            const Icon(CustomIcon.check),
          const SizedBox(width: 8),
          const Text('Pin on map'),
        ],
      ),
    );
  }
}

class _PinMapDialog extends ConsumerWidget {
  const _PinMapDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressProvider = ref.read(_textEditingControllerProvider('address'));
    void onUpdateAddress(String address) {
      addressProvider.text = address;
    }

    return AlertDialog(
      insetPadding: const EdgeInsets.all(24),
      title: const Text(
        'Pin on map',
        style: TextStyle(
          color: CustomColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Builder(
        builder: (context) {
          return SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Search to place location marker on the map.'),
                const SizedBox(height: 16),
                const _AutocompleteTextView(),
                const SizedBox(height: 16),
                Expanded(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Builder(
                      builder: (context) {
                        return SuggestMap(onUpdateAddress);
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
      actions: <Widget>[
        SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (addressProvider.text.isNotEmpty) {
                ref.read(stepIndexProvider.notifier).state++;
              }
            },
            child: const Text('Ok'),
          ),
        ),
      ],
    );
  }
}

class _AutocompleteTextView extends HookConsumerWidget
    with GooglePlacesOverlay {
  const _AutocompleteTextView();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _layerLink = useState(LayerLink());
    final _overlayEntry = useState<OverlayEntry?>(null);

    final googlePlacesAutocompleteController = ref.watch(
      googlePlacesAutocompleteControllerProvider.notifier,
    );

    // final addressController = useValueListenable(
    //   ref.watch(_textEditingControllerProvider('address')),
    // );
    // final _controller = useTextEditingController(text: addressController.text);
    final _controller = ref.watch(_textEditingControllerProvider('address'));

    Future<void> _pinCurrentLocation() async {
      // get current location
      final position = ref.read(currentPositionProvider).requireValue;
      if (position != null) {
        // set coordinate
        await ref
            .read(mapboxMapStateControllerProvider.notifier)
            .moveLocationAtMap(position.latitude, position.longitude);
      }
    }

    ref.listen(
      googlePlacesAutocompleteControllerProvider
          .select((value) => value.allPredictions),
      (_, predictions) {
        _overlayEntry.value?.remove();

        if (predictions.isEmpty) {
          _overlayEntry.value = null;
          return;
        }

        _overlayEntry.value = showPredictions(
          context,
          predictions,
          _layerLink.value,
          onTap: (int index) async {
            if (index >= predictions.length) {
              return;
            }

            ref
                .read(debouncerProvider(const Duration(milliseconds: 150)))
                .debounce(() async {
              final detail = await ref.read(
                getPlaceDetailsFromPlaceIdProvider(
                  predictions[index].placeId!,
                ).future,
              );

              if (detail == null) {
                return;
              }

              await ref
                  .read(mapboxMapStateControllerProvider.notifier)
                  .moveLocationAtMap(detail.latitude, detail.longitude);

              // update address
              ref.read(_textEditingControllerProvider('address')).text =
                  detail.address;

              if (detail.region != null) {
                // update region
                ref.read(_textEditingControllerProvider('region')).text =
                    detail.region!;
              }

              // onTap clear overlay
              googlePlacesAutocompleteController.removeOverlay();
              _overlayEntry.value?.remove();
              _overlayEntry.value = null;
            });
          },
        );
      },
    );

    return CompositedTransformTarget(
      link: _layerLink.value,
      child: TextFormField(
        controller: _controller,
        onChanged: googlePlacesAutocompleteController.textChanged,
        autovalidateMode: AutovalidateMode.disabled,
        autocorrect: false,
        cursorColor: CustomColors.primary,
        decoration: InputDecoration(
          prefixIcon: const Icon(CustomIcon.location),
          // center on self button
          suffixIcon: GestureDetector(
            onTap: _pinCurrentLocation,
            child: const Icon(
              Icons.my_location,
              size: 24,
            ),
          ),
          hintText: 'Enter the Address',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          border: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(16),
            gapPadding: 0,
          ),
          contentPadding: EdgeInsets.zero,
          fillColor: Colors.grey.shade200,
          filled: true,
        ),
      ),
    );
  }
}

class _AddressStep extends ConsumerWidget {
  const _AddressStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('name')),
          cursorColor: CustomColors.primary,
          // minLines: 2,
          maxLines: 2,
          decoration: InputDecoration(
            // prefixIcon: const Icon(Icons.stairs_outlined),
            hintText:
                'Name of Nursing Room\nExample: Sentosa - Nappy changing room',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('address_name')),
          cursorColor: CustomColors.primary,
          // minLines: 2,
          maxLines: 2,
          decoration: InputDecoration(
            // prefixIcon: const Icon(Icons.stairs_outlined),
            hintText:
                'Address Name of Nursing Room\nExample: Palawan Kidz City, Level 2, Near Lift',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('address')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            // prefixIcon: const Icon(Icons.stairs_outlined),
            hintText: 'Address',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('postal_code')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: 'Postal Code',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('description')),
          cursorColor: CustomColors.primary,
          // minLines: 2,
          maxLines: 2,
          decoration: InputDecoration(
            // prefixIcon: const Icon(Icons.stairs_outlined),
            hintText: 'Description of Location\nExample: Beside Bathroom',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
      ],
    );
  }
}

class _AmenitiesStep extends HookConsumerWidget {
  const _AmenitiesStep(this.onChanged);
  final void Function(
    Amenity, {
    required bool isSelected,
  }) onChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final amenitiesList = ref.watch(amenityListProvider);
    final _havePrivateFeedingRoom = useState<bool>(false);
    final amenitiesSelected = ref.watch(selectedAmenitiesNotifierProvider);

    return amenitiesList.when(
      data: (response) {
        final amenities = response.data;

        return Column(
          children: [
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: amenities.length,
              itemBuilder: (context, index) {
                return CheckboxListTile(
                  visualDensity: const VisualDensity(vertical: -4),
                  contentPadding: EdgeInsets.zero,
                  title: Text(amenities[index].name),
                  value: amenitiesSelected.value.contains(amenities[index]),
                  onChanged: (value) =>
                      onChanged(amenities[index], isSelected: value ?? false),
                );
              },
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Flexible(
                    child: Text(
                      'Is there a designated space for breastfeeding?',
                      overflow: TextOverflow.visible,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return const AlertDialog.adaptive(
                            content: Text(
                              'A designated breastfeeding space is a private space exclusively for mothers to breastfeed or pump breast milk, without diaper changing facilities.',
                            ),
                          );
                        },
                      );
                    },
                    style: TextButton.styleFrom(
                      minimumSize: Size.zero,
                      padding: EdgeInsets.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Icon(
                      CustomIcon.info,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              value: _havePrivateFeedingRoom.value,
              onChanged: (value) {
                _havePrivateFeedingRoom.value = !_havePrivateFeedingRoom.value;
              },
            ),
            if (_havePrivateFeedingRoom.value)
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 20),
                  Row(
                    children: [
                      Text(
                        'Private Feeding Room',
                        style: TextStyle(fontSize: 16),
                      ),
                      SizedBox(width: 16),
                      Expanded(child: _NumberPicker()),
                    ],
                  ),
                  // SizedBox(height: 20),
                  // Text('Rooms Lockable?'),
                  // RoomsLocakableRadioButtons(),
                  // ListView.builder(
                  //   physics: const NeverScrollableScrollPhysics(),
                  //   shrinkWrap: true,
                  //   itemCount: amenitiesList.length,
                  //   itemBuilder: (context, index) {
                  //     return CheckboxListTile(
                  //       visualDensity: const VisualDensity(vertical: -4),
                  //       contentPadding: EdgeInsets.zero,
                  //       title: Text(amenitiesList[index]),
                  //       value: amenitiesSelected.value.contains(amenitiesList[index]),
                  //       onChanged: (value) {
                  //         amenitiesSelected.value = (value ?? false)
                  //           ? [...amenitiesSelected.value, amenitiesList[index]]
                  //           : amenitiesSelected.value.where((amenity) => amenity != amenitiesList[index]).toList();
                  //       },
                  //     );
                  //   },
                  // ),
                  // SizedBox(height: 16),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     InkWell(
                  //       onTap: () {
                  //         selectedRoomsLockable.value = 'Lockable';
                  //       },
                  //       child: Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(
                  //             Icons.lock_outline,
                  //             color: selectedRoomsLockable.value == 'Lockable'
                  //                 ? CustomColors.primary
                  //                 : Colors.grey,
                  //           ),
                  //           const Text('Lockable'),
                  //           Radio(
                  //             value: 'Lockable',
                  //             groupValue: selectedRoomsLockable.value,
                  //             onChanged: (value) {
                  //               selectedRoomsLockable.value = value!;
                  //             },
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //     InkWell(
                  //       onTap: () {
                  //         selectedRoomsLockable.value = 'Unlockable';
                  //       },
                  //       child: Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(
                  //             Icons.lock_open_rounded,
                  //             color: selectedRoomsLockable.value == 'Unlockable'
                  //                 ? CustomColors.primary
                  //                 : Colors.grey,
                  //           ),
                  //           const Text('Unlockable'),
                  //           Radio(
                  //             value: 'Unlockable',
                  //             groupValue: selectedRoomsLockable.value,
                  //             onChanged: (value) {
                  //               selectedRoomsLockable.value = value!;
                  //             },
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //     InkWell(
                  //       onTap: () {
                  //         selectedRoomsLockable.value = 'Curtain';
                  //       },
                  //       child: Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(
                  //             Icons.curtains_outlined,
                  //             color: selectedRoomsLockable.value == 'Curtain'
                  //                 ? CustomColors.primary
                  //                 : Colors.grey,
                  //           ),
                  //           const Text('Curtain'),
                  //           Radio(
                  //             value: 'Curtain',
                  //             groupValue: selectedRoomsLockable.value,
                  //             onChanged: (value) {
                  //               selectedRoomsLockable.value = value!;
                  //             },
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //     InkWell(
                  //       onTap: () {
                  //         selectedRoomsLockable.value = 'Unsure';
                  //       },
                  //       child: Column(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(
                  //             Icons.help_outline_rounded,
                  //             color: selectedRoomsLockable.value == 'Unsure'
                  //                 ? CustomColors.primary
                  //                 : Colors.grey,
                  //           ),
                  //           const Text('Unsure?'),
                  //           Radio(
                  //             value: 'Unsure',
                  //             groupValue: selectedRoomsLockable.value,
                  //             onChanged: (value) {
                  //               selectedRoomsLockable.value = value!;
                  //             },
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ],
                  // ),
                  // const SizedBox(height: 20),
                  // ListView.builder(
                  //   physics: const NeverScrollableScrollPhysics(),
                  //   shrinkWrap: true,
                  //   itemCount: privateRoomAmenitiesList.length,
                  //   itemBuilder: (context, index) {
                  //     return CheckboxListTile(
                  //       visualDensity: const VisualDensity(vertical: -4),
                  //       contentPadding: EdgeInsets.zero,
                  //       title: Text(privateRoomAmenitiesList[index]),
                  //       value: selectedPrivateRoomAmenities.value
                  //           .contains(privateRoomAmenitiesList[index]),
                  //       onChanged: (value) {
                  //         selectedPrivateRoomAmenities.value = (value ?? false)
                  //             ? [
                  //                 ...selectedPrivateRoomAmenities.value,
                  //                 privateRoomAmenitiesList[index],
                  //               ]
                  //             : selectedPrivateRoomAmenities.value
                  //                 .where(
                  //                   (amenity) =>
                  //                       amenity !=
                  //                       privateRoomAmenitiesList[index],
                  //                 )
                  //                 .toList();
                  //       },
                  //     );
                  //   },
                  // ),
                ],
              ),
          ],
        );
      },
      loading: () {
        return const CircularProgressIndicator();
      },
      error: (error, stackTrace) {
        Groveman.error('_AmenitiesStep', error: error, stackTrace: stackTrace);
        return const SizedBox.shrink();
      },
    );
  }
}

class _NumberPicker extends HookConsumerWidget {
  const _NumberPicker();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _number = useState(0);

    void _incrementNumber() {
      _number.value++;
    }

    void _decrementNumber() {
      _number.value--;
    }

    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        color: Colors.grey.shade200,
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(CustomIcon.keyboardArrowLeft),
              onPressed: _number.value > 0 ? _decrementNumber : null,
              style: IconButton.styleFrom(
                minimumSize: Size.zero,
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              color: CustomColors.primary,
            ),
            Expanded(
              child: Center(
                child: Text(
                  '${_number.value}',
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(CustomIcon.keyboardArrowRight),
              onPressed: _incrementNumber,
              style: IconButton.styleFrom(
                minimumSize: Size.zero,
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              color: CustomColors.primary,
            ),
          ],
        ),
      ),
    );
  }
}

class _PhotoStep extends ConsumerWidget {
  const _PhotoStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photos =
        ref.watch(mediaStateControllerProvider.select((value) => value.photos));
    final mediaStateController =
        ref.watch(mediaStateControllerProvider.notifier);

    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          if (photos.isNotEmpty) ...[
            SizedBox(
              height: 160,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, index) => const SizedBox(width: 4),
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.file(photos[index]),
                      ),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                          child: InkWell(
                            onTap: () {
                              mediaStateController.removePhotoAt(index);
                            },
                            child: const Icon(
                              CustomIcon.close,
                              size: 12,
                              color: CustomColors.primary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
                itemCount: photos.length,
              ),
            ),
            const SizedBox(height: 8),
          ],
          FilledButton(
            onPressed: () {
              showCupertinoModalPopup<void>(
                context: context,
                builder: (BuildContext context) {
                  return const AddPhotoDialog();
                },
              );
            },
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(CustomIcon.cameraAdd),
                SizedBox(width: 8),
                Text('Add a photo'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// The owner/company
class _FacilityInformationStep extends ConsumerWidget {
  const _FacilityInformationStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        TextFormField(
          controller: ref.watch(_textEditingControllerProvider('company_name')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: 'Facility Provider Name',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        // TextFormField(
        //   controller:
        //       ref.watch(_textEditingControllerProvider('company_address')),
        //   cursorColor: CustomColors.primary,
        //   decoration: InputDecoration(
        //     hintText: 'Address',
        //     hintStyle: TextStyle(color: Colors.grey.shade500),
        //     border: OutlineInputBorder(
        //       borderSide: BorderSide.none,
        //       borderRadius: BorderRadius.circular(16),
        //       gapPadding: 0,
        //     ),
        //     contentPadding: const EdgeInsets.symmetric(horizontal: 15),
        //     fillColor: Colors.grey.shade200,
        //     filled: true,
        //   ),
        // ),
        // const SizedBox(height: 16),
        TextFormField(
          keyboardType: TextInputType.phone,
          maxLength: 18,
          controller:
              ref.watch(_textEditingControllerProvider('contact_number')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: 'Contact Number',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          keyboardType: TextInputType.phone,
          maxLength: 3,
          controller:
              ref.watch(_textEditingControllerProvider('country_dial_code')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: 'Country Dial Code - Eg. 65 for Singapore',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller:
              ref.watch(_textEditingControllerProvider('opening_hours')),
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: 'Opening Hours',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            fillColor: Colors.grey.shade200,
            filled: true,
          ),
        ),
      ],
    );
  }
}

class _SubmissionStep extends ConsumerWidget {
  const _SubmissionStep();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      controller: ref.watch(_textEditingControllerProvider('note')),
      maxLines: 7,
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        hintText: 'Additional comments about the nursing/lactation room?',
        hintStyle: TextStyle(color: Colors.grey.shade500),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        fillColor: Colors.grey.shade200,
        filled: true,
      ),
    );
  }
}
