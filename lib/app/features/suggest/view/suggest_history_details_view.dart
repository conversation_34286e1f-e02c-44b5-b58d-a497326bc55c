import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listing_files.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SuggestHistoryDetailsView extends HookConsumerWidget {
  const SuggestHistoryDetailsView({
    required this.initialListingSuggestion,
    super.key,
  });

  final ListingSuggestion initialListingSuggestion;

  static const routeName = 'suggest history details';
  static const routePath = '/suggest/history/details';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: Text(
                initialListingSuggestion.listingType == ListingType.gomama
                    ? 'Pod Suggestion'
                    : 'Nursing Facility Suggestion',
              ),
            ),
            pinned: true,
          ),
          _Body(initialListingSuggestion),
        ],
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(this.listingSuggestion);
  final ListingSuggestion listingSuggestion;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 30),
      sliver: SliverList.list(
        children: [
          Wrap(
            alignment: WrapAlignment.spaceBetween,
            runSpacing: 4,
            children: [
              Text(
                'Reference ID: ${listingSuggestion.id}',
                style: Theme.of(context).textTheme.labelSmall,
              ),
              Text(
                listingSuggestion.createdDate,
                style: Theme.of(context).textTheme.labelSmall,
              ),
            ],
          ),
          const SizedBox(height: 12),
          CustomStepper(
            steps: [
              CustomStep(
                title: 'Location',
                content: _LocationStep(listingSuggestion.addressName),
              ),
              CustomStep(
                title: 'Address',
                content: _AddressStep(listingSuggestion.fullAddress),
              ),
              if (listingSuggestion.listingType == ListingType.care)
                CustomStep(
                  title: 'Amenities',
                  content: _AmenitiesStep(
                    listingSuggestion.amenities,
                    listingSuggestion.numberOfPrivateFeedingRooms,
                  ),
                ),
              CustomStep(
                title: 'Photos',
                content: _PhotoStep(listingSuggestion.listingFiles),
              ),
              CustomStep(
                title: 'Facility Information',
                content: _FacilityInformationStep(
                  listingSuggestion.companyName ?? 'Company Name Not Provided, Pending Verification',
                  listingSuggestion.contactNumber ?? 'Contact Not Provided, Pending Verification',
                  listingSuggestion.openingHours,
                ),
              ),
              CustomStep(
                title: 'Submission Reason',
                content: _DescriptionStep(listingSuggestion.note ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}

class _LocationStep extends ConsumerWidget {
  const _LocationStep(this.addressName);

  final String? addressName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      enabled: false,
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        prefixIcon: const Icon(
          CustomIcon.location,
          color: CustomColors.primary,
        ),
        hintText: addressName,
        hintStyle: const TextStyle(color: CustomColors.placeholder),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        contentPadding: EdgeInsets.zero,
        fillColor: CustomColors.secondaryLight,
        filled: true,
      ),
    );
  }
}

class _AddressStep extends ConsumerWidget {
  const _AddressStep(this.fullAddress);

  final String? fullAddress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      enabled: false,
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        hintText: fullAddress,
        hintStyle: const TextStyle(color: CustomColors.placeholder),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        fillColor: CustomColors.secondaryLight,
        filled: true,
      ),
    );
  }
}

class _PhotoStep extends ConsumerWidget {
  const _PhotoStep(this.listingFiles);

  final List<ListingFile>? listingFiles;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          if (listingFiles!.isNotEmpty) ...[
            SizedBox(
              height: 150,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, index) => const SizedBox(width: 4),
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: CachedNetworkImage(
                          imageUrl: listingFiles![0].imageUrl ?? '',
                          // height: 100,
                          // width: 110,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const Center(
                            child: SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 1,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) =>
                              const Icon(CustomIcon.error),
                        ),
                      ),
                    ],
                  );
                },
                itemCount: listingFiles!.length,
              ),
            ),
            const SizedBox(height: 8),
          ],
        ],
      ),
    );
  }
}

class _DescriptionStep extends ConsumerWidget {
  const _DescriptionStep(this.description);

  final String? description;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextFormField(
      enabled: false,
      maxLines: 7,
      cursorColor: CustomColors.primary,
      decoration: InputDecoration(
        hintText: description,
        hintStyle: const TextStyle(color: CustomColors.placeholder),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(16),
          gapPadding: 0,
        ),
        contentPadding: const EdgeInsets.all(12),
        fillColor: CustomColors.secondaryLight,
        filled: true,
      ),
    );
  }
}

class _AmenitiesStep extends ConsumerWidget {
  const _AmenitiesStep(this.amenities, this.numberOfPrivateFeedingRooms);

  final List<Amenity>? amenities;
  final int? numberOfPrivateFeedingRooms;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final amenitiesList = ref.watch(amenityListProvider);

    return amenitiesList.when(
      data: (response) {
        final amenitiesListItems = response.data;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListView.builder(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: amenitiesListItems.length,
              itemBuilder: (context, index) {
                return CheckboxListTile(
                  visualDensity: const VisualDensity(vertical: -4),
                  contentPadding: EdgeInsets.zero,
                  title: Text(amenitiesListItems[index].name),
                  value: amenities?.contains(amenitiesListItems[index]),
                  enabled: false,
                  onChanged: (value) {},
                );
              },
            ),
            const SizedBox(height: 16),
            Text('No. of private feeding room: $numberOfPrivateFeedingRooms'),
          ],
        );
      },
      loading: () {
        return const CircularProgressIndicator();
      },
      error: (error, stackTrace) {
        Groveman.error('_AmenitiesStep', error: error, stackTrace: stackTrace);
        return const SizedBox.shrink();
      },
    );
  }
}

class _FacilityInformationStep extends ConsumerWidget {
  const _FacilityInformationStep(
    this.companyName,
    this.contactNumber,
    this.openingHours,
  );

  final String? companyName;
  final String? contactNumber;
  final String? openingHours;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        TextFormField(
          enabled: false,
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: companyName,
            hintStyle: const TextStyle(color: CustomColors.placeholder),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            fillColor: CustomColors.secondaryLight,
            filled: true,
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        TextFormField(
          enabled: false,
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: contactNumber,
            hintStyle: const TextStyle(color: CustomColors.placeholder),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            fillColor: CustomColors.secondaryLight,
            filled: true,
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        TextFormField(
          enabled: false,
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintText: openingHours,
            hintStyle: const TextStyle(color: CustomColors.placeholder),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            fillColor: CustomColors.secondaryLight,
            filled: true,
          ),
        ),
      ],
    );
  }
}

class CustomStep {
  CustomStep({
    required this.title,
    required this.content,
    this.isActive = false,
  });

  final String title;
  final Widget content;
  final bool isActive;
}

class CustomStepWidget extends StatelessWidget {
  const CustomStepWidget({
    super.key,
    required this.index,
    required this.step,
    required this.isLast,
  });
  final int index;
  final CustomStep step;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: CircleAvatar(
            radius: 12,
            child: Text(
              '$index',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: Colors.white,
                  ),
            ),
          ),
          title: Text(
            step.title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const IntrinsicWidth(
            child: Row(
              children: [
                SizedBox(
                  width: 11.5,
                ),
                // TODO(kkcy): fix VerticalDivider need to take up full height of Row.
                // if (isLast == false)
                //   const VerticalDivider(color: Colors.black, width: 1,),
              ],
            ),
          ),
          title: step.content,
        ),
      ],
    );
  }
}

class CustomStepper extends StatelessWidget {
  const CustomStepper({super.key, required this.steps});
  final List<CustomStep> steps;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: steps.length,
      itemBuilder: (context, index) {
        return CustomStepWidget(
          index: index + 1,
          step: steps[index],
          isLast: index + 1 == steps.length,
        );
      },
    );
  }
}
