// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'suggest_history_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingSuggestionIndexHash() =>
    r'1fb4382744d2daa4c84d005e2f925c591b8c99dd';

/// See also [_listingSuggestionIndex].
@ProviderFor(_listingSuggestionIndex)
final _listingSuggestionIndexProvider = AutoDisposeProvider<int>.internal(
  _listingSuggestionIndex,
  name: r'_listingSuggestionIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingSuggestionIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _ListingSuggestionIndexRef = AutoDisposeProviderRef<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
