import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'suggest_history_view.g.dart';

@riverpod
int _listingSuggestionIndex(_ListingSuggestionIndexRef ref) {
  throw UnimplementedError();
}

class SuggestHistoryView extends HookConsumerWidget {
  const SuggestHistoryView({super.key});

  static const routeName = 'suggest history';
  static const routePath = '/suggest/history';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: const Text('Suggestion History'),
              // actions: [
              //   IconButton(
              //     visualDensity: VisualDensity.compact,
              //     icon: const Icon(
              //       Icons.filter_alt,
              //       size: 20,
              //       color: CustomColors.primaries,
              //     ),
              //     onPressed: () {
              //       showCupertinoModalPopup(
              //         context: context,
              //         builder: (context) {
              //           return const FilterSheet();
              //         },
              //       );
              //     },
              //   ),
              // ],
            ),
            pinned: true,
          ),
          const _Body(),
        ],
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final position = ref.watch(currentPositionProvider).requireValue!;
    final listingCount = ref.watch(
      listingSuggestionsCountProvider(
        AllListingsInput(
          lon: position.longitude,
          lat: position.latitude,
        ),
      ),
    );

    return listingCount.when(
      loading: () => const SliverToBoxAdapter(
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (err, stack) => SliverToBoxAdapter(child: Text('Error $err')),
      data: (listingCount) {
        if (listingCount == 0) {
          return SliverToBoxAdapter(
            child: Center(
              child: Column(
                children: [
                  const SizedBox(height: 64),
                  Image.asset(
                    'assets/images/goma_sad.png',
                    height: 160,
                  ),
                  const Text(
                    'There are no suggested listings.',
                  ),
                ],
              ),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          sliver: SliverList.separated(
            itemBuilder: (context, index) {
              return ProviderScope(
                overrides: [
                  // ignore: scoped_providers_should_specify_dependencies
                  _listingSuggestionIndexProvider.overrideWithValue(index),
                ],
                child: const _ListingSuggestionCard(),
              );
            },
            separatorBuilder: (context, index) {
              return const SizedBox(height: 16);
            },
            itemCount: listingCount,
          ),
        );
      },
    );
  }
}

class _ListingSuggestionCard extends ConsumerWidget {
  const _ListingSuggestionCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(_listingSuggestionIndexProvider);
    final offset = AllListingsOffset(
      offset: index,
    );
    final listing = ref.watch(
      listingSuggestionAtIndexProvider(offset),
    );

    return listing.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listingSuggestion) {
        return Material(
          color: CustomColors.secondaryLight,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: const BorderSide(color: CustomColors.secondary),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: () {
              SuggestHistoryDetailsRoute(listingSuggestion).push(context);
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Wrap(
                    alignment: WrapAlignment.spaceBetween,
                    runSpacing: 6,
                    children: [
                      Text(
                        'Reference ID: ${listingSuggestion.id}',
                        style: textTheme(context).labelSmall,
                      ),
                      Text(
                        listingSuggestion.createdDate,
                        style: textTheme(context).labelSmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Divider(color: CustomColors.divider),
                  const SizedBox(height: 4),
                  Text(
                    listingSuggestion.name ?? 'Pending Review Listing',
                    style: textTheme(context).titleMedium!.copyWith(
                          color: CustomColors.primary,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                  ),
                  Text(
                    listingSuggestion.listingType == ListingType.gomama
                        ? 'Go!Mama Pod Location'
                        : 'Existing Nursing Facility',
                    style: textTheme(context).labelSmall!.copyWith(
                          color: CustomColors.placeholder,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text('Address: ${listingSuggestion.fullAddress}'),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
