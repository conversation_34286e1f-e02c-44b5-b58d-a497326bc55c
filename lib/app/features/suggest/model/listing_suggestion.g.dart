// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_suggestion.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingSuggestionImpl _$$ListingSuggestionImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingSuggestionImpl(
      id: json['id'] as String,
      name: json['name'] as String?,
      listingType:
          $enumDecodeNullable(_$ListingTypeEnumMap, json['listing_type']),
      companyName: json['company_name'] as String?,
      contactNumber: json['contact_number'] as String?,
      note: json['note'] as String?,
      description: json['description'] as String?,
      addressName: json['address_name'] as String?,
      fullAddress: json['full_address'] as String?,
      openingHours: json['opening_hours'] as String?,
      numberOfPrivateFeedingRooms:
          (json['number_of_private_feeding_rooms'] as num?)?.toInt(),
      position: json['position'] == null
          ? null
          : ListingPosition.fromJson(json['position'] as Map<String, dynamic>),
      listingFiles: (json['listing_files'] as List<dynamic>?)
          ?.map((e) => ListingFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => Amenity.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecodeNullable(_$ListingStatusEnumMap, json['status']),
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$ListingSuggestionImplToJson(
        _$ListingSuggestionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'listing_type': _$ListingTypeEnumMap[instance.listingType],
      'company_name': instance.companyName,
      'contact_number': instance.contactNumber,
      'note': instance.note,
      'description': instance.description,
      'address_name': instance.addressName,
      'full_address': instance.fullAddress,
      'opening_hours': instance.openingHours,
      'number_of_private_feeding_rooms': instance.numberOfPrivateFeedingRooms,
      'position': instance.position?.toJson(),
      'listing_files': instance.listingFiles?.map((e) => e.toJson()).toList(),
      'amenities': instance.amenities?.map((e) => e.toJson()).toList(),
      'status': _$ListingStatusEnumMap[instance.status],
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

const _$ListingTypeEnumMap = {
  ListingType.gomama: 'gomama',
  ListingType.care: 'care',
};

const _$ListingStatusEnumMap = {
  ListingStatus.idle: 'idle',
  ListingStatus.occupied: 'occupied',
  ListingStatus.disinfecting: 'disinfecting',
};

_$ListingSuggestionInputImpl _$$ListingSuggestionInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingSuggestionInputImpl(
      region: json['region'] as String,
      fullAddress: json['full_address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      note: json['note'] as String,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$ListingSuggestionInputImplToJson(
        _$ListingSuggestionInputImpl instance) =>
    <String, dynamic>{
      'region': instance.region,
      'full_address': instance.fullAddress,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'note': instance.note,
      'runtimeType': instance.$type,
    };

_$PodSuggestionInputImpl _$$PodSuggestionInputImplFromJson(
        Map<String, dynamic> json) =>
    _$PodSuggestionInputImpl(
      region: json['region'] as String,
      fullAddress: json['full_address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      note: json['note'] as String,
      listingType: json['listing_type'] as String? ?? 'gomama',
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$PodSuggestionInputImplToJson(
        _$PodSuggestionInputImpl instance) =>
    <String, dynamic>{
      'region': instance.region,
      'full_address': instance.fullAddress,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'note': instance.note,
      'listing_type': instance.listingType,
      'runtimeType': instance.$type,
    };

_$NursingFacilitySuggestionInputImpl
    _$$NursingFacilitySuggestionInputImplFromJson(Map<String, dynamic> json) =>
        _$NursingFacilitySuggestionInputImpl(
          region: json['region'] as String,
          fullAddress: json['full_address'] as String,
          latitude: (json['latitude'] as num).toDouble(),
          longitude: (json['longitude'] as num).toDouble(),
          note: json['note'] as String,
          listingType: json['listing_type'] as String? ?? 'care',
          addressName: json['address_name'] as String,
          description: json['description'] as String,
          amenities: (json['amenities[]'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
          companyName: json['company_name'] as String,
          postalCode: json['postal_code'] as String,
          contactNumber: json['contact_number'] as String,
          countryDialCode: json['country_dial_code'] as String,
          openingHours: json['opening_hours'] as String,
          $type: json['runtimeType'] as String?,
        );

Map<String, dynamic> _$$NursingFacilitySuggestionInputImplToJson(
        _$NursingFacilitySuggestionInputImpl instance) =>
    <String, dynamic>{
      'region': instance.region,
      'full_address': instance.fullAddress,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'note': instance.note,
      'listing_type': instance.listingType,
      'address_name': instance.addressName,
      'description': instance.description,
      'amenities[]': instance.amenities,
      'company_name': instance.companyName,
      'postal_code': instance.postalCode,
      'contact_number': instance.contactNumber,
      'country_dial_code': instance.countryDialCode,
      'opening_hours': instance.openingHours,
      'runtimeType': instance.$type,
    };

_$ListingSuggestionsResponseImpl _$$ListingSuggestionsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingSuggestionsResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => ListingSuggestion.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ListingSuggestionsResponseImplToJson(
        _$ListingSuggestionsResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
