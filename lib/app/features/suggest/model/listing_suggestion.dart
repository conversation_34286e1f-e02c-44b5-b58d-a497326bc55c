// ignore_for_file: invalid_annotation_target

import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listing_files.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:intl/intl.dart';

part 'listing_suggestion.freezed.dart';
part 'listing_suggestion.g.dart';

@freezed
class ListingSuggestion with _$ListingSuggestion {
  @CustomDateTimeConverter()
  const factory ListingSuggestion({
    required String id,
    String? name,
    ListingType? listingType,
    String? companyName,
    String? contactNumber,
    String? note,
    String? description,
    String? addressName,
    String? fullAddress,
    String? openingHours,
    int? numberOfPrivateFeedingRooms,
    ListingPosition? position,
    List<ListingFile>? listingFiles,
    List<Amenity>? amenities,
    ListingStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ListingSuggestion;

  const ListingSuggestion._();

  factory ListingSuggestion.fromJson(Json json) =>
      _$ListingSuggestionFromJson(json);

  String get createdDate =>
      DateFormat('dd MMM yyyy').format(createdAt ?? DateTime.now());
}

@freezed
class ListingSuggestionInput with _$ListingSuggestionInput {
  const factory ListingSuggestionInput({
    required String region,
    required String fullAddress,
    required double latitude,
    required double longitude,
    @JsonKey(includeFromJson: false) File? mainImageFile,
    @JsonKey(includeFromJson: false) List<File>? subImageFiles,
    required String note, // reason
  }) = _ListingSuggestionInput;

  @Implements<PodSuggestionInput>()
  const factory ListingSuggestionInput.podSuggestionInput({
    required String region,
    required String fullAddress,
    required double latitude,
    required double longitude,
    @JsonKey(includeFromJson: false) File? mainImageFile,
    @JsonKey(includeFromJson: false) List<File>? subImageFiles,
    required String note, // reason
    //
    @Default('gomama') String listingType,
  }) = _PodSuggestionInput;

  @Implements<NursingFacilitySuggestionInput>()
  const factory ListingSuggestionInput.nursingFacilitySuggestionInput({
    required String region,
    required String fullAddress,
    required double latitude,
    required double longitude,
    @JsonKey(includeFromJson: false) File? mainImageFile,
    @JsonKey(includeFromJson: false) List<File>? subImageFiles,
    required String note, // reason
    // nursing facility:
    @Default('care') String listingType,
    required String addressName,
    required String description,
    @JsonKey(name: 'amenities[]') required List<String> amenities,
    required String companyName,
    required String postalCode,
    required String contactNumber,
    required String countryDialCode,
    required String openingHours,
  }) = _NursingFacilitySuggestionInput;

  factory ListingSuggestionInput.fromJson(Json json) =>
      _$ListingSuggestionInputFromJson(json);
}

sealed class PodSuggestionInput implements ListingSuggestionInput {}

sealed class NursingFacilitySuggestionInput implements ListingSuggestionInput {}

@freezed
class ListingSuggestionsResponse with _$ListingSuggestionsResponse {
  const factory ListingSuggestionsResponse({
    required List<ListingSuggestion> data,
    required Pagination meta,
  }) = _ListingSuggestionsResponse;

  factory ListingSuggestionsResponse.fromJson(Json json) =>
      _$ListingSuggestionsResponseFromJson(json);
}
