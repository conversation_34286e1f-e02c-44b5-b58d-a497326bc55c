// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_suggestion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListingSuggestion _$ListingSuggestionFromJson(Map<String, dynamic> json) {
  return _ListingSuggestion.fromJson(json);
}

/// @nodoc
mixin _$ListingSuggestion {
  String get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  ListingType? get listingType => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;
  String? get contactNumber => throw _privateConstructorUsedError;
  String? get note => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get addressName => throw _privateConstructorUsedError;
  String? get fullAddress => throw _privateConstructorUsedError;
  String? get openingHours => throw _privateConstructorUsedError;
  int? get numberOfPrivateFeedingRooms => throw _privateConstructorUsedError;
  ListingPosition? get position => throw _privateConstructorUsedError;
  List<ListingFile>? get listingFiles => throw _privateConstructorUsedError;
  List<Amenity>? get amenities => throw _privateConstructorUsedError;
  ListingStatus? get status => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingSuggestionCopyWith<ListingSuggestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingSuggestionCopyWith<$Res> {
  factory $ListingSuggestionCopyWith(
          ListingSuggestion value, $Res Function(ListingSuggestion) then) =
      _$ListingSuggestionCopyWithImpl<$Res, ListingSuggestion>;
  @useResult
  $Res call(
      {String id,
      String? name,
      ListingType? listingType,
      String? companyName,
      String? contactNumber,
      String? note,
      String? description,
      String? addressName,
      String? fullAddress,
      String? openingHours,
      int? numberOfPrivateFeedingRooms,
      ListingPosition? position,
      List<ListingFile>? listingFiles,
      List<Amenity>? amenities,
      ListingStatus? status,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ListingPositionCopyWith<$Res>? get position;
}

/// @nodoc
class _$ListingSuggestionCopyWithImpl<$Res, $Val extends ListingSuggestion>
    implements $ListingSuggestionCopyWith<$Res> {
  _$ListingSuggestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? listingType = freezed,
    Object? companyName = freezed,
    Object? contactNumber = freezed,
    Object? note = freezed,
    Object? description = freezed,
    Object? addressName = freezed,
    Object? fullAddress = freezed,
    Object? openingHours = freezed,
    Object? numberOfPrivateFeedingRooms = freezed,
    Object? position = freezed,
    Object? listingFiles = freezed,
    Object? amenities = freezed,
    Object? status = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      listingType: freezed == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as ListingType?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactNumber: freezed == contactNumber
          ? _value.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      addressName: freezed == addressName
          ? _value.addressName
          : addressName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullAddress: freezed == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      openingHours: freezed == openingHours
          ? _value.openingHours
          : openingHours // ignore: cast_nullable_to_non_nullable
              as String?,
      numberOfPrivateFeedingRooms: freezed == numberOfPrivateFeedingRooms
          ? _value.numberOfPrivateFeedingRooms
          : numberOfPrivateFeedingRooms // ignore: cast_nullable_to_non_nullable
              as int?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as ListingPosition?,
      listingFiles: freezed == listingFiles
          ? _value.listingFiles
          : listingFiles // ignore: cast_nullable_to_non_nullable
              as List<ListingFile>?,
      amenities: freezed == amenities
          ? _value.amenities
          : amenities // ignore: cast_nullable_to_non_nullable
              as List<Amenity>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingPositionCopyWith<$Res>? get position {
    if (_value.position == null) {
      return null;
    }

    return $ListingPositionCopyWith<$Res>(_value.position!, (value) {
      return _then(_value.copyWith(position: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingSuggestionImplCopyWith<$Res>
    implements $ListingSuggestionCopyWith<$Res> {
  factory _$$ListingSuggestionImplCopyWith(_$ListingSuggestionImpl value,
          $Res Function(_$ListingSuggestionImpl) then) =
      __$$ListingSuggestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? name,
      ListingType? listingType,
      String? companyName,
      String? contactNumber,
      String? note,
      String? description,
      String? addressName,
      String? fullAddress,
      String? openingHours,
      int? numberOfPrivateFeedingRooms,
      ListingPosition? position,
      List<ListingFile>? listingFiles,
      List<Amenity>? amenities,
      ListingStatus? status,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ListingPositionCopyWith<$Res>? get position;
}

/// @nodoc
class __$$ListingSuggestionImplCopyWithImpl<$Res>
    extends _$ListingSuggestionCopyWithImpl<$Res, _$ListingSuggestionImpl>
    implements _$$ListingSuggestionImplCopyWith<$Res> {
  __$$ListingSuggestionImplCopyWithImpl(_$ListingSuggestionImpl _value,
      $Res Function(_$ListingSuggestionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? listingType = freezed,
    Object? companyName = freezed,
    Object? contactNumber = freezed,
    Object? note = freezed,
    Object? description = freezed,
    Object? addressName = freezed,
    Object? fullAddress = freezed,
    Object? openingHours = freezed,
    Object? numberOfPrivateFeedingRooms = freezed,
    Object? position = freezed,
    Object? listingFiles = freezed,
    Object? amenities = freezed,
    Object? status = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ListingSuggestionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      listingType: freezed == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as ListingType?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactNumber: freezed == contactNumber
          ? _value.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      addressName: freezed == addressName
          ? _value.addressName
          : addressName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullAddress: freezed == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      openingHours: freezed == openingHours
          ? _value.openingHours
          : openingHours // ignore: cast_nullable_to_non_nullable
              as String?,
      numberOfPrivateFeedingRooms: freezed == numberOfPrivateFeedingRooms
          ? _value.numberOfPrivateFeedingRooms
          : numberOfPrivateFeedingRooms // ignore: cast_nullable_to_non_nullable
              as int?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as ListingPosition?,
      listingFiles: freezed == listingFiles
          ? _value._listingFiles
          : listingFiles // ignore: cast_nullable_to_non_nullable
              as List<ListingFile>?,
      amenities: freezed == amenities
          ? _value._amenities
          : amenities // ignore: cast_nullable_to_non_nullable
              as List<Amenity>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ListingSuggestionImpl extends _ListingSuggestion {
  const _$ListingSuggestionImpl(
      {required this.id,
      this.name,
      this.listingType,
      this.companyName,
      this.contactNumber,
      this.note,
      this.description,
      this.addressName,
      this.fullAddress,
      this.openingHours,
      this.numberOfPrivateFeedingRooms,
      this.position,
      final List<ListingFile>? listingFiles,
      final List<Amenity>? amenities,
      this.status,
      this.createdAt,
      this.updatedAt})
      : _listingFiles = listingFiles,
        _amenities = amenities,
        super._();

  factory _$ListingSuggestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingSuggestionImplFromJson(json);

  @override
  final String id;
  @override
  final String? name;
  @override
  final ListingType? listingType;
  @override
  final String? companyName;
  @override
  final String? contactNumber;
  @override
  final String? note;
  @override
  final String? description;
  @override
  final String? addressName;
  @override
  final String? fullAddress;
  @override
  final String? openingHours;
  @override
  final int? numberOfPrivateFeedingRooms;
  @override
  final ListingPosition? position;
  final List<ListingFile>? _listingFiles;
  @override
  List<ListingFile>? get listingFiles {
    final value = _listingFiles;
    if (value == null) return null;
    if (_listingFiles is EqualUnmodifiableListView) return _listingFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Amenity>? _amenities;
  @override
  List<Amenity>? get amenities {
    final value = _amenities;
    if (value == null) return null;
    if (_amenities is EqualUnmodifiableListView) return _amenities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ListingStatus? status;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ListingSuggestion(id: $id, name: $name, listingType: $listingType, companyName: $companyName, contactNumber: $contactNumber, note: $note, description: $description, addressName: $addressName, fullAddress: $fullAddress, openingHours: $openingHours, numberOfPrivateFeedingRooms: $numberOfPrivateFeedingRooms, position: $position, listingFiles: $listingFiles, amenities: $amenities, status: $status, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingSuggestionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.listingType, listingType) ||
                other.listingType == listingType) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.addressName, addressName) ||
                other.addressName == addressName) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.openingHours, openingHours) ||
                other.openingHours == openingHours) &&
            (identical(other.numberOfPrivateFeedingRooms,
                    numberOfPrivateFeedingRooms) ||
                other.numberOfPrivateFeedingRooms ==
                    numberOfPrivateFeedingRooms) &&
            (identical(other.position, position) ||
                other.position == position) &&
            const DeepCollectionEquality()
                .equals(other._listingFiles, _listingFiles) &&
            const DeepCollectionEquality()
                .equals(other._amenities, _amenities) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      listingType,
      companyName,
      contactNumber,
      note,
      description,
      addressName,
      fullAddress,
      openingHours,
      numberOfPrivateFeedingRooms,
      position,
      const DeepCollectionEquality().hash(_listingFiles),
      const DeepCollectionEquality().hash(_amenities),
      status,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingSuggestionImplCopyWith<_$ListingSuggestionImpl> get copyWith =>
      __$$ListingSuggestionImplCopyWithImpl<_$ListingSuggestionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingSuggestionImplToJson(
      this,
    );
  }
}

abstract class _ListingSuggestion extends ListingSuggestion {
  const factory _ListingSuggestion(
      {required final String id,
      final String? name,
      final ListingType? listingType,
      final String? companyName,
      final String? contactNumber,
      final String? note,
      final String? description,
      final String? addressName,
      final String? fullAddress,
      final String? openingHours,
      final int? numberOfPrivateFeedingRooms,
      final ListingPosition? position,
      final List<ListingFile>? listingFiles,
      final List<Amenity>? amenities,
      final ListingStatus? status,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ListingSuggestionImpl;
  const _ListingSuggestion._() : super._();

  factory _ListingSuggestion.fromJson(Map<String, dynamic> json) =
      _$ListingSuggestionImpl.fromJson;

  @override
  String get id;
  @override
  String? get name;
  @override
  ListingType? get listingType;
  @override
  String? get companyName;
  @override
  String? get contactNumber;
  @override
  String? get note;
  @override
  String? get description;
  @override
  String? get addressName;
  @override
  String? get fullAddress;
  @override
  String? get openingHours;
  @override
  int? get numberOfPrivateFeedingRooms;
  @override
  ListingPosition? get position;
  @override
  List<ListingFile>? get listingFiles;
  @override
  List<Amenity>? get amenities;
  @override
  ListingStatus? get status;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ListingSuggestionImplCopyWith<_$ListingSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListingSuggestionInput _$ListingSuggestionInputFromJson(
    Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'default':
      return _ListingSuggestionInput.fromJson(json);
    case 'podSuggestionInput':
      return _PodSuggestionInput.fromJson(json);
    case 'nursingFacilitySuggestionInput':
      return _NursingFacilitySuggestionInput.fromJson(json);

    default:
      throw CheckedFromJsonException(
          json,
          'runtimeType',
          'ListingSuggestionInput',
          'Invalid union type "${json['runtimeType']}"!');
  }
}

/// @nodoc
mixin _$ListingSuggestionInput {
  String get region => throw _privateConstructorUsedError;
  String get fullAddress => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false)
  File? get mainImageFile => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles => throw _privateConstructorUsedError;
  String get note => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)
        $default, {
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)
        podSuggestionInput,
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)
        nursingFacilitySuggestionInput,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value) $default, {
    required TResult Function(_PodSuggestionInput value) podSuggestionInput,
    required TResult Function(_NursingFacilitySuggestionInput value)
        nursingFacilitySuggestionInput,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ListingSuggestionInput value)? $default, {
    TResult? Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult? Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value)? $default, {
    TResult Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingSuggestionInputCopyWith<ListingSuggestionInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingSuggestionInputCopyWith<$Res> {
  factory $ListingSuggestionInputCopyWith(ListingSuggestionInput value,
          $Res Function(ListingSuggestionInput) then) =
      _$ListingSuggestionInputCopyWithImpl<$Res, ListingSuggestionInput>;
  @useResult
  $Res call(
      {String region,
      String fullAddress,
      double latitude,
      double longitude,
      @JsonKey(includeFromJson: false) File? mainImageFile,
      @JsonKey(includeFromJson: false) List<File>? subImageFiles,
      String note});
}

/// @nodoc
class _$ListingSuggestionInputCopyWithImpl<$Res,
        $Val extends ListingSuggestionInput>
    implements $ListingSuggestionInputCopyWith<$Res> {
  _$ListingSuggestionInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = null,
    Object? fullAddress = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? mainImageFile = freezed,
    Object? subImageFiles = freezed,
    Object? note = null,
  }) {
    return _then(_value.copyWith(
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      mainImageFile: freezed == mainImageFile
          ? _value.mainImageFile
          : mainImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
      subImageFiles: freezed == subImageFiles
          ? _value.subImageFiles
          : subImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
      note: null == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingSuggestionInputImplCopyWith<$Res>
    implements $ListingSuggestionInputCopyWith<$Res> {
  factory _$$ListingSuggestionInputImplCopyWith(
          _$ListingSuggestionInputImpl value,
          $Res Function(_$ListingSuggestionInputImpl) then) =
      __$$ListingSuggestionInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String region,
      String fullAddress,
      double latitude,
      double longitude,
      @JsonKey(includeFromJson: false) File? mainImageFile,
      @JsonKey(includeFromJson: false) List<File>? subImageFiles,
      String note});
}

/// @nodoc
class __$$ListingSuggestionInputImplCopyWithImpl<$Res>
    extends _$ListingSuggestionInputCopyWithImpl<$Res,
        _$ListingSuggestionInputImpl>
    implements _$$ListingSuggestionInputImplCopyWith<$Res> {
  __$$ListingSuggestionInputImplCopyWithImpl(
      _$ListingSuggestionInputImpl _value,
      $Res Function(_$ListingSuggestionInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = null,
    Object? fullAddress = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? mainImageFile = freezed,
    Object? subImageFiles = freezed,
    Object? note = null,
  }) {
    return _then(_$ListingSuggestionInputImpl(
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      mainImageFile: freezed == mainImageFile
          ? _value.mainImageFile
          : mainImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
      subImageFiles: freezed == subImageFiles
          ? _value._subImageFiles
          : subImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
      note: null == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingSuggestionInputImpl implements _ListingSuggestionInput {
  const _$ListingSuggestionInputImpl(
      {required this.region,
      required this.fullAddress,
      required this.latitude,
      required this.longitude,
      @JsonKey(includeFromJson: false) this.mainImageFile,
      @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
      required this.note,
      final String? $type})
      : _subImageFiles = subImageFiles,
        $type = $type ?? 'default';

  factory _$ListingSuggestionInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingSuggestionInputImplFromJson(json);

  @override
  final String region;
  @override
  final String fullAddress;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  @JsonKey(includeFromJson: false)
  final File? mainImageFile;
  final List<File>? _subImageFiles;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles {
    final value = _subImageFiles;
    if (value == null) return null;
    if (_subImageFiles is EqualUnmodifiableListView) return _subImageFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String note;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListingSuggestionInput(region: $region, fullAddress: $fullAddress, latitude: $latitude, longitude: $longitude, mainImageFile: $mainImageFile, subImageFiles: $subImageFiles, note: $note)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingSuggestionInputImpl &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.mainImageFile, mainImageFile) ||
                other.mainImageFile == mainImageFile) &&
            const DeepCollectionEquality()
                .equals(other._subImageFiles, _subImageFiles) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      region,
      fullAddress,
      latitude,
      longitude,
      mainImageFile,
      const DeepCollectionEquality().hash(_subImageFiles),
      note);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingSuggestionInputImplCopyWith<_$ListingSuggestionInputImpl>
      get copyWith => __$$ListingSuggestionInputImplCopyWithImpl<
          _$ListingSuggestionInputImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)
        $default, {
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)
        podSuggestionInput,
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)
        nursingFacilitySuggestionInput,
  }) {
    return $default(region, fullAddress, latitude, longitude, mainImageFile,
        subImageFiles, note);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
  }) {
    return $default?.call(region, fullAddress, latitude, longitude,
        mainImageFile, subImageFiles, note);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(region, fullAddress, latitude, longitude, mainImageFile,
          subImageFiles, note);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value) $default, {
    required TResult Function(_PodSuggestionInput value) podSuggestionInput,
    required TResult Function(_NursingFacilitySuggestionInput value)
        nursingFacilitySuggestionInput,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ListingSuggestionInput value)? $default, {
    TResult? Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult? Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value)? $default, {
    TResult Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingSuggestionInputImplToJson(
      this,
    );
  }
}

abstract class _ListingSuggestionInput implements ListingSuggestionInput {
  const factory _ListingSuggestionInput(
      {required final String region,
      required final String fullAddress,
      required final double latitude,
      required final double longitude,
      @JsonKey(includeFromJson: false) final File? mainImageFile,
      @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
      required final String note}) = _$ListingSuggestionInputImpl;

  factory _ListingSuggestionInput.fromJson(Map<String, dynamic> json) =
      _$ListingSuggestionInputImpl.fromJson;

  @override
  String get region;
  @override
  String get fullAddress;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  @JsonKey(includeFromJson: false)
  File? get mainImageFile;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles;
  @override
  String get note;
  @override
  @JsonKey(ignore: true)
  _$$ListingSuggestionInputImplCopyWith<_$ListingSuggestionInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PodSuggestionInputImplCopyWith<$Res>
    implements $ListingSuggestionInputCopyWith<$Res> {
  factory _$$PodSuggestionInputImplCopyWith(_$PodSuggestionInputImpl value,
          $Res Function(_$PodSuggestionInputImpl) then) =
      __$$PodSuggestionInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String region,
      String fullAddress,
      double latitude,
      double longitude,
      @JsonKey(includeFromJson: false) File? mainImageFile,
      @JsonKey(includeFromJson: false) List<File>? subImageFiles,
      String note,
      String listingType});
}

/// @nodoc
class __$$PodSuggestionInputImplCopyWithImpl<$Res>
    extends _$ListingSuggestionInputCopyWithImpl<$Res, _$PodSuggestionInputImpl>
    implements _$$PodSuggestionInputImplCopyWith<$Res> {
  __$$PodSuggestionInputImplCopyWithImpl(_$PodSuggestionInputImpl _value,
      $Res Function(_$PodSuggestionInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = null,
    Object? fullAddress = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? mainImageFile = freezed,
    Object? subImageFiles = freezed,
    Object? note = null,
    Object? listingType = null,
  }) {
    return _then(_$PodSuggestionInputImpl(
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      mainImageFile: freezed == mainImageFile
          ? _value.mainImageFile
          : mainImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
      subImageFiles: freezed == subImageFiles
          ? _value._subImageFiles
          : subImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
      note: null == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
      listingType: null == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PodSuggestionInputImpl implements _PodSuggestionInput {
  const _$PodSuggestionInputImpl(
      {required this.region,
      required this.fullAddress,
      required this.latitude,
      required this.longitude,
      @JsonKey(includeFromJson: false) this.mainImageFile,
      @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
      required this.note,
      this.listingType = 'gomama',
      final String? $type})
      : _subImageFiles = subImageFiles,
        $type = $type ?? 'podSuggestionInput';

  factory _$PodSuggestionInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$PodSuggestionInputImplFromJson(json);

  @override
  final String region;
  @override
  final String fullAddress;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  @JsonKey(includeFromJson: false)
  final File? mainImageFile;
  final List<File>? _subImageFiles;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles {
    final value = _subImageFiles;
    if (value == null) return null;
    if (_subImageFiles is EqualUnmodifiableListView) return _subImageFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String note;
// reason
//
  @override
  @JsonKey()
  final String listingType;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListingSuggestionInput.podSuggestionInput(region: $region, fullAddress: $fullAddress, latitude: $latitude, longitude: $longitude, mainImageFile: $mainImageFile, subImageFiles: $subImageFiles, note: $note, listingType: $listingType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PodSuggestionInputImpl &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.mainImageFile, mainImageFile) ||
                other.mainImageFile == mainImageFile) &&
            const DeepCollectionEquality()
                .equals(other._subImageFiles, _subImageFiles) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.listingType, listingType) ||
                other.listingType == listingType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      region,
      fullAddress,
      latitude,
      longitude,
      mainImageFile,
      const DeepCollectionEquality().hash(_subImageFiles),
      note,
      listingType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PodSuggestionInputImplCopyWith<_$PodSuggestionInputImpl> get copyWith =>
      __$$PodSuggestionInputImplCopyWithImpl<_$PodSuggestionInputImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)
        $default, {
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)
        podSuggestionInput,
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)
        nursingFacilitySuggestionInput,
  }) {
    return podSuggestionInput(region, fullAddress, latitude, longitude,
        mainImageFile, subImageFiles, note, listingType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
  }) {
    return podSuggestionInput?.call(region, fullAddress, latitude, longitude,
        mainImageFile, subImageFiles, note, listingType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if (podSuggestionInput != null) {
      return podSuggestionInput(region, fullAddress, latitude, longitude,
          mainImageFile, subImageFiles, note, listingType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value) $default, {
    required TResult Function(_PodSuggestionInput value) podSuggestionInput,
    required TResult Function(_NursingFacilitySuggestionInput value)
        nursingFacilitySuggestionInput,
  }) {
    return podSuggestionInput(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ListingSuggestionInput value)? $default, {
    TResult? Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult? Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
  }) {
    return podSuggestionInput?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value)? $default, {
    TResult Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if (podSuggestionInput != null) {
      return podSuggestionInput(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$PodSuggestionInputImplToJson(
      this,
    );
  }
}

abstract class _PodSuggestionInput
    implements ListingSuggestionInput, PodSuggestionInput {
  const factory _PodSuggestionInput(
      {required final String region,
      required final String fullAddress,
      required final double latitude,
      required final double longitude,
      @JsonKey(includeFromJson: false) final File? mainImageFile,
      @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
      required final String note,
      final String listingType}) = _$PodSuggestionInputImpl;

  factory _PodSuggestionInput.fromJson(Map<String, dynamic> json) =
      _$PodSuggestionInputImpl.fromJson;

  @override
  String get region;
  @override
  String get fullAddress;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  @JsonKey(includeFromJson: false)
  File? get mainImageFile;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles;
  @override
  String get note; // reason
//
  String get listingType;
  @override
  @JsonKey(ignore: true)
  _$$PodSuggestionInputImplCopyWith<_$PodSuggestionInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NursingFacilitySuggestionInputImplCopyWith<$Res>
    implements $ListingSuggestionInputCopyWith<$Res> {
  factory _$$NursingFacilitySuggestionInputImplCopyWith(
          _$NursingFacilitySuggestionInputImpl value,
          $Res Function(_$NursingFacilitySuggestionInputImpl) then) =
      __$$NursingFacilitySuggestionInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String region,
      String fullAddress,
      double latitude,
      double longitude,
      @JsonKey(includeFromJson: false) File? mainImageFile,
      @JsonKey(includeFromJson: false) List<File>? subImageFiles,
      String note,
      String listingType,
      String addressName,
      String description,
      @JsonKey(name: 'amenities[]') List<String> amenities,
      String companyName,
      String postalCode,
      String contactNumber,
      String countryDialCode,
      String openingHours});
}

/// @nodoc
class __$$NursingFacilitySuggestionInputImplCopyWithImpl<$Res>
    extends _$ListingSuggestionInputCopyWithImpl<$Res,
        _$NursingFacilitySuggestionInputImpl>
    implements _$$NursingFacilitySuggestionInputImplCopyWith<$Res> {
  __$$NursingFacilitySuggestionInputImplCopyWithImpl(
      _$NursingFacilitySuggestionInputImpl _value,
      $Res Function(_$NursingFacilitySuggestionInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = null,
    Object? fullAddress = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? mainImageFile = freezed,
    Object? subImageFiles = freezed,
    Object? note = null,
    Object? listingType = null,
    Object? addressName = null,
    Object? description = null,
    Object? amenities = null,
    Object? companyName = null,
    Object? postalCode = null,
    Object? contactNumber = null,
    Object? countryDialCode = null,
    Object? openingHours = null,
  }) {
    return _then(_$NursingFacilitySuggestionInputImpl(
      region: null == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      mainImageFile: freezed == mainImageFile
          ? _value.mainImageFile
          : mainImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
      subImageFiles: freezed == subImageFiles
          ? _value._subImageFiles
          : subImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
      note: null == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
      listingType: null == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as String,
      addressName: null == addressName
          ? _value.addressName
          : addressName // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      amenities: null == amenities
          ? _value._amenities
          : amenities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      postalCode: null == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String,
      contactNumber: null == contactNumber
          ? _value.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryDialCode: null == countryDialCode
          ? _value.countryDialCode
          : countryDialCode // ignore: cast_nullable_to_non_nullable
              as String,
      openingHours: null == openingHours
          ? _value.openingHours
          : openingHours // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NursingFacilitySuggestionInputImpl
    implements _NursingFacilitySuggestionInput {
  const _$NursingFacilitySuggestionInputImpl(
      {required this.region,
      required this.fullAddress,
      required this.latitude,
      required this.longitude,
      @JsonKey(includeFromJson: false) this.mainImageFile,
      @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
      required this.note,
      this.listingType = 'care',
      required this.addressName,
      required this.description,
      @JsonKey(name: 'amenities[]') required final List<String> amenities,
      required this.companyName,
      required this.postalCode,
      required this.contactNumber,
      required this.countryDialCode,
      required this.openingHours,
      final String? $type})
      : _subImageFiles = subImageFiles,
        _amenities = amenities,
        $type = $type ?? 'nursingFacilitySuggestionInput';

  factory _$NursingFacilitySuggestionInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$NursingFacilitySuggestionInputImplFromJson(json);

  @override
  final String region;
  @override
  final String fullAddress;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  @JsonKey(includeFromJson: false)
  final File? mainImageFile;
  final List<File>? _subImageFiles;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles {
    final value = _subImageFiles;
    if (value == null) return null;
    if (_subImageFiles is EqualUnmodifiableListView) return _subImageFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String note;
// reason
// nursing facility:
  @override
  @JsonKey()
  final String listingType;
  @override
  final String addressName;
  @override
  final String description;
  final List<String> _amenities;
  @override
  @JsonKey(name: 'amenities[]')
  List<String> get amenities {
    if (_amenities is EqualUnmodifiableListView) return _amenities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_amenities);
  }

  @override
  final String companyName;
  @override
  final String postalCode;
  @override
  final String contactNumber;
  @override
  final String countryDialCode;
  @override
  final String openingHours;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ListingSuggestionInput.nursingFacilitySuggestionInput(region: $region, fullAddress: $fullAddress, latitude: $latitude, longitude: $longitude, mainImageFile: $mainImageFile, subImageFiles: $subImageFiles, note: $note, listingType: $listingType, addressName: $addressName, description: $description, amenities: $amenities, companyName: $companyName, postalCode: $postalCode, contactNumber: $contactNumber, countryDialCode: $countryDialCode, openingHours: $openingHours)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NursingFacilitySuggestionInputImpl &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.mainImageFile, mainImageFile) ||
                other.mainImageFile == mainImageFile) &&
            const DeepCollectionEquality()
                .equals(other._subImageFiles, _subImageFiles) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.listingType, listingType) ||
                other.listingType == listingType) &&
            (identical(other.addressName, addressName) ||
                other.addressName == addressName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._amenities, _amenities) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.countryDialCode, countryDialCode) ||
                other.countryDialCode == countryDialCode) &&
            (identical(other.openingHours, openingHours) ||
                other.openingHours == openingHours));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      region,
      fullAddress,
      latitude,
      longitude,
      mainImageFile,
      const DeepCollectionEquality().hash(_subImageFiles),
      note,
      listingType,
      addressName,
      description,
      const DeepCollectionEquality().hash(_amenities),
      companyName,
      postalCode,
      contactNumber,
      countryDialCode,
      openingHours);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NursingFacilitySuggestionInputImplCopyWith<
          _$NursingFacilitySuggestionInputImpl>
      get copyWith => __$$NursingFacilitySuggestionInputImplCopyWithImpl<
          _$NursingFacilitySuggestionInputImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)
        $default, {
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)
        podSuggestionInput,
    required TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)
        nursingFacilitySuggestionInput,
  }) {
    return nursingFacilitySuggestionInput(
        region,
        fullAddress,
        latitude,
        longitude,
        mainImageFile,
        subImageFiles,
        note,
        listingType,
        addressName,
        description,
        amenities,
        companyName,
        postalCode,
        contactNumber,
        countryDialCode,
        openingHours);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult? Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
  }) {
    return nursingFacilitySuggestionInput?.call(
        region,
        fullAddress,
        latitude,
        longitude,
        mainImageFile,
        subImageFiles,
        note,
        listingType,
        addressName,
        description,
        amenities,
        companyName,
        postalCode,
        contactNumber,
        countryDialCode,
        openingHours);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note)?
        $default, {
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType)?
        podSuggestionInput,
    TResult Function(
            String region,
            String fullAddress,
            double latitude,
            double longitude,
            @JsonKey(includeFromJson: false) File? mainImageFile,
            @JsonKey(includeFromJson: false) List<File>? subImageFiles,
            String note,
            String listingType,
            String addressName,
            String description,
            @JsonKey(name: 'amenities[]') List<String> amenities,
            String companyName,
            String postalCode,
            String contactNumber,
            String countryDialCode,
            String openingHours)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if (nursingFacilitySuggestionInput != null) {
      return nursingFacilitySuggestionInput(
          region,
          fullAddress,
          latitude,
          longitude,
          mainImageFile,
          subImageFiles,
          note,
          listingType,
          addressName,
          description,
          amenities,
          companyName,
          postalCode,
          contactNumber,
          countryDialCode,
          openingHours);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value) $default, {
    required TResult Function(_PodSuggestionInput value) podSuggestionInput,
    required TResult Function(_NursingFacilitySuggestionInput value)
        nursingFacilitySuggestionInput,
  }) {
    return nursingFacilitySuggestionInput(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ListingSuggestionInput value)? $default, {
    TResult? Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult? Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
  }) {
    return nursingFacilitySuggestionInput?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ListingSuggestionInput value)? $default, {
    TResult Function(_PodSuggestionInput value)? podSuggestionInput,
    TResult Function(_NursingFacilitySuggestionInput value)?
        nursingFacilitySuggestionInput,
    required TResult orElse(),
  }) {
    if (nursingFacilitySuggestionInput != null) {
      return nursingFacilitySuggestionInput(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$NursingFacilitySuggestionInputImplToJson(
      this,
    );
  }
}

abstract class _NursingFacilitySuggestionInput
    implements ListingSuggestionInput, NursingFacilitySuggestionInput {
  const factory _NursingFacilitySuggestionInput(
          {required final String region,
          required final String fullAddress,
          required final double latitude,
          required final double longitude,
          @JsonKey(includeFromJson: false) final File? mainImageFile,
          @JsonKey(includeFromJson: false) final List<File>? subImageFiles,
          required final String note,
          final String listingType,
          required final String addressName,
          required final String description,
          @JsonKey(name: 'amenities[]') required final List<String> amenities,
          required final String companyName,
          required final String postalCode,
          required final String contactNumber,
          required final String countryDialCode,
          required final String openingHours}) =
      _$NursingFacilitySuggestionInputImpl;

  factory _NursingFacilitySuggestionInput.fromJson(Map<String, dynamic> json) =
      _$NursingFacilitySuggestionInputImpl.fromJson;

  @override
  String get region;
  @override
  String get fullAddress;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  @JsonKey(includeFromJson: false)
  File? get mainImageFile;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get subImageFiles;
  @override
  String get note; // reason
// nursing facility:
  String get listingType;
  String get addressName;
  String get description;
  @JsonKey(name: 'amenities[]')
  List<String> get amenities;
  String get companyName;
  String get postalCode;
  String get contactNumber;
  String get countryDialCode;
  String get openingHours;
  @override
  @JsonKey(ignore: true)
  _$$NursingFacilitySuggestionInputImplCopyWith<
          _$NursingFacilitySuggestionInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ListingSuggestionsResponse _$ListingSuggestionsResponseFromJson(
    Map<String, dynamic> json) {
  return _ListingSuggestionsResponse.fromJson(json);
}

/// @nodoc
mixin _$ListingSuggestionsResponse {
  List<ListingSuggestion> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingSuggestionsResponseCopyWith<ListingSuggestionsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingSuggestionsResponseCopyWith<$Res> {
  factory $ListingSuggestionsResponseCopyWith(ListingSuggestionsResponse value,
          $Res Function(ListingSuggestionsResponse) then) =
      _$ListingSuggestionsResponseCopyWithImpl<$Res,
          ListingSuggestionsResponse>;
  @useResult
  $Res call({List<ListingSuggestion> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$ListingSuggestionsResponseCopyWithImpl<$Res,
        $Val extends ListingSuggestionsResponse>
    implements $ListingSuggestionsResponseCopyWith<$Res> {
  _$ListingSuggestionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ListingSuggestion>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingSuggestionsResponseImplCopyWith<$Res>
    implements $ListingSuggestionsResponseCopyWith<$Res> {
  factory _$$ListingSuggestionsResponseImplCopyWith(
          _$ListingSuggestionsResponseImpl value,
          $Res Function(_$ListingSuggestionsResponseImpl) then) =
      __$$ListingSuggestionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ListingSuggestion> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$ListingSuggestionsResponseImplCopyWithImpl<$Res>
    extends _$ListingSuggestionsResponseCopyWithImpl<$Res,
        _$ListingSuggestionsResponseImpl>
    implements _$$ListingSuggestionsResponseImplCopyWith<$Res> {
  __$$ListingSuggestionsResponseImplCopyWithImpl(
      _$ListingSuggestionsResponseImpl _value,
      $Res Function(_$ListingSuggestionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$ListingSuggestionsResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ListingSuggestion>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingSuggestionsResponseImpl implements _ListingSuggestionsResponse {
  const _$ListingSuggestionsResponseImpl(
      {required final List<ListingSuggestion> data, required this.meta})
      : _data = data;

  factory _$ListingSuggestionsResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ListingSuggestionsResponseImplFromJson(json);

  final List<ListingSuggestion> _data;
  @override
  List<ListingSuggestion> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'ListingSuggestionsResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingSuggestionsResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingSuggestionsResponseImplCopyWith<_$ListingSuggestionsResponseImpl>
      get copyWith => __$$ListingSuggestionsResponseImplCopyWithImpl<
          _$ListingSuggestionsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingSuggestionsResponseImplToJson(
      this,
    );
  }
}

abstract class _ListingSuggestionsResponse
    implements ListingSuggestionsResponse {
  const factory _ListingSuggestionsResponse(
      {required final List<ListingSuggestion> data,
      required final Pagination meta}) = _$ListingSuggestionsResponseImpl;

  factory _ListingSuggestionsResponse.fromJson(Map<String, dynamic> json) =
      _$ListingSuggestionsResponseImpl.fromJson;

  @override
  List<ListingSuggestion> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$ListingSuggestionsResponseImplCopyWith<_$ListingSuggestionsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
