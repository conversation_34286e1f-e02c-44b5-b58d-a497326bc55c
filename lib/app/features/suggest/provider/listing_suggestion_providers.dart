import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/repository/amenity_repository.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_suggestion_providers.g.dart';

/// keep alive
/// because static values fetch once can be reuse multiple times
@Riverpod(keepAlive: true)
Future<AmenitiesResponse> amenityList(AmenityListRef ref) async {
  return ref.watch(amenityRepositoryProvider).fetchAmenities();
}

@riverpod
FutureOr<PostResponse<Listing>> suggestListing(
  SuggestListingRef ref,
  ListingSuggestionInput input,
) async {
  // TODO(kkcy): test this
  final response =
      await ref.watch(listingRepositoryProvider).suggestListing(input);

  return response;
}

@riverpod
Future<ListingSuggestionsResponse> listingSuggestionsPages(
  ListingSuggestionsPagesRef ref,
  AllListingsPagination meta,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  if (cancelToken.isCancelled) throw Exception();

  final sorting = ref.watch(listingSortsProvider);
  Groveman.info('sorting selected', error: sorting);

  return ref.watch(listingRepositoryProvider).fetchSuggestions(
        meta.input,
        offset: meta.page,
        cancelToken: cancelToken,
      );
}

@riverpod
AsyncValue<int> listingSuggestionsCount(
  ListingSuggestionsCountRef ref,
  AllListingsInput? input,
) {
  final meta = AllListingsPagination(page: 0, input: input);

  return ref
      .watch(listingSuggestionsPagesProvider(meta))
      .whenData((value) => value.meta.total);
}

@riverpod
AsyncValue<ListingSuggestion> listingSuggestionAtIndex(
  ListingSuggestionAtIndexRef ref,
  AllListingsOffset query,
) {
  final offsetInPage = query.offset % kPageLimit;

  final meta = AllListingsPagination(
    page: query.offset ~/ kPageLimit,
    input: query.input,
  );

  return ref.watch(listingSuggestionsPagesProvider(meta)).whenData(
        (value) => value.data[offsetInPage],
      );
}
