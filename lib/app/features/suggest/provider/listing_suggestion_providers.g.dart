// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_suggestion_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$amenityListHash() => r'071a73063d58da6c03fd83c40666f2b0d1aecc50';

/// keep alive
/// because static values fetch once can be reuse multiple times
///
/// Copied from [amenityList].
@ProviderFor(amenityList)
final amenityListProvider = FutureProvider<AmenitiesResponse>.internal(
  amenityList,
  name: r'amenityListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$amenityListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AmenityListRef = FutureProviderRef<AmenitiesResponse>;
String _$suggestListingHash() => r'022d4f3b4937032e3b990aeaf7c1114f9fbb243c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [suggestListing].
@ProviderFor(suggestListing)
const suggestListingProvider = SuggestListingFamily();

/// See also [suggestListing].
class SuggestListingFamily extends Family<AsyncValue<PostResponse<Listing>>> {
  /// See also [suggestListing].
  const SuggestListingFamily();

  /// See also [suggestListing].
  SuggestListingProvider call(
    ListingSuggestionInput input,
  ) {
    return SuggestListingProvider(
      input,
    );
  }

  @override
  SuggestListingProvider getProviderOverride(
    covariant SuggestListingProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'suggestListingProvider';
}

/// See also [suggestListing].
class SuggestListingProvider
    extends AutoDisposeFutureProvider<PostResponse<Listing>> {
  /// See also [suggestListing].
  SuggestListingProvider(
    ListingSuggestionInput input,
  ) : this._internal(
          (ref) => suggestListing(
            ref as SuggestListingRef,
            input,
          ),
          from: suggestListingProvider,
          name: r'suggestListingProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$suggestListingHash,
          dependencies: SuggestListingFamily._dependencies,
          allTransitiveDependencies:
              SuggestListingFamily._allTransitiveDependencies,
          input: input,
        );

  SuggestListingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final ListingSuggestionInput input;

  @override
  Override overrideWith(
    FutureOr<PostResponse<Listing>> Function(SuggestListingRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SuggestListingProvider._internal(
        (ref) => create(ref as SuggestListingRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<PostResponse<Listing>> createElement() {
    return _SuggestListingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SuggestListingProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SuggestListingRef on AutoDisposeFutureProviderRef<PostResponse<Listing>> {
  /// The parameter `input` of this provider.
  ListingSuggestionInput get input;
}

class _SuggestListingProviderElement
    extends AutoDisposeFutureProviderElement<PostResponse<Listing>>
    with SuggestListingRef {
  _SuggestListingProviderElement(super.provider);

  @override
  ListingSuggestionInput get input => (origin as SuggestListingProvider).input;
}

String _$listingSuggestionsPagesHash() =>
    r'5abcb65506d3ec4913894f8f085168d74ef9ecc2';

/// See also [listingSuggestionsPages].
@ProviderFor(listingSuggestionsPages)
const listingSuggestionsPagesProvider = ListingSuggestionsPagesFamily();

/// See also [listingSuggestionsPages].
class ListingSuggestionsPagesFamily
    extends Family<AsyncValue<ListingSuggestionsResponse>> {
  /// See also [listingSuggestionsPages].
  const ListingSuggestionsPagesFamily();

  /// See also [listingSuggestionsPages].
  ListingSuggestionsPagesProvider call(
    AllListingsPagination meta,
  ) {
    return ListingSuggestionsPagesProvider(
      meta,
    );
  }

  @override
  ListingSuggestionsPagesProvider getProviderOverride(
    covariant ListingSuggestionsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingSuggestionsPagesProvider';
}

/// See also [listingSuggestionsPages].
class ListingSuggestionsPagesProvider
    extends AutoDisposeFutureProvider<ListingSuggestionsResponse> {
  /// See also [listingSuggestionsPages].
  ListingSuggestionsPagesProvider(
    AllListingsPagination meta,
  ) : this._internal(
          (ref) => listingSuggestionsPages(
            ref as ListingSuggestionsPagesRef,
            meta,
          ),
          from: listingSuggestionsPagesProvider,
          name: r'listingSuggestionsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingSuggestionsPagesHash,
          dependencies: ListingSuggestionsPagesFamily._dependencies,
          allTransitiveDependencies:
              ListingSuggestionsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  ListingSuggestionsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final AllListingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingSuggestionsResponse> Function(
            ListingSuggestionsPagesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingSuggestionsPagesProvider._internal(
        (ref) => create(ref as ListingSuggestionsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingSuggestionsResponse> createElement() {
    return _ListingSuggestionsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingSuggestionsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingSuggestionsPagesRef
    on AutoDisposeFutureProviderRef<ListingSuggestionsResponse> {
  /// The parameter `meta` of this provider.
  AllListingsPagination get meta;
}

class _ListingSuggestionsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingSuggestionsResponse>
    with ListingSuggestionsPagesRef {
  _ListingSuggestionsPagesProviderElement(super.provider);

  @override
  AllListingsPagination get meta =>
      (origin as ListingSuggestionsPagesProvider).meta;
}

String _$listingSuggestionsCountHash() =>
    r'bbaf33299dbd5538da3bfe46f717ff5b6602425b';

/// See also [listingSuggestionsCount].
@ProviderFor(listingSuggestionsCount)
const listingSuggestionsCountProvider = ListingSuggestionsCountFamily();

/// See also [listingSuggestionsCount].
class ListingSuggestionsCountFamily extends Family<AsyncValue<int>> {
  /// See also [listingSuggestionsCount].
  const ListingSuggestionsCountFamily();

  /// See also [listingSuggestionsCount].
  ListingSuggestionsCountProvider call(
    AllListingsInput? input,
  ) {
    return ListingSuggestionsCountProvider(
      input,
    );
  }

  @override
  ListingSuggestionsCountProvider getProviderOverride(
    covariant ListingSuggestionsCountProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingSuggestionsCountProvider';
}

/// See also [listingSuggestionsCount].
class ListingSuggestionsCountProvider
    extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [listingSuggestionsCount].
  ListingSuggestionsCountProvider(
    AllListingsInput? input,
  ) : this._internal(
          (ref) => listingSuggestionsCount(
            ref as ListingSuggestionsCountRef,
            input,
          ),
          from: listingSuggestionsCountProvider,
          name: r'listingSuggestionsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingSuggestionsCountHash,
          dependencies: ListingSuggestionsCountFamily._dependencies,
          allTransitiveDependencies:
              ListingSuggestionsCountFamily._allTransitiveDependencies,
          input: input,
        );

  ListingSuggestionsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final AllListingsInput? input;

  @override
  Override overrideWith(
    AsyncValue<int> Function(ListingSuggestionsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingSuggestionsCountProvider._internal(
        (ref) => create(ref as ListingSuggestionsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _ListingSuggestionsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingSuggestionsCountProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingSuggestionsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `input` of this provider.
  AllListingsInput? get input;
}

class _ListingSuggestionsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with ListingSuggestionsCountRef {
  _ListingSuggestionsCountProviderElement(super.provider);

  @override
  AllListingsInput? get input =>
      (origin as ListingSuggestionsCountProvider).input;
}

String _$listingSuggestionAtIndexHash() =>
    r'c954401f076d062ff3c8a2a348d9c446f010b9af';

/// See also [listingSuggestionAtIndex].
@ProviderFor(listingSuggestionAtIndex)
const listingSuggestionAtIndexProvider = ListingSuggestionAtIndexFamily();

/// See also [listingSuggestionAtIndex].
class ListingSuggestionAtIndexFamily
    extends Family<AsyncValue<ListingSuggestion>> {
  /// See also [listingSuggestionAtIndex].
  const ListingSuggestionAtIndexFamily();

  /// See also [listingSuggestionAtIndex].
  ListingSuggestionAtIndexProvider call(
    AllListingsOffset query,
  ) {
    return ListingSuggestionAtIndexProvider(
      query,
    );
  }

  @override
  ListingSuggestionAtIndexProvider getProviderOverride(
    covariant ListingSuggestionAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingSuggestionAtIndexProvider';
}

/// See also [listingSuggestionAtIndex].
class ListingSuggestionAtIndexProvider
    extends AutoDisposeProvider<AsyncValue<ListingSuggestion>> {
  /// See also [listingSuggestionAtIndex].
  ListingSuggestionAtIndexProvider(
    AllListingsOffset query,
  ) : this._internal(
          (ref) => listingSuggestionAtIndex(
            ref as ListingSuggestionAtIndexRef,
            query,
          ),
          from: listingSuggestionAtIndexProvider,
          name: r'listingSuggestionAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingSuggestionAtIndexHash,
          dependencies: ListingSuggestionAtIndexFamily._dependencies,
          allTransitiveDependencies:
              ListingSuggestionAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  ListingSuggestionAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final AllListingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<ListingSuggestion> Function(ListingSuggestionAtIndexRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingSuggestionAtIndexProvider._internal(
        (ref) => create(ref as ListingSuggestionAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<ListingSuggestion>> createElement() {
    return _ListingSuggestionAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingSuggestionAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingSuggestionAtIndexRef
    on AutoDisposeProviderRef<AsyncValue<ListingSuggestion>> {
  /// The parameter `query` of this provider.
  AllListingsOffset get query;
}

class _ListingSuggestionAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<ListingSuggestion>>
    with ListingSuggestionAtIndexRef {
  _ListingSuggestionAtIndexProviderElement(super.provider);

  @override
  AllListingsOffset get query =>
      (origin as ListingSuggestionAtIndexProvider).query;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
