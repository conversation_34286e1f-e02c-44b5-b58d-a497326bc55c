import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddPhotoDialog extends HookConsumerWidget {
  const AddPhotoDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaStateController =
        ref.watch(mediaStateControllerProvider.notifier);

    Widget adaptiveAction({
      required BuildContext context,
      required VoidCallback onPressed,
      required Widget child,
    }) {
      final theme = Theme.of(context);

      switch (theme.platform) {
        case TargetPlatform.android:
        case TargetPlatform.fuchsia:
        case TargetPlatform.linux:
        case TargetPlatform.windows:
          return TextButton(onPressed: onPressed, child: child);
        case TargetPlatform.iOS:
        case TargetPlatform.macOS:
          return CupertinoDialogAction(onPressed: onPressed, child: child);
      }
    }

    return CupertinoActionSheet(
      actions: [
        CupertinoActionSheetAction(
          onPressed: () async {
            try {
              await mediaStateController.takePhoto();
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            } on PlatformException catch (e) {
              if (context.mounted) {
                if (e.code == 'camera_access_denied') {
                  await showAdaptiveDialog(
                    context: context,
                    builder: (context) => AlertDialog.adaptive(
                      title: const Text('Permission Required'),
                      content: const Text(
                        'Please enable camera access in your device settings to take photos.',
                      ),
                      actions: [
                        adaptiveAction(
                          context: context,
                          onPressed: () => Navigator.pop(context),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                }
              }
            } catch (e) {
              if (context.mounted) {
                await showAdaptiveDialog(
                  context: context,
                  builder: (context) => AlertDialog.adaptive(
                    title: const Text('Error'),
                    content: const Text(
                      'An unexpected error occurred while accessing camera.',
                    ),
                    actions: [
                      adaptiveAction(
                        context: context,
                        onPressed: () => Navigator.pop(context),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
                );
              }
            }
          },
          child: const Text('Take a photo'),
        ),
        CupertinoActionSheetAction(
          onPressed: () async {
            try {
              await mediaStateController.selectPhoto();
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            } on PlatformException catch (e) {
              if (context.mounted) {
                if (e.code == 'photo_access_denied') {
                  await showAdaptiveDialog(
                    context: context,
                    builder: (context) => AlertDialog.adaptive(
                      title: const Text('Permission Required'),
                      content: const Text(
                        'Please enable photo library access in your device settings to select photos.',
                      ),
                      actions: [
                        adaptiveAction(
                          context: context,
                          onPressed: () => Navigator.pop(context),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                }
              }
            } catch (e) {
              if (context.mounted) {
                await showAdaptiveDialog(
                  context: context,
                  builder: (context) => AlertDialog.adaptive(
                    title: const Text('Error'),
                    content: const Text(
                      'An unexpected error occurred while accessing photos.',
                    ),
                    actions: [
                      adaptiveAction(
                        context: context,
                        onPressed: () => Navigator.pop(context),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
                );
              }
            }
          },
          child: const Text('Choose from library'),
        ),
      ],
      cancelButton: CupertinoActionSheetAction(
        onPressed: () {
          Navigator.of(context).pop();
        },
        isDestructiveAction: true,
        child: const Text('Cancel'),
      ),
    );

    // return AlertDialog.adaptive(
    //   actions: [
    //     AdaptiveTextButton(
    //       onPressed: () async {
    //         await mediaStateController.takePhoto();
    //         if (context.mounted) {
    //           Navigator.of(context).pop();
    //         }
    //       },
    //       child: const Text('Take a photo'),
    //     ),
    //     AdaptiveTextButton(
    //       onPressed: () async {
    //         await mediaStateController.selectPhoto();
    //         if (context.mounted) {
    //           Navigator.of(context).pop();
    //         }
    //       },
    //       child: const Text('Choose from library'),
    //     ),
    //   ],
    //   content: IntrinsicHeight(
    //     child: Column(
    //       children: [
    //         SizedBox(
    //           width: double.infinity,
    //           child: TextButton(
    //             onPressed: () async {
    //               await mediaStateController.takePhoto();
    //               if (context.mounted) {
    //                 Navigator.of(context).pop();
    //               }
    //             },
    //             child: Row(
    //               children: [
    //                 Container(
    //                   decoration: const BoxDecoration(
    //                     shape: BoxShape.circle,
    //                     color: CustomColors.primary,
    //                   ),
    //                   padding: const EdgeInsets.all(8),
    //                   child: const Icon(
    //                     CustomIcon.addAPhoto,
    //                     color: Colors.white,
    //                     size: 20,
    //                   ),
    //                 ),
    //                 const SizedBox(width: 16),
    //                 const Text('Take a photo'),
    //               ],
    //             ),
    //           ),
    //         ),
    //         SizedBox(
    //           width: double.infinity,
    //           child: TextButton(
    //             onPressed: () async {
    //               await mediaStateController.selectPhoto();
    //               if (context.mounted) {
    //                 Navigator.of(context).pop();
    //               }
    //             },
    //             child: Row(
    //               children: [
    //                 Container(
    //                   decoration: const BoxDecoration(
    //                     shape: BoxShape.circle,
    //                     color: CustomColors.primary,
    //                   ),
    //                   padding: const EdgeInsets.all(8),
    //                   child: const Icon(
    //                     CustomIcon.image,
    //                     color: Colors.white,
    //                     size: 20,
    //                   ),
    //                 ),
    //                 const SizedBox(width: 16),
    //                 const Text('Choose from library'),
    //               ],
    //             ),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    // );
  }
}
