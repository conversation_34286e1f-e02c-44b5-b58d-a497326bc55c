import 'package:flutter/cupertino.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/listing_media_provider.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddListingPhotoDialog extends HookConsumerWidget {
  const AddListingPhotoDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final listingMediaStateController =
        ref.watch(listingMediaStateControllerProvider.notifier);

    return CupertinoActionSheet(
      actions: [
        CupertinoActionSheetAction(
          onPressed: () async {
            await listingMediaStateController.takePhoto();
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: const Text('Take a photo'),
        ),
        CupertinoActionSheetAction(
          onPressed: () async {
            await listingMediaStateController.selectPhoto();
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: const Text('Choose from library'),
        ),
      ],
      cancelButton: CupertinoActionSheetAction(
        onPressed: () {
          Navigator.of(context).pop();
        },
        isDestructiveAction: true,
        child: const Text('Cancel'),
      ),
    );
  }
}
