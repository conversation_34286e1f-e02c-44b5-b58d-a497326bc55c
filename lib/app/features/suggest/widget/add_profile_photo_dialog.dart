import 'package:flutter/cupertino.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/profile_media_provider.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddProfilePhotoDialog extends HookConsumerWidget {
  const AddProfilePhotoDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileMediaStateController =
        ref.watch(profileMediaStateControllerProvider.notifier);

    return CupertinoActionSheet(
      actions: [
        CupertinoActionSheetAction(
          onPressed: () async {
            await profileMediaStateController.takePhoto();
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: const Text('Take a photo'),
        ),
        CupertinoActionSheetAction(
          onPressed: () async {
            await profileMediaStateController.selectPhoto();
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: const Text('Choose from library'),
        ),
      ],
      cancelButton: CupertinoActionSheetAction(
        onPressed: () {
          Navigator.of(context).pop();
        },
        isDestructiveAction: true,
        child: const Text('Cancel'),
      ),
    );
  }
}
