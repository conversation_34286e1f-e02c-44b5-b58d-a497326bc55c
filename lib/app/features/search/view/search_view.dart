import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/explore/provider/explore_providers.dart';
import 'package:gomama/app/features/explore/provider/search_providers.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/listing/widget/listing_card.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_view.g.dart';

@riverpod
int _searchListingIndex(_SearchListingIndexRef ref) {
  throw UnimplementedError();
}

@riverpod
class _SearchQuery extends _$SearchQuery {
  @override
  String build() {
    return ref.read(markerSelectedProvider)?.name ?? '';
  }

  void set(String value) {
    state = value;
  }
}

class SearchView extends HookConsumerWidget {
  const SearchView({super.key});

  static const routeName = 'search';
  static const routePath = '/search';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final query = ref.watch(_searchQueryProvider);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight: kToolbarHeight +
                  32 +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              minHeight: kToolbarHeight +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              child: Column(
                children: [
                  SizedBox(
                    height: max(
                      mediaQuery(context).viewPadding.top,
                      kTextTabBarHeight,
                    ),
                  ),
                  _Searchbar(query),
                ],
              ),
              automaticallyImplyLeading: false,
            ),
            pinned: true,
          ),
          const _Body(),
        ],
      ),
    );
  }
}

class _Searchbar extends HookConsumerWidget {
  const _Searchbar(this.initialQuery);
  final String initialQuery;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = useTextEditingController(text: initialQuery);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Hero(
        tag: 'searchbar',
        child: SizedBox(
          height: 40,
          child: Material(
            elevation: 4,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            child: TextField(
              autocorrect: false,
              enableSuggestions: false,
              controller: _controller,
              onChanged: (value) {
                ref.read(_searchQueryProvider.notifier).set(value);
              },
              decoration: InputDecoration(
                prefixIcon: const BackButton(
                  style: ButtonStyle(
                    iconSize: WidgetStatePropertyAll(20),
                    iconColor: WidgetStatePropertyAll(CustomColors.primaries),
                  ),
                ),
                suffixIcon: IconButton(
                  visualDensity: VisualDensity.compact,
                  icon: const Icon(
                    CustomIcon.filter,
                    size: 20,
                    color: CustomColors.primaries,
                  ),
                  onPressed: () {
                    showCupertinoModalPopup(
                      context: context,
                      builder: (context) {
                        return const FilterSheet();
                      },
                    );
                  },
                ),
                hintText: 'Search listings',
                hintStyle: textTheme(context)
                    .bodyMedium!
                    .copyWith(color: CustomColors.placeholder),
                contentPadding: const EdgeInsets.fromLTRB(0, 3.5, 0, 0),
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
              autofocus: true,
            ),
          ),
        ),
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..watch(amenityFiltersProvider)
      ..watch(listingSortsProvider);

    final position = ref.watch(currentPositionProvider).requireValue!;
    final listingCount = ref.watch(
      searchListingsCountProvider(
        AllListingsInput(
          lon: position.longitude,
          lat: position.latitude,
          keywords: ref.watch(_searchQueryProvider),
        ),
      ),
    );

    return listingCount.when(
      loading: () => const SliverToBoxAdapter(
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (err, stack) => SliverToBoxAdapter(child: Text('Error $err')),
      data: (listingCount) {
        if (listingCount == 0) {
          return SliverToBoxAdapter(
            child: Center(
              child: Column(
                children: [
                  const SizedBox(height: 64),
                  Image.asset(
                    'assets/images/goma_sad.png',
                    height: 160,
                  ),
                  const Text(
                    "Please try different keywords or amenities selection.\nUse ',' comma to split\nFirst word will be used to match the listing name\ne.g. pod, sentosa\n'pod' will match with listing name\n'sentosa' is pontential keywords associate with it\nkeywords includes location/activities/names/opening such as\ncare, go!mama, breasfeeding, sentosa, 24hr",
                  ),
                ],
              ),
            ),
          );
        }

        return SliverList.separated(
          itemBuilder: (context, index) {
            return ProviderScope(
              overrides: [
                // ignore: scoped_providers_should_specify_dependencies
                _searchListingIndexProvider.overrideWithValue(index),
              ],
              child: const _ListingCard(),
            );
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 16);
          },
          itemCount: listingCount,
        );
      },
    );
  }
}

class _ListingCard extends ConsumerWidget {
  const _ListingCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final position = ref.watch(currentPositionProvider).requireValue!;
    final index = ref.watch(_searchListingIndexProvider);
    final offset = AllListingsOffset(
      offset: index,
      input: AllListingsInput(
        lon: position.longitude,
        lat: position.latitude,
        keywords: ref.watch(_searchQueryProvider),
      ),
    );
    final listing = ref.watch(
      searchListingAtIndexProvider(offset),
    );

    return listing.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listing) {
        return ListingCard(listing);
      },
    );
  }
}
