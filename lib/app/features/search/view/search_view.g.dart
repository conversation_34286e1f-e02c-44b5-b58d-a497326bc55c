// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchListingIndexHash() =>
    r'73af64958894ca68c9c2c5970acea08cf67f6076';

/// See also [_searchListingIndex].
@ProviderFor(_searchListingIndex)
final _searchListingIndexProvider = AutoDisposeProvider<int>.internal(
  _searchListingIndex,
  name: r'_searchListingIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchListingIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _SearchListingIndexRef = AutoDisposeProviderRef<int>;
String _$searchQueryHash() => r'52f36b1648fc6ef8495000a2ab24cb5ca9d23cda';

/// See also [_SearchQuery].
@ProviderFor(_SearchQuery)
final _searchQueryProvider =
    AutoDisposeNotifierProvider<_SearchQuery, String>.internal(
  _SearchQuery.new,
  name: r'_searchQueryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchQueryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchQuery = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
