import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'position_providers.g.dart';

@riverpod
FutureOr<Position?> currentPosition(CurrentPositionRef ref) async {
  final permission = await Permission.locationWhenInUse.request();

  if (permission.isGranted) {
    return await Geolocator.getCurrentPosition();
  }

  return null;
}
