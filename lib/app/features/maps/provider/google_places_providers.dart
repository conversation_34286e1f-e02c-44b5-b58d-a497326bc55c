// ignore_for_file: use_setters_to_change_properties

import 'dart:async';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/utils/debounce_provider.dart';
import 'package:gomama/app/core/utils/region_from_postal.dart';
import 'package:gomama/app/features/maps/model/google_places.dart';
import 'package:gomama/app/features/maps/model/google_places_autocomplete.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

part 'google_places_providers.g.dart';

const pointOfInterestsTypes = [
  'tourist_attraction',
  'museum',
  'art_gallery',
  'landmark',
  'amusement_park',
  'aquarium',
  'zoo',
  'park',
  'natural_feature',
  'point_of_interest',
];

@riverpod
FutureOr<List<GooglePlacePrediction>> getLocation(
  GetLocationRef ref,
  String text,
  List<String> countries,
  String sessionToken,
) async {
  /// NOTE:
  /// cannot debounce with cancelToken if
  /// this provider is not used in UI view

  // final cancelToken = CancelToken();
  // ref.onDispose(cancelToken.cancel);

  // // Debouncing the request
  // await Future<void>.delayed(const Duration(milliseconds: 250));
  // if (cancelToken.isCancelled) {
  //   // throw Exception();
  //   return [];
  // }

  if (text.isEmpty) {
    return [];
  }

  var url =
      'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$text&key=${Environment.googleMapKey}&sessiontoken=$sessionToken';

  for (var i = 0; i < countries.length; i++) {
    final country = countries[i];

    if (i == 0) {
      url = '$url&components=country:$country';
    } else {
      url = '$url|country:$country';
    }
  }

  final response = await Dio().get(url);

  final subscriptionResponse =
      GooglePlacesAutocompleteResponse.fromJson(response.data as Json);

  if (subscriptionResponse.predictions!.isNotEmpty) {
    return subscriptionResponse.predictions!;
  }

  return [];
}

@riverpod
FutureOr<SanitisedGooglePlaceDetail?> getPlaceDetailsFromPlaceId(
  GetPlaceDetailsFromPlaceIdRef ref,
  String placeId,
) async {
  try {
    final url =
        'https://maps.googleapis.com/maps/api/place/details/json?placeid=$placeId&key=${Environment.googleMapKey}';
    final response = await Dio().get(url);

    final placeDetails = GooglePlaceDetails.fromJson(response.data as Json);

    final latitude = double.parse(
      placeDetails.result!.geometry!.location!.lat.toString(),
    );
    final longitude = double.parse(
      placeDetails.result!.geometry!.location!.lng.toString(),
    );

    final address = placeDetails.result?.formattedAddress ??
        placeDetails.result?.adrAddress ??
        '';

    final postalCode = placeDetails.result?.addressComponents
        ?.firstWhereOrNull(
          (element) =>
              element.types?.any((element) => element == 'postal_code') ??
              false,
        )
        ?.shortName;
    Groveman.info('postal code', error: postalCode);

    final region = postalCode?.regionFromPostal();
    Groveman.info('region', error: region);

    return SanitisedGooglePlaceDetail(
      latitude: latitude,
      longitude: longitude,
      address: address,
      region: region,
    );
  } catch (e) {
    rethrow;
  }
}

@riverpod
FutureOr<SanitisedGooglePlaceDetail?> getNearbyPlaceFromCoordinates(
  GetNearbyPlaceFromCoordinatesRef ref,
  double latitude,
  double longitude,
) async {
  // const radius = 100;

  try {
    final types = pointOfInterestsTypes.join('|');
    final url =
        // 'https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=$latitude,$longitude&radius=$radius&type=$types&key=${Environment.googleMapKey}';
        // 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?location=$latitude,$longitude&radius=$radius&type=$types&key=${Environment.googleMapKey}';
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&result_type=$types&key=${Environment.googleMapKey}';

    final response = await Dio().get(url);

    final nearbyPlaces = GoogleNearbyPlaces.fromJson(response.data as Json);
    // pick the first nearby place
    if(nearbyPlaces.results?.isEmpty ?? false){
      return null;
    }
    final googlePlaceResult = nearbyPlaces.results!.first;
    final address = googlePlaceResult.formattedAddress ?? '';
    final postalCode = googlePlaceResult.addressComponents
        ?.firstWhereOrNull(
          (element) =>
              element.types?.any((element) => element == 'postal_code') ??
              false,
        )
        ?.shortName;
    final region = postalCode?.regionFromPostal();

    return SanitisedGooglePlaceDetail(
      latitude: latitude,
      longitude: longitude,
      // name: name,
      address: address,
      region: region,
    );
  } catch (e) {
    rethrow;
  }
}

@riverpod
class GooglePlacesAutocompleteController
    extends _$GooglePlacesAutocompleteController {
  @override
  GooglePlacesAutocompleteState build() {
    final sessionToken = const Uuid().v4();

    return GooglePlacesAutocompleteState(
      sessionToken: sessionToken, // refresh on every on click
      countries: ['SG'],
    );
  }

  Future<void> textChanged(String text) async {
    ref
        .read(debouncerProvider(const Duration(milliseconds: 250)))
        .debounce(() async {
      final predictions = await ref.read(
        getLocationProvider(text, state.countries, state.sessionToken!).future,
      );

      state = state.copyWith(
        allPredictions: predictions,
      );
    });
  }

  void removeOverlay() {
    // reset to new session
    final sessionToken = const Uuid().v4();
    state = state.copyWith(sessionToken: sessionToken);
  }
}
