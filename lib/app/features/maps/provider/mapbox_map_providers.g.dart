// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mapbox_map_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingsGeojsonHash() => r'789c63793c51a240c058f9187810b32de4a06ac5';

/// See also [listingsGeojson].
@ProviderFor(listingsGeojson)
final listingsGeojsonProvider = FutureProvider<String>.internal(
  listingsGeojson,
  name: r'listingsGeojsonProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingsGeojsonHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListingsGeojsonRef = FutureProviderRef<String>;
String _$mapboxMapStateControllerHash() =>
    r'228bbca84ef63a3ae9b82601386edd295926675b';

/// See also [MapboxMapStateController].
@ProviderFor(MapboxMapStateController)
final mapboxMapStateControllerProvider = AutoDisposeNotifierProvider<
    MapboxMapStateController, MapBoxMapState>.internal(
  MapboxMapStateController.new,
  name: r'mapboxMapStateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mapboxMapStateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MapboxMapStateController = AutoDisposeNotifier<MapBoxMapState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
