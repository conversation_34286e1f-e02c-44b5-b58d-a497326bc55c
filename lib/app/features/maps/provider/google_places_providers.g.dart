// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_places_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$getLocationHash() => r'c8bb96b8439df19344db60e63f23dc34b412a90b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getLocation].
@ProviderFor(getLocation)
const getLocationProvider = GetLocationFamily();

/// See also [getLocation].
class GetLocationFamily
    extends Family<AsyncValue<List<GooglePlacePrediction>>> {
  /// See also [getLocation].
  const GetLocationFamily();

  /// See also [getLocation].
  GetLocationProvider call(
    String text,
    List<String> countries,
    String sessionToken,
  ) {
    return GetLocationProvider(
      text,
      countries,
      sessionToken,
    );
  }

  @override
  GetLocationProvider getProviderOverride(
    covariant GetLocationProvider provider,
  ) {
    return call(
      provider.text,
      provider.countries,
      provider.sessionToken,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getLocationProvider';
}

/// See also [getLocation].
class GetLocationProvider
    extends AutoDisposeFutureProvider<List<GooglePlacePrediction>> {
  /// See also [getLocation].
  GetLocationProvider(
    String text,
    List<String> countries,
    String sessionToken,
  ) : this._internal(
          (ref) => getLocation(
            ref as GetLocationRef,
            text,
            countries,
            sessionToken,
          ),
          from: getLocationProvider,
          name: r'getLocationProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getLocationHash,
          dependencies: GetLocationFamily._dependencies,
          allTransitiveDependencies:
              GetLocationFamily._allTransitiveDependencies,
          text: text,
          countries: countries,
          sessionToken: sessionToken,
        );

  GetLocationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.text,
    required this.countries,
    required this.sessionToken,
  }) : super.internal();

  final String text;
  final List<String> countries;
  final String sessionToken;

  @override
  Override overrideWith(
    FutureOr<List<GooglePlacePrediction>> Function(GetLocationRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetLocationProvider._internal(
        (ref) => create(ref as GetLocationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        text: text,
        countries: countries,
        sessionToken: sessionToken,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<GooglePlacePrediction>>
      createElement() {
    return _GetLocationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetLocationProvider &&
        other.text == text &&
        other.countries == countries &&
        other.sessionToken == sessionToken;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, text.hashCode);
    hash = _SystemHash.combine(hash, countries.hashCode);
    hash = _SystemHash.combine(hash, sessionToken.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetLocationRef
    on AutoDisposeFutureProviderRef<List<GooglePlacePrediction>> {
  /// The parameter `text` of this provider.
  String get text;

  /// The parameter `countries` of this provider.
  List<String> get countries;

  /// The parameter `sessionToken` of this provider.
  String get sessionToken;
}

class _GetLocationProviderElement
    extends AutoDisposeFutureProviderElement<List<GooglePlacePrediction>>
    with GetLocationRef {
  _GetLocationProviderElement(super.provider);

  @override
  String get text => (origin as GetLocationProvider).text;
  @override
  List<String> get countries => (origin as GetLocationProvider).countries;
  @override
  String get sessionToken => (origin as GetLocationProvider).sessionToken;
}

String _$getPlaceDetailsFromPlaceIdHash() =>
    r'e2cb82827f281b3aa65e7cf6318cb6b80f621a19';

/// See also [getPlaceDetailsFromPlaceId].
@ProviderFor(getPlaceDetailsFromPlaceId)
const getPlaceDetailsFromPlaceIdProvider = GetPlaceDetailsFromPlaceIdFamily();

/// See also [getPlaceDetailsFromPlaceId].
class GetPlaceDetailsFromPlaceIdFamily
    extends Family<AsyncValue<SanitisedGooglePlaceDetail?>> {
  /// See also [getPlaceDetailsFromPlaceId].
  const GetPlaceDetailsFromPlaceIdFamily();

  /// See also [getPlaceDetailsFromPlaceId].
  GetPlaceDetailsFromPlaceIdProvider call(
    String placeId,
  ) {
    return GetPlaceDetailsFromPlaceIdProvider(
      placeId,
    );
  }

  @override
  GetPlaceDetailsFromPlaceIdProvider getProviderOverride(
    covariant GetPlaceDetailsFromPlaceIdProvider provider,
  ) {
    return call(
      provider.placeId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getPlaceDetailsFromPlaceIdProvider';
}

/// See also [getPlaceDetailsFromPlaceId].
class GetPlaceDetailsFromPlaceIdProvider
    extends AutoDisposeFutureProvider<SanitisedGooglePlaceDetail?> {
  /// See also [getPlaceDetailsFromPlaceId].
  GetPlaceDetailsFromPlaceIdProvider(
    String placeId,
  ) : this._internal(
          (ref) => getPlaceDetailsFromPlaceId(
            ref as GetPlaceDetailsFromPlaceIdRef,
            placeId,
          ),
          from: getPlaceDetailsFromPlaceIdProvider,
          name: r'getPlaceDetailsFromPlaceIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getPlaceDetailsFromPlaceIdHash,
          dependencies: GetPlaceDetailsFromPlaceIdFamily._dependencies,
          allTransitiveDependencies:
              GetPlaceDetailsFromPlaceIdFamily._allTransitiveDependencies,
          placeId: placeId,
        );

  GetPlaceDetailsFromPlaceIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.placeId,
  }) : super.internal();

  final String placeId;

  @override
  Override overrideWith(
    FutureOr<SanitisedGooglePlaceDetail?> Function(
            GetPlaceDetailsFromPlaceIdRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetPlaceDetailsFromPlaceIdProvider._internal(
        (ref) => create(ref as GetPlaceDetailsFromPlaceIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        placeId: placeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SanitisedGooglePlaceDetail?>
      createElement() {
    return _GetPlaceDetailsFromPlaceIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetPlaceDetailsFromPlaceIdProvider &&
        other.placeId == placeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, placeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetPlaceDetailsFromPlaceIdRef
    on AutoDisposeFutureProviderRef<SanitisedGooglePlaceDetail?> {
  /// The parameter `placeId` of this provider.
  String get placeId;
}

class _GetPlaceDetailsFromPlaceIdProviderElement
    extends AutoDisposeFutureProviderElement<SanitisedGooglePlaceDetail?>
    with GetPlaceDetailsFromPlaceIdRef {
  _GetPlaceDetailsFromPlaceIdProviderElement(super.provider);

  @override
  String get placeId => (origin as GetPlaceDetailsFromPlaceIdProvider).placeId;
}

String _$getNearbyPlaceFromCoordinatesHash() =>
    r'4792c01bc556afd3fff7a6f1e5e85c937c49013c';

/// See also [getNearbyPlaceFromCoordinates].
@ProviderFor(getNearbyPlaceFromCoordinates)
const getNearbyPlaceFromCoordinatesProvider =
    GetNearbyPlaceFromCoordinatesFamily();

/// See also [getNearbyPlaceFromCoordinates].
class GetNearbyPlaceFromCoordinatesFamily
    extends Family<AsyncValue<SanitisedGooglePlaceDetail?>> {
  /// See also [getNearbyPlaceFromCoordinates].
  const GetNearbyPlaceFromCoordinatesFamily();

  /// See also [getNearbyPlaceFromCoordinates].
  GetNearbyPlaceFromCoordinatesProvider call(
    double latitude,
    double longitude,
  ) {
    return GetNearbyPlaceFromCoordinatesProvider(
      latitude,
      longitude,
    );
  }

  @override
  GetNearbyPlaceFromCoordinatesProvider getProviderOverride(
    covariant GetNearbyPlaceFromCoordinatesProvider provider,
  ) {
    return call(
      provider.latitude,
      provider.longitude,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getNearbyPlaceFromCoordinatesProvider';
}

/// See also [getNearbyPlaceFromCoordinates].
class GetNearbyPlaceFromCoordinatesProvider
    extends AutoDisposeFutureProvider<SanitisedGooglePlaceDetail?> {
  /// See also [getNearbyPlaceFromCoordinates].
  GetNearbyPlaceFromCoordinatesProvider(
    double latitude,
    double longitude,
  ) : this._internal(
          (ref) => getNearbyPlaceFromCoordinates(
            ref as GetNearbyPlaceFromCoordinatesRef,
            latitude,
            longitude,
          ),
          from: getNearbyPlaceFromCoordinatesProvider,
          name: r'getNearbyPlaceFromCoordinatesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getNearbyPlaceFromCoordinatesHash,
          dependencies: GetNearbyPlaceFromCoordinatesFamily._dependencies,
          allTransitiveDependencies:
              GetNearbyPlaceFromCoordinatesFamily._allTransitiveDependencies,
          latitude: latitude,
          longitude: longitude,
        );

  GetNearbyPlaceFromCoordinatesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.latitude,
    required this.longitude,
  }) : super.internal();

  final double latitude;
  final double longitude;

  @override
  Override overrideWith(
    FutureOr<SanitisedGooglePlaceDetail?> Function(
            GetNearbyPlaceFromCoordinatesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetNearbyPlaceFromCoordinatesProvider._internal(
        (ref) => create(ref as GetNearbyPlaceFromCoordinatesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        latitude: latitude,
        longitude: longitude,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SanitisedGooglePlaceDetail?>
      createElement() {
    return _GetNearbyPlaceFromCoordinatesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetNearbyPlaceFromCoordinatesProvider &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, latitude.hashCode);
    hash = _SystemHash.combine(hash, longitude.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin GetNearbyPlaceFromCoordinatesRef
    on AutoDisposeFutureProviderRef<SanitisedGooglePlaceDetail?> {
  /// The parameter `latitude` of this provider.
  double get latitude;

  /// The parameter `longitude` of this provider.
  double get longitude;
}

class _GetNearbyPlaceFromCoordinatesProviderElement
    extends AutoDisposeFutureProviderElement<SanitisedGooglePlaceDetail?>
    with GetNearbyPlaceFromCoordinatesRef {
  _GetNearbyPlaceFromCoordinatesProviderElement(super.provider);

  @override
  double get latitude =>
      (origin as GetNearbyPlaceFromCoordinatesProvider).latitude;
  @override
  double get longitude =>
      (origin as GetNearbyPlaceFromCoordinatesProvider).longitude;
}

String _$googlePlacesAutocompleteControllerHash() =>
    r'fb6c289ffaab1418ffb26132a71276e4c29f9fee';

/// See also [GooglePlacesAutocompleteController].
@ProviderFor(GooglePlacesAutocompleteController)
final googlePlacesAutocompleteControllerProvider = AutoDisposeNotifierProvider<
    GooglePlacesAutocompleteController, GooglePlacesAutocompleteState>.internal(
  GooglePlacesAutocompleteController.new,
  name: r'googlePlacesAutocompleteControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$googlePlacesAutocompleteControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GooglePlacesAutocompleteController
    = AutoDisposeNotifier<GooglePlacesAutocompleteState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
