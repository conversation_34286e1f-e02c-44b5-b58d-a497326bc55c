import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/extensions.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/explore/provider/explore_providers.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/maps/model/map_input.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

/// NOTE: sample code to enable calling child function from parent
// typedef ExploreMapBuilder = void Function(
//   BuildContext context,
//   void Function() onResetPosition,
// );

// (in meters)
const thresholdDistance = 10.0;

class AnnotationClickListener extends OnPointAnnotationClickListener {
  AnnotationClickListener({
    required this.onTap,
  });

  final void Function(String value) onTap;
  Timer? _debounce;

  @override
  void onPointAnnotationClick(PointAnnotation annotation) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }
    Groveman.info('onPointAnnotationClick debounced', error: annotation.id);

    _debounce = Timer(const Duration(milliseconds: 20), () {
      onTap.call(annotation.id);
    });
  }
}

class ExploreMap extends ConsumerStatefulWidget {
  const ExploreMap(
    this.latitude,
    this.longitude, {
    required this.geojson,
    super.key,
  });
  final double latitude, longitude;
  final String geojson;

  @override
  ConsumerState createState() => ExploreMapState();
}

class ExploreMapState extends ConsumerState<ExploreMap> {
  MapboxMap? mapboxMap;
  List<String> addedPoints = [];
  List<PointAnnotation> annotations = [];
  Map<String, Listing> annotationGroupMap = {};
  PointAnnotationManager? pointAnnotationManager;
  Timer? debounce;
  late Uint8List podImage, careImage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    debounce?.cancel();
    super.dispose();
  }

  Future<void> _onMapCreated(MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;

    await mapboxMap.compass.updateSettings(CompassSettings(enabled: false));
    await mapboxMap.location.updateSettings(
      LocationComponentSettings(
        enabled: true,
        puckBearingEnabled: true,
        showAccuracyRing: true,
        puckBearing: PuckBearing.HEADING,
        pulsingEnabled: false,
      ),
    );
    await mapboxMap.logo.updateSettings(
      LogoSettings(
        marginLeft: 4,
        marginBottom: kBottomNavigationBarHeight + 28,
      ),
    );
    await mapboxMap.attribution.updateSettings(
      AttributionSettings(
        marginRight: 4,
        marginBottom: kBottomNavigationBarHeight + 28,
      ),
    );
    await mapboxMap.scaleBar.updateSettings(ScaleBarSettings(enabled: false));

    await _addIconImage();
    await _addSource();
    await _addLayers();

    /// NOTE: need to add twice, sometimes mapbox failed to load on first try
    Future.delayed(const Duration(seconds: 1), () async {
      await _addSource();
      await _addLayers();
    });

    ref
      // listen to selected marker
      ..listenManual(markerSelectedProvider, (previous, next) async {
        // Update unclustered icon size
        final iconSizeExpression = [
          'case',
          [
            '==',
            ['get', 'id'],
            ['literal', next?.id ?? ''],
          ],
          1.4,
          1.0,
        ];

        await mapboxMap.style.setStyleLayerProperty(
          'unclustered-point',
          'icon-size',
          jsonEncode(iconSizeExpression),
        );

        // repaint map so the icon size are updated
        await mapboxMap.triggerRepaint();
      })
      // listen to listing types after map is ready
      ..listenManual(
        listingTypeFiltersProvider,
        (_, next) async {
          List<dynamic> unclusteredFilter;
          List<dynamic> clusterCountField;
          List<dynamic> clusterFilter;

          if (next.contains('gomama')) {
            // Show only gomama points
            clusterCountField = ['get', 'gomama_count'];
            // Show only gomama points and clusters with gomama_count > 0
            clusterFilter = [
              'all',
              ['has', 'point_count'],
              [
                '>',
                ['get', 'gomama_count'],
                0,
              ]
            ];

            // Show type 'gomama' unclustered points
            unclusteredFilter = [
              'all',
              [
                '!',
                ['has', 'point_count'],
              ],
              [
                '==',
                ['get', 'type'],
                'gomama',
              ]
            ];
          } else if (next.contains('care')) {
            // Show only care points
            clusterCountField = ['get', 'care_count'];
            // Show only care points and clusters with care_count > 0
            clusterFilter = [
              'all',
              ['has', 'point_count'],
              [
                '>',
                ['get', 'care_count'],
                0,
              ]
            ];

            // Show type 'care' unclustered points
            unclusteredFilter = [
              'all',
              [
                '!',
                ['has', 'point_count'],
              ],
              [
                '==',
                ['get', 'type'],
                'care',
              ]
            ];
          } else {
            // Show all points
            clusterCountField = ['get', 'point_count_abbreviated'];
            clusterFilter = ['has', 'point_count'];
            unclusteredFilter = [
              '!',
              ['has', 'point_count'],
            ];
          }
          // Update clusters layer filter
          await mapboxMap.style.setStyleLayerProperty(
            'clusters',
            'filter',
            jsonEncode(clusterFilter),
          );

          // Update cluster count layer filter and text
          await mapboxMap.style.setStyleLayerProperty(
            'cluster-count',
            'filter',
            jsonEncode(clusterFilter),
          );

          // Update the text field for cluster count
          await mapboxMap.style.setStyleLayerProperty(
            'cluster-count',
            'text-field',
            jsonEncode(clusterCountField),
          );

          // Update unclustered points layer
          await mapboxMap.style.setStyleLayerProperty(
            'unclustered-point',
            'filter',
            jsonEncode(unclusteredFilter),
          );
        },
      );
  }

  Future<void> _onTap(MapContentGestureContext context) async {
    final nearestListing = await _findNearestMarker(context.touchPosition);

    if (nearestListing != null) {
      // set marker
      ref.read(markerSelectedProvider.notifier).setMarker(nearestListing);

      // center camera to the marker
      if (nearestListing.position != null) {
        final listingCoordinateValue = nearestListing.position?.coordinate;
        final listingCoordinate = Position(
          listingCoordinateValue?.x ?? widget.longitude,
          listingCoordinateValue?.y ?? widget.latitude,
        );

        await mapboxMap?.flyTo(
          CameraOptions(
            center: Point(
              coordinates: listingCoordinate,
            ),
            padding: MbxEdgeInsets(
              top: 0,
              left: 0,
              bottom: 300,
              right: 0,
            ),
          ),
          MapAnimationOptions(),
        );
      }
    } else {
      ref.read(markerSelectedProvider.notifier).setMarker(null);
    }
  }

  Future<void> _addLayers() async {
    await mapboxMap?.style.styleLayerExists('clusters').then((value) async {
      if (!value) {
        await _addClusteredLayer();
        await _addClusterCount();
        await _addUnclusteredLayer();
      }
    });
  }

  Future<void> _addSource() async {
    await mapboxMap?.style.styleSourceExists('listings').then((value) async {
      if (!value) {
        await mapboxMap?.style.addSource(
          GeoJsonSource(
            id: 'listings',
            data: widget.geojson,
            cluster: true,
            clusterMaxZoom: 16,
            clusterRadius: 30,
            clusterProperties: {
              'gomama_count': [
                '+',
                [
                  'case',
                  [
                    '==',
                    ['get', 'type'],
                    'gomama',
                  ],
                  1,
                  0,
                ]
              ],
              'care_count': [
                '+',
                [
                  'case',
                  [
                    '==',
                    ['get', 'type'],
                    'care',
                  ],
                  1,
                  0,
                ]
              ],
            },
          ),
        );
      }
    });
  }

  Future<void> _addIconImage() async {
    late Size purpleSize, blueSize;

    await rootBundle.load('assets/maps/purple_marker.png').then((bytes) async {
      podImage = bytes.buffer.asUint8List();

      final codec = await ui.instantiateImageCodec(bytes.buffer.asUint8List());
      final fi = await codec.getNextFrame();

      purpleSize = Size(
        width: fi.image.width.toDouble(),
        height: fi.image.height.toDouble(),
      );
    });

    await rootBundle.load('assets/maps/blue_marker.png').then((bytes) async {
      careImage = bytes.buffer.asUint8List();

      final codec = await ui.instantiateImageCodec(bytes.buffer.asUint8List());
      final fi = await codec.getNextFrame();

      blueSize = Size(
        width: fi.image.width.toDouble(),
        height: fi.image.height.toDouble(),
      );
    });

    await mapboxMap?.style.addStyleImage(
      'purple_marker',
      1,
      MbxImage(
        width: purpleSize.width.toInt(),
        height: purpleSize.height.toInt(),
        data: podImage,
      ),
      false,
      [],
      [],
      null,
    );

    await mapboxMap?.style.addStyleImage(
      'blue_marker',
      1,
      MbxImage(
        width: blueSize.width.toInt(),
        height: blueSize.height.toInt(),
        data: careImage,
      ),
      false,
      [],
      [],
      null,
    );
  }

  Future<void> _addClusteredLayer() async {
    // Use step expressions (https://docs.mapbox.com/mapbox-gl-js/style-spec/#expressions-step)
    // with three steps to implement three types of circles:
    //   * Blue, 20px circles when point count is less than 100
    //   * Yellow, 30px circles when point count is between 100 and 750
    //   * Pink, 40px circles when point count is greater than or equal to 750
    await mapboxMap?.style.addLayer(
      CircleLayer(
        id: 'clusters',
        sourceId: 'listings',
        filter: ['has', 'point_count'],
        // circleColorExpression: [
        //   'case',
        //   [
        //     '>',
        //     ['get', 'gomama_count'],
        //     0,
        //   ],
        //   [
        //     'case',
        //     [
        //       '>',
        //       ['get', 'care_count'],
        //       0,
        //     ],
        //     '#EB4335', // mixed clusters
        //     '#4b0668', // gomama-only clusters
        //   ],
        //   '#51bbd6', //care-only clusters
        // ],
        circleColorExpression: [
          'step',
          ['get', 'point_count'],
          '#FFB6C1',
          100,
          '#F1F075',
        ],
        circleRadiusExpression: [
          'step',
          ['get', 'point_count'],
          20,
          100,
          30,
          750,
          40,
        ],
      ),
    );
  }

  Future<void> _addClusterCount() async {
    await mapboxMap?.style.addLayer(
      SymbolLayer(
        id: 'cluster-count',
        sourceId: 'listings',
        filter: ['has', 'point_count'],
        textField: '{point_count_abbreviated}',
        textFont: ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
        textSize: 12,
        textAllowOverlap: true,
        iconAllowOverlap: true,
        textColor: 0xffffffff,
      ),
    );
  }

  Future<void> _addUnclusteredLayer() async {
    // load unclustered layer
    await mapboxMap?.style.addLayer(
      SymbolLayer(
        id: 'unclustered-point',
        sourceId: 'listings',
        filter: [
          '!',
          ['has', 'point_count'],
        ],
        iconImageExpression: [
          'case',
          [
            '==',
            ['get', 'type'],
            'care',
          ],
          'blue_marker',
          'purple_marker',
        ],
        iconSize: 1,
        iconAllowOverlap: true,
        textAllowOverlap: true,
      ),
    );
  }

  Future<void> _onResetPosition() async {
    await mapboxMap?.flyTo(
      CameraOptions(
        center: Point(
          coordinates: Position(
            widget.longitude,
            widget.latitude,
          ),
        ),
        zoom: 14,
      ),
      MapAnimationOptions(),
    );
  }

  Future<void> _onPinGomamaPods() async {
    // estimated center point of singapore
    final singaporeCenter = Position(
      103.8198,
      1.3521,
    );

    // zoom out from the center of singapore
    await mapboxMap?.flyTo(
      CameraOptions(
        center: Point(coordinates: singaporeCenter),
        zoom: 10,
      ),
      MapAnimationOptions(),
    );

    // set filter to gomama
    ref.read(listingTypeFiltersProvider.notifier).setList(['gomama']);
  }

  void _onCameraMoved(CameraChangedEventData cameraChangedEventData) {
    // Debounce the camera movement to update position
    if (debounce?.isActive ?? false) {
      debounce?.cancel();
    }

    debounce = Timer(const Duration(milliseconds: 300), () async {
      final camera = await mapboxMap!.getCameraState();
      final zoom = camera.zoom;

      ref.read(exploreMapPositionProvider.notifier).setPosition(
            MapInput(
              latitude: camera.center.coordinates.lat as double,
              longitude: camera.center.coordinates.lng as double,
              zoom: zoom,
            ),
          );
    });
  }

  Future<Listing?> _findNearestMarker(ScreenCoordinate touchPosition) async {
    final features = await mapboxMap?.queryRenderedFeatures(
      RenderedQueryGeometry.fromScreenCoordinate(touchPosition),
      RenderedQueryOptions(
        layerIds: [
          'unclustered-point',
          'clusters', // to detect cluster marker tap as well
        ],
      ),
    );

    Groveman.debug('marker selected', error: features);

    if (features != null && features.isNotEmpty) {
      final feature = features.first;

      if (feature?.layers.first == 'unclustered-point') {
        // return listing detail if unclustered point tapped
        final properties = Json.from(
          feature!.queriedFeature.feature['properties']! as Map,
        );

        // final listing =
        //     Listing.fromJson(Map<String, dynamic>.from(properties! as Map));

        final listing = await ref
            .read(singleListingProvider(properties['id'] as String).future);

        return listing;
      } else if (feature?.layers.first == 'clusters') {
        // zoom in on cluster on tapped
        try {
          final cameraState = await mapboxMap?.getCameraState();
          final currentZoom = cameraState?.zoom ?? 14;
          final geometry =
              Json.from(feature!.queriedFeature.feature['geometry']! as Map);
          final coordinates = geometry['coordinates'] as List;
          final longitude = coordinates[0] as double;
          final latitude = coordinates[1] as double;

          await mapboxMap?.flyTo(
            CameraOptions(
              center: Point(
                coordinates: Position(
                  longitude,
                  latitude,
                ),
              ),
              zoom: currentZoom >= 14
                  ? (currentZoom + 2 > 30 ? 20 : currentZoom + 2)
                  : 14,
            ),
            MapAnimationOptions(),
          );
        } catch (e) {
          Groveman.error('error parsing cluster', error: e);
          return null;
        }

        return null;
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        MapWidget(
          key: const ValueKey('mapWidget'),
          mapOptions: MapOptions(
            pixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          cameraOptions: CameraOptions(
            center: Point(
              coordinates: Position(
                widget.longitude,
                widget.latitude,
              ),
            ),
            zoom: 14,
          ),
          gestureRecognizers: Platform.isAndroid
              ? const {
                  Factory<EagerGestureRecognizer>(EagerGestureRecognizer.new),
                }
              : {},
          onMapCreated: _onMapCreated,
          onCameraChangeListener: _onCameraMoved,
          onTapListener: _onTap,
        ),
        Positioned(
          bottom: kBottomNavigationBarHeight + 64,
          right: 16,
          child: FloatingActionButton(
            heroTag: 'reset_position_button',
            onPressed: _onResetPosition,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(100),
              ),
            ),
            backgroundColor: CustomColors.primary,
            child: const Icon(Icons.my_location),
          ),
        ),
        Positioned(
          bottom: kBottomNavigationBarHeight + 132,
          right: 16,
          child: FloatingActionButton(
            heroTag: 'gomama_button',
            onPressed: _onPinGomamaPods,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(100),
              ),
            ),
            backgroundColor: CustomColors.primary,
            child: const Icon(
              CustomIcon.gomLogoOnly,
              size: 24,
            ),
          ),
        ),
      ],
    );
  }
}
