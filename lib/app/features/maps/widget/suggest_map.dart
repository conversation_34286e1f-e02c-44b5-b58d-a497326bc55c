import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gomama/app/features/maps/provider/mapbox_map_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class SuggestMap extends ConsumerStatefulWidget {
  const SuggestMap(this.onUpdateAddress, {super.key});
  final Function onUpdateAddress;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SuggestMapState();
}

class _SuggestMapState extends ConsumerState<SuggestMap>
    with SingleTickerProviderStateMixin {
  MapboxMap? mapboxMap;
  late AnimationController _animationController;
  bool isDragging = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pinCurrentLocation() async {
    // get current location
    final position = ref.read(currentPositionProvider).requireValue;
    if (position != null) {
      // set coordinate
      await ref
          .read(mapboxMapStateControllerProvider.notifier)
          .moveLocationAtMap(position.latitude, position.longitude);
    }
  }

  void _onMapCreated(MapboxMap mapboxMap) {
    this.mapboxMap = mapboxMap;
    ref
        .read(mapboxMapStateControllerProvider.notifier)
        .setController(mapboxMap);

    mapboxMap.compass.updateSettings(CompassSettings(enabled: false));
    mapboxMap.scaleBar.updateSettings(ScaleBarSettings(enabled: false));
  }

  Future<void> _onCameraIdle(event) async {
    setState(() {
      isDragging = false;
    });

    await _animationController.reverse();

    final camera = await mapboxMap!.getCameraState();

    final place =
        await ref.read(mapboxMapStateControllerProvider.notifier).onMoved(
              camera.center.coordinates.lat as double,
              camera.center.coordinates.lng as double,
            );

    if (place != null) {
      widget.onUpdateAddress(place.address);
    }
  }

  void _onCameraMoved(cameraChangedEventData) {
    if (!isDragging) {
      setState(() {
        isDragging = true;
      });

      _animationController.forward();
    }
  }

  // initialize with current location
  void _onMapLoaded(MapLoadedEventData mapLoadedEventData) {
    _pinCurrentLocation();
  }

  @override
  Widget build(BuildContext context) {
    final mapboxMapState = ref.watch(mapboxMapStateControllerProvider);

    return Stack(
      children: [
        MapWidget(
          key: const ValueKey('SuggestMap'),
          mapOptions: MapOptions(
            pixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          cameraOptions: CameraOptions(
            center: Point(
              coordinates: Position(
                mapboxMapState.longitude,
                mapboxMapState.latitude,
              ),
            ),
            zoom: 16,
          ),
          onMapCreated: _onMapCreated,
          onMapIdleListener: _onCameraIdle,
          onCameraChangeListener: _onCameraMoved,
          onMapLoadedListener: _onMapLoaded,
        ),
        if (mapboxMapState.showMarker) ...[
          Center(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _animationController.value,
                  child: Container(
                    width: 8,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              },
            ),
          ),
          Center(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, -24 - 10 * _animationController.value),
                  child: const Icon(
                    Icons.location_pin,
                    color: Colors.red,
                    size: 48,
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}
