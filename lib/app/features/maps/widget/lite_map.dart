import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class LiteMap extends ConsumerStatefulWidget {
  const LiteMap({
    required this.listing,
    super.key,
  });
  final Listing listing;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LiteMapState();
}

class _LiteMapState extends ConsumerState<LiteMap>
    with SingleTickerProviderStateMixin {
  MapboxMap? mapboxMap;
  // PointAnnotation? pointAnnotation;
  PointAnnotationManager? pointAnnotationManager;
  late Uint8List annotationImage;
  late double iconSize;

  @override
  void initState() {
    super.initState();

    var asset = 'assets/maps/purple_marker.png';
    if (widget.listing.listingType != ListingType.gomama) {
      asset = 'assets/maps/blue_marker.png';
    }

    rootBundle.load(asset).then((bytes) async {
      annotationImage = bytes.buffer.asUint8List();
      // get image size
      final codec = await ui.instantiateImageCodec(bytes.buffer.asUint8List());
      final fi = await codec.getNextFrame();

      final imageSize = math.max(fi.image.width, fi.image.height);
      const referenceSize = 96;
      iconSize = referenceSize / imageSize;
    });
  }

  void _onMapCreated(MapboxMap mapboxMap) {
    this.mapboxMap = mapboxMap;

    mapboxMap.gestures.updateSettings(
      GesturesSettings(
        pitchEnabled: false,
        rotateEnabled: false,
        scrollEnabled: false,
        pinchPanEnabled: false,
        quickZoomEnabled: false,
        pinchToZoomEnabled: false,
        doubleTapToZoomInEnabled: false,
        rotateDecelerationEnabled: false,
        scrollDecelerationEnabled: false,
        doubleTouchToZoomOutEnabled: false,
        pinchToZoomDecelerationEnabled: false,
        simultaneousRotateAndPinchToZoomEnabled: false,
      ),
    );
    mapboxMap.compass.updateSettings(CompassSettings(enabled: false));
    mapboxMap.scaleBar.updateSettings(ScaleBarSettings(enabled: false));
    mapboxMap.annotations.createPointAnnotationManager().then((value) async {
      pointAnnotationManager = value;

      final pointAnnotationOption = PointAnnotationOptions(
        geometry: Point(
          coordinates: Position(
            widget.listing.position?.coordinate.x ?? 0,
            widget.listing.position?.coordinate.y ?? 0,
          ),
        ),
        image: annotationImage,
        iconSize: iconSize,
        iconAnchor: IconAnchor.BOTTOM,
      );

      await value.create(pointAnnotationOption);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MapWidget(
      key: const ValueKey('liteMap'),
      mapOptions: MapOptions(
        pixelRatio: MediaQuery.of(context).devicePixelRatio,
      ),
      cameraOptions: CameraOptions(
        center: Point(
          coordinates: Position(
            widget.listing.position?.coordinate.x ?? 0,
            widget.listing.position?.coordinate.y ?? 0,
          ),
        ),
        zoom: 16,
      ),
      onMapCreated: _onMapCreated,
    );
  }
}
