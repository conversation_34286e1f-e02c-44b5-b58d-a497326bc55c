// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'google_places_autocomplete.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GooglePlacesAutocompleteState {
  String? get sessionToken => throw _privateConstructorUsedError;
  List<String> get countries => throw _privateConstructorUsedError;
  List<GooglePlacePrediction> get allPredictions =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $GooglePlacesAutocompleteStateCopyWith<GooglePlacesAutocompleteState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlacesAutocompleteStateCopyWith<$Res> {
  factory $GooglePlacesAutocompleteStateCopyWith(
          GooglePlacesAutocompleteState value,
          $Res Function(GooglePlacesAutocompleteState) then) =
      _$GooglePlacesAutocompleteStateCopyWithImpl<$Res,
          GooglePlacesAutocompleteState>;
  @useResult
  $Res call(
      {String? sessionToken,
      List<String> countries,
      List<GooglePlacePrediction> allPredictions});
}

/// @nodoc
class _$GooglePlacesAutocompleteStateCopyWithImpl<$Res,
        $Val extends GooglePlacesAutocompleteState>
    implements $GooglePlacesAutocompleteStateCopyWith<$Res> {
  _$GooglePlacesAutocompleteStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = freezed,
    Object? countries = null,
    Object? allPredictions = null,
  }) {
    return _then(_value.copyWith(
      sessionToken: freezed == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      countries: null == countries
          ? _value.countries
          : countries // ignore: cast_nullable_to_non_nullable
              as List<String>,
      allPredictions: null == allPredictions
          ? _value.allPredictions
          : allPredictions // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePrediction>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlacesAutocompleteStateImplCopyWith<$Res>
    implements $GooglePlacesAutocompleteStateCopyWith<$Res> {
  factory _$$GooglePlacesAutocompleteStateImplCopyWith(
          _$GooglePlacesAutocompleteStateImpl value,
          $Res Function(_$GooglePlacesAutocompleteStateImpl) then) =
      __$$GooglePlacesAutocompleteStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? sessionToken,
      List<String> countries,
      List<GooglePlacePrediction> allPredictions});
}

/// @nodoc
class __$$GooglePlacesAutocompleteStateImplCopyWithImpl<$Res>
    extends _$GooglePlacesAutocompleteStateCopyWithImpl<$Res,
        _$GooglePlacesAutocompleteStateImpl>
    implements _$$GooglePlacesAutocompleteStateImplCopyWith<$Res> {
  __$$GooglePlacesAutocompleteStateImplCopyWithImpl(
      _$GooglePlacesAutocompleteStateImpl _value,
      $Res Function(_$GooglePlacesAutocompleteStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = freezed,
    Object? countries = null,
    Object? allPredictions = null,
  }) {
    return _then(_$GooglePlacesAutocompleteStateImpl(
      sessionToken: freezed == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      countries: null == countries
          ? _value._countries
          : countries // ignore: cast_nullable_to_non_nullable
              as List<String>,
      allPredictions: null == allPredictions
          ? _value._allPredictions
          : allPredictions // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePrediction>,
    ));
  }
}

/// @nodoc

class _$GooglePlacesAutocompleteStateImpl
    implements _GooglePlacesAutocompleteState {
  _$GooglePlacesAutocompleteStateImpl(
      {this.sessionToken,
      final List<String> countries = const [],
      final List<GooglePlacePrediction> allPredictions = const []})
      : _countries = countries,
        _allPredictions = allPredictions;

  @override
  final String? sessionToken;
  final List<String> _countries;
  @override
  @JsonKey()
  List<String> get countries {
    if (_countries is EqualUnmodifiableListView) return _countries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_countries);
  }

  final List<GooglePlacePrediction> _allPredictions;
  @override
  @JsonKey()
  List<GooglePlacePrediction> get allPredictions {
    if (_allPredictions is EqualUnmodifiableListView) return _allPredictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allPredictions);
  }

  @override
  String toString() {
    return 'GooglePlacesAutocompleteState(sessionToken: $sessionToken, countries: $countries, allPredictions: $allPredictions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlacesAutocompleteStateImpl &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            const DeepCollectionEquality()
                .equals(other._countries, _countries) &&
            const DeepCollectionEquality()
                .equals(other._allPredictions, _allPredictions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionToken,
      const DeepCollectionEquality().hash(_countries),
      const DeepCollectionEquality().hash(_allPredictions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlacesAutocompleteStateImplCopyWith<
          _$GooglePlacesAutocompleteStateImpl>
      get copyWith => __$$GooglePlacesAutocompleteStateImplCopyWithImpl<
          _$GooglePlacesAutocompleteStateImpl>(this, _$identity);
}

abstract class _GooglePlacesAutocompleteState
    implements GooglePlacesAutocompleteState {
  factory _GooglePlacesAutocompleteState(
          {final String? sessionToken,
          final List<String> countries,
          final List<GooglePlacePrediction> allPredictions}) =
      _$GooglePlacesAutocompleteStateImpl;

  @override
  String? get sessionToken;
  @override
  List<String> get countries;
  @override
  List<GooglePlacePrediction> get allPredictions;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlacesAutocompleteStateImplCopyWith<
          _$GooglePlacesAutocompleteStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
