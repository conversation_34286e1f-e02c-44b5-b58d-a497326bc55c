import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'google_places.freezed.dart';
part 'google_places.g.dart';

@freezed
class GooglePlaceDetails with _$GooglePlaceDetails {
  factory GooglePlaceDetails({
    GooglePlaceResult? result,
    String? status,
  }) = _GooglePlaceDetails;

  factory GooglePlaceDetails.fromJson(Json json) =>
      _$GooglePlaceDetailsFromJson(json);
}

@freezed
class GoogleNearbyPlaces with _$GoogleNearbyPlaces {
  factory GoogleNearbyPlaces({
    List<GoogleGeocodeResult>? results,
    String? status,
  }) = _GoogleNearbyPlaces;

  factory GoogleNearbyPlaces.fromJson(Json json) =>
      _$GoogleNearbyPlacesFromJson(json);
}

@freezed
class SanitisedGooglePlaceDetail with _$SanitisedGooglePlaceDetail {
  factory SanitisedGooglePlaceDetail({
    String? region,
    String? name,
    required String address,
    required double latitude,
    required double longitude,
  }) = _SanitisedGooglePlaceDetail;
}

@freezed
class GoogleGeocodeResult with _$GoogleGeocodeResult {
  factory GoogleGeocodeResult({
    List<GooglePlaceAddressComponents>? addressComponents,
    String? formattedAddress,
    GooglePlaceGeometry? geometry,
    String? placeId,
    List<String>? types,
  }) = _GoogleGeocodeResult;

  factory GoogleGeocodeResult.fromJson(Json json) =>
      _$GoogleGeocodeResultFromJson(json);
}

@freezed
class GooglePlaceResult with _$GooglePlaceResult {
  factory GooglePlaceResult({
    List<GooglePlaceAddressComponents>? addressComponents,
    String? adrAddress,
    String? formattedAddress,
    GooglePlaceGeometry? geometry,
    String? icon,
    String? name,
    List<GooglePlacePhotos>? photos,
    String? placeId,
    String? reference,
    String? scope,
    List<String>? types,
    String? url,
    int? utcOffset,
    String? vicinity,
    String? website,
  }) = _GooglePlaceResult;

  factory GooglePlaceResult.fromJson(Json json) =>
      _$GooglePlaceResultFromJson(json);
}

@freezed
class GooglePlaceAddressComponents with _$GooglePlaceAddressComponents {
  factory GooglePlaceAddressComponents({
    String? longName,
    String? shortName,
    List<String>? types,
  }) = _GooglePlaceAddressComponents;

  factory GooglePlaceAddressComponents.fromJson(Json json) =>
      _$GooglePlaceAddressComponentsFromJson(json);
}

@freezed
class GooglePlaceGeometry with _$GooglePlaceGeometry {
  factory GooglePlaceGeometry({
    GooglePlaceLocation? location,
    GooglePlaceViewport? viewport,
  }) = _GooglePlaceGeometry;

  factory GooglePlaceGeometry.fromJson(Json json) =>
      _$GooglePlaceGeometryFromJson(json);
}

@freezed
class GooglePlaceLocation with _$GooglePlaceLocation {
  factory GooglePlaceLocation({
    double? lat,
    double? lng,
  }) = _GooglePlaceLocation;

  factory GooglePlaceLocation.fromJson(Json json) =>
      _$GooglePlaceLocationFromJson(json);
}

@freezed
class GooglePlaceViewport with _$GooglePlaceViewport {
  factory GooglePlaceViewport({
    GooglePlaceLocation? northeast,
    GooglePlaceLocation? southwest,
  }) = _GooglePlaceViewport;

  factory GooglePlaceViewport.fromJson(Json json) =>
      _$GooglePlaceViewportFromJson(json);
}

@freezed
class GooglePlacePhotos with _$GooglePlacePhotos {
  factory GooglePlacePhotos({
    int? height,
    List<String>? htmlAttributions,
    String? photoReference,
    int? width,
  }) = _GooglePlacePhotos;

  factory GooglePlacePhotos.fromJson(Json json) =>
      _$GooglePlacePhotosFromJson(json);
}

@freezed
class GooglePlacesAutocompleteResponse with _$GooglePlacesAutocompleteResponse {
  factory GooglePlacesAutocompleteResponse({
    List<GooglePlacePrediction>? predictions,
    String? status,
  }) = _GooglePlacesAutocompleteResponse;

  factory GooglePlacesAutocompleteResponse.fromJson(Json json) =>
      _$GooglePlacesAutocompleteResponseFromJson(json);
}

@freezed
class GooglePlacePrediction with _$GooglePlacePrediction {
  factory GooglePlacePrediction({
    String? description,
    String? id,
    List<GooglePlaceMatchedSubstrings>? matchedSubstrings,
    String? placeId,
    String? reference,
    GooglePlaceStructuredFormatting? structuredFormatting,
    List<GooglePlaceTerms>? terms,
    List<String>? types,
    String? lat,
    String? lng,
  }) = _GooglePlacePrediction;

  factory GooglePlacePrediction.fromJson(Json json) =>
      _$GooglePlacePredictionFromJson(json);
}

@freezed
class GooglePlaceMatchedSubstrings with _$GooglePlaceMatchedSubstrings {
  factory GooglePlaceMatchedSubstrings({
    int? length,
    int? offset,
  }) = GooglePlace_MatchedSubstrings;

  factory GooglePlaceMatchedSubstrings.fromJson(Json json) =>
      _$GooglePlaceMatchedSubstringsFromJson(json);
}

@freezed
class GooglePlaceStructuredFormatting with _$GooglePlaceStructuredFormatting {
  factory GooglePlaceStructuredFormatting({
    String? mainText,
    String? secondaryText,
  }) = GooglePlace_StructuredFormatting;

  factory GooglePlaceStructuredFormatting.fromJson(Json json) =>
      _$GooglePlaceStructuredFormattingFromJson(json);
}

@freezed
class GooglePlaceTerms with _$GooglePlaceTerms {
  factory GooglePlaceTerms({
    int? offset,
    String? value,
  }) = GooglePlace_Terms;

  factory GooglePlaceTerms.fromJson(Json json) =>
      _$GooglePlaceTermsFromJson(json);
}
