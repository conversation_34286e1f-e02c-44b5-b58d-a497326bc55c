// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'google_places.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GooglePlaceDetails _$GooglePlaceDetailsFromJson(Map<String, dynamic> json) {
  return _GooglePlaceDetails.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceDetails {
  GooglePlaceResult? get result => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceDetailsCopyWith<GooglePlaceDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceDetailsCopyWith<$Res> {
  factory $GooglePlaceDetailsCopyWith(
          GooglePlaceDetails value, $Res Function(GooglePlaceDetails) then) =
      _$GooglePlaceDetailsCopyWithImpl<$Res, GooglePlaceDetails>;
  @useResult
  $Res call({GooglePlaceResult? result, String? status});

  $GooglePlaceResultCopyWith<$Res>? get result;
}

/// @nodoc
class _$GooglePlaceDetailsCopyWithImpl<$Res, $Val extends GooglePlaceDetails>
    implements $GooglePlaceDetailsCopyWith<$Res> {
  _$GooglePlaceDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? result = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      result: freezed == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as GooglePlaceResult?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceResultCopyWith<$Res>? get result {
    if (_value.result == null) {
      return null;
    }

    return $GooglePlaceResultCopyWith<$Res>(_value.result!, (value) {
      return _then(_value.copyWith(result: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GooglePlaceDetailsImplCopyWith<$Res>
    implements $GooglePlaceDetailsCopyWith<$Res> {
  factory _$$GooglePlaceDetailsImplCopyWith(_$GooglePlaceDetailsImpl value,
          $Res Function(_$GooglePlaceDetailsImpl) then) =
      __$$GooglePlaceDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({GooglePlaceResult? result, String? status});

  @override
  $GooglePlaceResultCopyWith<$Res>? get result;
}

/// @nodoc
class __$$GooglePlaceDetailsImplCopyWithImpl<$Res>
    extends _$GooglePlaceDetailsCopyWithImpl<$Res, _$GooglePlaceDetailsImpl>
    implements _$$GooglePlaceDetailsImplCopyWith<$Res> {
  __$$GooglePlaceDetailsImplCopyWithImpl(_$GooglePlaceDetailsImpl _value,
      $Res Function(_$GooglePlaceDetailsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? result = freezed,
    Object? status = freezed,
  }) {
    return _then(_$GooglePlaceDetailsImpl(
      result: freezed == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as GooglePlaceResult?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceDetailsImpl implements _GooglePlaceDetails {
  _$GooglePlaceDetailsImpl({this.result, this.status});

  factory _$GooglePlaceDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlaceDetailsImplFromJson(json);

  @override
  final GooglePlaceResult? result;
  @override
  final String? status;

  @override
  String toString() {
    return 'GooglePlaceDetails(result: $result, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceDetailsImpl &&
            (identical(other.result, result) || other.result == result) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, result, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceDetailsImplCopyWith<_$GooglePlaceDetailsImpl> get copyWith =>
      __$$GooglePlaceDetailsImplCopyWithImpl<_$GooglePlaceDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceDetailsImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceDetails implements GooglePlaceDetails {
  factory _GooglePlaceDetails(
      {final GooglePlaceResult? result,
      final String? status}) = _$GooglePlaceDetailsImpl;

  factory _GooglePlaceDetails.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceDetailsImpl.fromJson;

  @override
  GooglePlaceResult? get result;
  @override
  String? get status;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceDetailsImplCopyWith<_$GooglePlaceDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GoogleNearbyPlaces _$GoogleNearbyPlacesFromJson(Map<String, dynamic> json) {
  return _GoogleNearbyPlaces.fromJson(json);
}

/// @nodoc
mixin _$GoogleNearbyPlaces {
  List<GoogleGeocodeResult>? get results => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GoogleNearbyPlacesCopyWith<GoogleNearbyPlaces> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoogleNearbyPlacesCopyWith<$Res> {
  factory $GoogleNearbyPlacesCopyWith(
          GoogleNearbyPlaces value, $Res Function(GoogleNearbyPlaces) then) =
      _$GoogleNearbyPlacesCopyWithImpl<$Res, GoogleNearbyPlaces>;
  @useResult
  $Res call({List<GoogleGeocodeResult>? results, String? status});
}

/// @nodoc
class _$GoogleNearbyPlacesCopyWithImpl<$Res, $Val extends GoogleNearbyPlaces>
    implements $GoogleNearbyPlacesCopyWith<$Res> {
  _$GoogleNearbyPlacesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      results: freezed == results
          ? _value.results
          : results // ignore: cast_nullable_to_non_nullable
              as List<GoogleGeocodeResult>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoogleNearbyPlacesImplCopyWith<$Res>
    implements $GoogleNearbyPlacesCopyWith<$Res> {
  factory _$$GoogleNearbyPlacesImplCopyWith(_$GoogleNearbyPlacesImpl value,
          $Res Function(_$GoogleNearbyPlacesImpl) then) =
      __$$GoogleNearbyPlacesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<GoogleGeocodeResult>? results, String? status});
}

/// @nodoc
class __$$GoogleNearbyPlacesImplCopyWithImpl<$Res>
    extends _$GoogleNearbyPlacesCopyWithImpl<$Res, _$GoogleNearbyPlacesImpl>
    implements _$$GoogleNearbyPlacesImplCopyWith<$Res> {
  __$$GoogleNearbyPlacesImplCopyWithImpl(_$GoogleNearbyPlacesImpl _value,
      $Res Function(_$GoogleNearbyPlacesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = freezed,
    Object? status = freezed,
  }) {
    return _then(_$GoogleNearbyPlacesImpl(
      results: freezed == results
          ? _value._results
          : results // ignore: cast_nullable_to_non_nullable
              as List<GoogleGeocodeResult>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoogleNearbyPlacesImpl implements _GoogleNearbyPlaces {
  _$GoogleNearbyPlacesImpl(
      {final List<GoogleGeocodeResult>? results, this.status})
      : _results = results;

  factory _$GoogleNearbyPlacesImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoogleNearbyPlacesImplFromJson(json);

  final List<GoogleGeocodeResult>? _results;
  @override
  List<GoogleGeocodeResult>? get results {
    final value = _results;
    if (value == null) return null;
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? status;

  @override
  String toString() {
    return 'GoogleNearbyPlaces(results: $results, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoogleNearbyPlacesImpl &&
            const DeepCollectionEquality().equals(other._results, _results) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_results), status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GoogleNearbyPlacesImplCopyWith<_$GoogleNearbyPlacesImpl> get copyWith =>
      __$$GoogleNearbyPlacesImplCopyWithImpl<_$GoogleNearbyPlacesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoogleNearbyPlacesImplToJson(
      this,
    );
  }
}

abstract class _GoogleNearbyPlaces implements GoogleNearbyPlaces {
  factory _GoogleNearbyPlaces(
      {final List<GoogleGeocodeResult>? results,
      final String? status}) = _$GoogleNearbyPlacesImpl;

  factory _GoogleNearbyPlaces.fromJson(Map<String, dynamic> json) =
      _$GoogleNearbyPlacesImpl.fromJson;

  @override
  List<GoogleGeocodeResult>? get results;
  @override
  String? get status;
  @override
  @JsonKey(ignore: true)
  _$$GoogleNearbyPlacesImplCopyWith<_$GoogleNearbyPlacesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SanitisedGooglePlaceDetail {
  String? get region => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SanitisedGooglePlaceDetailCopyWith<SanitisedGooglePlaceDetail>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SanitisedGooglePlaceDetailCopyWith<$Res> {
  factory $SanitisedGooglePlaceDetailCopyWith(SanitisedGooglePlaceDetail value,
          $Res Function(SanitisedGooglePlaceDetail) then) =
      _$SanitisedGooglePlaceDetailCopyWithImpl<$Res,
          SanitisedGooglePlaceDetail>;
  @useResult
  $Res call(
      {String? region,
      String? name,
      String address,
      double latitude,
      double longitude});
}

/// @nodoc
class _$SanitisedGooglePlaceDetailCopyWithImpl<$Res,
        $Val extends SanitisedGooglePlaceDetail>
    implements $SanitisedGooglePlaceDetailCopyWith<$Res> {
  _$SanitisedGooglePlaceDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = freezed,
    Object? name = freezed,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_value.copyWith(
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SanitisedGooglePlaceDetailImplCopyWith<$Res>
    implements $SanitisedGooglePlaceDetailCopyWith<$Res> {
  factory _$$SanitisedGooglePlaceDetailImplCopyWith(
          _$SanitisedGooglePlaceDetailImpl value,
          $Res Function(_$SanitisedGooglePlaceDetailImpl) then) =
      __$$SanitisedGooglePlaceDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? region,
      String? name,
      String address,
      double latitude,
      double longitude});
}

/// @nodoc
class __$$SanitisedGooglePlaceDetailImplCopyWithImpl<$Res>
    extends _$SanitisedGooglePlaceDetailCopyWithImpl<$Res,
        _$SanitisedGooglePlaceDetailImpl>
    implements _$$SanitisedGooglePlaceDetailImplCopyWith<$Res> {
  __$$SanitisedGooglePlaceDetailImplCopyWithImpl(
      _$SanitisedGooglePlaceDetailImpl _value,
      $Res Function(_$SanitisedGooglePlaceDetailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? region = freezed,
    Object? name = freezed,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$SanitisedGooglePlaceDetailImpl(
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$SanitisedGooglePlaceDetailImpl implements _SanitisedGooglePlaceDetail {
  _$SanitisedGooglePlaceDetailImpl(
      {this.region,
      this.name,
      required this.address,
      required this.latitude,
      required this.longitude});

  @override
  final String? region;
  @override
  final String? name;
  @override
  final String address;
  @override
  final double latitude;
  @override
  final double longitude;

  @override
  String toString() {
    return 'SanitisedGooglePlaceDetail(region: $region, name: $name, address: $address, latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SanitisedGooglePlaceDetailImpl &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, region, name, address, latitude, longitude);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SanitisedGooglePlaceDetailImplCopyWith<_$SanitisedGooglePlaceDetailImpl>
      get copyWith => __$$SanitisedGooglePlaceDetailImplCopyWithImpl<
          _$SanitisedGooglePlaceDetailImpl>(this, _$identity);
}

abstract class _SanitisedGooglePlaceDetail
    implements SanitisedGooglePlaceDetail {
  factory _SanitisedGooglePlaceDetail(
      {final String? region,
      final String? name,
      required final String address,
      required final double latitude,
      required final double longitude}) = _$SanitisedGooglePlaceDetailImpl;

  @override
  String? get region;
  @override
  String? get name;
  @override
  String get address;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  @JsonKey(ignore: true)
  _$$SanitisedGooglePlaceDetailImplCopyWith<_$SanitisedGooglePlaceDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GoogleGeocodeResult _$GoogleGeocodeResultFromJson(Map<String, dynamic> json) {
  return _GoogleGeocodeResult.fromJson(json);
}

/// @nodoc
mixin _$GoogleGeocodeResult {
  List<GooglePlaceAddressComponents>? get addressComponents =>
      throw _privateConstructorUsedError;
  String? get formattedAddress => throw _privateConstructorUsedError;
  GooglePlaceGeometry? get geometry => throw _privateConstructorUsedError;
  String? get placeId => throw _privateConstructorUsedError;
  List<String>? get types => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GoogleGeocodeResultCopyWith<GoogleGeocodeResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoogleGeocodeResultCopyWith<$Res> {
  factory $GoogleGeocodeResultCopyWith(
          GoogleGeocodeResult value, $Res Function(GoogleGeocodeResult) then) =
      _$GoogleGeocodeResultCopyWithImpl<$Res, GoogleGeocodeResult>;
  @useResult
  $Res call(
      {List<GooglePlaceAddressComponents>? addressComponents,
      String? formattedAddress,
      GooglePlaceGeometry? geometry,
      String? placeId,
      List<String>? types});

  $GooglePlaceGeometryCopyWith<$Res>? get geometry;
}

/// @nodoc
class _$GoogleGeocodeResultCopyWithImpl<$Res, $Val extends GoogleGeocodeResult>
    implements $GoogleGeocodeResultCopyWith<$Res> {
  _$GoogleGeocodeResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressComponents = freezed,
    Object? formattedAddress = freezed,
    Object? geometry = freezed,
    Object? placeId = freezed,
    Object? types = freezed,
  }) {
    return _then(_value.copyWith(
      addressComponents: freezed == addressComponents
          ? _value.addressComponents
          : addressComponents // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceAddressComponents>?,
      formattedAddress: freezed == formattedAddress
          ? _value.formattedAddress
          : formattedAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      geometry: freezed == geometry
          ? _value.geometry
          : geometry // ignore: cast_nullable_to_non_nullable
              as GooglePlaceGeometry?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceGeometryCopyWith<$Res>? get geometry {
    if (_value.geometry == null) {
      return null;
    }

    return $GooglePlaceGeometryCopyWith<$Res>(_value.geometry!, (value) {
      return _then(_value.copyWith(geometry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GoogleGeocodeResultImplCopyWith<$Res>
    implements $GoogleGeocodeResultCopyWith<$Res> {
  factory _$$GoogleGeocodeResultImplCopyWith(_$GoogleGeocodeResultImpl value,
          $Res Function(_$GoogleGeocodeResultImpl) then) =
      __$$GoogleGeocodeResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<GooglePlaceAddressComponents>? addressComponents,
      String? formattedAddress,
      GooglePlaceGeometry? geometry,
      String? placeId,
      List<String>? types});

  @override
  $GooglePlaceGeometryCopyWith<$Res>? get geometry;
}

/// @nodoc
class __$$GoogleGeocodeResultImplCopyWithImpl<$Res>
    extends _$GoogleGeocodeResultCopyWithImpl<$Res, _$GoogleGeocodeResultImpl>
    implements _$$GoogleGeocodeResultImplCopyWith<$Res> {
  __$$GoogleGeocodeResultImplCopyWithImpl(_$GoogleGeocodeResultImpl _value,
      $Res Function(_$GoogleGeocodeResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressComponents = freezed,
    Object? formattedAddress = freezed,
    Object? geometry = freezed,
    Object? placeId = freezed,
    Object? types = freezed,
  }) {
    return _then(_$GoogleGeocodeResultImpl(
      addressComponents: freezed == addressComponents
          ? _value._addressComponents
          : addressComponents // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceAddressComponents>?,
      formattedAddress: freezed == formattedAddress
          ? _value.formattedAddress
          : formattedAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      geometry: freezed == geometry
          ? _value.geometry
          : geometry // ignore: cast_nullable_to_non_nullable
              as GooglePlaceGeometry?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoogleGeocodeResultImpl implements _GoogleGeocodeResult {
  _$GoogleGeocodeResultImpl(
      {final List<GooglePlaceAddressComponents>? addressComponents,
      this.formattedAddress,
      this.geometry,
      this.placeId,
      final List<String>? types})
      : _addressComponents = addressComponents,
        _types = types;

  factory _$GoogleGeocodeResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoogleGeocodeResultImplFromJson(json);

  final List<GooglePlaceAddressComponents>? _addressComponents;
  @override
  List<GooglePlaceAddressComponents>? get addressComponents {
    final value = _addressComponents;
    if (value == null) return null;
    if (_addressComponents is EqualUnmodifiableListView)
      return _addressComponents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? formattedAddress;
  @override
  final GooglePlaceGeometry? geometry;
  @override
  final String? placeId;
  final List<String>? _types;
  @override
  List<String>? get types {
    final value = _types;
    if (value == null) return null;
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GoogleGeocodeResult(addressComponents: $addressComponents, formattedAddress: $formattedAddress, geometry: $geometry, placeId: $placeId, types: $types)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoogleGeocodeResultImpl &&
            const DeepCollectionEquality()
                .equals(other._addressComponents, _addressComponents) &&
            (identical(other.formattedAddress, formattedAddress) ||
                other.formattedAddress == formattedAddress) &&
            (identical(other.geometry, geometry) ||
                other.geometry == geometry) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            const DeepCollectionEquality().equals(other._types, _types));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addressComponents),
      formattedAddress,
      geometry,
      placeId,
      const DeepCollectionEquality().hash(_types));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GoogleGeocodeResultImplCopyWith<_$GoogleGeocodeResultImpl> get copyWith =>
      __$$GoogleGeocodeResultImplCopyWithImpl<_$GoogleGeocodeResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoogleGeocodeResultImplToJson(
      this,
    );
  }
}

abstract class _GoogleGeocodeResult implements GoogleGeocodeResult {
  factory _GoogleGeocodeResult(
      {final List<GooglePlaceAddressComponents>? addressComponents,
      final String? formattedAddress,
      final GooglePlaceGeometry? geometry,
      final String? placeId,
      final List<String>? types}) = _$GoogleGeocodeResultImpl;

  factory _GoogleGeocodeResult.fromJson(Map<String, dynamic> json) =
      _$GoogleGeocodeResultImpl.fromJson;

  @override
  List<GooglePlaceAddressComponents>? get addressComponents;
  @override
  String? get formattedAddress;
  @override
  GooglePlaceGeometry? get geometry;
  @override
  String? get placeId;
  @override
  List<String>? get types;
  @override
  @JsonKey(ignore: true)
  _$$GoogleGeocodeResultImplCopyWith<_$GoogleGeocodeResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlaceResult _$GooglePlaceResultFromJson(Map<String, dynamic> json) {
  return _GooglePlaceResult.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceResult {
  List<GooglePlaceAddressComponents>? get addressComponents =>
      throw _privateConstructorUsedError;
  String? get adrAddress => throw _privateConstructorUsedError;
  String? get formattedAddress => throw _privateConstructorUsedError;
  GooglePlaceGeometry? get geometry => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<GooglePlacePhotos>? get photos => throw _privateConstructorUsedError;
  String? get placeId => throw _privateConstructorUsedError;
  String? get reference => throw _privateConstructorUsedError;
  String? get scope => throw _privateConstructorUsedError;
  List<String>? get types => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  int? get utcOffset => throw _privateConstructorUsedError;
  String? get vicinity => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceResultCopyWith<GooglePlaceResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceResultCopyWith<$Res> {
  factory $GooglePlaceResultCopyWith(
          GooglePlaceResult value, $Res Function(GooglePlaceResult) then) =
      _$GooglePlaceResultCopyWithImpl<$Res, GooglePlaceResult>;
  @useResult
  $Res call(
      {List<GooglePlaceAddressComponents>? addressComponents,
      String? adrAddress,
      String? formattedAddress,
      GooglePlaceGeometry? geometry,
      String? icon,
      String? name,
      List<GooglePlacePhotos>? photos,
      String? placeId,
      String? reference,
      String? scope,
      List<String>? types,
      String? url,
      int? utcOffset,
      String? vicinity,
      String? website});

  $GooglePlaceGeometryCopyWith<$Res>? get geometry;
}

/// @nodoc
class _$GooglePlaceResultCopyWithImpl<$Res, $Val extends GooglePlaceResult>
    implements $GooglePlaceResultCopyWith<$Res> {
  _$GooglePlaceResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressComponents = freezed,
    Object? adrAddress = freezed,
    Object? formattedAddress = freezed,
    Object? geometry = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? photos = freezed,
    Object? placeId = freezed,
    Object? reference = freezed,
    Object? scope = freezed,
    Object? types = freezed,
    Object? url = freezed,
    Object? utcOffset = freezed,
    Object? vicinity = freezed,
    Object? website = freezed,
  }) {
    return _then(_value.copyWith(
      addressComponents: freezed == addressComponents
          ? _value.addressComponents
          : addressComponents // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceAddressComponents>?,
      adrAddress: freezed == adrAddress
          ? _value.adrAddress
          : adrAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      formattedAddress: freezed == formattedAddress
          ? _value.formattedAddress
          : formattedAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      geometry: freezed == geometry
          ? _value.geometry
          : geometry // ignore: cast_nullable_to_non_nullable
              as GooglePlaceGeometry?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePhotos>?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      reference: freezed == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      utcOffset: freezed == utcOffset
          ? _value.utcOffset
          : utcOffset // ignore: cast_nullable_to_non_nullable
              as int?,
      vicinity: freezed == vicinity
          ? _value.vicinity
          : vicinity // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceGeometryCopyWith<$Res>? get geometry {
    if (_value.geometry == null) {
      return null;
    }

    return $GooglePlaceGeometryCopyWith<$Res>(_value.geometry!, (value) {
      return _then(_value.copyWith(geometry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GooglePlaceResultImplCopyWith<$Res>
    implements $GooglePlaceResultCopyWith<$Res> {
  factory _$$GooglePlaceResultImplCopyWith(_$GooglePlaceResultImpl value,
          $Res Function(_$GooglePlaceResultImpl) then) =
      __$$GooglePlaceResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<GooglePlaceAddressComponents>? addressComponents,
      String? adrAddress,
      String? formattedAddress,
      GooglePlaceGeometry? geometry,
      String? icon,
      String? name,
      List<GooglePlacePhotos>? photos,
      String? placeId,
      String? reference,
      String? scope,
      List<String>? types,
      String? url,
      int? utcOffset,
      String? vicinity,
      String? website});

  @override
  $GooglePlaceGeometryCopyWith<$Res>? get geometry;
}

/// @nodoc
class __$$GooglePlaceResultImplCopyWithImpl<$Res>
    extends _$GooglePlaceResultCopyWithImpl<$Res, _$GooglePlaceResultImpl>
    implements _$$GooglePlaceResultImplCopyWith<$Res> {
  __$$GooglePlaceResultImplCopyWithImpl(_$GooglePlaceResultImpl _value,
      $Res Function(_$GooglePlaceResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addressComponents = freezed,
    Object? adrAddress = freezed,
    Object? formattedAddress = freezed,
    Object? geometry = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? photos = freezed,
    Object? placeId = freezed,
    Object? reference = freezed,
    Object? scope = freezed,
    Object? types = freezed,
    Object? url = freezed,
    Object? utcOffset = freezed,
    Object? vicinity = freezed,
    Object? website = freezed,
  }) {
    return _then(_$GooglePlaceResultImpl(
      addressComponents: freezed == addressComponents
          ? _value._addressComponents
          : addressComponents // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceAddressComponents>?,
      adrAddress: freezed == adrAddress
          ? _value.adrAddress
          : adrAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      formattedAddress: freezed == formattedAddress
          ? _value.formattedAddress
          : formattedAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      geometry: freezed == geometry
          ? _value.geometry
          : geometry // ignore: cast_nullable_to_non_nullable
              as GooglePlaceGeometry?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePhotos>?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      reference: freezed == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      utcOffset: freezed == utcOffset
          ? _value.utcOffset
          : utcOffset // ignore: cast_nullable_to_non_nullable
              as int?,
      vicinity: freezed == vicinity
          ? _value.vicinity
          : vicinity // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceResultImpl implements _GooglePlaceResult {
  _$GooglePlaceResultImpl(
      {final List<GooglePlaceAddressComponents>? addressComponents,
      this.adrAddress,
      this.formattedAddress,
      this.geometry,
      this.icon,
      this.name,
      final List<GooglePlacePhotos>? photos,
      this.placeId,
      this.reference,
      this.scope,
      final List<String>? types,
      this.url,
      this.utcOffset,
      this.vicinity,
      this.website})
      : _addressComponents = addressComponents,
        _photos = photos,
        _types = types;

  factory _$GooglePlaceResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlaceResultImplFromJson(json);

  final List<GooglePlaceAddressComponents>? _addressComponents;
  @override
  List<GooglePlaceAddressComponents>? get addressComponents {
    final value = _addressComponents;
    if (value == null) return null;
    if (_addressComponents is EqualUnmodifiableListView)
      return _addressComponents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? adrAddress;
  @override
  final String? formattedAddress;
  @override
  final GooglePlaceGeometry? geometry;
  @override
  final String? icon;
  @override
  final String? name;
  final List<GooglePlacePhotos>? _photos;
  @override
  List<GooglePlacePhotos>? get photos {
    final value = _photos;
    if (value == null) return null;
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? placeId;
  @override
  final String? reference;
  @override
  final String? scope;
  final List<String>? _types;
  @override
  List<String>? get types {
    final value = _types;
    if (value == null) return null;
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? url;
  @override
  final int? utcOffset;
  @override
  final String? vicinity;
  @override
  final String? website;

  @override
  String toString() {
    return 'GooglePlaceResult(addressComponents: $addressComponents, adrAddress: $adrAddress, formattedAddress: $formattedAddress, geometry: $geometry, icon: $icon, name: $name, photos: $photos, placeId: $placeId, reference: $reference, scope: $scope, types: $types, url: $url, utcOffset: $utcOffset, vicinity: $vicinity, website: $website)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceResultImpl &&
            const DeepCollectionEquality()
                .equals(other._addressComponents, _addressComponents) &&
            (identical(other.adrAddress, adrAddress) ||
                other.adrAddress == adrAddress) &&
            (identical(other.formattedAddress, formattedAddress) ||
                other.formattedAddress == formattedAddress) &&
            (identical(other.geometry, geometry) ||
                other.geometry == geometry) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.reference, reference) ||
                other.reference == reference) &&
            (identical(other.scope, scope) || other.scope == scope) &&
            const DeepCollectionEquality().equals(other._types, _types) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.utcOffset, utcOffset) ||
                other.utcOffset == utcOffset) &&
            (identical(other.vicinity, vicinity) ||
                other.vicinity == vicinity) &&
            (identical(other.website, website) || other.website == website));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addressComponents),
      adrAddress,
      formattedAddress,
      geometry,
      icon,
      name,
      const DeepCollectionEquality().hash(_photos),
      placeId,
      reference,
      scope,
      const DeepCollectionEquality().hash(_types),
      url,
      utcOffset,
      vicinity,
      website);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceResultImplCopyWith<_$GooglePlaceResultImpl> get copyWith =>
      __$$GooglePlaceResultImplCopyWithImpl<_$GooglePlaceResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceResultImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceResult implements GooglePlaceResult {
  factory _GooglePlaceResult(
      {final List<GooglePlaceAddressComponents>? addressComponents,
      final String? adrAddress,
      final String? formattedAddress,
      final GooglePlaceGeometry? geometry,
      final String? icon,
      final String? name,
      final List<GooglePlacePhotos>? photos,
      final String? placeId,
      final String? reference,
      final String? scope,
      final List<String>? types,
      final String? url,
      final int? utcOffset,
      final String? vicinity,
      final String? website}) = _$GooglePlaceResultImpl;

  factory _GooglePlaceResult.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceResultImpl.fromJson;

  @override
  List<GooglePlaceAddressComponents>? get addressComponents;
  @override
  String? get adrAddress;
  @override
  String? get formattedAddress;
  @override
  GooglePlaceGeometry? get geometry;
  @override
  String? get icon;
  @override
  String? get name;
  @override
  List<GooglePlacePhotos>? get photos;
  @override
  String? get placeId;
  @override
  String? get reference;
  @override
  String? get scope;
  @override
  List<String>? get types;
  @override
  String? get url;
  @override
  int? get utcOffset;
  @override
  String? get vicinity;
  @override
  String? get website;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceResultImplCopyWith<_$GooglePlaceResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlaceAddressComponents _$GooglePlaceAddressComponentsFromJson(
    Map<String, dynamic> json) {
  return _GooglePlaceAddressComponents.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceAddressComponents {
  String? get longName => throw _privateConstructorUsedError;
  String? get shortName => throw _privateConstructorUsedError;
  List<String>? get types => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceAddressComponentsCopyWith<GooglePlaceAddressComponents>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceAddressComponentsCopyWith<$Res> {
  factory $GooglePlaceAddressComponentsCopyWith(
          GooglePlaceAddressComponents value,
          $Res Function(GooglePlaceAddressComponents) then) =
      _$GooglePlaceAddressComponentsCopyWithImpl<$Res,
          GooglePlaceAddressComponents>;
  @useResult
  $Res call({String? longName, String? shortName, List<String>? types});
}

/// @nodoc
class _$GooglePlaceAddressComponentsCopyWithImpl<$Res,
        $Val extends GooglePlaceAddressComponents>
    implements $GooglePlaceAddressComponentsCopyWith<$Res> {
  _$GooglePlaceAddressComponentsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? longName = freezed,
    Object? shortName = freezed,
    Object? types = freezed,
  }) {
    return _then(_value.copyWith(
      longName: freezed == longName
          ? _value.longName
          : longName // ignore: cast_nullable_to_non_nullable
              as String?,
      shortName: freezed == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlaceAddressComponentsImplCopyWith<$Res>
    implements $GooglePlaceAddressComponentsCopyWith<$Res> {
  factory _$$GooglePlaceAddressComponentsImplCopyWith(
          _$GooglePlaceAddressComponentsImpl value,
          $Res Function(_$GooglePlaceAddressComponentsImpl) then) =
      __$$GooglePlaceAddressComponentsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? longName, String? shortName, List<String>? types});
}

/// @nodoc
class __$$GooglePlaceAddressComponentsImplCopyWithImpl<$Res>
    extends _$GooglePlaceAddressComponentsCopyWithImpl<$Res,
        _$GooglePlaceAddressComponentsImpl>
    implements _$$GooglePlaceAddressComponentsImplCopyWith<$Res> {
  __$$GooglePlaceAddressComponentsImplCopyWithImpl(
      _$GooglePlaceAddressComponentsImpl _value,
      $Res Function(_$GooglePlaceAddressComponentsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? longName = freezed,
    Object? shortName = freezed,
    Object? types = freezed,
  }) {
    return _then(_$GooglePlaceAddressComponentsImpl(
      longName: freezed == longName
          ? _value.longName
          : longName // ignore: cast_nullable_to_non_nullable
              as String?,
      shortName: freezed == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String?,
      types: freezed == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceAddressComponentsImpl
    implements _GooglePlaceAddressComponents {
  _$GooglePlaceAddressComponentsImpl(
      {this.longName, this.shortName, final List<String>? types})
      : _types = types;

  factory _$GooglePlaceAddressComponentsImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$GooglePlaceAddressComponentsImplFromJson(json);

  @override
  final String? longName;
  @override
  final String? shortName;
  final List<String>? _types;
  @override
  List<String>? get types {
    final value = _types;
    if (value == null) return null;
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GooglePlaceAddressComponents(longName: $longName, shortName: $shortName, types: $types)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceAddressComponentsImpl &&
            (identical(other.longName, longName) ||
                other.longName == longName) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            const DeepCollectionEquality().equals(other._types, _types));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, longName, shortName,
      const DeepCollectionEquality().hash(_types));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceAddressComponentsImplCopyWith<
          _$GooglePlaceAddressComponentsImpl>
      get copyWith => __$$GooglePlaceAddressComponentsImplCopyWithImpl<
          _$GooglePlaceAddressComponentsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceAddressComponentsImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceAddressComponents
    implements GooglePlaceAddressComponents {
  factory _GooglePlaceAddressComponents(
      {final String? longName,
      final String? shortName,
      final List<String>? types}) = _$GooglePlaceAddressComponentsImpl;

  factory _GooglePlaceAddressComponents.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceAddressComponentsImpl.fromJson;

  @override
  String? get longName;
  @override
  String? get shortName;
  @override
  List<String>? get types;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceAddressComponentsImplCopyWith<
          _$GooglePlaceAddressComponentsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GooglePlaceGeometry _$GooglePlaceGeometryFromJson(Map<String, dynamic> json) {
  return _GooglePlaceGeometry.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceGeometry {
  GooglePlaceLocation? get location => throw _privateConstructorUsedError;
  GooglePlaceViewport? get viewport => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceGeometryCopyWith<GooglePlaceGeometry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceGeometryCopyWith<$Res> {
  factory $GooglePlaceGeometryCopyWith(
          GooglePlaceGeometry value, $Res Function(GooglePlaceGeometry) then) =
      _$GooglePlaceGeometryCopyWithImpl<$Res, GooglePlaceGeometry>;
  @useResult
  $Res call({GooglePlaceLocation? location, GooglePlaceViewport? viewport});

  $GooglePlaceLocationCopyWith<$Res>? get location;
  $GooglePlaceViewportCopyWith<$Res>? get viewport;
}

/// @nodoc
class _$GooglePlaceGeometryCopyWithImpl<$Res, $Val extends GooglePlaceGeometry>
    implements $GooglePlaceGeometryCopyWith<$Res> {
  _$GooglePlaceGeometryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? location = freezed,
    Object? viewport = freezed,
  }) {
    return _then(_value.copyWith(
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
      viewport: freezed == viewport
          ? _value.viewport
          : viewport // ignore: cast_nullable_to_non_nullable
              as GooglePlaceViewport?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceLocationCopyWith<$Res>? get location {
    if (_value.location == null) {
      return null;
    }

    return $GooglePlaceLocationCopyWith<$Res>(_value.location!, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceViewportCopyWith<$Res>? get viewport {
    if (_value.viewport == null) {
      return null;
    }

    return $GooglePlaceViewportCopyWith<$Res>(_value.viewport!, (value) {
      return _then(_value.copyWith(viewport: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GooglePlaceGeometryImplCopyWith<$Res>
    implements $GooglePlaceGeometryCopyWith<$Res> {
  factory _$$GooglePlaceGeometryImplCopyWith(_$GooglePlaceGeometryImpl value,
          $Res Function(_$GooglePlaceGeometryImpl) then) =
      __$$GooglePlaceGeometryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({GooglePlaceLocation? location, GooglePlaceViewport? viewport});

  @override
  $GooglePlaceLocationCopyWith<$Res>? get location;
  @override
  $GooglePlaceViewportCopyWith<$Res>? get viewport;
}

/// @nodoc
class __$$GooglePlaceGeometryImplCopyWithImpl<$Res>
    extends _$GooglePlaceGeometryCopyWithImpl<$Res, _$GooglePlaceGeometryImpl>
    implements _$$GooglePlaceGeometryImplCopyWith<$Res> {
  __$$GooglePlaceGeometryImplCopyWithImpl(_$GooglePlaceGeometryImpl _value,
      $Res Function(_$GooglePlaceGeometryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? location = freezed,
    Object? viewport = freezed,
  }) {
    return _then(_$GooglePlaceGeometryImpl(
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
      viewport: freezed == viewport
          ? _value.viewport
          : viewport // ignore: cast_nullable_to_non_nullable
              as GooglePlaceViewport?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceGeometryImpl implements _GooglePlaceGeometry {
  _$GooglePlaceGeometryImpl({this.location, this.viewport});

  factory _$GooglePlaceGeometryImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlaceGeometryImplFromJson(json);

  @override
  final GooglePlaceLocation? location;
  @override
  final GooglePlaceViewport? viewport;

  @override
  String toString() {
    return 'GooglePlaceGeometry(location: $location, viewport: $viewport)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceGeometryImpl &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.viewport, viewport) ||
                other.viewport == viewport));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, location, viewport);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceGeometryImplCopyWith<_$GooglePlaceGeometryImpl> get copyWith =>
      __$$GooglePlaceGeometryImplCopyWithImpl<_$GooglePlaceGeometryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceGeometryImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceGeometry implements GooglePlaceGeometry {
  factory _GooglePlaceGeometry(
      {final GooglePlaceLocation? location,
      final GooglePlaceViewport? viewport}) = _$GooglePlaceGeometryImpl;

  factory _GooglePlaceGeometry.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceGeometryImpl.fromJson;

  @override
  GooglePlaceLocation? get location;
  @override
  GooglePlaceViewport? get viewport;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceGeometryImplCopyWith<_$GooglePlaceGeometryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlaceLocation _$GooglePlaceLocationFromJson(Map<String, dynamic> json) {
  return _GooglePlaceLocation.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceLocation {
  double? get lat => throw _privateConstructorUsedError;
  double? get lng => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceLocationCopyWith<GooglePlaceLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceLocationCopyWith<$Res> {
  factory $GooglePlaceLocationCopyWith(
          GooglePlaceLocation value, $Res Function(GooglePlaceLocation) then) =
      _$GooglePlaceLocationCopyWithImpl<$Res, GooglePlaceLocation>;
  @useResult
  $Res call({double? lat, double? lng});
}

/// @nodoc
class _$GooglePlaceLocationCopyWithImpl<$Res, $Val extends GooglePlaceLocation>
    implements $GooglePlaceLocationCopyWith<$Res> {
  _$GooglePlaceLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_value.copyWith(
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlaceLocationImplCopyWith<$Res>
    implements $GooglePlaceLocationCopyWith<$Res> {
  factory _$$GooglePlaceLocationImplCopyWith(_$GooglePlaceLocationImpl value,
          $Res Function(_$GooglePlaceLocationImpl) then) =
      __$$GooglePlaceLocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? lat, double? lng});
}

/// @nodoc
class __$$GooglePlaceLocationImplCopyWithImpl<$Res>
    extends _$GooglePlaceLocationCopyWithImpl<$Res, _$GooglePlaceLocationImpl>
    implements _$$GooglePlaceLocationImplCopyWith<$Res> {
  __$$GooglePlaceLocationImplCopyWithImpl(_$GooglePlaceLocationImpl _value,
      $Res Function(_$GooglePlaceLocationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_$GooglePlaceLocationImpl(
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceLocationImpl implements _GooglePlaceLocation {
  _$GooglePlaceLocationImpl({this.lat, this.lng});

  factory _$GooglePlaceLocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlaceLocationImplFromJson(json);

  @override
  final double? lat;
  @override
  final double? lng;

  @override
  String toString() {
    return 'GooglePlaceLocation(lat: $lat, lng: $lng)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceLocationImpl &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lat, lng);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceLocationImplCopyWith<_$GooglePlaceLocationImpl> get copyWith =>
      __$$GooglePlaceLocationImplCopyWithImpl<_$GooglePlaceLocationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceLocationImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceLocation implements GooglePlaceLocation {
  factory _GooglePlaceLocation({final double? lat, final double? lng}) =
      _$GooglePlaceLocationImpl;

  factory _GooglePlaceLocation.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceLocationImpl.fromJson;

  @override
  double? get lat;
  @override
  double? get lng;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceLocationImplCopyWith<_$GooglePlaceLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlaceViewport _$GooglePlaceViewportFromJson(Map<String, dynamic> json) {
  return _GooglePlaceViewport.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceViewport {
  GooglePlaceLocation? get northeast => throw _privateConstructorUsedError;
  GooglePlaceLocation? get southwest => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceViewportCopyWith<GooglePlaceViewport> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceViewportCopyWith<$Res> {
  factory $GooglePlaceViewportCopyWith(
          GooglePlaceViewport value, $Res Function(GooglePlaceViewport) then) =
      _$GooglePlaceViewportCopyWithImpl<$Res, GooglePlaceViewport>;
  @useResult
  $Res call({GooglePlaceLocation? northeast, GooglePlaceLocation? southwest});

  $GooglePlaceLocationCopyWith<$Res>? get northeast;
  $GooglePlaceLocationCopyWith<$Res>? get southwest;
}

/// @nodoc
class _$GooglePlaceViewportCopyWithImpl<$Res, $Val extends GooglePlaceViewport>
    implements $GooglePlaceViewportCopyWith<$Res> {
  _$GooglePlaceViewportCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? northeast = freezed,
    Object? southwest = freezed,
  }) {
    return _then(_value.copyWith(
      northeast: freezed == northeast
          ? _value.northeast
          : northeast // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
      southwest: freezed == southwest
          ? _value.southwest
          : southwest // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceLocationCopyWith<$Res>? get northeast {
    if (_value.northeast == null) {
      return null;
    }

    return $GooglePlaceLocationCopyWith<$Res>(_value.northeast!, (value) {
      return _then(_value.copyWith(northeast: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceLocationCopyWith<$Res>? get southwest {
    if (_value.southwest == null) {
      return null;
    }

    return $GooglePlaceLocationCopyWith<$Res>(_value.southwest!, (value) {
      return _then(_value.copyWith(southwest: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GooglePlaceViewportImplCopyWith<$Res>
    implements $GooglePlaceViewportCopyWith<$Res> {
  factory _$$GooglePlaceViewportImplCopyWith(_$GooglePlaceViewportImpl value,
          $Res Function(_$GooglePlaceViewportImpl) then) =
      __$$GooglePlaceViewportImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({GooglePlaceLocation? northeast, GooglePlaceLocation? southwest});

  @override
  $GooglePlaceLocationCopyWith<$Res>? get northeast;
  @override
  $GooglePlaceLocationCopyWith<$Res>? get southwest;
}

/// @nodoc
class __$$GooglePlaceViewportImplCopyWithImpl<$Res>
    extends _$GooglePlaceViewportCopyWithImpl<$Res, _$GooglePlaceViewportImpl>
    implements _$$GooglePlaceViewportImplCopyWith<$Res> {
  __$$GooglePlaceViewportImplCopyWithImpl(_$GooglePlaceViewportImpl _value,
      $Res Function(_$GooglePlaceViewportImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? northeast = freezed,
    Object? southwest = freezed,
  }) {
    return _then(_$GooglePlaceViewportImpl(
      northeast: freezed == northeast
          ? _value.northeast
          : northeast // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
      southwest: freezed == southwest
          ? _value.southwest
          : southwest // ignore: cast_nullable_to_non_nullable
              as GooglePlaceLocation?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlaceViewportImpl implements _GooglePlaceViewport {
  _$GooglePlaceViewportImpl({this.northeast, this.southwest});

  factory _$GooglePlaceViewportImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlaceViewportImplFromJson(json);

  @override
  final GooglePlaceLocation? northeast;
  @override
  final GooglePlaceLocation? southwest;

  @override
  String toString() {
    return 'GooglePlaceViewport(northeast: $northeast, southwest: $southwest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlaceViewportImpl &&
            (identical(other.northeast, northeast) ||
                other.northeast == northeast) &&
            (identical(other.southwest, southwest) ||
                other.southwest == southwest));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, northeast, southwest);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlaceViewportImplCopyWith<_$GooglePlaceViewportImpl> get copyWith =>
      __$$GooglePlaceViewportImplCopyWithImpl<_$GooglePlaceViewportImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlaceViewportImplToJson(
      this,
    );
  }
}

abstract class _GooglePlaceViewport implements GooglePlaceViewport {
  factory _GooglePlaceViewport(
      {final GooglePlaceLocation? northeast,
      final GooglePlaceLocation? southwest}) = _$GooglePlaceViewportImpl;

  factory _GooglePlaceViewport.fromJson(Map<String, dynamic> json) =
      _$GooglePlaceViewportImpl.fromJson;

  @override
  GooglePlaceLocation? get northeast;
  @override
  GooglePlaceLocation? get southwest;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlaceViewportImplCopyWith<_$GooglePlaceViewportImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlacePhotos _$GooglePlacePhotosFromJson(Map<String, dynamic> json) {
  return _GooglePlacePhotos.fromJson(json);
}

/// @nodoc
mixin _$GooglePlacePhotos {
  int? get height => throw _privateConstructorUsedError;
  List<String>? get htmlAttributions => throw _privateConstructorUsedError;
  String? get photoReference => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlacePhotosCopyWith<GooglePlacePhotos> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlacePhotosCopyWith<$Res> {
  factory $GooglePlacePhotosCopyWith(
          GooglePlacePhotos value, $Res Function(GooglePlacePhotos) then) =
      _$GooglePlacePhotosCopyWithImpl<$Res, GooglePlacePhotos>;
  @useResult
  $Res call(
      {int? height,
      List<String>? htmlAttributions,
      String? photoReference,
      int? width});
}

/// @nodoc
class _$GooglePlacePhotosCopyWithImpl<$Res, $Val extends GooglePlacePhotos>
    implements $GooglePlacePhotosCopyWith<$Res> {
  _$GooglePlacePhotosCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? htmlAttributions = freezed,
    Object? photoReference = freezed,
    Object? width = freezed,
  }) {
    return _then(_value.copyWith(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      htmlAttributions: freezed == htmlAttributions
          ? _value.htmlAttributions
          : htmlAttributions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      photoReference: freezed == photoReference
          ? _value.photoReference
          : photoReference // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlacePhotosImplCopyWith<$Res>
    implements $GooglePlacePhotosCopyWith<$Res> {
  factory _$$GooglePlacePhotosImplCopyWith(_$GooglePlacePhotosImpl value,
          $Res Function(_$GooglePlacePhotosImpl) then) =
      __$$GooglePlacePhotosImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? height,
      List<String>? htmlAttributions,
      String? photoReference,
      int? width});
}

/// @nodoc
class __$$GooglePlacePhotosImplCopyWithImpl<$Res>
    extends _$GooglePlacePhotosCopyWithImpl<$Res, _$GooglePlacePhotosImpl>
    implements _$$GooglePlacePhotosImplCopyWith<$Res> {
  __$$GooglePlacePhotosImplCopyWithImpl(_$GooglePlacePhotosImpl _value,
      $Res Function(_$GooglePlacePhotosImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? htmlAttributions = freezed,
    Object? photoReference = freezed,
    Object? width = freezed,
  }) {
    return _then(_$GooglePlacePhotosImpl(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      htmlAttributions: freezed == htmlAttributions
          ? _value._htmlAttributions
          : htmlAttributions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      photoReference: freezed == photoReference
          ? _value.photoReference
          : photoReference // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlacePhotosImpl implements _GooglePlacePhotos {
  _$GooglePlacePhotosImpl(
      {this.height,
      final List<String>? htmlAttributions,
      this.photoReference,
      this.width})
      : _htmlAttributions = htmlAttributions;

  factory _$GooglePlacePhotosImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlacePhotosImplFromJson(json);

  @override
  final int? height;
  final List<String>? _htmlAttributions;
  @override
  List<String>? get htmlAttributions {
    final value = _htmlAttributions;
    if (value == null) return null;
    if (_htmlAttributions is EqualUnmodifiableListView)
      return _htmlAttributions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? photoReference;
  @override
  final int? width;

  @override
  String toString() {
    return 'GooglePlacePhotos(height: $height, htmlAttributions: $htmlAttributions, photoReference: $photoReference, width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlacePhotosImpl &&
            (identical(other.height, height) || other.height == height) &&
            const DeepCollectionEquality()
                .equals(other._htmlAttributions, _htmlAttributions) &&
            (identical(other.photoReference, photoReference) ||
                other.photoReference == photoReference) &&
            (identical(other.width, width) || other.width == width));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      height,
      const DeepCollectionEquality().hash(_htmlAttributions),
      photoReference,
      width);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlacePhotosImplCopyWith<_$GooglePlacePhotosImpl> get copyWith =>
      __$$GooglePlacePhotosImplCopyWithImpl<_$GooglePlacePhotosImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlacePhotosImplToJson(
      this,
    );
  }
}

abstract class _GooglePlacePhotos implements GooglePlacePhotos {
  factory _GooglePlacePhotos(
      {final int? height,
      final List<String>? htmlAttributions,
      final String? photoReference,
      final int? width}) = _$GooglePlacePhotosImpl;

  factory _GooglePlacePhotos.fromJson(Map<String, dynamic> json) =
      _$GooglePlacePhotosImpl.fromJson;

  @override
  int? get height;
  @override
  List<String>? get htmlAttributions;
  @override
  String? get photoReference;
  @override
  int? get width;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlacePhotosImplCopyWith<_$GooglePlacePhotosImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GooglePlacesAutocompleteResponse _$GooglePlacesAutocompleteResponseFromJson(
    Map<String, dynamic> json) {
  return _GooglePlacesAutocompleteResponse.fromJson(json);
}

/// @nodoc
mixin _$GooglePlacesAutocompleteResponse {
  List<GooglePlacePrediction>? get predictions =>
      throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlacesAutocompleteResponseCopyWith<GooglePlacesAutocompleteResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlacesAutocompleteResponseCopyWith<$Res> {
  factory $GooglePlacesAutocompleteResponseCopyWith(
          GooglePlacesAutocompleteResponse value,
          $Res Function(GooglePlacesAutocompleteResponse) then) =
      _$GooglePlacesAutocompleteResponseCopyWithImpl<$Res,
          GooglePlacesAutocompleteResponse>;
  @useResult
  $Res call({List<GooglePlacePrediction>? predictions, String? status});
}

/// @nodoc
class _$GooglePlacesAutocompleteResponseCopyWithImpl<$Res,
        $Val extends GooglePlacesAutocompleteResponse>
    implements $GooglePlacesAutocompleteResponseCopyWith<$Res> {
  _$GooglePlacesAutocompleteResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? predictions = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      predictions: freezed == predictions
          ? _value.predictions
          : predictions // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePrediction>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlacesAutocompleteResponseImplCopyWith<$Res>
    implements $GooglePlacesAutocompleteResponseCopyWith<$Res> {
  factory _$$GooglePlacesAutocompleteResponseImplCopyWith(
          _$GooglePlacesAutocompleteResponseImpl value,
          $Res Function(_$GooglePlacesAutocompleteResponseImpl) then) =
      __$$GooglePlacesAutocompleteResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<GooglePlacePrediction>? predictions, String? status});
}

/// @nodoc
class __$$GooglePlacesAutocompleteResponseImplCopyWithImpl<$Res>
    extends _$GooglePlacesAutocompleteResponseCopyWithImpl<$Res,
        _$GooglePlacesAutocompleteResponseImpl>
    implements _$$GooglePlacesAutocompleteResponseImplCopyWith<$Res> {
  __$$GooglePlacesAutocompleteResponseImplCopyWithImpl(
      _$GooglePlacesAutocompleteResponseImpl _value,
      $Res Function(_$GooglePlacesAutocompleteResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? predictions = freezed,
    Object? status = freezed,
  }) {
    return _then(_$GooglePlacesAutocompleteResponseImpl(
      predictions: freezed == predictions
          ? _value._predictions
          : predictions // ignore: cast_nullable_to_non_nullable
              as List<GooglePlacePrediction>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlacesAutocompleteResponseImpl
    implements _GooglePlacesAutocompleteResponse {
  _$GooglePlacesAutocompleteResponseImpl(
      {final List<GooglePlacePrediction>? predictions, this.status})
      : _predictions = predictions;

  factory _$GooglePlacesAutocompleteResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$GooglePlacesAutocompleteResponseImplFromJson(json);

  final List<GooglePlacePrediction>? _predictions;
  @override
  List<GooglePlacePrediction>? get predictions {
    final value = _predictions;
    if (value == null) return null;
    if (_predictions is EqualUnmodifiableListView) return _predictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? status;

  @override
  String toString() {
    return 'GooglePlacesAutocompleteResponse(predictions: $predictions, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlacesAutocompleteResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._predictions, _predictions) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_predictions), status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlacesAutocompleteResponseImplCopyWith<
          _$GooglePlacesAutocompleteResponseImpl>
      get copyWith => __$$GooglePlacesAutocompleteResponseImplCopyWithImpl<
          _$GooglePlacesAutocompleteResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlacesAutocompleteResponseImplToJson(
      this,
    );
  }
}

abstract class _GooglePlacesAutocompleteResponse
    implements GooglePlacesAutocompleteResponse {
  factory _GooglePlacesAutocompleteResponse(
      {final List<GooglePlacePrediction>? predictions,
      final String? status}) = _$GooglePlacesAutocompleteResponseImpl;

  factory _GooglePlacesAutocompleteResponse.fromJson(
          Map<String, dynamic> json) =
      _$GooglePlacesAutocompleteResponseImpl.fromJson;

  @override
  List<GooglePlacePrediction>? get predictions;
  @override
  String? get status;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlacesAutocompleteResponseImplCopyWith<
          _$GooglePlacesAutocompleteResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GooglePlacePrediction _$GooglePlacePredictionFromJson(
    Map<String, dynamic> json) {
  return _GooglePlacePrediction.fromJson(json);
}

/// @nodoc
mixin _$GooglePlacePrediction {
  String? get description => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  List<GooglePlaceMatchedSubstrings>? get matchedSubstrings =>
      throw _privateConstructorUsedError;
  String? get placeId => throw _privateConstructorUsedError;
  String? get reference => throw _privateConstructorUsedError;
  GooglePlaceStructuredFormatting? get structuredFormatting =>
      throw _privateConstructorUsedError;
  List<GooglePlaceTerms>? get terms => throw _privateConstructorUsedError;
  List<String>? get types => throw _privateConstructorUsedError;
  String? get lat => throw _privateConstructorUsedError;
  String? get lng => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlacePredictionCopyWith<GooglePlacePrediction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlacePredictionCopyWith<$Res> {
  factory $GooglePlacePredictionCopyWith(GooglePlacePrediction value,
          $Res Function(GooglePlacePrediction) then) =
      _$GooglePlacePredictionCopyWithImpl<$Res, GooglePlacePrediction>;
  @useResult
  $Res call(
      {String? description,
      String? id,
      List<GooglePlaceMatchedSubstrings>? matchedSubstrings,
      String? placeId,
      String? reference,
      GooglePlaceStructuredFormatting? structuredFormatting,
      List<GooglePlaceTerms>? terms,
      List<String>? types,
      String? lat,
      String? lng});

  $GooglePlaceStructuredFormattingCopyWith<$Res>? get structuredFormatting;
}

/// @nodoc
class _$GooglePlacePredictionCopyWithImpl<$Res,
        $Val extends GooglePlacePrediction>
    implements $GooglePlacePredictionCopyWith<$Res> {
  _$GooglePlacePredictionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? id = freezed,
    Object? matchedSubstrings = freezed,
    Object? placeId = freezed,
    Object? reference = freezed,
    Object? structuredFormatting = freezed,
    Object? terms = freezed,
    Object? types = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      matchedSubstrings: freezed == matchedSubstrings
          ? _value.matchedSubstrings
          : matchedSubstrings // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceMatchedSubstrings>?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      reference: freezed == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as String?,
      structuredFormatting: freezed == structuredFormatting
          ? _value.structuredFormatting
          : structuredFormatting // ignore: cast_nullable_to_non_nullable
              as GooglePlaceStructuredFormatting?,
      terms: freezed == terms
          ? _value.terms
          : terms // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceTerms>?,
      types: freezed == types
          ? _value.types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GooglePlaceStructuredFormattingCopyWith<$Res>? get structuredFormatting {
    if (_value.structuredFormatting == null) {
      return null;
    }

    return $GooglePlaceStructuredFormattingCopyWith<$Res>(
        _value.structuredFormatting!, (value) {
      return _then(_value.copyWith(structuredFormatting: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GooglePlacePredictionImplCopyWith<$Res>
    implements $GooglePlacePredictionCopyWith<$Res> {
  factory _$$GooglePlacePredictionImplCopyWith(
          _$GooglePlacePredictionImpl value,
          $Res Function(_$GooglePlacePredictionImpl) then) =
      __$$GooglePlacePredictionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? description,
      String? id,
      List<GooglePlaceMatchedSubstrings>? matchedSubstrings,
      String? placeId,
      String? reference,
      GooglePlaceStructuredFormatting? structuredFormatting,
      List<GooglePlaceTerms>? terms,
      List<String>? types,
      String? lat,
      String? lng});

  @override
  $GooglePlaceStructuredFormattingCopyWith<$Res>? get structuredFormatting;
}

/// @nodoc
class __$$GooglePlacePredictionImplCopyWithImpl<$Res>
    extends _$GooglePlacePredictionCopyWithImpl<$Res,
        _$GooglePlacePredictionImpl>
    implements _$$GooglePlacePredictionImplCopyWith<$Res> {
  __$$GooglePlacePredictionImplCopyWithImpl(_$GooglePlacePredictionImpl _value,
      $Res Function(_$GooglePlacePredictionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? id = freezed,
    Object? matchedSubstrings = freezed,
    Object? placeId = freezed,
    Object? reference = freezed,
    Object? structuredFormatting = freezed,
    Object? terms = freezed,
    Object? types = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_$GooglePlacePredictionImpl(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      matchedSubstrings: freezed == matchedSubstrings
          ? _value._matchedSubstrings
          : matchedSubstrings // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceMatchedSubstrings>?,
      placeId: freezed == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String?,
      reference: freezed == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as String?,
      structuredFormatting: freezed == structuredFormatting
          ? _value.structuredFormatting
          : structuredFormatting // ignore: cast_nullable_to_non_nullable
              as GooglePlaceStructuredFormatting?,
      terms: freezed == terms
          ? _value._terms
          : terms // ignore: cast_nullable_to_non_nullable
              as List<GooglePlaceTerms>?,
      types: freezed == types
          ? _value._types
          : types // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlacePredictionImpl implements _GooglePlacePrediction {
  _$GooglePlacePredictionImpl(
      {this.description,
      this.id,
      final List<GooglePlaceMatchedSubstrings>? matchedSubstrings,
      this.placeId,
      this.reference,
      this.structuredFormatting,
      final List<GooglePlaceTerms>? terms,
      final List<String>? types,
      this.lat,
      this.lng})
      : _matchedSubstrings = matchedSubstrings,
        _terms = terms,
        _types = types;

  factory _$GooglePlacePredictionImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlacePredictionImplFromJson(json);

  @override
  final String? description;
  @override
  final String? id;
  final List<GooglePlaceMatchedSubstrings>? _matchedSubstrings;
  @override
  List<GooglePlaceMatchedSubstrings>? get matchedSubstrings {
    final value = _matchedSubstrings;
    if (value == null) return null;
    if (_matchedSubstrings is EqualUnmodifiableListView)
      return _matchedSubstrings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? placeId;
  @override
  final String? reference;
  @override
  final GooglePlaceStructuredFormatting? structuredFormatting;
  final List<GooglePlaceTerms>? _terms;
  @override
  List<GooglePlaceTerms>? get terms {
    final value = _terms;
    if (value == null) return null;
    if (_terms is EqualUnmodifiableListView) return _terms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _types;
  @override
  List<String>? get types {
    final value = _types;
    if (value == null) return null;
    if (_types is EqualUnmodifiableListView) return _types;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? lat;
  @override
  final String? lng;

  @override
  String toString() {
    return 'GooglePlacePrediction(description: $description, id: $id, matchedSubstrings: $matchedSubstrings, placeId: $placeId, reference: $reference, structuredFormatting: $structuredFormatting, terms: $terms, types: $types, lat: $lat, lng: $lng)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlacePredictionImpl &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._matchedSubstrings, _matchedSubstrings) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.reference, reference) ||
                other.reference == reference) &&
            (identical(other.structuredFormatting, structuredFormatting) ||
                other.structuredFormatting == structuredFormatting) &&
            const DeepCollectionEquality().equals(other._terms, _terms) &&
            const DeepCollectionEquality().equals(other._types, _types) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      description,
      id,
      const DeepCollectionEquality().hash(_matchedSubstrings),
      placeId,
      reference,
      structuredFormatting,
      const DeepCollectionEquality().hash(_terms),
      const DeepCollectionEquality().hash(_types),
      lat,
      lng);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlacePredictionImplCopyWith<_$GooglePlacePredictionImpl>
      get copyWith => __$$GooglePlacePredictionImplCopyWithImpl<
          _$GooglePlacePredictionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlacePredictionImplToJson(
      this,
    );
  }
}

abstract class _GooglePlacePrediction implements GooglePlacePrediction {
  factory _GooglePlacePrediction(
      {final String? description,
      final String? id,
      final List<GooglePlaceMatchedSubstrings>? matchedSubstrings,
      final String? placeId,
      final String? reference,
      final GooglePlaceStructuredFormatting? structuredFormatting,
      final List<GooglePlaceTerms>? terms,
      final List<String>? types,
      final String? lat,
      final String? lng}) = _$GooglePlacePredictionImpl;

  factory _GooglePlacePrediction.fromJson(Map<String, dynamic> json) =
      _$GooglePlacePredictionImpl.fromJson;

  @override
  String? get description;
  @override
  String? get id;
  @override
  List<GooglePlaceMatchedSubstrings>? get matchedSubstrings;
  @override
  String? get placeId;
  @override
  String? get reference;
  @override
  GooglePlaceStructuredFormatting? get structuredFormatting;
  @override
  List<GooglePlaceTerms>? get terms;
  @override
  List<String>? get types;
  @override
  String? get lat;
  @override
  String? get lng;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlacePredictionImplCopyWith<_$GooglePlacePredictionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GooglePlaceMatchedSubstrings _$GooglePlaceMatchedSubstringsFromJson(
    Map<String, dynamic> json) {
  return GooglePlace_MatchedSubstrings.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceMatchedSubstrings {
  int? get length => throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceMatchedSubstringsCopyWith<GooglePlaceMatchedSubstrings>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceMatchedSubstringsCopyWith<$Res> {
  factory $GooglePlaceMatchedSubstringsCopyWith(
          GooglePlaceMatchedSubstrings value,
          $Res Function(GooglePlaceMatchedSubstrings) then) =
      _$GooglePlaceMatchedSubstringsCopyWithImpl<$Res,
          GooglePlaceMatchedSubstrings>;
  @useResult
  $Res call({int? length, int? offset});
}

/// @nodoc
class _$GooglePlaceMatchedSubstringsCopyWithImpl<$Res,
        $Val extends GooglePlaceMatchedSubstrings>
    implements $GooglePlaceMatchedSubstringsCopyWith<$Res> {
  _$GooglePlaceMatchedSubstringsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? length = freezed,
    Object? offset = freezed,
  }) {
    return _then(_value.copyWith(
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlace_MatchedSubstringsImplCopyWith<$Res>
    implements $GooglePlaceMatchedSubstringsCopyWith<$Res> {
  factory _$$GooglePlace_MatchedSubstringsImplCopyWith(
          _$GooglePlace_MatchedSubstringsImpl value,
          $Res Function(_$GooglePlace_MatchedSubstringsImpl) then) =
      __$$GooglePlace_MatchedSubstringsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? length, int? offset});
}

/// @nodoc
class __$$GooglePlace_MatchedSubstringsImplCopyWithImpl<$Res>
    extends _$GooglePlaceMatchedSubstringsCopyWithImpl<$Res,
        _$GooglePlace_MatchedSubstringsImpl>
    implements _$$GooglePlace_MatchedSubstringsImplCopyWith<$Res> {
  __$$GooglePlace_MatchedSubstringsImplCopyWithImpl(
      _$GooglePlace_MatchedSubstringsImpl _value,
      $Res Function(_$GooglePlace_MatchedSubstringsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? length = freezed,
    Object? offset = freezed,
  }) {
    return _then(_$GooglePlace_MatchedSubstringsImpl(
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlace_MatchedSubstringsImpl
    implements GooglePlace_MatchedSubstrings {
  _$GooglePlace_MatchedSubstringsImpl({this.length, this.offset});

  factory _$GooglePlace_MatchedSubstringsImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$GooglePlace_MatchedSubstringsImplFromJson(json);

  @override
  final int? length;
  @override
  final int? offset;

  @override
  String toString() {
    return 'GooglePlaceMatchedSubstrings(length: $length, offset: $offset)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlace_MatchedSubstringsImpl &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, length, offset);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlace_MatchedSubstringsImplCopyWith<
          _$GooglePlace_MatchedSubstringsImpl>
      get copyWith => __$$GooglePlace_MatchedSubstringsImplCopyWithImpl<
          _$GooglePlace_MatchedSubstringsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlace_MatchedSubstringsImplToJson(
      this,
    );
  }
}

abstract class GooglePlace_MatchedSubstrings
    implements GooglePlaceMatchedSubstrings {
  factory GooglePlace_MatchedSubstrings(
      {final int? length,
      final int? offset}) = _$GooglePlace_MatchedSubstringsImpl;

  factory GooglePlace_MatchedSubstrings.fromJson(Map<String, dynamic> json) =
      _$GooglePlace_MatchedSubstringsImpl.fromJson;

  @override
  int? get length;
  @override
  int? get offset;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlace_MatchedSubstringsImplCopyWith<
          _$GooglePlace_MatchedSubstringsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GooglePlaceStructuredFormatting _$GooglePlaceStructuredFormattingFromJson(
    Map<String, dynamic> json) {
  return GooglePlace_StructuredFormatting.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceStructuredFormatting {
  String? get mainText => throw _privateConstructorUsedError;
  String? get secondaryText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceStructuredFormattingCopyWith<GooglePlaceStructuredFormatting>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceStructuredFormattingCopyWith<$Res> {
  factory $GooglePlaceStructuredFormattingCopyWith(
          GooglePlaceStructuredFormatting value,
          $Res Function(GooglePlaceStructuredFormatting) then) =
      _$GooglePlaceStructuredFormattingCopyWithImpl<$Res,
          GooglePlaceStructuredFormatting>;
  @useResult
  $Res call({String? mainText, String? secondaryText});
}

/// @nodoc
class _$GooglePlaceStructuredFormattingCopyWithImpl<$Res,
        $Val extends GooglePlaceStructuredFormatting>
    implements $GooglePlaceStructuredFormattingCopyWith<$Res> {
  _$GooglePlaceStructuredFormattingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainText = freezed,
    Object? secondaryText = freezed,
  }) {
    return _then(_value.copyWith(
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      secondaryText: freezed == secondaryText
          ? _value.secondaryText
          : secondaryText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlace_StructuredFormattingImplCopyWith<$Res>
    implements $GooglePlaceStructuredFormattingCopyWith<$Res> {
  factory _$$GooglePlace_StructuredFormattingImplCopyWith(
          _$GooglePlace_StructuredFormattingImpl value,
          $Res Function(_$GooglePlace_StructuredFormattingImpl) then) =
      __$$GooglePlace_StructuredFormattingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mainText, String? secondaryText});
}

/// @nodoc
class __$$GooglePlace_StructuredFormattingImplCopyWithImpl<$Res>
    extends _$GooglePlaceStructuredFormattingCopyWithImpl<$Res,
        _$GooglePlace_StructuredFormattingImpl>
    implements _$$GooglePlace_StructuredFormattingImplCopyWith<$Res> {
  __$$GooglePlace_StructuredFormattingImplCopyWithImpl(
      _$GooglePlace_StructuredFormattingImpl _value,
      $Res Function(_$GooglePlace_StructuredFormattingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainText = freezed,
    Object? secondaryText = freezed,
  }) {
    return _then(_$GooglePlace_StructuredFormattingImpl(
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      secondaryText: freezed == secondaryText
          ? _value.secondaryText
          : secondaryText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlace_StructuredFormattingImpl
    implements GooglePlace_StructuredFormatting {
  _$GooglePlace_StructuredFormattingImpl({this.mainText, this.secondaryText});

  factory _$GooglePlace_StructuredFormattingImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$GooglePlace_StructuredFormattingImplFromJson(json);

  @override
  final String? mainText;
  @override
  final String? secondaryText;

  @override
  String toString() {
    return 'GooglePlaceStructuredFormatting(mainText: $mainText, secondaryText: $secondaryText)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlace_StructuredFormattingImpl &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.secondaryText, secondaryText) ||
                other.secondaryText == secondaryText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mainText, secondaryText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlace_StructuredFormattingImplCopyWith<
          _$GooglePlace_StructuredFormattingImpl>
      get copyWith => __$$GooglePlace_StructuredFormattingImplCopyWithImpl<
          _$GooglePlace_StructuredFormattingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlace_StructuredFormattingImplToJson(
      this,
    );
  }
}

abstract class GooglePlace_StructuredFormatting
    implements GooglePlaceStructuredFormatting {
  factory GooglePlace_StructuredFormatting(
      {final String? mainText,
      final String? secondaryText}) = _$GooglePlace_StructuredFormattingImpl;

  factory GooglePlace_StructuredFormatting.fromJson(Map<String, dynamic> json) =
      _$GooglePlace_StructuredFormattingImpl.fromJson;

  @override
  String? get mainText;
  @override
  String? get secondaryText;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlace_StructuredFormattingImplCopyWith<
          _$GooglePlace_StructuredFormattingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GooglePlaceTerms _$GooglePlaceTermsFromJson(Map<String, dynamic> json) {
  return GooglePlace_Terms.fromJson(json);
}

/// @nodoc
mixin _$GooglePlaceTerms {
  int? get offset => throw _privateConstructorUsedError;
  String? get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GooglePlaceTermsCopyWith<GooglePlaceTerms> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GooglePlaceTermsCopyWith<$Res> {
  factory $GooglePlaceTermsCopyWith(
          GooglePlaceTerms value, $Res Function(GooglePlaceTerms) then) =
      _$GooglePlaceTermsCopyWithImpl<$Res, GooglePlaceTerms>;
  @useResult
  $Res call({int? offset, String? value});
}

/// @nodoc
class _$GooglePlaceTermsCopyWithImpl<$Res, $Val extends GooglePlaceTerms>
    implements $GooglePlaceTermsCopyWith<$Res> {
  _$GooglePlaceTermsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = freezed,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GooglePlace_TermsImplCopyWith<$Res>
    implements $GooglePlaceTermsCopyWith<$Res> {
  factory _$$GooglePlace_TermsImplCopyWith(_$GooglePlace_TermsImpl value,
          $Res Function(_$GooglePlace_TermsImpl) then) =
      __$$GooglePlace_TermsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? offset, String? value});
}

/// @nodoc
class __$$GooglePlace_TermsImplCopyWithImpl<$Res>
    extends _$GooglePlaceTermsCopyWithImpl<$Res, _$GooglePlace_TermsImpl>
    implements _$$GooglePlace_TermsImplCopyWith<$Res> {
  __$$GooglePlace_TermsImplCopyWithImpl(_$GooglePlace_TermsImpl _value,
      $Res Function(_$GooglePlace_TermsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = freezed,
    Object? value = freezed,
  }) {
    return _then(_$GooglePlace_TermsImpl(
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GooglePlace_TermsImpl implements GooglePlace_Terms {
  _$GooglePlace_TermsImpl({this.offset, this.value});

  factory _$GooglePlace_TermsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GooglePlace_TermsImplFromJson(json);

  @override
  final int? offset;
  @override
  final String? value;

  @override
  String toString() {
    return 'GooglePlaceTerms(offset: $offset, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePlace_TermsImpl &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, offset, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GooglePlace_TermsImplCopyWith<_$GooglePlace_TermsImpl> get copyWith =>
      __$$GooglePlace_TermsImplCopyWithImpl<_$GooglePlace_TermsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GooglePlace_TermsImplToJson(
      this,
    );
  }
}

abstract class GooglePlace_Terms implements GooglePlaceTerms {
  factory GooglePlace_Terms({final int? offset, final String? value}) =
      _$GooglePlace_TermsImpl;

  factory GooglePlace_Terms.fromJson(Map<String, dynamic> json) =
      _$GooglePlace_TermsImpl.fromJson;

  @override
  int? get offset;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$GooglePlace_TermsImplCopyWith<_$GooglePlace_TermsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
