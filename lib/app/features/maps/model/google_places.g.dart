// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_places.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GooglePlaceDetailsImpl _$$GooglePlaceDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceDetailsImpl(
      result: json['result'] == null
          ? null
          : GooglePlaceResult.fromJson(json['result'] as Map<String, dynamic>),
      status: json['status'] as String?,
    );

Map<String, dynamic> _$$GooglePlaceDetailsImplToJson(
        _$GooglePlaceDetailsImpl instance) =>
    <String, dynamic>{
      'result': instance.result?.toJson(),
      'status': instance.status,
    };

_$GoogleNearbyPlacesImpl _$$GoogleNearbyPlacesImplFromJson(
        Map<String, dynamic> json) =>
    _$GoogleNearbyPlacesImpl(
      results: (json['results'] as List<dynamic>?)
          ?.map((e) => GoogleGeocodeResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String?,
    );

Map<String, dynamic> _$$GoogleNearbyPlacesImplToJson(
        _$GoogleNearbyPlacesImpl instance) =>
    <String, dynamic>{
      'results': instance.results?.map((e) => e.toJson()).toList(),
      'status': instance.status,
    };

_$GoogleGeocodeResultImpl _$$GoogleGeocodeResultImplFromJson(
        Map<String, dynamic> json) =>
    _$GoogleGeocodeResultImpl(
      addressComponents: (json['address_components'] as List<dynamic>?)
          ?.map((e) =>
              GooglePlaceAddressComponents.fromJson(e as Map<String, dynamic>))
          .toList(),
      formattedAddress: json['formatted_address'] as String?,
      geometry: json['geometry'] == null
          ? null
          : GooglePlaceGeometry.fromJson(
              json['geometry'] as Map<String, dynamic>),
      placeId: json['place_id'] as String?,
      types:
          (json['types'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$GoogleGeocodeResultImplToJson(
        _$GoogleGeocodeResultImpl instance) =>
    <String, dynamic>{
      'address_components':
          instance.addressComponents?.map((e) => e.toJson()).toList(),
      'formatted_address': instance.formattedAddress,
      'geometry': instance.geometry?.toJson(),
      'place_id': instance.placeId,
      'types': instance.types,
    };

_$GooglePlaceResultImpl _$$GooglePlaceResultImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceResultImpl(
      addressComponents: (json['address_components'] as List<dynamic>?)
          ?.map((e) =>
              GooglePlaceAddressComponents.fromJson(e as Map<String, dynamic>))
          .toList(),
      adrAddress: json['adr_address'] as String?,
      formattedAddress: json['formatted_address'] as String?,
      geometry: json['geometry'] == null
          ? null
          : GooglePlaceGeometry.fromJson(
              json['geometry'] as Map<String, dynamic>),
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      photos: (json['photos'] as List<dynamic>?)
          ?.map((e) => GooglePlacePhotos.fromJson(e as Map<String, dynamic>))
          .toList(),
      placeId: json['place_id'] as String?,
      reference: json['reference'] as String?,
      scope: json['scope'] as String?,
      types:
          (json['types'] as List<dynamic>?)?.map((e) => e as String).toList(),
      url: json['url'] as String?,
      utcOffset: (json['utc_offset'] as num?)?.toInt(),
      vicinity: json['vicinity'] as String?,
      website: json['website'] as String?,
    );

Map<String, dynamic> _$$GooglePlaceResultImplToJson(
        _$GooglePlaceResultImpl instance) =>
    <String, dynamic>{
      'address_components':
          instance.addressComponents?.map((e) => e.toJson()).toList(),
      'adr_address': instance.adrAddress,
      'formatted_address': instance.formattedAddress,
      'geometry': instance.geometry?.toJson(),
      'icon': instance.icon,
      'name': instance.name,
      'photos': instance.photos?.map((e) => e.toJson()).toList(),
      'place_id': instance.placeId,
      'reference': instance.reference,
      'scope': instance.scope,
      'types': instance.types,
      'url': instance.url,
      'utc_offset': instance.utcOffset,
      'vicinity': instance.vicinity,
      'website': instance.website,
    };

_$GooglePlaceAddressComponentsImpl _$$GooglePlaceAddressComponentsImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceAddressComponentsImpl(
      longName: json['long_name'] as String?,
      shortName: json['short_name'] as String?,
      types:
          (json['types'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$GooglePlaceAddressComponentsImplToJson(
        _$GooglePlaceAddressComponentsImpl instance) =>
    <String, dynamic>{
      'long_name': instance.longName,
      'short_name': instance.shortName,
      'types': instance.types,
    };

_$GooglePlaceGeometryImpl _$$GooglePlaceGeometryImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceGeometryImpl(
      location: json['location'] == null
          ? null
          : GooglePlaceLocation.fromJson(
              json['location'] as Map<String, dynamic>),
      viewport: json['viewport'] == null
          ? null
          : GooglePlaceViewport.fromJson(
              json['viewport'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$GooglePlaceGeometryImplToJson(
        _$GooglePlaceGeometryImpl instance) =>
    <String, dynamic>{
      'location': instance.location?.toJson(),
      'viewport': instance.viewport?.toJson(),
    };

_$GooglePlaceLocationImpl _$$GooglePlaceLocationImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceLocationImpl(
      lat: (json['lat'] as num?)?.toDouble(),
      lng: (json['lng'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$GooglePlaceLocationImplToJson(
        _$GooglePlaceLocationImpl instance) =>
    <String, dynamic>{
      'lat': instance.lat,
      'lng': instance.lng,
    };

_$GooglePlaceViewportImpl _$$GooglePlaceViewportImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlaceViewportImpl(
      northeast: json['northeast'] == null
          ? null
          : GooglePlaceLocation.fromJson(
              json['northeast'] as Map<String, dynamic>),
      southwest: json['southwest'] == null
          ? null
          : GooglePlaceLocation.fromJson(
              json['southwest'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$GooglePlaceViewportImplToJson(
        _$GooglePlaceViewportImpl instance) =>
    <String, dynamic>{
      'northeast': instance.northeast?.toJson(),
      'southwest': instance.southwest?.toJson(),
    };

_$GooglePlacePhotosImpl _$$GooglePlacePhotosImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlacePhotosImpl(
      height: (json['height'] as num?)?.toInt(),
      htmlAttributions: (json['html_attributions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      photoReference: json['photo_reference'] as String?,
      width: (json['width'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GooglePlacePhotosImplToJson(
        _$GooglePlacePhotosImpl instance) =>
    <String, dynamic>{
      'height': instance.height,
      'html_attributions': instance.htmlAttributions,
      'photo_reference': instance.photoReference,
      'width': instance.width,
    };

_$GooglePlacesAutocompleteResponseImpl
    _$$GooglePlacesAutocompleteResponseImplFromJson(
            Map<String, dynamic> json) =>
        _$GooglePlacesAutocompleteResponseImpl(
          predictions: (json['predictions'] as List<dynamic>?)
              ?.map((e) =>
                  GooglePlacePrediction.fromJson(e as Map<String, dynamic>))
              .toList(),
          status: json['status'] as String?,
        );

Map<String, dynamic> _$$GooglePlacesAutocompleteResponseImplToJson(
        _$GooglePlacesAutocompleteResponseImpl instance) =>
    <String, dynamic>{
      'predictions': instance.predictions?.map((e) => e.toJson()).toList(),
      'status': instance.status,
    };

_$GooglePlacePredictionImpl _$$GooglePlacePredictionImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlacePredictionImpl(
      description: json['description'] as String?,
      id: json['id'] as String?,
      matchedSubstrings: (json['matched_substrings'] as List<dynamic>?)
          ?.map((e) =>
              GooglePlaceMatchedSubstrings.fromJson(e as Map<String, dynamic>))
          .toList(),
      placeId: json['place_id'] as String?,
      reference: json['reference'] as String?,
      structuredFormatting: json['structured_formatting'] == null
          ? null
          : GooglePlaceStructuredFormatting.fromJson(
              json['structured_formatting'] as Map<String, dynamic>),
      terms: (json['terms'] as List<dynamic>?)
          ?.map((e) => GooglePlaceTerms.fromJson(e as Map<String, dynamic>))
          .toList(),
      types:
          (json['types'] as List<dynamic>?)?.map((e) => e as String).toList(),
      lat: json['lat'] as String?,
      lng: json['lng'] as String?,
    );

Map<String, dynamic> _$$GooglePlacePredictionImplToJson(
        _$GooglePlacePredictionImpl instance) =>
    <String, dynamic>{
      'description': instance.description,
      'id': instance.id,
      'matched_substrings':
          instance.matchedSubstrings?.map((e) => e.toJson()).toList(),
      'place_id': instance.placeId,
      'reference': instance.reference,
      'structured_formatting': instance.structuredFormatting?.toJson(),
      'terms': instance.terms?.map((e) => e.toJson()).toList(),
      'types': instance.types,
      'lat': instance.lat,
      'lng': instance.lng,
    };

_$GooglePlace_MatchedSubstringsImpl
    _$$GooglePlace_MatchedSubstringsImplFromJson(Map<String, dynamic> json) =>
        _$GooglePlace_MatchedSubstringsImpl(
          length: (json['length'] as num?)?.toInt(),
          offset: (json['offset'] as num?)?.toInt(),
        );

Map<String, dynamic> _$$GooglePlace_MatchedSubstringsImplToJson(
        _$GooglePlace_MatchedSubstringsImpl instance) =>
    <String, dynamic>{
      'length': instance.length,
      'offset': instance.offset,
    };

_$GooglePlace_StructuredFormattingImpl
    _$$GooglePlace_StructuredFormattingImplFromJson(
            Map<String, dynamic> json) =>
        _$GooglePlace_StructuredFormattingImpl(
          mainText: json['main_text'] as String?,
          secondaryText: json['secondary_text'] as String?,
        );

Map<String, dynamic> _$$GooglePlace_StructuredFormattingImplToJson(
        _$GooglePlace_StructuredFormattingImpl instance) =>
    <String, dynamic>{
      'main_text': instance.mainText,
      'secondary_text': instance.secondaryText,
    };

_$GooglePlace_TermsImpl _$$GooglePlace_TermsImplFromJson(
        Map<String, dynamic> json) =>
    _$GooglePlace_TermsImpl(
      offset: (json['offset'] as num?)?.toInt(),
      value: json['value'] as String?,
    );

Map<String, dynamic> _$$GooglePlace_TermsImplToJson(
        _$GooglePlace_TermsImpl instance) =>
    <String, dynamic>{
      'offset': instance.offset,
      'value': instance.value,
    };
