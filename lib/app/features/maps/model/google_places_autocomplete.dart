import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/features/maps/model/google_places.dart';

part 'google_places_autocomplete.freezed.dart';

@freezed
class GooglePlacesAutocompleteState with _$GooglePlacesAutocompleteState {
  factory GooglePlacesAutocompleteState({
    String? sessionToken,
    @Default([]) List<String> countries,
    @Default([]) List<GooglePlacePrediction> allPredictions,
  }) = _GooglePlacesAutocompleteState;

  // factory GooglePlacesAutocompleteState.fromJson(Json json) =>
  //     _$GooglePlacesAutocompleteStateFromJson(json);
}
