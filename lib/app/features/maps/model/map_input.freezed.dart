// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'map_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MapInput {
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  double get zoom => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MapInputCopyWith<MapInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MapInputCopyWith<$Res> {
  factory $MapInputCopyWith(MapInput value, $Res Function(MapInput) then) =
      _$MapInputCopyWithImpl<$Res, MapInput>;
  @useResult
  $Res call({double latitude, double longitude, double zoom});
}

/// @nodoc
class _$MapInputCopyWithImpl<$Res, $Val extends MapInput>
    implements $MapInputCopyWith<$Res> {
  _$MapInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? zoom = null,
  }) {
    return _then(_value.copyWith(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      zoom: null == zoom
          ? _value.zoom
          : zoom // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MapInputImplCopyWith<$Res>
    implements $MapInputCopyWith<$Res> {
  factory _$$MapInputImplCopyWith(
          _$MapInputImpl value, $Res Function(_$MapInputImpl) then) =
      __$$MapInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double latitude, double longitude, double zoom});
}

/// @nodoc
class __$$MapInputImplCopyWithImpl<$Res>
    extends _$MapInputCopyWithImpl<$Res, _$MapInputImpl>
    implements _$$MapInputImplCopyWith<$Res> {
  __$$MapInputImplCopyWithImpl(
      _$MapInputImpl _value, $Res Function(_$MapInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? zoom = null,
  }) {
    return _then(_$MapInputImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      zoom: null == zoom
          ? _value.zoom
          : zoom // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$MapInputImpl implements _MapInput {
  _$MapInputImpl(
      {required this.latitude, required this.longitude, required this.zoom});

  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final double zoom;

  @override
  String toString() {
    return 'MapInput(latitude: $latitude, longitude: $longitude, zoom: $zoom)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MapInputImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.zoom, zoom) || other.zoom == zoom));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude, zoom);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MapInputImplCopyWith<_$MapInputImpl> get copyWith =>
      __$$MapInputImplCopyWithImpl<_$MapInputImpl>(this, _$identity);
}

abstract class _MapInput implements MapInput {
  factory _MapInput(
      {required final double latitude,
      required final double longitude,
      required final double zoom}) = _$MapInputImpl;

  @override
  double get latitude;
  @override
  double get longitude;
  @override
  double get zoom;
  @override
  @JsonKey(ignore: true)
  _$$MapInputImplCopyWith<_$MapInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MapBoxMapState {
  MapboxMap? get mapController => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  bool get showMarker => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MapBoxMapStateCopyWith<MapBoxMapState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MapBoxMapStateCopyWith<$Res> {
  factory $MapBoxMapStateCopyWith(
          MapBoxMapState value, $Res Function(MapBoxMapState) then) =
      _$MapBoxMapStateCopyWithImpl<$Res, MapBoxMapState>;
  @useResult
  $Res call(
      {MapboxMap? mapController,
      double latitude,
      double longitude,
      bool showMarker});
}

/// @nodoc
class _$MapBoxMapStateCopyWithImpl<$Res, $Val extends MapBoxMapState>
    implements $MapBoxMapStateCopyWith<$Res> {
  _$MapBoxMapStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapController = freezed,
    Object? latitude = null,
    Object? longitude = null,
    Object? showMarker = null,
  }) {
    return _then(_value.copyWith(
      mapController: freezed == mapController
          ? _value.mapController
          : mapController // ignore: cast_nullable_to_non_nullable
              as MapboxMap?,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      showMarker: null == showMarker
          ? _value.showMarker
          : showMarker // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MapBoxMapStateImplCopyWith<$Res>
    implements $MapBoxMapStateCopyWith<$Res> {
  factory _$$MapBoxMapStateImplCopyWith(_$MapBoxMapStateImpl value,
          $Res Function(_$MapBoxMapStateImpl) then) =
      __$$MapBoxMapStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MapboxMap? mapController,
      double latitude,
      double longitude,
      bool showMarker});
}

/// @nodoc
class __$$MapBoxMapStateImplCopyWithImpl<$Res>
    extends _$MapBoxMapStateCopyWithImpl<$Res, _$MapBoxMapStateImpl>
    implements _$$MapBoxMapStateImplCopyWith<$Res> {
  __$$MapBoxMapStateImplCopyWithImpl(
      _$MapBoxMapStateImpl _value, $Res Function(_$MapBoxMapStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapController = freezed,
    Object? latitude = null,
    Object? longitude = null,
    Object? showMarker = null,
  }) {
    return _then(_$MapBoxMapStateImpl(
      mapController: freezed == mapController
          ? _value.mapController
          : mapController // ignore: cast_nullable_to_non_nullable
              as MapboxMap?,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      showMarker: null == showMarker
          ? _value.showMarker
          : showMarker // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MapBoxMapStateImpl implements _MapBoxMapState {
  _$MapBoxMapStateImpl(
      {this.mapController,
      required this.latitude,
      required this.longitude,
      this.showMarker = false});

  @override
  final MapboxMap? mapController;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  @JsonKey()
  final bool showMarker;

  @override
  String toString() {
    return 'MapBoxMapState(mapController: $mapController, latitude: $latitude, longitude: $longitude, showMarker: $showMarker)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MapBoxMapStateImpl &&
            (identical(other.mapController, mapController) ||
                other.mapController == mapController) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.showMarker, showMarker) ||
                other.showMarker == showMarker));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, mapController, latitude, longitude, showMarker);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MapBoxMapStateImplCopyWith<_$MapBoxMapStateImpl> get copyWith =>
      __$$MapBoxMapStateImplCopyWithImpl<_$MapBoxMapStateImpl>(
          this, _$identity);
}

abstract class _MapBoxMapState implements MapBoxMapState {
  factory _MapBoxMapState(
      {final MapboxMap? mapController,
      required final double latitude,
      required final double longitude,
      final bool showMarker}) = _$MapBoxMapStateImpl;

  @override
  MapboxMap? get mapController;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  bool get showMarker;
  @override
  @JsonKey(ignore: true)
  _$$MapBoxMapStateImplCopyWith<_$MapBoxMapStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
