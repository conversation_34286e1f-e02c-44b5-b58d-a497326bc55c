import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/custom_list_tile.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HelpView extends HookConsumerWidget {
  const HelpView({super.key});

  static const routeName = 'help';
  static const routePath = '/help';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [CustomColors.secondary, CustomColors.primaries.shade100],
          begin: Alignment.topLeft,
          end: Alignment.topRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomColors.secondary,
                  CustomColors.primaries.shade100,
                ],
                begin: Alignment.topLeft,
                end: Alignment.topRight,
              ),
            ),
          ),
          iconTheme: const IconThemeData(
            color: CustomColors.primaries,
          ),
          foregroundColor: CustomColors.primaries,
          title: const Text(
            'Help',
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        body: ClipPath(
          clipper: RoundedClipper(),
          child: Container(
            color: Colors.white,
            height: double.infinity,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 32),
                  CustomListTile(
                    leadingIcon: CustomIcon.faq,
                    titleString: 'FAQs',
                    onTap: () {},
                  ),
                  const SizedBox(height: 12),
                  CustomListTile(
                    leadingIcon: CustomIcon.whatsapp,
                    titleString: 'WhatsApp Us',
                    onTap: () {
                      ref.read(
                        sendWhatsAppMessageProvider(
                          'Hi GO!MAMA, I need some help on ',
                          Environment.gomamaWhatsappPhone,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 12),
                  CustomListTile(
                    leadingIcon: CustomIcon.help,
                    titleString: 'Feedback',
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
