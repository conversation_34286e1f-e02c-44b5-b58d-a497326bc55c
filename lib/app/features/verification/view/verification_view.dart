import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/view/profile_view.dart';
import 'package:gomama/app/features/edit_profile/view/selfie_form_view.dart';
import 'package:gomama/app/features/singpass/view/singpass_browser.dart';
import 'package:gomama/app/features/singpass/view/singpass_form_view.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:rive/rive.dart';

class VerificationView extends ConsumerWidget {
  const VerificationView({super.key});

  static const routeName = 'verification';
  static const routePath = '/verification';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _step = ref.watch(
      verificationFlowControllerProvider.select((value) => value.step),
    );

    return StarsBackground(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: _step < 30
              ? Center(
                  child: BackButton(
                    onPressed: () {
                      if (context.canPop()) {
                        context.pop();
                      } else {
                        const ProfileRoute().go(context);
                      }
                    },
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(Colors.white),
                      iconColor: WidgetStateProperty.all(CustomColors.primary),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        ),
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            const _RiveBackground(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 36),
              child: Builder(
                builder: (context) {
                  if (_step == 1) {
                    return const _ChildCheck();
                  }

                  if (_step == 2) {
                    return const _StillLactatingCheck();
                  }

                  if (_step == 3) {
                    return const _NationalityCheck();
                  }

                  if (_step == 4) {
                    return const _SingpassCheck();
                  }

                  if (_step == 5) {
                    return const _SelfieCheck('Verify with Go!Mama.');
                  }

                  if (_step == 6) {
                    return const _SelfieCheck(
                      'For an alternative login option, please verify your identity with a selfie.',
                    );
                  }

                  if (_step == 20) {
                    return const _WhatsappContact();
                  }

                  if (_step == 22) {
                    return const _WhatsappContact(
                      description:
                          'Thank you for your interest. These pods are exclusively for breastfeeding mothers with babies under 36 months. We appreciate your understanding.',
                    );
                  }

                  if (_step == 23) {
                    return const _WhatsappContact(
                      description: """
We apologize for the inconvenience. Singpass is unable to retrieve you or your child's information. Please verify your Singpass details or contact Singpass for assistance in updating them.\n
For an alternative login option, please verify your identity with a selfie.
""",
                    );
                  }

                  if (_step == 25) {
                    return const _WhatsappContact(
                      description:
                          'You have exceeded the allowed number of attempts. Please contact us for assistance.',
                    );
                  }

                  if (_step == 26) {
                    return const _WhatsappContact(
                      description:
                          'We were unable to verify your selfie due to our anti-spoofing measures. Please try again, and if you need assistance, feel free to contact our customer support team.',
                    );
                  }

                  if (_step == 27) {
                    return const _WhatsappContact(
                      description: 'Detection failed, please try again.',
                    );
                  }

                  if (_step == 28) {
                    return const _WhatsappContact(
                      description:
                          'No face is found in the image, please try again.',
                    );
                  }

                  if (_step == 29) {
                    return const _WhatsappContact(
                      description:
                          'More than one face is in the selfie, please try again.',
                    );
                  }

                  if (_step == 30 || _step == 40) {
                    // do nothing
                    return const SizedBox.shrink();
                  }

                  if (_step == 50) {
                    return const _Success();
                  }

                  return const _Error();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ChildCheck extends ConsumerWidget {
  const _ChildCheck();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 32),
        Text(
          'Are you a mother with a child below 36 months old?',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        Row(
          children: [
            BrandButton(
              onPressed: () {
                _controller
                  ..setHasChild36Months(true)
                  ..next();
              },
              child: const Text('Yes'),
            ),
            const Spacer(),
            BrandButton(
              onPressed: () {
                _controller
                  ..setHasChild36Months(false)
                  ..next();
              },
              child: const Text('No'),
            ),
          ],
        ),
      ],
    );
  }
}

class _StillLactatingCheck extends ConsumerWidget {
  const _StillLactatingCheck();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 32),
        Text(
          'Are you continuing to breastfeed your child beyond 36 months of age?',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        Row(
          children: [
            BrandButton(
              onPressed: () {
                _controller
                  ..setStillLactatingAfter36Months(true)
                  ..next();
              },
              child: const Text('Yes'),
            ),
            const Spacer(),
            BrandButton(
              onPressed: () {
                _controller
                  ..setStillLactatingAfter36Months(false)
                  ..next();
              },
              child: const Text('No'),
            ),
          ],
        ),
      ],
    );
  }
}

class _NationalityCheck extends ConsumerWidget {
  const _NationalityCheck();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 32),
        Text(
          'Are you Singaporean,\nPermanent Resident,\nWork or Visitor Pass holder?',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        Row(
          children: [
            BrandButton(
              onPressed: () {
                _controller
                  ..setIsLocal(true)
                  ..next();
              },
              child: const Text('Yes'),
            ),
            const Spacer(),
            BrandButton(
              onPressed: () {
                _controller
                  ..setIsLocal(false)
                  ..next();
              },
              child: const Text('No'),
            ),
          ],
        ),
      ],
    );
  }
}

class _SingpassCheck extends ConsumerWidget {
  const _SingpassCheck();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);
    final _controllerState = ref.watch(verificationFlowControllerProvider);

    return Column(
      children: [
        const SizedBox(height: 32),
        Text(
          'Verify with Singpass',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        BrandButton.singpass(
          padding: const EdgeInsets.symmetric(vertical: 16),
          onPressed: () async {
            if (_controllerState.isLoading) {
              return;
            }

            final _singpassUrl = await _controller.verifyWithSingpass();

            // need to store the reference to the browser
            // to be able to close it in router after successful verification
            await ref.read(singpassBrowserControllerProvider).open(
                  url: WebUri(_singpassUrl),
                  settings: singpassChromeSafariBrowserSettings,
                );

            // NOTE: failed or error state will be handled in router /singpass/myinfo/callback

            Future.delayed(
              const Duration(seconds: 1),
              () => _controller.setIsLoading(false),
            );
          },
          child: _controllerState.isLoading
              ? const Text('Loading...')
              : const Text('Retrieve Myinfo with Singpass'),
        ),
      ],
    );
  }
}

class _SelfieCheck extends ConsumerWidget {
  const _SelfieCheck(this.message);
  final String message;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);
    final _controllerState = ref.watch(verificationFlowControllerProvider);
    final user = ref.watch(authControllerProvider).requireValue;

    ref.listen(verificationFlowControllerProvider, (prev, next) {
      if (prev?.step != next.step && next.step == 40) {
        // TODO(kkcy): selfieImagePath should not be null
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            return SelfieFormView(photoPath: next.selfieImagePath ?? '');
          },
        );
      }
    });

    if (_controllerState.waitingForSelfie) {
      return Column(
        children: [
          const SizedBox(height: 32),
          Text(
            'Verifying with Go!Mama',
            style:
                textTheme(context).titleMedium!.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 32),
          const Center(
            child: SizedBox(
              height: 16,
              width: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    }

    if (user.latestVerifySelfieFailCount! >= Environment.maxSelfieFailTries) {
      return const _WhatsappContact(
        description:
            'You have exceeded the allowed number of attempts. Please contact us for assistance.',
      );
    }

    return Column(
      children: [
        const SizedBox(height: 32),
        Text(
          message,
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        BrandButton(
          padding: const EdgeInsets.symmetric(vertical: 16),
          onPressed: () {
            if (_controllerState.isLoading ||
                _controllerState.waitingForSelfie) {
              return;
            }

            _controller.verifySelfie();
          },
          child: _controllerState.isLoading
              ? const Text('Loading...')
              : const Text('Take a selfie!'),
        ),
      ],
    );
  }
}

class _WhatsappContact extends ConsumerWidget {
  const _WhatsappContact({
    this.description,
  });
  final String? description;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 32),
        if (description != null) ...[
          Text(
            description!,
            style:
                textTheme(context).titleMedium!.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
        ],
        Text(
          'Please contact our customer service for support.',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        BrandButton(
          padding: const EdgeInsets.symmetric(vertical: 16),
          onPressed: () {
            ref.read(
              sendWhatsAppMessageProvider(
                'Hi GO!MAMA, I need some help on ',
                Environment.gomamaWhatsappPhone,
              ),
            );
          },
          child: const Text('Support'),
        ),
      ],
    );
  }
}

class _Success extends ConsumerWidget {
  const _Success();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 32),
        Text(
          'Enjoy your\nexperience Mummy!',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        BrandButton(
          padding: const EdgeInsets.symmetric(vertical: 16),
          onPressed: () {
            context.pop();
          },
          child: const Text('Yay!'),
        ),
      ],
    );
  }
}

class _Error extends ConsumerWidget {
  const _Error();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 32),
        Text(
          'Sorry, you do not meet the criteria for automated clearance. Please contact our customer service for support.',
          style: textTheme(context).titleMedium!.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 32),
        BrandButton(
          padding: const EdgeInsets.symmetric(vertical: 16),
          onPressed: () {
            ref.read(
              sendWhatsAppMessageProvider(
                'Hi GO!MAMA, I need some help on ',
                Environment.gomamaWhatsappPhone,
              ),
            );
          },
          child: const Text('Support'),
        ),
      ],
    );
  }
}

class _RiveBackground extends HookConsumerWidget {
  const _RiveBackground();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = useState<StateMachineController?>(null);
    final _yesStep0 = useState<SMITrigger?>(null);
    final _yesStep1 = useState<SMITrigger?>(null);
    final _yesStep2 = useState<SMITrigger?>(null);
    final _noStep0 = useState<SMITrigger?>(null);
    final _noStep1 = useState<SMITrigger?>(null);
    final _noStep2 = useState<SMITrigger?>(null);

    void _onRiveInit(Artboard artboard) {
      _controller.value = StateMachineController.fromArtboard(
        artboard,
        'State Machine 1',
      );

      if (_controller.value != null) {
        artboard.addController(_controller.value!);

        // TODO(kkcy): unable to find input with name
        for (final input in _controller.value!.inputs) {
          switch (input.name) {
            case 'Yes Step 0':
              _yesStep0.value = input as SMITrigger;
              break;
            case 'Yes Step 1':
              _yesStep1.value = input as SMITrigger;
              break;
            case 'Yes Step 2':
              _yesStep2.value = input as SMITrigger;
              break;
            case 'No Step 0':
              _noStep0.value = input as SMITrigger;
              break;
            case 'No Step 1':
              _noStep1.value = input as SMITrigger;
              break;
            case 'No Step 2':
              _noStep2.value = input as SMITrigger;
              break;
          }
        }
      }
    }

    ref.listen(
        verificationFlowControllerProvider.select(
          (value) => value.step,
        ), (prev, next) {
      switch (next) {
        case 1:
          break;
        case 2:
          _yesStep0.value?.fire();
          break;
        case 3:
          _yesStep0.value?.fire();
          break;
        case 4:
        case 5:
          _yesStep1.value?.fire();
          _yesStep1.value?.fire();
          break;
        case 6:
          _yesStep1.value?.fire();
          break;
        case 30:
        case 40:
        case 50:
          _yesStep2.value?.fire();
          break;
        case 20:
        case 21:
        case 22:
        default:
          _noStep0.value?.fire();
          _noStep1.value?.fire();
          _noStep2.value?.fire();
          break;
      }
    });

    return RiveAnimation.asset(
      'assets/rives/verify_access.riv',
      fit: BoxFit.cover,
      alignment: Alignment.center,
      onInit: _onRiveInit,
    );
  }
}
