// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verification_flow.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VerificationFlowState {
  int get step => throw _privateConstructorUsedError; // 1 child check
// 2 nationality check
// 3 singpass check
// 4 selfie check
// 20 whatsapp
// 21 error
// 22 no children less than 3 years old
// 30 singpass loading
// 40 selfie loading
// 50 success
  bool? get hasChild36Months => throw _privateConstructorUsedError;
  bool? get stillLactatingAfter36Months => throw _privateConstructorUsedError;
  bool? get isLocal => throw _privateConstructorUsedError;
  bool? get isSelfieVerified => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get waitingForSelfie => throw _privateConstructorUsedError;
  String? get selfieImagePath => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $VerificationFlowStateCopyWith<VerificationFlowState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationFlowStateCopyWith<$Res> {
  factory $VerificationFlowStateCopyWith(VerificationFlowState value,
          $Res Function(VerificationFlowState) then) =
      _$VerificationFlowStateCopyWithImpl<$Res, VerificationFlowState>;
  @useResult
  $Res call(
      {int step,
      bool? hasChild36Months,
      bool? stillLactatingAfter36Months,
      bool? isLocal,
      bool? isSelfieVerified,
      bool isLoading,
      bool waitingForSelfie,
      String? selfieImagePath});
}

/// @nodoc
class _$VerificationFlowStateCopyWithImpl<$Res,
        $Val extends VerificationFlowState>
    implements $VerificationFlowStateCopyWith<$Res> {
  _$VerificationFlowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? hasChild36Months = freezed,
    Object? stillLactatingAfter36Months = freezed,
    Object? isLocal = freezed,
    Object? isSelfieVerified = freezed,
    Object? isLoading = null,
    Object? waitingForSelfie = null,
    Object? selfieImagePath = freezed,
  }) {
    return _then(_value.copyWith(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as int,
      hasChild36Months: freezed == hasChild36Months
          ? _value.hasChild36Months
          : hasChild36Months // ignore: cast_nullable_to_non_nullable
              as bool?,
      stillLactatingAfter36Months: freezed == stillLactatingAfter36Months
          ? _value.stillLactatingAfter36Months
          : stillLactatingAfter36Months // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLocal: freezed == isLocal
          ? _value.isLocal
          : isLocal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelfieVerified: freezed == isSelfieVerified
          ? _value.isSelfieVerified
          : isSelfieVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      waitingForSelfie: null == waitingForSelfie
          ? _value.waitingForSelfie
          : waitingForSelfie // ignore: cast_nullable_to_non_nullable
              as bool,
      selfieImagePath: freezed == selfieImagePath
          ? _value.selfieImagePath
          : selfieImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerificationFlowStateImplCopyWith<$Res>
    implements $VerificationFlowStateCopyWith<$Res> {
  factory _$$VerificationFlowStateImplCopyWith(
          _$VerificationFlowStateImpl value,
          $Res Function(_$VerificationFlowStateImpl) then) =
      __$$VerificationFlowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int step,
      bool? hasChild36Months,
      bool? stillLactatingAfter36Months,
      bool? isLocal,
      bool? isSelfieVerified,
      bool isLoading,
      bool waitingForSelfie,
      String? selfieImagePath});
}

/// @nodoc
class __$$VerificationFlowStateImplCopyWithImpl<$Res>
    extends _$VerificationFlowStateCopyWithImpl<$Res,
        _$VerificationFlowStateImpl>
    implements _$$VerificationFlowStateImplCopyWith<$Res> {
  __$$VerificationFlowStateImplCopyWithImpl(_$VerificationFlowStateImpl _value,
      $Res Function(_$VerificationFlowStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? step = null,
    Object? hasChild36Months = freezed,
    Object? stillLactatingAfter36Months = freezed,
    Object? isLocal = freezed,
    Object? isSelfieVerified = freezed,
    Object? isLoading = null,
    Object? waitingForSelfie = null,
    Object? selfieImagePath = freezed,
  }) {
    return _then(_$VerificationFlowStateImpl(
      step: null == step
          ? _value.step
          : step // ignore: cast_nullable_to_non_nullable
              as int,
      hasChild36Months: freezed == hasChild36Months
          ? _value.hasChild36Months
          : hasChild36Months // ignore: cast_nullable_to_non_nullable
              as bool?,
      stillLactatingAfter36Months: freezed == stillLactatingAfter36Months
          ? _value.stillLactatingAfter36Months
          : stillLactatingAfter36Months // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLocal: freezed == isLocal
          ? _value.isLocal
          : isLocal // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelfieVerified: freezed == isSelfieVerified
          ? _value.isSelfieVerified
          : isSelfieVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      waitingForSelfie: null == waitingForSelfie
          ? _value.waitingForSelfie
          : waitingForSelfie // ignore: cast_nullable_to_non_nullable
              as bool,
      selfieImagePath: freezed == selfieImagePath
          ? _value.selfieImagePath
          : selfieImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$VerificationFlowStateImpl implements _VerificationFlowState {
  _$VerificationFlowStateImpl(
      {this.step = 1,
      this.hasChild36Months,
      this.stillLactatingAfter36Months,
      this.isLocal,
      this.isSelfieVerified,
      this.isLoading = false,
      this.waitingForSelfie = false,
      this.selfieImagePath});

  @override
  @JsonKey()
  final int step;
// 1 child check
// 2 nationality check
// 3 singpass check
// 4 selfie check
// 20 whatsapp
// 21 error
// 22 no children less than 3 years old
// 30 singpass loading
// 40 selfie loading
// 50 success
  @override
  final bool? hasChild36Months;
  @override
  final bool? stillLactatingAfter36Months;
  @override
  final bool? isLocal;
  @override
  final bool? isSelfieVerified;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool waitingForSelfie;
  @override
  final String? selfieImagePath;

  @override
  String toString() {
    return 'VerificationFlowState(step: $step, hasChild36Months: $hasChild36Months, stillLactatingAfter36Months: $stillLactatingAfter36Months, isLocal: $isLocal, isSelfieVerified: $isSelfieVerified, isLoading: $isLoading, waitingForSelfie: $waitingForSelfie, selfieImagePath: $selfieImagePath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationFlowStateImpl &&
            (identical(other.step, step) || other.step == step) &&
            (identical(other.hasChild36Months, hasChild36Months) ||
                other.hasChild36Months == hasChild36Months) &&
            (identical(other.stillLactatingAfter36Months,
                    stillLactatingAfter36Months) ||
                other.stillLactatingAfter36Months ==
                    stillLactatingAfter36Months) &&
            (identical(other.isLocal, isLocal) || other.isLocal == isLocal) &&
            (identical(other.isSelfieVerified, isSelfieVerified) ||
                other.isSelfieVerified == isSelfieVerified) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.waitingForSelfie, waitingForSelfie) ||
                other.waitingForSelfie == waitingForSelfie) &&
            (identical(other.selfieImagePath, selfieImagePath) ||
                other.selfieImagePath == selfieImagePath));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      step,
      hasChild36Months,
      stillLactatingAfter36Months,
      isLocal,
      isSelfieVerified,
      isLoading,
      waitingForSelfie,
      selfieImagePath);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationFlowStateImplCopyWith<_$VerificationFlowStateImpl>
      get copyWith => __$$VerificationFlowStateImplCopyWithImpl<
          _$VerificationFlowStateImpl>(this, _$identity);
}

abstract class _VerificationFlowState implements VerificationFlowState {
  factory _VerificationFlowState(
      {final int step,
      final bool? hasChild36Months,
      final bool? stillLactatingAfter36Months,
      final bool? isLocal,
      final bool? isSelfieVerified,
      final bool isLoading,
      final bool waitingForSelfie,
      final String? selfieImagePath}) = _$VerificationFlowStateImpl;

  @override
  int get step;
  @override // 1 child check
// 2 nationality check
// 3 singpass check
// 4 selfie check
// 20 whatsapp
// 21 error
// 22 no children less than 3 years old
// 30 singpass loading
// 40 selfie loading
// 50 success
  bool? get hasChild36Months;
  @override
  bool? get stillLactatingAfter36Months;
  @override
  bool? get isLocal;
  @override
  bool? get isSelfieVerified;
  @override
  bool get isLoading;
  @override
  bool get waitingForSelfie;
  @override
  String? get selfieImagePath;
  @override
  @JsonKey(ignore: true)
  _$$VerificationFlowStateImplCopyWith<_$VerificationFlowStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
