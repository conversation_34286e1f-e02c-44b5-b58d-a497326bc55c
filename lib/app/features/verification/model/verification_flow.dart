import 'package:freezed_annotation/freezed_annotation.dart';

part 'verification_flow.freezed.dart';

@freezed
class VerificationFlowState with _$VerificationFlowState {
  factory VerificationFlowState({
    @Default(1) int step,
    // 1 child check
    // 2 nationality check
    // 3 singpass check
    // 4 selfie check
    // 20 whatsapp
    // 21 error
    // 22 no children less than 3 years old
    // 30 singpass loading
    // 40 selfie loading
    // 50 success
    bool? hasChild36Months,
    bool? stillLactatingAfter36Months,
    bool? isLocal,
    bool? isSelfieVerified,
    @Default(false) bool isLoading,
    @Default(false) bool waitingForSelfie,
    String? selfieImagePath,
  }) = _VerificationFlowState;
}
