// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$singpassBrowserControllerHash() =>
    r'65bb299fd85faf382f5403f9c3f3e900564dd868';

/// See also [singpassBrowserController].
@ProviderFor(singpassBrowserController)
final singpassBrowserControllerProvider = Provider<SingpassBrowser>.internal(
  singpassBrowserController,
  name: r'singpassBrowserControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$singpassBrowserControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SingpassBrowserControllerRef = ProviderRef<SingpassBrowser>;
String _$sendWhatsAppMessageHash() =>
    r'b29a74ebab9c51dda19826d95b45f04d37f0f63c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [sendWhatsAppMessage].
@ProviderFor(sendWhatsAppMessage)
const sendWhatsAppMessageProvider = SendWhatsAppMessageFamily();

/// See also [sendWhatsAppMessage].
class SendWhatsAppMessageFamily extends Family<AsyncValue<void>> {
  /// See also [sendWhatsAppMessage].
  const SendWhatsAppMessageFamily();

  /// See also [sendWhatsAppMessage].
  SendWhatsAppMessageProvider call(
    String text,
    String fullMobileNumber,
  ) {
    return SendWhatsAppMessageProvider(
      text,
      fullMobileNumber,
    );
  }

  @override
  SendWhatsAppMessageProvider getProviderOverride(
    covariant SendWhatsAppMessageProvider provider,
  ) {
    return call(
      provider.text,
      provider.fullMobileNumber,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sendWhatsAppMessageProvider';
}

/// See also [sendWhatsAppMessage].
class SendWhatsAppMessageProvider extends AutoDisposeFutureProvider<void> {
  /// See also [sendWhatsAppMessage].
  SendWhatsAppMessageProvider(
    String text,
    String fullMobileNumber,
  ) : this._internal(
          (ref) => sendWhatsAppMessage(
            ref as SendWhatsAppMessageRef,
            text,
            fullMobileNumber,
          ),
          from: sendWhatsAppMessageProvider,
          name: r'sendWhatsAppMessageProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$sendWhatsAppMessageHash,
          dependencies: SendWhatsAppMessageFamily._dependencies,
          allTransitiveDependencies:
              SendWhatsAppMessageFamily._allTransitiveDependencies,
          text: text,
          fullMobileNumber: fullMobileNumber,
        );

  SendWhatsAppMessageProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.text,
    required this.fullMobileNumber,
  }) : super.internal();

  final String text;
  final String fullMobileNumber;

  @override
  Override overrideWith(
    FutureOr<void> Function(SendWhatsAppMessageRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SendWhatsAppMessageProvider._internal(
        (ref) => create(ref as SendWhatsAppMessageRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        text: text,
        fullMobileNumber: fullMobileNumber,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _SendWhatsAppMessageProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SendWhatsAppMessageProvider &&
        other.text == text &&
        other.fullMobileNumber == fullMobileNumber;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, text.hashCode);
    hash = _SystemHash.combine(hash, fullMobileNumber.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SendWhatsAppMessageRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `text` of this provider.
  String get text;

  /// The parameter `fullMobileNumber` of this provider.
  String get fullMobileNumber;
}

class _SendWhatsAppMessageProviderElement
    extends AutoDisposeFutureProviderElement<void> with SendWhatsAppMessageRef {
  _SendWhatsAppMessageProviderElement(super.provider);

  @override
  String get text => (origin as SendWhatsAppMessageProvider).text;
  @override
  String get fullMobileNumber =>
      (origin as SendWhatsAppMessageProvider).fullMobileNumber;
}

String _$verificationFlowControllerHash() =>
    r'd212d228e897a2e6e49031b8e2c13df5ddb60997';

/// See also [VerificationFlowController].
@ProviderFor(VerificationFlowController)
final verificationFlowControllerProvider = AutoDisposeNotifierProvider<
    VerificationFlowController, VerificationFlowState>.internal(
  VerificationFlowController.new,
  name: r'verificationFlowControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$verificationFlowControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VerificationFlowController
    = AutoDisposeNotifier<VerificationFlowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
