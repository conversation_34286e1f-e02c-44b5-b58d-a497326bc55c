// ignore_for_file: avoid_positional_boolean_parameters

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/auth/repository/user_repository.dart';
import 'package:gomama/app/features/edit_profile/model/selfie_verification.dart';
import 'package:gomama/app/features/edit_profile/model/singpass_verification.dart';
import 'package:gomama/app/features/singpass/view/singpass_browser.dart';
import 'package:gomama/app/features/verification/model/verification_flow.dart';
import 'package:groveman/groveman.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

part 'verification_providers.g.dart';

@Riverpod(keepAlive: true)
SingpassBrowser singpassBrowserController(SingpassBrowserControllerRef ref) {
  return SingpassBrowser();
}

@Riverpod()
class VerificationFlowController extends _$VerificationFlowController {
  @override
  VerificationFlowState build() {
    return VerificationFlowState();
  }

  void toSingpass() {
    state = state.copyWith(step: 3);
  }

  void toSelfie() {
    state = state.copyWith(step: 4);
  }

  void toSingpassError(int errorStep) {
    state = state.copyWith(step: errorStep);
  }

  void prev() {
    if (state.step == 4) {
      state = state.copyWith(step: 2);
    } else {
      state = state.copyWith(step: state.step - 1);
    }
  }

  void next() {
    if (state.step == 1 && state.hasChild36Months == false) {
      state = state.copyWith(step: 2);
      return;
    }

    if (state.step == 2 && state.stillLactatingAfter36Months == true) {
      state = state.copyWith(step: 6);
      return;
    }

    if (state.step == 2 && state.stillLactatingAfter36Months == false) {
      state = state.copyWith(step: 21);
      return;
    }

    if (state.step == 1 && state.hasChild36Months == true) {
      state = state.copyWith(step: 3);
      return;
    }

    if (state.step == 3 && state.isLocal == true) {
      state = state.copyWith(step: 4);
      return;
    }

    if (state.step == 3 && state.isLocal == false) {
      state = state.copyWith(step: 5);
      return;
    }
  }

  void setHasChild36Months(bool value) {
    state = state.copyWith(hasChild36Months: value);
  }

  void setStillLactatingAfter36Months(bool value) {
    state = state.copyWith(stillLactatingAfter36Months: value);
  }

  void setIsLocal(bool value) {
    state = state.copyWith(isLocal: value);
  }

  void setIsLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setError() {
    state = state.copyWith(step: 20);
  }

  Future<String> verifyWithSingpass() async {
    state = state.copyWith(isLoading: true);

    // 1. initialize singpass
    final url = await ref.read(userRepositoryProvider).singpassInitialize();

    // state = state.copyWith(isLoading: false);

    // 2. open a webview to singpass
    return url;

    // final _authSingpassProvider = ref.read(authSingpassProvider)
    //   ..browserExitCallback = () {
    //     state = state.copyWith(isLoading: false);
    //   };
    // _authSingpassProvider.currentRoute = RouteConstants.profile;
    // _authSingpassProvider.previousRoute = RouteConstants.profile;
    // await _authSingpassProvider.verify();

    // TODO(kkcy): go to singpass form
    // final param = photo.path.replaceAll('/', '@');
    // final _router = AutoRouter.of(context);
    // await _router.pushNamed(
    //   '/auth-singpass-form/$param',
    // );
  }

  Future<void> verifySelfie() async {
    state = state.copyWith(isLoading: true);
    var cv2ErrorCode = '';
    // 0. ask for camera permission first
    await Permission.camera.request();

    try {
      // 1. get a selfie
      final _picker = ImagePicker();
      final photo = await _picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
        maxWidth: 300,
      );

      // 2. send selfie to gtriip
      if (photo != null) {
        final photoFile = File(photo.path);
        final headers = {
          'x-api-key': Environment.selfieVerifyApiKey,
        };
        final formData = FormData.fromMap({
          'pic_file': await MultipartFile.fromFile(photoFile.path),
        });

        state = state.copyWith(waitingForSelfie: true);

        final token = CancelToken();

        /// NOTE: wait for 30s max
        final timer = Timer(const Duration(seconds: 30), () {
          Groveman.debug('selfie waiting', error: 'cancel req');
          token.cancel();
          state = state.copyWith(step: 20);
        });

        final response = await ref.read(repositoryProvider).multipartPost(
              Environment.selfieVerifyApiUrl,
              data: formData,
              options: Options(headers: headers),
              cancelToken: token,
            );

        if (response.statusCode == 200) {
          final genderResponse = response.data as Json;
          Groveman.info('gender', error: genderResponse);

          if (genderResponse['success'] == true) {
            if (genderResponse['gender'] == 'Male') {
              Groveman.info('gender', error: 'Male');
              cv2ErrorCode = 'detect_male';
              // 3. block male
              state = state.copyWith(step: 22);
            } else if (genderResponse['gender'] == 'Female') {
              Groveman.info('gender', error: 'Female');

              // 3. female grant access;
              state = state.copyWith(
                step: 40,
                selfieImagePath: photo.path,
              );
            }
          }
        } else {
          // try again
          Groveman.info('selfie failed');
          state = state.copyWith(step: 20);
        }

        timer.cancel();
      } else {
        cv2ErrorCode = 'no_photo';
        Groveman.debug('selfie failed', error: 'No photo');
      }
    } catch (e, s) {
      Groveman.warning(
        'verifySelfie',
        error: e,
        stackTrace: s,
        extra: {
          'cv2ErrorCode': cv2ErrorCode,
        },
      );

      cv2ErrorCode = (e as dynamic).data['error_code'].toString();
    }

    if (cv2ErrorCode != '') {
      ref.read(
        addSelfieFailCountProvider,
      ); // cv2 has error, considered fail, request backend to add fail counter.
    }

    switch (cv2ErrorCode) {
      case '053':
        state = state.copyWith(step: 26);
        break;
      case '071':
        state = state.copyWith(step: 27);
        break;
      case '072':
        state = state.copyWith(step: 28);
        break;
      case '073':
        state = state.copyWith(step: 29);
        break;
      default:
        if (cv2ErrorCode != '') {
          state = state.copyWith(step: 51);
        }
        break;
    }

    state = state.copyWith(isLoading: false, waitingForSelfie: false);
  }

  Future<void> submitGomamaVerification(Json data, String photoPath) async {
    try {
      var input = SelfieVerificationInput(
        email: data['email_address'] as String,
        firstName: data['first_name'] as String,
        lastName: data['last_name'] as String,
        finOrPassport: data['fin_or_passport'] as String,
        birthday: DateTime.parse(data['birthday'] as String),
        childrenBirthdays: data['children_birthdays'] as List<DateTime>,
        mobileNumber: (data['country_code'] as String).trim() +
            (data['mobile_number'] as String),
        gender: (data['gender'] as String).toLowerCase(),
      );

      if (photoPath.isNotEmpty) {
        final selfie = File(photoPath);

        input = input.copyWith(
          userSelfieFile: selfie,
          userSelfieFileCopy: selfie,
        );
      }

      final response =
          await ref.read(userRepositoryProvider).gomamaVerify(input);

      processUserResponse(
        response,
        input.gender,
        null,
        input.childrenBirthdays,
      );
    } catch (error, stackTrace) {
      Groveman.warning(
        'submitGomamaVerification',
        error: error,
        stackTrace: stackTrace,
      );

      state = state.copyWith(step: 20);
    }
  }

  Future<void> submitSingpassVerification(Json data) async {
    try {
      // ignore: omit_local_variable_types
      final Json transformedData = {};

      // transform children_birthdays_$index to children_birthdays
      data.forEach((key, value) {
        // Check if the key starts with 'child_birthday_'
        if (key.startsWith('children_birthdays_')) {
          // Extract the index from the key
          int.parse(key.split('_').last);

          // Initialize the 'children_birthdays' list if it doesn't exist
          if (!transformedData.containsKey('children_birthdays')) {
            transformedData['children_birthdays'] = <String>[];
          }

          // Add the value to the 'children_birthdays' list
          transformedData['children_birthdays'].add(value);
        } else {
          // Copy other keys and values to the new Map
          transformedData[key] = value;
        }
      });

      // child_birthday / children_birthdays process
      if (transformedData['children_birthdays'] == null) {
        transformedData.remove('children_birthdays');
      }

      if (transformedData['child_birthday'] == null ||
          transformedData['child_birthday'] == '') {
        transformedData.remove('child_birthday');
      }

      // full name process
      final spaceIndex = (transformedData['full_name'] as String).indexOf(' ');
      if (spaceIndex == -1) {
        // if there's no space, consider the entire string as the first name
        transformedData['first_name'] = transformedData['full_name'];
        transformedData['last_name'] = '';
      } else {
        final firstName = (transformedData['full_name'] as String)
            .substring(0, spaceIndex)
            .trim();
        final lastName = (transformedData['full_name'] as String)
            .substring(spaceIndex + 1)
            .trim();

        transformedData['first_name'] = firstName;
        transformedData['last_name'] = lastName;
      }
      transformedData.remove('full_name');

      // Gender process
      transformedData['gender'] =
          (transformedData['gender'] as String).toLowerCase();

      // Singpass might return empty children birthdays, this condition checking prevent submit SingpassVerificationInput parsing error.
      if (transformedData['children_birthdays'] != null &&
          (transformedData['children_birthdays'] as List<dynamic>).isNotEmpty) {
        for (final birthday
            in transformedData['children_birthdays'] as List<dynamic>) {
          if (birthday == '') {
            // ignore this entry if we have another child that is valid
            if ((transformedData['children_birthdays'] as List<dynamic>)
                .any((testBirthday) => testBirthday != '')) {
              continue;
            }

            state = state.copyWith(step: 23);
            return;
          }
        }
      }

      if (transformedData['child_birthday'] != null &&
          transformedData['child_birthday'] == '') {
        state = state.copyWith(step: 23);
        return;
      }

      final input = SingpassVerificationInput.fromJson(transformedData);
      Groveman.debug('singpassVerify', error: input);

      final response =
          await ref.watch(userRepositoryProvider).singpassVerify(input);

      processUserResponse(
        response,
        input.gender,
        input.childBirthday,
        input.childrenBirthdays,
      );
    } catch (error, stackTrace) {
      Groveman.warning(
        'submitSingpassVerification',
        error: error,
        stackTrace: stackTrace,
      );

      state = state.copyWith(step: 20);
    }
  }

  void processUserResponse(
    PostResponse<User> response,
    String gender,
    DateTime? childBirthday,
    List<DateTime>? childrenBirthdays,
  ) {
    if (!response.success) {
      // backend response failed
      state = state.copyWith(step: 20);
      return;
    }

    ref.read(authControllerProvider.notifier).setUser(response.data);

    // find if user's youngest child is less than 3 years old
    var youngestChildBirthdate = childBirthday;
    final userGender = gender;

    if (userGender != 'female') {
      state = state.copyWith(step: 22);
      return;
    }

    if (childrenBirthdays != null) {
      youngestChildBirthdate = childrenBirthdays
          .reduce((current, next) => current.isAfter(next) ? current : next);
    }

    if (youngestChildBirthdate == null) {
      // technically this should not be triggered because if date returned by Singpass is empty string.
      // earlier if condition already checked that.
      // no child -> failed
      state = state.copyWith(step: 23);
      return;
    }

    if (DateTime(
      youngestChildBirthdate.year + 3,
      youngestChildBirthdate.month,
      youngestChildBirthdate.day,
    ).isBefore(DateTime.now())) {
      // child is older than 3 years old -> failed
      state = state.copyWith(step: 22);
    } else {
      state = state.copyWith(step: 50);
    }
  }
}

@riverpod
FutureOr<void> sendWhatsAppMessage(
  SendWhatsAppMessageRef ref,
  String text,
  String fullMobileNumber,
) async {
  final _url =
      Uri.parse('https://wa.me/$fullMobileNumber?text=${Uri.parse(text)}');

  try {
    if (await canLaunchUrl(_url)) {
      await launchUrl(_url);
    } else {
      if (Platform.isIOS) {
        await launchUrlString('sms:$fullMobileNumber&body=${Uri.parse(text)}');
        return;
      }

      await launchUrlString('sms:$fullMobileNumber?body=${Uri.parse(text)}');
    }
  } catch (e, s) {
    Groveman.warning('sendWhatsAppMessage', error: e, stackTrace: s);
  }
}
