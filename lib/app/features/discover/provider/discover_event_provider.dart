import 'package:gomama/app/features/discover/model/discover_event.dart';
import 'package:gomama/app/features/discover/repository/discover_event_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'discover_event_provider.g.dart';

@riverpod
Future<List<DiscoverEvent>?> discoverEvents(
  DiscoverEventsRef ref, {
  EventFilter? filter,
}) async {
  try {
    final response =
        await ref.watch(discoverEventRepositoryProvider).fetchEvents(
              filter: filter,
            );

    return response.data;
  } catch (e) {
    Groveman.error('discoverEvents', error: e);
    return null;
  }
}

@riverpod
Future<DiscoverEvent?> discoverEvent(
  DiscoverEventRef ref,
  String eventId,
) async {
  try {
    final response =
        await ref.watch(discoverEventRepositoryProvider).fetchEvent(eventId);

    return response;
  } catch (e) {
    Groveman.error('discoverEvent', error: e);
    return null;
  }
}
