// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_event_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$discoverEventsHash() => r'e9825f25f05ee55ecbea0ac1ceac1658cc1ff78d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [discoverEvents].
@ProviderFor(discoverEvents)
const discoverEventsProvider = DiscoverEventsFamily();

/// See also [discoverEvents].
class DiscoverEventsFamily extends Family<AsyncValue<List<DiscoverEvent>?>> {
  /// See also [discoverEvents].
  const DiscoverEventsFamily();

  /// See also [discoverEvents].
  DiscoverEventsProvider call({
    EventFilter? filter,
  }) {
    return DiscoverEventsProvider(
      filter: filter,
    );
  }

  @override
  DiscoverEventsProvider getProviderOverride(
    covariant DiscoverEventsProvider provider,
  ) {
    return call(
      filter: provider.filter,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'discoverEventsProvider';
}

/// See also [discoverEvents].
class DiscoverEventsProvider
    extends AutoDisposeFutureProvider<List<DiscoverEvent>?> {
  /// See also [discoverEvents].
  DiscoverEventsProvider({
    EventFilter? filter,
  }) : this._internal(
          (ref) => discoverEvents(
            ref as DiscoverEventsRef,
            filter: filter,
          ),
          from: discoverEventsProvider,
          name: r'discoverEventsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$discoverEventsHash,
          dependencies: DiscoverEventsFamily._dependencies,
          allTransitiveDependencies:
              DiscoverEventsFamily._allTransitiveDependencies,
          filter: filter,
        );

  DiscoverEventsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.filter,
  }) : super.internal();

  final EventFilter? filter;

  @override
  Override overrideWith(
    FutureOr<List<DiscoverEvent>?> Function(DiscoverEventsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DiscoverEventsProvider._internal(
        (ref) => create(ref as DiscoverEventsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        filter: filter,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<DiscoverEvent>?> createElement() {
    return _DiscoverEventsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DiscoverEventsProvider && other.filter == filter;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, filter.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DiscoverEventsRef on AutoDisposeFutureProviderRef<List<DiscoverEvent>?> {
  /// The parameter `filter` of this provider.
  EventFilter? get filter;
}

class _DiscoverEventsProviderElement
    extends AutoDisposeFutureProviderElement<List<DiscoverEvent>?>
    with DiscoverEventsRef {
  _DiscoverEventsProviderElement(super.provider);

  @override
  EventFilter? get filter => (origin as DiscoverEventsProvider).filter;
}

String _$discoverEventHash() => r'0466fc34fe59ebf1469cb7535c8e52cf36e67cf2';

/// See also [discoverEvent].
@ProviderFor(discoverEvent)
const discoverEventProvider = DiscoverEventFamily();

/// See also [discoverEvent].
class DiscoverEventFamily extends Family<AsyncValue<DiscoverEvent?>> {
  /// See also [discoverEvent].
  const DiscoverEventFamily();

  /// See also [discoverEvent].
  DiscoverEventProvider call(
    String eventId,
  ) {
    return DiscoverEventProvider(
      eventId,
    );
  }

  @override
  DiscoverEventProvider getProviderOverride(
    covariant DiscoverEventProvider provider,
  ) {
    return call(
      provider.eventId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'discoverEventProvider';
}

/// See also [discoverEvent].
class DiscoverEventProvider extends AutoDisposeFutureProvider<DiscoverEvent?> {
  /// See also [discoverEvent].
  DiscoverEventProvider(
    String eventId,
  ) : this._internal(
          (ref) => discoverEvent(
            ref as DiscoverEventRef,
            eventId,
          ),
          from: discoverEventProvider,
          name: r'discoverEventProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$discoverEventHash,
          dependencies: DiscoverEventFamily._dependencies,
          allTransitiveDependencies:
              DiscoverEventFamily._allTransitiveDependencies,
          eventId: eventId,
        );

  DiscoverEventProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final String eventId;

  @override
  Override overrideWith(
    FutureOr<DiscoverEvent?> Function(DiscoverEventRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DiscoverEventProvider._internal(
        (ref) => create(ref as DiscoverEventRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<DiscoverEvent?> createElement() {
    return _DiscoverEventProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DiscoverEventProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DiscoverEventRef on AutoDisposeFutureProviderRef<DiscoverEvent?> {
  /// The parameter `eventId` of this provider.
  String get eventId;
}

class _DiscoverEventProviderElement
    extends AutoDisposeFutureProviderElement<DiscoverEvent?>
    with DiscoverEventRef {
  _DiscoverEventProviderElement(super.provider);

  @override
  String get eventId => (origin as DiscoverEventProvider).eventId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
