// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'discover_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DiscoverEvent _$DiscoverEventFromJson(Map<String, dynamic> json) {
  return _DiscoverEvent.fromJson(json);
}

/// @nodoc
mixin _$DiscoverEvent {
  int get id => throw _privateConstructorUsedError;
  String? get thumbnail => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  EventFilter? get contentTag => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DiscoverEventCopyWith<DiscoverEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverEventCopyWith<$Res> {
  factory $DiscoverEventCopyWith(
          DiscoverEvent value, $Res Function(DiscoverEvent) then) =
      _$DiscoverEventCopyWithImpl<$Res, DiscoverEvent>;
  @useResult
  $Res call(
      {int id,
      String? thumbnail,
      String? title,
      String? description,
      String? content,
      EventFilter? contentTag,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$DiscoverEventCopyWithImpl<$Res, $Val extends DiscoverEvent>
    implements $DiscoverEventCopyWith<$Res> {
  _$DiscoverEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? thumbnail = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? content = freezed,
    Object? contentTag = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentTag: freezed == contentTag
          ? _value.contentTag
          : contentTag // ignore: cast_nullable_to_non_nullable
              as EventFilter?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DiscoverEventImplCopyWith<$Res>
    implements $DiscoverEventCopyWith<$Res> {
  factory _$$DiscoverEventImplCopyWith(
          _$DiscoverEventImpl value, $Res Function(_$DiscoverEventImpl) then) =
      __$$DiscoverEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? thumbnail,
      String? title,
      String? description,
      String? content,
      EventFilter? contentTag,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$DiscoverEventImplCopyWithImpl<$Res>
    extends _$DiscoverEventCopyWithImpl<$Res, _$DiscoverEventImpl>
    implements _$$DiscoverEventImplCopyWith<$Res> {
  __$$DiscoverEventImplCopyWithImpl(
      _$DiscoverEventImpl _value, $Res Function(_$DiscoverEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? thumbnail = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? content = freezed,
    Object? contentTag = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$DiscoverEventImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentTag: freezed == contentTag
          ? _value.contentTag
          : contentTag // ignore: cast_nullable_to_non_nullable
              as EventFilter?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$DiscoverEventImpl implements _DiscoverEvent {
  _$DiscoverEventImpl(
      {required this.id,
      this.thumbnail,
      this.title,
      this.description,
      this.content,
      this.contentTag,
      this.createdAt,
      this.updatedAt});

  factory _$DiscoverEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiscoverEventImplFromJson(json);

  @override
  final int id;
  @override
  final String? thumbnail;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final String? content;
  @override
  final EventFilter? contentTag;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'DiscoverEvent(id: $id, thumbnail: $thumbnail, title: $title, description: $description, content: $content, contentTag: $contentTag, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscoverEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.contentTag, contentTag) ||
                other.contentTag == contentTag) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, thumbnail, title,
      description, content, contentTag, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscoverEventImplCopyWith<_$DiscoverEventImpl> get copyWith =>
      __$$DiscoverEventImplCopyWithImpl<_$DiscoverEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscoverEventImplToJson(
      this,
    );
  }
}

abstract class _DiscoverEvent implements DiscoverEvent {
  factory _DiscoverEvent(
      {required final int id,
      final String? thumbnail,
      final String? title,
      final String? description,
      final String? content,
      final EventFilter? contentTag,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$DiscoverEventImpl;

  factory _DiscoverEvent.fromJson(Map<String, dynamic> json) =
      _$DiscoverEventImpl.fromJson;

  @override
  int get id;
  @override
  String? get thumbnail;
  @override
  String? get title;
  @override
  String? get description;
  @override
  String? get content;
  @override
  EventFilter? get contentTag;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$DiscoverEventImplCopyWith<_$DiscoverEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DiscoverEventResponse _$DiscoverEventResponseFromJson(
    Map<String, dynamic> json) {
  return _DiscoverEventResponse.fromJson(json);
}

/// @nodoc
mixin _$DiscoverEventResponse {
  List<DiscoverEvent> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DiscoverEventResponseCopyWith<DiscoverEventResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverEventResponseCopyWith<$Res> {
  factory $DiscoverEventResponseCopyWith(DiscoverEventResponse value,
          $Res Function(DiscoverEventResponse) then) =
      _$DiscoverEventResponseCopyWithImpl<$Res, DiscoverEventResponse>;
  @useResult
  $Res call({List<DiscoverEvent> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$DiscoverEventResponseCopyWithImpl<$Res,
        $Val extends DiscoverEventResponse>
    implements $DiscoverEventResponseCopyWith<$Res> {
  _$DiscoverEventResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DiscoverEvent>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DiscoverEventResponseImplCopyWith<$Res>
    implements $DiscoverEventResponseCopyWith<$Res> {
  factory _$$DiscoverEventResponseImplCopyWith(
          _$DiscoverEventResponseImpl value,
          $Res Function(_$DiscoverEventResponseImpl) then) =
      __$$DiscoverEventResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<DiscoverEvent> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$DiscoverEventResponseImplCopyWithImpl<$Res>
    extends _$DiscoverEventResponseCopyWithImpl<$Res,
        _$DiscoverEventResponseImpl>
    implements _$$DiscoverEventResponseImplCopyWith<$Res> {
  __$$DiscoverEventResponseImplCopyWithImpl(_$DiscoverEventResponseImpl _value,
      $Res Function(_$DiscoverEventResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$DiscoverEventResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<DiscoverEvent>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DiscoverEventResponseImpl implements _DiscoverEventResponse {
  _$DiscoverEventResponseImpl(
      {required final List<DiscoverEvent> data, required this.meta})
      : _data = data;

  factory _$DiscoverEventResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiscoverEventResponseImplFromJson(json);

  final List<DiscoverEvent> _data;
  @override
  List<DiscoverEvent> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'DiscoverEventResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscoverEventResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscoverEventResponseImplCopyWith<_$DiscoverEventResponseImpl>
      get copyWith => __$$DiscoverEventResponseImplCopyWithImpl<
          _$DiscoverEventResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscoverEventResponseImplToJson(
      this,
    );
  }
}

abstract class _DiscoverEventResponse implements DiscoverEventResponse {
  factory _DiscoverEventResponse(
      {required final List<DiscoverEvent> data,
      required final Pagination meta}) = _$DiscoverEventResponseImpl;

  factory _DiscoverEventResponse.fromJson(Map<String, dynamic> json) =
      _$DiscoverEventResponseImpl.fromJson;

  @override
  List<DiscoverEvent> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$DiscoverEventResponseImplCopyWith<_$DiscoverEventResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
