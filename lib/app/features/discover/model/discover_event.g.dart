// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DiscoverEventImpl _$$DiscoverEventImplFromJson(Map<String, dynamic> json) =>
    _$DiscoverEventImpl(
      id: (json['id'] as num).toInt(),
      thumbnail: json['thumbnail'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      content: json['content'] as String?,
      contentTag:
          $enumDecodeNullable(_$EventFilterEnumMap, json['content_tag']),
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$DiscoverEventImplToJson(_$DiscoverEventImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'thumbnail': instance.thumbnail,
      'title': instance.title,
      'description': instance.description,
      'content': instance.content,
      'content_tag': _$EventFilterEnumMap[instance.contentTag],
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

const _$EventFilterEnumMap = {
  EventFilter.all: 'all',
  EventFilter.new_pods: 'new_pods',
  EventFilter.latest_news: 'latest_news',
  EventFilter.events: 'events',
};

_$DiscoverEventResponseImpl _$$DiscoverEventResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DiscoverEventResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => DiscoverEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DiscoverEventResponseImplToJson(
        _$DiscoverEventResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
