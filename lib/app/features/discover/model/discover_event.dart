import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';

part 'discover_event.freezed.dart';
part 'discover_event.g.dart';

enum EventFilter {
  all,
  // ignore: constant_identifier_names
  new_pods,
  // ignore: constant_identifier_names
  latest_news,
  events,
}

@freezed
class DiscoverEvent with _$DiscoverEvent {
  @CustomDateTimeConverter()
  factory DiscoverEvent({
    required int id,
    String? thumbnail,
    String? title,
    String? description,
    String? content,
    EventFilter? contentTag,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DiscoverEvent;

  factory DiscoverEvent.fromJson(Json json) => _$DiscoverEventFromJson(json);
}

@freezed
class DiscoverEventResponse with _$DiscoverEventResponse {
  factory DiscoverEventResponse({
    required List<DiscoverEvent> data,
    required Pagination meta,
  }) = _DiscoverEventResponse;

  factory DiscoverEventResponse.fromJson(Json json) =>
      _$DiscoverEventResponseFromJson(json);
}
