import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/discover/model/discover_event.dart';
import 'package:gomama/app/features/discover/provider/discover_event_provider.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher_string.dart';

class DiscoverView extends HookConsumerWidget {
  const DiscoverView({super.key});

  static const routeName = 'discover';
  static const routePath = '/discover';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTag = useState<EventFilter>(EventFilter.all);
    final events = ref.watch(discoverEventsProvider(filter: selectedTag.value));

    return StarsBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(
            height: max(
              mediaQuery(context).viewPadding.top,
              kTextTabBarHeight,
            ),
          ),
          Text(
            'Announcements  🎉',
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: CustomColors.secondaryLight,
                ),
            textAlign: TextAlign.center,
          ),
          const MilkBackground(),
          // filter chips
          Container(
            height: 56,
            color: CustomColors.secondaryLight,
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                children: [
                  GestureDetector(
                    onTap: () {
                      selectedTag.value = EventFilter.all;
                      ref.invalidate(discoverEventsProvider);
                    },
                    child: Chip(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      label: Text(
                        'All',
                        style: TextStyle(
                          color: selectedTag.value == EventFilter.all
                              ? CustomColors.secondaryLight
                              : CustomColors.primary,
                        ),
                      ),
                      side: BorderSide(
                        color: selectedTag.value == EventFilter.all
                            ? CustomColors.secondaryLight
                            : CustomColors.primary,
                      ),
                      backgroundColor: selectedTag.value == EventFilter.all
                          ? CustomColors.primary
                          : CustomColors.secondaryLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: () {
                      selectedTag.value = EventFilter.new_pods;
                      ref.invalidate(discoverEventsProvider);
                    },
                    child: Chip(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      label: Text(
                        'New Pods',
                        style: TextStyle(
                          color: selectedTag.value == EventFilter.new_pods
                              ? CustomColors.secondaryLight
                              : CustomColors.primary,
                        ),
                      ),
                      side: BorderSide(
                        color: selectedTag.value == EventFilter.new_pods
                            ? CustomColors.secondaryLight
                            : CustomColors.primary,
                      ),
                      backgroundColor: selectedTag.value == EventFilter.new_pods
                          ? CustomColors.primary
                          : CustomColors.secondaryLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: () {
                      selectedTag.value = EventFilter.latest_news;
                      ref.invalidate(discoverEventsProvider);
                    },
                    child: Chip(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      label: Text(
                        'Latest News',
                        style: TextStyle(
                          color: selectedTag.value == EventFilter.latest_news
                              ? CustomColors.secondaryLight
                              : CustomColors.primary,
                        ),
                      ),
                      side: BorderSide(
                        color: selectedTag.value == EventFilter.latest_news
                            ? CustomColors.secondaryLight
                            : CustomColors.primary,
                      ),
                      backgroundColor:
                          selectedTag.value == EventFilter.latest_news
                              ? CustomColors.primary
                              : CustomColors.secondaryLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: () {
                      selectedTag.value = EventFilter.events;
                      ref.invalidate(discoverEventsProvider);
                    },
                    child: Chip(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      label: Text(
                        'Events',
                        style: TextStyle(
                          color: selectedTag.value == EventFilter.events
                              ? CustomColors.secondaryLight
                              : CustomColors.primary,
                        ),
                      ),
                      side: BorderSide(
                        color: selectedTag.value == EventFilter.events
                            ? CustomColors.secondaryLight
                            : CustomColors.primary,
                      ),
                      backgroundColor: selectedTag.value == EventFilter.events
                          ? CustomColors.primary
                          : CustomColors.secondaryLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // event list
          events.when(
            data: (events) {
              if (events == null) {
                return const Expanded(
                  child: ColoredBox(
                    color: CustomColors.secondaryLight,
                  ),
                );
              }

              return Expanded(
                child: DecoratedBox(
                  decoration: const BoxDecoration(
                    color: CustomColors.secondaryLight,
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.only(bottom: 32),
                    child: ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        final event = events[index];

                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  DiscoverDetailsRoute(event.id.toString())
                                      .push(context);
                                },
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 100,
                                      child: AspectRatio(
                                        aspectRatio: 1,
                                        child: CachedNetworkImage(
                                          imageUrl: event.thumbnail ?? '',
                                          imageBuilder:
                                              (context, imageProvider) {
                                            return DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                  16,
                                                ),
                                                image: DecorationImage(
                                                  image: imageProvider,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            );
                                          },
                                          errorWidget: (context, url, error) =>
                                              const Icon(
                                            CustomIcon.error,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            event.title ?? '',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium!
                                                .copyWith(
                                                  color: CustomColors.primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          Text(
                                            event.createdAt == null
                                                ? ''
                                                : DateFormat(
                                                    'dd MMM yyyy, hh:mm a',
                                                  ).format(
                                                    event.createdAt!,
                                                  ),
                                            style: Theme.of(context)
                                                .textTheme
                                                .labelMedium!
                                                .copyWith(
                                                  color: Colors.grey,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                event.description ?? '',
                                textAlign: TextAlign.start,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: Colors.grey.shade600,
                                    ),
                              ),
                            ],
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Divider(color: Colors.grey.shade400);
                      },
                      itemCount: events.length,
                    ),
                  ),
                ),
              );
            },
            error: (error, stackTrace) {
              return const Expanded(
                child: ColoredBox(
                  color: CustomColors.secondaryLight,
                ),
              );
            },
            loading: () {
              return const Expanded(
                child: ColoredBox(
                  color: CustomColors.secondaryLight,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
