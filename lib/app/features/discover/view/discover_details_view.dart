import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/features/discover/provider/discover_event_provider.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher_string.dart';

class DiscoverDetailsView extends HookConsumerWidget {
  const DiscoverDetailsView(this.eventId, {super.key});

  final String eventId;
  static const routeName = 'discover-detail';
  static const routePath = ':eventId';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final event = ref.watch(discoverEventProvider(eventId));

    return Scaffold(
      backgroundColor: CustomColors.primary,
      appBar: AppBar(
        centerTitle: true,
        foregroundColor: CustomColors.secondaryLight,
        title: const Text('Announcements Detail'),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const MilkBackground(),
          event.when(
            data: (event) {
              if (event == null) {
                return const Expanded(
                  child: ColoredBox(
                    color: CustomColors.secondaryLight,
                  ),
                );
              }

              return Expanded(
                child: DecoratedBox(
                  decoration: const BoxDecoration(
                    color: CustomColors.secondaryLight,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(bottom: 32),
                      child: Column(
                        children: [
                          Text(
                            event.title ?? '',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 24),
                          SizedBox(
                            width: double.maxFinite,
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: CachedNetworkImage(
                                imageUrl: event.thumbnail ?? '',
                                imageBuilder: (context, imageProvider) {
                                  return DecoratedBox(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      image: DecorationImage(
                                        image: imageProvider,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  );
                                },
                                errorWidget: (context, url, error) =>
                                    const Icon(CustomIcon.error),
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),
                          HtmlWidget(event.content ?? ''),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
            error: (error, stackTrace) {
              return const Expanded(
                child: ColoredBox(
                  color: CustomColors.secondaryLight,
                ),
              );
            },
            loading: () {
              return const Expanded(
                child: ColoredBox(
                  color: CustomColors.secondaryLight,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
