// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_event_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$discoverEventRepositoryHash() =>
    r'6eeb8590860c83b208bbc2b7875fdfa0a48ff375';

/// See also [discoverEventRepository].
@ProviderFor(discoverEventRepository)
final discoverEventRepositoryProvider =
    Provider<DiscoverEventRepository>.internal(
  discoverEventRepository,
  name: r'discoverEventRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$discoverEventRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DiscoverEventRepositoryRef = ProviderRef<DiscoverEventRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
