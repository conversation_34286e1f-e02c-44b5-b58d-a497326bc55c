import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/discover/model/discover_event.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'discover_event_repository.g.dart';

@Riverpod(keepAlive: true)
DiscoverEventRepository discoverEventRepository(
  DiscoverEventRepositoryRef ref,
) =>
    DiscoverEventRepository(ref);

class DiscoverEventRepository {
  DiscoverEventRepository(this.ref);
  final DiscoverEventRepositoryRef ref;

  Future<DiscoverEventResponse> fetchEvents({EventFilter? filter}) async {
    try {
      final queryParameters = <String, dynamic>{};
      if (filter != null && filter != EventFilter.all) {
        queryParameters['category_tag'] = filter.name;
      }

      final response = await ref.read(repositoryProvider).get<Json>(
            '/cms/event-infos',
            queryParameters:
                queryParameters.isNotEmpty ? queryParameters : null,
          );

      return DiscoverEventResponse.fromJson(response.data!);
    } catch (error, stack) {
      Groveman.warning('fetchEvents', error: error, stackTrace: stack);
      rethrow;
    }
  }

  Future<DiscoverEvent> fetchEvent(String eventId) async {
    try {
      final response = await ref
          .read(repositoryProvider)
          .get<Json>('/cms/event-infos/$eventId');
      return DiscoverEvent.fromJson(response.data!['data'] as Json);
    } catch (error, stack) {
      Groveman.warning('fetchEvent', error: error, stackTrace: stack);
      rethrow;
    }
  }
}
