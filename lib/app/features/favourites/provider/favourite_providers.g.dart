// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favouriteListingsPagesHash() =>
    r'40954f6b604561c6eaf3b8a5924a9c4aa48b655b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [favouriteListingsPages].
@ProviderFor(favouriteListingsPages)
const favouriteListingsPagesProvider = FavouriteListingsPagesFamily();

/// See also [favouriteListingsPages].
class FavouriteListingsPagesFamily
    extends Family<AsyncValue<ListingsResponse>> {
  /// See also [favouriteListingsPages].
  const FavouriteListingsPagesFamily();

  /// See also [favouriteListingsPages].
  FavouriteListingsPagesProvider call(
    AllListingsPagination meta,
  ) {
    return FavouriteListingsPagesProvider(
      meta,
    );
  }

  @override
  FavouriteListingsPagesProvider getProviderOverride(
    covariant FavouriteListingsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'favouriteListingsPagesProvider';
}

/// See also [favouriteListingsPages].
class FavouriteListingsPagesProvider
    extends AutoDisposeFutureProvider<ListingsResponse> {
  /// See also [favouriteListingsPages].
  FavouriteListingsPagesProvider(
    AllListingsPagination meta,
  ) : this._internal(
          (ref) => favouriteListingsPages(
            ref as FavouriteListingsPagesRef,
            meta,
          ),
          from: favouriteListingsPagesProvider,
          name: r'favouriteListingsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$favouriteListingsPagesHash,
          dependencies: FavouriteListingsPagesFamily._dependencies,
          allTransitiveDependencies:
              FavouriteListingsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  FavouriteListingsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final AllListingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingsResponse> Function(FavouriteListingsPagesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FavouriteListingsPagesProvider._internal(
        (ref) => create(ref as FavouriteListingsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingsResponse> createElement() {
    return _FavouriteListingsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FavouriteListingsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FavouriteListingsPagesRef
    on AutoDisposeFutureProviderRef<ListingsResponse> {
  /// The parameter `meta` of this provider.
  AllListingsPagination get meta;
}

class _FavouriteListingsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingsResponse>
    with FavouriteListingsPagesRef {
  _FavouriteListingsPagesProviderElement(super.provider);

  @override
  AllListingsPagination get meta =>
      (origin as FavouriteListingsPagesProvider).meta;
}

String _$favouriteListingsCountHash() =>
    r'2f58f5e33a95b5924b06c693d40ad0826d518c1a';

/// See also [favouriteListingsCount].
@ProviderFor(favouriteListingsCount)
const favouriteListingsCountProvider = FavouriteListingsCountFamily();

/// See also [favouriteListingsCount].
class FavouriteListingsCountFamily extends Family<AsyncValue<int>> {
  /// See also [favouriteListingsCount].
  const FavouriteListingsCountFamily();

  /// See also [favouriteListingsCount].
  FavouriteListingsCountProvider call(
    AllListingsInput? input,
  ) {
    return FavouriteListingsCountProvider(
      input,
    );
  }

  @override
  FavouriteListingsCountProvider getProviderOverride(
    covariant FavouriteListingsCountProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'favouriteListingsCountProvider';
}

/// See also [favouriteListingsCount].
class FavouriteListingsCountProvider
    extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [favouriteListingsCount].
  FavouriteListingsCountProvider(
    AllListingsInput? input,
  ) : this._internal(
          (ref) => favouriteListingsCount(
            ref as FavouriteListingsCountRef,
            input,
          ),
          from: favouriteListingsCountProvider,
          name: r'favouriteListingsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$favouriteListingsCountHash,
          dependencies: FavouriteListingsCountFamily._dependencies,
          allTransitiveDependencies:
              FavouriteListingsCountFamily._allTransitiveDependencies,
          input: input,
        );

  FavouriteListingsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final AllListingsInput? input;

  @override
  Override overrideWith(
    AsyncValue<int> Function(FavouriteListingsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FavouriteListingsCountProvider._internal(
        (ref) => create(ref as FavouriteListingsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _FavouriteListingsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FavouriteListingsCountProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FavouriteListingsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `input` of this provider.
  AllListingsInput? get input;
}

class _FavouriteListingsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with FavouriteListingsCountRef {
  _FavouriteListingsCountProviderElement(super.provider);

  @override
  AllListingsInput? get input =>
      (origin as FavouriteListingsCountProvider).input;
}

String _$favouriteListingAtIndexHash() =>
    r'568b643480077157ef6169e380109dfe2152083a';

/// See also [favouriteListingAtIndex].
@ProviderFor(favouriteListingAtIndex)
const favouriteListingAtIndexProvider = FavouriteListingAtIndexFamily();

/// See also [favouriteListingAtIndex].
class FavouriteListingAtIndexFamily extends Family<AsyncValue<Listing>> {
  /// See also [favouriteListingAtIndex].
  const FavouriteListingAtIndexFamily();

  /// See also [favouriteListingAtIndex].
  FavouriteListingAtIndexProvider call(
    AllListingsOffset query,
  ) {
    return FavouriteListingAtIndexProvider(
      query,
    );
  }

  @override
  FavouriteListingAtIndexProvider getProviderOverride(
    covariant FavouriteListingAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'favouriteListingAtIndexProvider';
}

/// See also [favouriteListingAtIndex].
class FavouriteListingAtIndexProvider
    extends AutoDisposeProvider<AsyncValue<Listing>> {
  /// See also [favouriteListingAtIndex].
  FavouriteListingAtIndexProvider(
    AllListingsOffset query,
  ) : this._internal(
          (ref) => favouriteListingAtIndex(
            ref as FavouriteListingAtIndexRef,
            query,
          ),
          from: favouriteListingAtIndexProvider,
          name: r'favouriteListingAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$favouriteListingAtIndexHash,
          dependencies: FavouriteListingAtIndexFamily._dependencies,
          allTransitiveDependencies:
              FavouriteListingAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  FavouriteListingAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final AllListingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<Listing> Function(FavouriteListingAtIndexRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FavouriteListingAtIndexProvider._internal(
        (ref) => create(ref as FavouriteListingAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<Listing>> createElement() {
    return _FavouriteListingAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FavouriteListingAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FavouriteListingAtIndexRef
    on AutoDisposeProviderRef<AsyncValue<Listing>> {
  /// The parameter `query` of this provider.
  AllListingsOffset get query;
}

class _FavouriteListingAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<Listing>>
    with FavouriteListingAtIndexRef {
  _FavouriteListingAtIndexProviderElement(super.provider);

  @override
  AllListingsOffset get query =>
      (origin as FavouriteListingAtIndexProvider).query;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
