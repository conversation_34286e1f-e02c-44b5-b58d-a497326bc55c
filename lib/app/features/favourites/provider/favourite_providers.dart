import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/update_favourite_listings_input.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'favourite_providers.g.dart';

@riverpod
Future<ListingsResponse> favouriteListingsPages(
  FavouriteListingsPagesRef ref,
  AllListingsPagination meta,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  if (cancelToken.isCancelled) throw Exception();

  final sorting = ref.watch(listingSortsProvider);
  final amenities = ref.watch(amenityFiltersProvider);
  Groveman
    ..info('amenities selected', error: amenities)
    ..info('sorting selected', error: sorting);

  return ref.watch(listingRepositoryProvider).fetchFavouriteListings(
        meta.input,
        offset: meta.page,
        cancelToken: cancelToken,
        amenities: amenities,
        sorting: sorting,
      );
}

@riverpod
AsyncValue<int> favouriteListingsCount(
  FavouriteListingsCountRef ref,
  AllListingsInput? input,
) {
  final meta = AllListingsPagination(page: 0, input: input);

  return ref
      .watch(favouriteListingsPagesProvider(meta))
      .whenData((value) => value.meta.total);
}

@riverpod
AsyncValue<Listing> favouriteListingAtIndex(
  FavouriteListingAtIndexRef ref,
  AllListingsOffset query,
) {
  final offsetInPage = query.offset % kPageLimit;

  final meta = AllListingsPagination(
    page: query.offset ~/ kPageLimit,
    input: query.input,
  );

  return ref.watch(favouriteListingsPagesProvider(meta)).whenData(
        (value) => value.data[offsetInPage],
      );
}
