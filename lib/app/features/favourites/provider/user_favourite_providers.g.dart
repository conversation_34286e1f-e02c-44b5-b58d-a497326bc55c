// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_favourite_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateFavouriteListingsHash() =>
    r'ebc95fd37586820449cdd083f6411d6ecdddf2b5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [updateFavouriteListings].
@ProviderFor(updateFavouriteListings)
const updateFavouriteListingsProvider = UpdateFavouriteListingsFamily();

/// See also [updateFavouriteListings].
class UpdateFavouriteListingsFamily extends Family<AsyncValue<bool>> {
  /// See also [updateFavouriteListings].
  const UpdateFavouriteListingsFamily();

  /// See also [updateFavouriteListings].
  UpdateFavouriteListingsProvider call(
    UpdateFavouriteListingsInput input,
  ) {
    return UpdateFavouriteListingsProvider(
      input,
    );
  }

  @override
  UpdateFavouriteListingsProvider getProviderOverride(
    covariant UpdateFavouriteListingsProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'updateFavouriteListingsProvider';
}

/// See also [updateFavouriteListings].
class UpdateFavouriteListingsProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [updateFavouriteListings].
  UpdateFavouriteListingsProvider(
    UpdateFavouriteListingsInput input,
  ) : this._internal(
          (ref) => updateFavouriteListings(
            ref as UpdateFavouriteListingsRef,
            input,
          ),
          from: updateFavouriteListingsProvider,
          name: r'updateFavouriteListingsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$updateFavouriteListingsHash,
          dependencies: UpdateFavouriteListingsFamily._dependencies,
          allTransitiveDependencies:
              UpdateFavouriteListingsFamily._allTransitiveDependencies,
          input: input,
        );

  UpdateFavouriteListingsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final UpdateFavouriteListingsInput input;

  @override
  Override overrideWith(
    FutureOr<bool> Function(UpdateFavouriteListingsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateFavouriteListingsProvider._internal(
        (ref) => create(ref as UpdateFavouriteListingsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _UpdateFavouriteListingsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateFavouriteListingsProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateFavouriteListingsRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `input` of this provider.
  UpdateFavouriteListingsInput get input;
}

class _UpdateFavouriteListingsProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with UpdateFavouriteListingsRef {
  _UpdateFavouriteListingsProviderElement(super.provider);

  @override
  UpdateFavouriteListingsInput get input =>
      (origin as UpdateFavouriteListingsProvider).input;
}

String _$userFavouriteListingsHash() =>
    r'20b2b257d284fa4535741bf8acf0d78fc4a106b4';

/// See also [UserFavouriteListings].
@ProviderFor(UserFavouriteListings)
final userFavouriteListingsProvider =
    NotifierProvider<UserFavouriteListings, List<String>>.internal(
  UserFavouriteListings.new,
  name: r'userFavouriteListingsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userFavouriteListingsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserFavouriteListings = Notifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
