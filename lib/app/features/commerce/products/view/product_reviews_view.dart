import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:gomama/app/features/commerce/review/widget/product_review_card.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class ProductReviewsView extends ConsumerWidget {
  const ProductReviewsView(this.productId, {super.key});

  final String productId;
  static const routeName = 'user-reviews';
  static const routePath = 'products/:productId/user-reviews';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productOverview =
        ref.watch(productOverviewProvider(productId: productId));

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: CustomColors.primary,
        foregroundColor: Colors.white,
        title: const Text('Reviews'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            productOverview.when(
              data: (overview) => Padding(
                padding: const EdgeInsets.fromLTRB(8, 24, 8, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          double.parse(overview?.avgRating ?? '0.0')
                              .toStringAsFixed(1),
                          style: textTheme(context)
                              .titleLarge!
                              .copyWith(fontSize: 48, height: 0.9),
                        ),
                        const SizedBox(width: 8),
                        for (int i = 0; i < 5; i++)
                          const Icon(
                            CustomIcon.star,
                            color: Colors.yellow,
                            size: 24,
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Based on ${overview?.total ?? 0} Reviews'),
                  ],
                ),
              ),
              error: (_, __) => const SizedBox.shrink(),
              loading: () => const SizedBox.shrink(),
            ),
            _ReviewList(productId),
          ],
        ),
      ),
    );
  }
}

class _ReviewList extends ConsumerWidget {
  const _ReviewList(this.productId);

  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncResponse =
        ref.watch(productReviewsControllerProvider(productId: productId));
    final isLoading = ref.watch(productReviewIsBusyProvider);

    return asyncResponse.when(
      data: (response) {
        if (response == null) {
          return const SizedBox.shrink();
        }

        final productReviews = response.data;
        final pagination = response.meta;

        if (productReviews.isEmpty) {
          return const _EmptyGallery();
        }

        return ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
          itemCount: productReviews.length +
              (pagination.currentPage < pagination.lastPage ? 1 : 0),
          itemBuilder: (context, index) {
            // load more button
            if (index == productReviews.length &&
                pagination.currentPage < pagination.lastPage) {
              return isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : FilledButton(
                      style: FilledButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      onPressed: () {
                        ref
                            .read(
                              productReviewsControllerProvider(
                                productId: productId,
                              ).notifier,
                            )
                            .fetchMore();
                      },
                      child: const Text('Load more'),
                    );
            }

            return _ReviewCard(productReviews[index]);
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 16);
          },
        );
      },
      error: (error, stackTrace) {
        return const SizedBox.shrink();
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}

class _ReviewCard extends ConsumerWidget {
  const _ReviewCard(this.productReview);

  final ProductReview productReview;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productReview.name,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: textTheme(context).bodyLarge,
                    ),
                    Row(
                      children: [
                        for (int i = 0; i < productReview.score; i++)
                          const Icon(
                            CustomIcon.star,
                            color: CustomColors.primary,
                            size: 10,
                          ),
                        const SizedBox(width: 2),
                        Text(
                          productReview.score.toStringAsFixed(1),
                          style: textTheme(context)
                              .labelSmall!
                              .copyWith(color: CustomColors.primary),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                DateFormat('dd/MM/yyyy').format(productReview.createdAt),
                style: textTheme(context).labelSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(productReview.content),
        ],
      ),
    );
  }
}

class _EmptyGallery extends ConsumerWidget {
  const _EmptyGallery();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 48),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Image.asset(
              'assets/images/goma_sad.png',
              height: 195,
            ),
            Text(
              'Gallery is Empty',
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'No products have been added to the gallery yet.',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
