import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/products/provider/favourite_product_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_card.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyLikesView extends ConsumerWidget {
  const MyLikesView({super.key});

  static const routeName = 'my-likes';
  static const routePath = 'my-likes';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: CustomColors.primary,
        foregroundColor: Colors.white,
        title: const Text('My Likes'),
      ),
      body: const _LikedProductsList(),
    );
  }
}

class _LikedProductsList extends ConsumerWidget {
  const _LikedProductsList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final likedProducts = ref.watch(favouriteProductsProvider);

    return likedProducts.when(
      data: (products) {
        if (products.isEmpty == true) {
          return const _EmptyLike();
        }

        return GridView.count(
          shrinkWrap: true,
          padding: const EdgeInsets.fromLTRB(8, 24, 8, 36),
          crossAxisCount: 2,
          childAspectRatio: 180 / 290,
          crossAxisSpacing: 10,
          mainAxisSpacing: 14,
          children: products.map((product) {
            return ProductCard(product!);
          }).toList(),
        );
      },
      error: (error, stackTrace) {
        return const SizedBox.shrink();
      },
      loading: () {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }
}

class _EmptyLike extends ConsumerWidget {
  const _EmptyLike();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 48),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Image.asset(
              'assets/images/goma_sad.png',
              height: 195,
            ),
            Text(
              'Oops, Your List Are Empty.',
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Start exploring our shop and add your favourite products.',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: () {
                const CommerceRoute().go(context);
              },
              child: const Text("Let's Go"),
            ),
          ],
        ),
      ),
    );
  }
}
