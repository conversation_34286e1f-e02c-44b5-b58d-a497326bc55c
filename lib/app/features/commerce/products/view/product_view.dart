import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_button.dart';
import 'package:gomama/app/features/commerce/cart/widget/commerce_refresh_Indicator.dart';
import 'package:gomama/app/features/commerce/cart/widget/verify_email.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_header.dart';
import 'package:gomama/app/features/commerce/products/widget/product_review_section.dart';
import 'package:gomama/app/features/commerce/products/widget/product_swipeable_banner.dart';
import 'package:gomama/app/features/commerce/products/widget/variant_picker.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class ProductView extends HookConsumerWidget {
  const ProductView(this.productHandle, {super.key});

  final String productHandle;
  static const routeName = 'product-view';
  static const routePath = 'products/:productHandle';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // initialize cart here in case deeplink opens the product view
    ref.watch(cartControllerProvider);

    final quantity = useState<int>(1);

    final addingToCart = ref.watch(cartIsBusyProvider);
    final product = ref.watch(productProvider(productHandle));

    Future<void> _addToCart({
      required Product product,
      bool? instant = false,
    }) async {
      try {
        // check user shopify profile
        final user = ref.watch(authControllerProvider).requireValue;
        final shopifyProfile = user.shopifyProfile;

        // verify email
        if (shopifyProfile == null) {
          showEmailVerificationDialog(context: context);
          return;
        }

        // variant picker
        if (product.productVariants.length > 1) {
          showVariantPicker(
            context: context,
            product: product,
          );
          return;
        }

        // instant add to cart
        await ref.read(cartControllerProvider.notifier).instantAddToCart(
              product,
              quantity.value,
            );

        // go cart view if 'buy now'
        if (context.mounted && instant == true) {
          await const CartRoute().push(context);
        }
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${product.title} added to cart.',
              ),
            ),
          );
        }
        return;
      } catch (error, stack) {
        Groveman.error(
          '_addToCart',
          error: error,
          stackTrace: stack,
        );
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Failed to add to cart, please try again.',
              ),
            ),
          );
        }
        return;
      } finally {
        ref.invalidate(cartControllerProvider);
      }
    }

    return product.when(
      data: (product) {
        if (product == null) {
          return const SizedBox.shrink();
        }

        final galleryFrames = product.images
            .map(
              (productMedia) => PhotoViewGalleryPageOptions(
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 1.25,
                imageProvider: CachedNetworkImageProvider(
                  productMedia.originalSrc,
                ),
              ),
            )
            .toList();
        final singleVariantOutOfStock = product.productVariants.length <= 1 &&
            product.productVariants.first.quantityAvailable < 1;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: CustomColors.primaries.shade50,
            actions: [
              IconButton(
                onPressed: () async {
                  try {
                    final buo = BranchUniversalObject(
                      canonicalIdentifier: '/commerce/products/$productHandle',
                      title: product.title,
                      contentMetadata: BranchContentMetaData()
                        ..addCustomMetadata('productHandle', productHandle)
                        ..addCustomMetadata('productTitle', product.title),
                    );

                    FlutterBranchSdk.registerView(
                      buo: buo,
                    );

                    final lp = BranchLinkProperties(
                      feature: 'sharing',
                      channel: 'app',
                    );

                    final response = await FlutterBranchSdk.getShortUrl(
                      buo: buo,
                      linkProperties: lp,
                    );

                    await Share.share(response.result as String);
                  } catch (e) {
                    debugPrint(e.toString());
                  }
                },
                icon: const Icon(CustomIcon.share),
              ),
              PopupMenuButton<String>(
                padding: EdgeInsets.zero,
                menuPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                position: PopupMenuPosition.under,
                onSelected: (value) async {
                  switch (value) {
                    case 'home':
                      const ExploreRoute().go(context);
                      break;
                    case 'shop':
                      const CommerceRoute().go(context);
                      break;
                    case 'support':
                      await const CommerceHelpRoute().push(context);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'home',
                    child: Row(
                      children: [
                        Icon(Icons.home_outlined),
                        SizedBox(width: 6),
                        Text('Back to homepage'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'shop',
                    child: Row(
                      children: [
                        Icon(Icons.shopping_bag_outlined),
                        SizedBox(width: 6),
                        Text('Back to shop'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'support',
                    child: Row(
                      children: [
                        Icon(Icons.info_outlined),
                        SizedBox(width: 6),
                        Text('Need Help?'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          bottomNavigationBar: ColoredBox(
            color: CustomColors.primaries.shade50,
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                  0,
                  16,
                  0,
                  mediaQuery(context).padding.bottom > 0
                      ? 0
                      : 16, // with notch ? 0 : 16
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    const SizedBox(width: 16),
                    // const Icon(Icons.chat_outlined),
                    // const SizedBox(width: 8),
                    const CartButton(),
                    const SizedBox(width: 16),
                    // add to cart OR select variant OR verify email
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        onPressed:
                            singleVariantOutOfStock || addingToCart == true
                                ? null
                                : () => _addToCart(product: product),
                        child: Text(
                          singleVariantOutOfStock
                              ? 'Out of stock'
                              : addingToCart == true
                                  ? 'Adding...'
                                  : 'Add to cart',
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // buy now
                    Expanded(
                      child: FilledButton(
                        style: FilledButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        onPressed: singleVariantOutOfStock ||
                                addingToCart == true
                            ? null
                            : () => _addToCart(product: product, instant: true),
                        child: Text(
                          singleVariantOutOfStock ? 'Out of stock' : 'Buy Now',
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                ),
              ),
            ),
          ),
          body: CommerceRefreshIndicator(
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                SliverList.list(
                  children: [
                    // product image slider
                    ProductSwipeableBanner(galleryFrames: galleryFrames),

                    // TODO flash sales

                    Padding(
                      padding: const EdgeInsets.fromLTRB(8, 16, 8, 32),
                      child: Column(
                        children: [
                          // product header
                          ProductHeader(product),

                          ProductReviewSection(product.id),

                          // product details
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Text(
                                'Product Details',
                                style: textTheme(context).titleMedium!.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                product.description ??
                                    'No information added by seller.',
                                style: textTheme(context).bodyMedium,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
      error: (error, stackTrace) {
        return Scaffold(
          appBar: AppBar(),
          body: const SizedBox.shrink(),
        );
      },
      loading: () {
        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(),
          body: const Center(child: CircularProgressIndicator()),
        );
      },
    );
  }
}
