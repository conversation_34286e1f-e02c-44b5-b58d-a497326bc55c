import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class ProductBuyerGallery extends ConsumerWidget {
  const ProductBuyerGallery(this.productReviews, {super.key});

  final List<ProductReview> productReviews;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (productReviews.isEmpty == true) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Buyer Gallery',
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 155,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              return _ReviewCard(productReviews[index]);
            },
            separatorBuilder: (context, index) {
              return const SizedBox(width: 16);
            },
            itemCount: productReviews.length,
          ),
        ),
        Divider(
          height: 32,
          color: Colors.grey.shade300,
        ),
      ],
    );
  }
}

class _ReviewCard extends ConsumerWidget {
  const _ReviewCard(this.productReview);
  final ProductReview productReview;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () =>
          ProductReviewsRoute(productReview.product.customId).push(context),
      child: Card(
        child: AspectRatio(
          aspectRatio: 255 / 190,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        for (int i = 0; i < productReview.score; i++)
                          const Icon(
                            CustomIcon.star,
                            color: CustomColors.primary,
                            size: 10,
                          ),
                        const SizedBox(width: 2),
                        Text(
                          productReview.score.toStringAsFixed(1),
                          style: textTheme(context)
                              .labelSmall!
                              .copyWith(color: CustomColors.primary),
                        ),
                      ],
                    ),
                    Text(
                      DateFormat.yMMMd().format(productReview.createdAt),
                      style: textTheme(context)
                          .labelSmall!
                          .copyWith(color: CustomColors.primary),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Expanded(child: Text(productReview.content)),
                Text(
                  '@${productReview.name}',
                  style: textTheme(context)
                      .labelSmall!
                      .copyWith(color: CustomColors.primary),
                ),
                const SizedBox(height: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
