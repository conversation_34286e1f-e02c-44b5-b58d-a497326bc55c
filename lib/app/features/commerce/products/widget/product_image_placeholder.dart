import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductImagePlaceholder extends ConsumerWidget {
  const ProductImagePlaceholder({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: CustomColors.placeholder.withAlpha(150),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
