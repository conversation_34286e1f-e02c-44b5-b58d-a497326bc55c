import 'package:flutter/material.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductRating extends ConsumerWidget {
  const ProductRating({
    super.key,
    required this.productId,
    this.compact = false,
  });

  final String productId;
  final bool? compact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productOverview = ref
        .watch(productOverviewProvider(productId: productId.split('/').last));
    final productSalesCount = ref.watch(productSalesCountProvider(productId));

    final textStyle = compact == true
        ? textTheme(context).labelSmall
        : textTheme(context).labelLarge;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.star, size: compact == true ? 12 : 18, color: Colors.yellow),
        const SizedBox(width: 2),
        Text.rich(
          TextSpan(
            children: [
              // First TextSpan using productOverview AsyncValue
              productOverview.when(
                data: (overview) => TextSpan(
                  text: double.parse(overview?.avgRating ?? '0.0')
                      .toStringAsFixed(1),
                  style: textStyle,
                ),
                error: (error, stackTrace) {
                  Groveman.error(
                    'ProductRating',
                    error: error,
                    stackTrace: stackTrace,
                  );
                  return TextSpan(text: '0.0', style: textStyle);
                },
                loading: () => TextSpan(text: '0.0', style: textStyle),
              ),
              // Separator
              TextSpan(text: ' | ', style: textStyle),
              // Third TextSpan using productSalesCount AsyncValue
              productSalesCount.when(
                data: (salesCount) => TextSpan(
                  text: '$salesCount sold',
                  style: textStyle,
                ),
                error: (error, stackTrace) {
                  Groveman.error(
                    'productSalesCount',
                    error: error,
                    stackTrace: stackTrace,
                  );
                  return TextSpan(text: '0 sold', style: textStyle);
                },
                loading: () => TextSpan(text: '0 sold', style: textStyle),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
