import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/widget/approved_badge.dart';
import 'package:gomama/app/features/commerce/products/widget/product_image_placeholder.dart';
import 'package:gomama/app/features/commerce/products/widget/product_rating.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class ProductCard extends ConsumerWidget {
  const ProductCard(this.product, {super.key});

  final Product product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        ProductRoute(product.handle!).push(context);
      },
      child: Container(
        decoration: const BoxDecoration(
          color: CustomColors.backgroundCream,
          borderRadius: BorderRadius.all(Radius.circular(6)),
        ),
        clipBehavior: Clip.hardEdge,
        child: Column(
          children: [
            AspectRatio(
              aspectRatio: 1,
              child: CachedNetworkImage(
                imageUrl: product.image,
                imageBuilder: (context, imageProvider) {
                  return DecoratedBox(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
                placeholder: (context, url) => const ProductImagePlaceholder(),
                errorWidget: (context, url, error) =>
                    const Icon(CustomIcon.error),
              ),
            ),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (product.tags.contains('Go!Mama certified'))
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: ApprovedBadge(),
                    ),
                  const SizedBox(height: 4),
                  Text(
                    product.title,
                    maxLines: 2,
                    style: textTheme(context).labelLarge!.copyWith(
                          overflow: TextOverflow.ellipsis,
                        ),
                  ),
                  if (product.vendor.isNotEmpty)
                    RichText(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'by ',
                            style: textTheme(context).labelSmall,
                          ),
                          TextSpan(
                            text: product.vendor,
                            style: textTheme(context).labelMedium!.copyWith(
                                  color: CustomColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                  // product rating
                  ProductRating(
                    productId: product.id,
                    compact: true,
                  ),
                  Text(
                    product.formattedPrice,
                    style: textTheme(context).labelLarge!.copyWith(
                          fontSize: 18,
                          color: CustomColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
