import 'package:flutter/material.dart';
import 'package:gomama/app/features/commerce/products/widget/product_buyer_gallery.dart';
import 'package:gomama/app/features/commerce/products/widget/product_verified_user.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductReviewSection extends ConsumerWidget {
  const ProductReviewSection(this.productId, {super.key});

  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productReviews =
        ref.watch(productReviewsProvider(productId: productId.split('/').last));

    return productReviews.when(
      data: (reviews) {
        if (reviews == null || reviews.isEmpty == true) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Buyer Gallery',
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 12),
              Text(
                'No reviews available for this product',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Divider(
                height: 32,
                color: Colors.grey.shade300,
              ),
            ],
          );
        }

        // find the latest and highest rated review
        final bestReview = reviews.reduce((currentBest, element) {
          return element.createdAt.isAfter(currentBest.createdAt) ||
                  element.score > currentBest.score
              ? element
              : currentBest;
        });
        // exclude the best one from the list
        final processedReviews =
            reviews.where((element) => element.id != bestReview.id).toList();

        return Column(
          children: [
            ProductFeaturedReview(bestReview),
            Divider(
              height: 32,
              color: Colors.grey.shade300,
            ),
            ProductBuyerGallery(processedReviews),
          ],
        );
      },
      error: (error, stackTrace) => const SizedBox.shrink(),
      loading: () => const SizedBox.shrink(),
    );
  }
}
