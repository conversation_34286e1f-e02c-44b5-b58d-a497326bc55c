import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/widget/approved_badge.dart';
import 'package:gomama/app/features/commerce/products/widget/product_like_button.dart';
import 'package:gomama/app/features/commerce/products/widget/product_price.dart';
import 'package:gomama/app/features/commerce/products/widget/product_rating.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class ProductHeader extends ConsumerWidget {
  const ProductHeader(this.product, {super.key});

  final Product product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deliveryDate = DateTime.now().add(const Duration(days: 5));
    final deliveryDateEnd = DateTime.now().add(const Duration(days: 8));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // approved badge
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (product.tags.contains('Go!Mama certified'))
              const ApprovedBadge(isDetailPage: true)
            else
              const SizedBox.shrink(),
            ProductLikeButton(product.id),
          ],
        ),
        // product price
        ProductPrice(product),
        // product title
        Text(
          product.title,
          style: textTheme(context).titleLarge!.copyWith(color: Colors.black),
        ),
        if (product.vendor.isNotEmpty)
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'by ',
                  style: textTheme(context).titleSmall!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextSpan(
                  text: product.vendor,
                  style: textTheme(context).titleLarge!.copyWith(
                        color: CustomColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      CommerceSearchListingRoute(product.vendor).push(context);
                    },
                ),
              ],
            ),
          ),
        const SizedBox(height: 6),
        // product rating
        ProductRating(productId: product.id),
        const SizedBox(height: 20),
        // TODO: dynamic delivery day (PH & Weekend detection)
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(Icons.local_shipping),
            const SizedBox(width: 9),
            Expanded(
              child: Text(
                'Guaranteed Delivery by ${DateFormat('d MMM').format(deliveryDate)} - ${DateFormat('d MMM').format(deliveryDateEnd)}, with shipping fee \$0.00',
              ),
            ),
          ],
        ),
        Divider(height: 32, color: Colors.grey.shade300),
        const Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(Icons.verified_user),
            SizedBox(width: 9),
            Expanded(child: Text('5-day Delivery  | 15-Day Free Returns')),
          ],
        ),
        Divider(height: 32, color: Colors.grey.shade300),
      ],
    );
  }
}
