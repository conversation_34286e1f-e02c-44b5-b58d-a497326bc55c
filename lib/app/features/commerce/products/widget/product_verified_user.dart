import 'package:flutter/material.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductFeaturedReview extends ConsumerWidget {
  const ProductFeaturedReview(this.productReview, {super.key});

  final ProductReview productReview;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reviewOverview = ref.watch(
      productOverviewProvider(productId: productReview.product.customId),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            reviewOverview.when(
              data: (overview) {
                if (overview == null) return const SizedBox.shrink();

                return Text(
                  double.parse(overview.avgRating ?? '0.0').toStringAsFixed(2),
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                );
              },
              error: (error, stackTrace) => const SizedBox.shrink(),
              loading: () => const SizedBox.shrink(),
            ),
            const Icon(Icons.star, size: 22, color: Colors.yellow),
            const SizedBox(width: 2),
            Text(
              'Mommies Approved',
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.verified,
              size: 18,
            ),
            const SizedBox(width: 2),
            Text(
              productReview.name,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          productReview.content,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(height: 0),
        ),
      ],
    );
  }
}
