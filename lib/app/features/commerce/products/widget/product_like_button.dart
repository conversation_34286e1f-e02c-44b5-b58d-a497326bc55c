import 'package:flutter/material.dart';
import 'package:gomama/app/features/commerce/products/provider/favourite_product_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductLikeButton extends ConsumerWidget {
  const ProductLikeButton(this.productId, {super.key});

  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favouriteProducts =
        ref.watch(favouriteProductIdsProvider(isLocal: true));

    return favouriteProducts.when(
      data: (products) {
        final isLiked = products.contains(productId);

        return IconButton(
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          visualDensity: VisualDensity.compact,
          icon: Icon(
            isLiked ? Icons.favorite : Icons.favorite_outline,
          ),
          onPressed: () async {
            try {
              await ref
                  .read(favouriteProductControllerProvider.notifier)
                  .toggleFavourite(productId);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Product ${isLiked ? 'removed from' : 'added to'} likes',
                    ),
                    duration: const Duration(milliseconds: 500),
                  ),
                );
              }
            } catch (error) {
              Groveman.error('productLikeButton', error: error);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to set default: $error'),
                    duration: const Duration(milliseconds: 500),
                  ),
                );
              }
            } finally {
              ref.invalidate(favouriteProductIdsProvider(isLocal: true));
            }
          },
        );
      },
      error: (error, stackTrace) => IconButton(
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        visualDensity: VisualDensity.compact,
        icon: Icon(
          Icons.favorite_outline,
          color: Theme.of(context).disabledColor,
        ),
        onPressed: null,
      ),
      loading: () => IconButton(
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        visualDensity: VisualDensity.compact,
        icon: Icon(
          Icons.favorite_outline,
          color: Theme.of(context).disabledColor,
        ),
        onPressed: null,
      ),
    );
  }
}
