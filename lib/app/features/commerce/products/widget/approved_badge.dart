import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ApprovedBadge extends ConsumerWidget {
  const ApprovedBadge({super.key, this.isDetailPage = false});

  final bool? isDetailPage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isDetailPage == true ? 8 : 4,
        vertical: isDetailPage == true ? 6 : 2,
      ),
      decoration: BoxDecoration(
        color: CustomColors.primary,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.verified,
            size: isDetailPage == true ? 16 : 10,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            'Approved By Go!Mama',
            style: Theme.of(context).textTheme.labelSmall!.copyWith(
                  fontSize: isDetailPage == true ? 14 : 8,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
          ),
        ],
      ),
    );
  }
}
