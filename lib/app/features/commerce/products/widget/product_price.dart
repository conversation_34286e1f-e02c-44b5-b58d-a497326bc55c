import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/models/src/product/product.dart';

class ProductPrice extends ConsumerWidget {
  const ProductPrice(this.product, {super.key});

  final Product product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final discountFloat =
        ((product.compareAtPrice - product.price) / product.compareAtPrice) *
            100;

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            product.formattedPrice,
            style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  color: CustomColors.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(width: 12),
          if (product.compareAtPrice > 0) ...[
            Text(
              product.compareAtPriceFormatted,
              style: const TextStyle(decoration: TextDecoration.lineThrough),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                color: CustomColors.primaries.shade50,
                child: Text(
                  '-${discountFloat.toStringAsFixed(0)}%',
                  style: textTheme(context)
                      .bodyMedium!
                      .copyWith(color: CustomColors.primary),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
