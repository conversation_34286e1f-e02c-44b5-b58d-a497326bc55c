import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_price.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

void showVariantPicker({
  required BuildContext context,
  required Product? product,
  Line? cartLine,
}) {
  showModalBottomSheet<Widget>(
    context: context,
    isScrollControlled: true,
    showDragHandle: true,
    backgroundColor: CustomColors.secondaryLight,
    constraints: BoxConstraints(
      maxHeight:
          MediaQuery.of(context).size.height * (product == null ? 0.4 : 0.9),
    ),
    builder: (context) {
      if (product == null) {
        return const Center(
          child: Text('Product not found.'),
        );
      }

      return _VariantPickerContent(
        product: product,
        cartLine: cartLine,
      );
    },
  );
}

class _VariantPickerContent extends HookConsumerWidget {
  const _VariantPickerContent({required this.product, this.cartLine});

  final Product product;
  final Line? cartLine;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final options = product.options;
    final cartIsBusy = ref.watch(cartIsBusyProvider);

    // find default selected variant from cartLine => variant id
    final defaultSelectedVariant = cartLine != null
        ? product.productVariants.firstWhere(
            (variant) => variant.id == cartLine?.variantId,
            orElse: () => product.productVariants.first,
          )
        : null;
    // initialize with cartLine if available
    final quantity = useState<int>(cartLine?.quantity ?? 1);
    final selectedOptions = useState<List<SelectedOption>>(
      defaultSelectedVariant?.selectedOptions ?? [],
    );
    final selectedVariant = useState<ProductVariant?>(defaultSelectedVariant);

    // update selected variant
    useEffect(
      () {
        // only filter if all selected options are available
        if (selectedOptions.value.isEmpty ||
            selectedOptions.value.length != options.length) {
          return null;
        }

        for (final variant in product.productVariants) {
          if (variant.selectedOptions!.every(
            (variantOption) => selectedOptions.value.any(
              (selectedOption) =>
                  selectedOption.name == variantOption.name &&
                  selectedOption.value == variantOption.value,
            ),
          )) {
            selectedVariant.value = variant;
          }
        }

        return null;
      },
      [selectedOptions.value],
    );

    if (options.isEmpty) {
      return const Center(
        child: Text('No options available.'),
      );
    }

    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: const EdgeInsets.only(left: 28, right: 28, bottom: 28),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // variant preview (image & price)
                // use first product image when not selected
                SizedBox(
                  height: 120,
                  child: Row(
                    children: [
                      AspectRatio(
                        aspectRatio: 1,
                        child: CachedNetworkImage(
                          imageUrl: selectedVariant.value?.image?.originalSrc ??
                              product.image,
                          imageBuilder: (context, imageProvider) {
                            return DecoratedBox(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            );
                          },
                          errorWidget: (context, url, error) =>
                              const Icon(CustomIcon.error),
                        ),
                      ),
                      // ProductPrice(product),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.title,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            Text(
                              selectedVariant.value?.price.formattedPrice ??
                                  product.formattedPrice,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            if (selectedVariant.value != null)
                              Align(
                                alignment: Alignment.bottomLeft,
                                child: Text(
                                  'Stock: ${selectedVariant.value?.quantityAvailable ?? 0}',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                Divider(color: Colors.grey.shade300),

                // variant group lists
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final option = options[index];

                    return _VariantSection(
                      label: option.name,
                      optionList: option.values,
                      selectedOptions: selectedOptions,
                      availableVariants: product.productVariants,
                    );
                  },
                  separatorBuilder: (context, index) =>
                      Divider(color: Colors.grey.shade300),
                  itemCount: options.length,
                ),

                Divider(color: Colors.grey.shade300),

                // quantity control
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Quantity',
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  color: CustomColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      Container(
                        height: 32,
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(32),
                        ),
                        child: SizedBox(
                          width: 56,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              GestureDetector(
                                onTap: () {
                                  if (selectedVariant.value == null ||
                                      quantity.value <= 1) return;
                                  quantity.value -= 1;
                                },
                                child: const Icon(
                                  Icons.remove,
                                  size: 14,
                                  color: CustomColors.primary,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${quantity.value}',
                                textAlign: TextAlign.center,
                                style: Theme.of(context)
                                    .textTheme
                                    .labelMedium!
                                    .copyWith(
                                      color: CustomColors.primary,
                                    ),
                              ),
                              const SizedBox(width: 4),
                              GestureDetector(
                                onTap: () {
                                  if (selectedVariant.value == null ||
                                      quantity.value >=
                                          selectedVariant
                                              .value!.quantityAvailable) return;

                                  quantity.value += 1;
                                },
                                child: const Icon(
                                  Icons.add,
                                  size: 14,
                                  color: CustomColors.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Divider(color: Colors.grey.shade300),

                // update cart
                if (cartLine != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      onPressed: (selectedVariant.value != null &&
                                  selectedVariant.value!.quantityAvailable <
                                      1) ||
                              cartIsBusy == true ||
                              selectedVariant.value == null
                          ? null
                          : () async {
                              await ref
                                  .read(cartControllerProvider.notifier)
                                  .updateCart(
                                    cartLine!.id!,
                                    selectedVariant.value!.id,
                                    quantity.value,
                                  );
                              ref.invalidate(cartControllerProvider);

                              // close modal
                              if (context.mounted) {
                                context.pop();
                              }
                            },
                      child: Text(
                        (selectedVariant.value != null &&
                                selectedVariant.value!.quantityAvailable < 1)
                            ? 'Out of stock'
                            : cartIsBusy == true
                                ? 'Updating...'
                                : 'Update cart',
                      ),
                    ),
                  )
                else
                  // add to cart
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      onPressed: (selectedVariant.value != null &&
                                  selectedVariant.value!.quantityAvailable <
                                      1) ||
                              cartIsBusy == true ||
                              selectedVariant.value == null
                          ? null
                          : () async {
                              await ref
                                  .read(cartControllerProvider.notifier)
                                  .addToCart(
                                    selectedVariant.value!.id,
                                    quantity.value,
                                  );
                              ref.invalidate(cartControllerProvider);

                              // close modal
                              if (context.mounted) {
                                context.pop();
                              }
                            },
                      child: Text(
                        (selectedVariant.value != null &&
                                selectedVariant.value!.quantityAvailable < 1)
                            ? 'Out of stock'
                            : cartIsBusy == true
                                ? 'Adding...'
                                : 'Add to cart',
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 28,
          child: GestureDetector(
            onTap: () => context.pop(),
            child: const Icon(Icons.close),
          ),
        ),
      ],
    );
  }
}

class _VariantSection extends HookConsumerWidget {
  const _VariantSection({
    required this.label,
    required this.optionList,
    required this.selectedOptions,
    required this.availableVariants,
  });

  final String label;
  final List<String> optionList;
  final ValueNotifier<List<SelectedOption>> selectedOptions;
  final List<ProductVariant> availableVariants;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: CustomColors.primary,
                fontWeight: FontWeight.bold,
              ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Wrap(
            spacing: 4,
            runSpacing: 6,
            children: optionList
                .map(
                  (option) => GestureDetector(
                    onTap: () {
                      // is the option.value already selected
                      final isValueSelected = selectedOptions.value
                          .any((element) => element.value == option);
                      // is the option itself already selected
                      final isOptionSelected = selectedOptions.value
                          .any((element) => element.name == label);

                      // if option.value is already selected, remove it
                      if (isValueSelected == true) {
                        selectedOptions.value = selectedOptions.value
                            .where((_option) => _option.value != option)
                            .toList();
                      }
                      // if different option.value is already selected, replace it
                      else if (isOptionSelected == true) {
                        final updatedOptions =
                            selectedOptions.value.map((_option) {
                          if (_option.name == label) {
                            return SelectedOption(name: label, value: option);
                          }
                          return _option;
                        }).toList();
                        selectedOptions.value = updatedOptions;
                      }
                      // if no option is selected, add it
                      else {
                        selectedOptions.value = [
                          ...selectedOptions.value,
                          SelectedOption(name: label, value: option),
                        ];
                      }
                    },
                    child: Chip(
                      label: Text(option),
                      visualDensity: VisualDensity.compact,
                      backgroundColor: selectedOptions.value
                              .any((element) => element.value == option)
                          ? CustomColors.primary
                          : CustomColors.secondaryLight,
                      labelStyle: TextStyle(
                        color: selectedOptions.value
                                .any((element) => element.value == option)
                            ? CustomColors.secondaryLight
                            : CustomColors.primary,
                      ),
                      side: BorderSide(color: Colors.grey.shade400),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                  ),
                )
                .toList()
                .cast<Widget>(),
          ),
        ),
      ],
    );
  }
}
