import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ProductSwipeableBanner extends HookConsumerWidget {
  const ProductSwipeableBanner({
    super.key,
    required this.galleryFrames,
  });

  final List<PhotoViewGalleryPageOptions> galleryFrames;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPage = useState<int>(0);
    final _bannerController = usePageController();

    return Stack(
      children: [
        AspectRatio(
          aspectRatio: 1,
          child: PhotoViewGallery(
            pageController: _bannerController,
            pageOptions: galleryFrames,
            loadingBuilder: (context, progress) => const Center(
              child: SizedBox(width: 280, child: LoadingView()),
            ),
            backgroundDecoration: const BoxDecoration(
              color: Colors.transparent,
            ),
            onPageChanged: (page) {
              currentPage.value = page;
            },
          ),
        ),
        // pagination dots
        if (galleryFrames.length > 1)
          Positioned(
            bottom: 12,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(galleryFrames.length, (index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3.5),
                  child: Icon(
                    currentPage.value == index
                        ? Icons.radio_button_checked
                        : Icons.radio_button_off,
                    size: 9,
                  ),
                );
              }),
            ),
          ),
      ],
    );
  }
}
