// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productHash() => r'ce8fc74d9e8d45377221853b456eb1ff2ef861f4';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [product].
@ProviderFor(product)
const productProvider = ProductFamily();

/// See also [product].
class ProductFamily extends Family<AsyncValue<Product?>> {
  /// See also [product].
  const ProductFamily();

  /// See also [product].
  ProductProvider call(
    String handle,
  ) {
    return ProductProvider(
      handle,
    );
  }

  @override
  ProductProvider getProviderOverride(
    covariant ProductProvider provider,
  ) {
    return call(
      provider.handle,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productProvider';
}

/// See also [product].
class ProductProvider extends AutoDisposeFutureProvider<Product?> {
  /// See also [product].
  ProductProvider(
    String handle,
  ) : this._internal(
          (ref) => product(
            ref as ProductRef,
            handle,
          ),
          from: productProvider,
          name: r'productProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productHash,
          dependencies: ProductFamily._dependencies,
          allTransitiveDependencies: ProductFamily._allTransitiveDependencies,
          handle: handle,
        );

  ProductProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.handle,
  }) : super.internal();

  final String handle;

  @override
  Override overrideWith(
    FutureOr<Product?> Function(ProductRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductProvider._internal(
        (ref) => create(ref as ProductRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        handle: handle,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Product?> createElement() {
    return _ProductProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductProvider && other.handle == handle;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, handle.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductRef on AutoDisposeFutureProviderRef<Product?> {
  /// The parameter `handle` of this provider.
  String get handle;
}

class _ProductProviderElement extends AutoDisposeFutureProviderElement<Product?>
    with ProductRef {
  _ProductProviderElement(super.provider);

  @override
  String get handle => (origin as ProductProvider).handle;
}

String _$productsHash() => r'e50ba490a13b11cb7a5c62642b42471ce93690bf';

/// See also [products].
@ProviderFor(products)
final productsProvider = AutoDisposeFutureProvider<List<Product>>.internal(
  products,
  name: r'productsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$productsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ProductsRef = AutoDisposeFutureProviderRef<List<Product>>;
String _$collectionProductsHash() =>
    r'a8257053064f31b0c7373bf028e6672b98c8bcd6';

/// See also [collectionProducts].
@ProviderFor(collectionProducts)
const collectionProductsProvider = CollectionProductsFamily();

/// See also [collectionProducts].
class CollectionProductsFamily extends Family<AsyncValue<List<Product>>> {
  /// See also [collectionProducts].
  const CollectionProductsFamily();

  /// See also [collectionProducts].
  CollectionProductsProvider call(
    String collectionId, {
    int? limit,
  }) {
    return CollectionProductsProvider(
      collectionId,
      limit: limit,
    );
  }

  @override
  CollectionProductsProvider getProviderOverride(
    covariant CollectionProductsProvider provider,
  ) {
    return call(
      provider.collectionId,
      limit: provider.limit,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'collectionProductsProvider';
}

/// See also [collectionProducts].
class CollectionProductsProvider
    extends AutoDisposeFutureProvider<List<Product>> {
  /// See also [collectionProducts].
  CollectionProductsProvider(
    String collectionId, {
    int? limit,
  }) : this._internal(
          (ref) => collectionProducts(
            ref as CollectionProductsRef,
            collectionId,
            limit: limit,
          ),
          from: collectionProductsProvider,
          name: r'collectionProductsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$collectionProductsHash,
          dependencies: CollectionProductsFamily._dependencies,
          allTransitiveDependencies:
              CollectionProductsFamily._allTransitiveDependencies,
          collectionId: collectionId,
          limit: limit,
        );

  CollectionProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.collectionId,
    required this.limit,
  }) : super.internal();

  final String collectionId;
  final int? limit;

  @override
  Override overrideWith(
    FutureOr<List<Product>> Function(CollectionProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CollectionProductsProvider._internal(
        (ref) => create(ref as CollectionProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        collectionId: collectionId,
        limit: limit,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Product>> createElement() {
    return _CollectionProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CollectionProductsProvider &&
        other.collectionId == collectionId &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, collectionId.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CollectionProductsRef on AutoDisposeFutureProviderRef<List<Product>> {
  /// The parameter `collectionId` of this provider.
  String get collectionId;

  /// The parameter `limit` of this provider.
  int? get limit;
}

class _CollectionProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<Product>>
    with CollectionProductsRef {
  _CollectionProductsProviderElement(super.provider);

  @override
  String get collectionId =>
      (origin as CollectionProductsProvider).collectionId;
  @override
  int? get limit => (origin as CollectionProductsProvider).limit;
}

String _$queriedProductsHash() => r'90abf9e409a7624334b7b02eea3f24aabd871779';

/// See also [queriedProducts].
@ProviderFor(queriedProducts)
const queriedProductsProvider = QueriedProductsFamily();

/// See also [queriedProducts].
class QueriedProductsFamily extends Family<AsyncValue<List<Product>?>> {
  /// See also [queriedProducts].
  const QueriedProductsFamily();

  /// See also [queriedProducts].
  QueriedProductsProvider call(
    String? keywords,
  ) {
    return QueriedProductsProvider(
      keywords,
    );
  }

  @override
  QueriedProductsProvider getProviderOverride(
    covariant QueriedProductsProvider provider,
  ) {
    return call(
      provider.keywords,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'queriedProductsProvider';
}

/// See also [queriedProducts].
class QueriedProductsProvider
    extends AutoDisposeFutureProvider<List<Product>?> {
  /// See also [queriedProducts].
  QueriedProductsProvider(
    String? keywords,
  ) : this._internal(
          (ref) => queriedProducts(
            ref as QueriedProductsRef,
            keywords,
          ),
          from: queriedProductsProvider,
          name: r'queriedProductsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$queriedProductsHash,
          dependencies: QueriedProductsFamily._dependencies,
          allTransitiveDependencies:
              QueriedProductsFamily._allTransitiveDependencies,
          keywords: keywords,
        );

  QueriedProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.keywords,
  }) : super.internal();

  final String? keywords;

  @override
  Override overrideWith(
    FutureOr<List<Product>?> Function(QueriedProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QueriedProductsProvider._internal(
        (ref) => create(ref as QueriedProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        keywords: keywords,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Product>?> createElement() {
    return _QueriedProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QueriedProductsProvider && other.keywords == keywords;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, keywords.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin QueriedProductsRef on AutoDisposeFutureProviderRef<List<Product>?> {
  /// The parameter `keywords` of this provider.
  String? get keywords;
}

class _QueriedProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<Product>?>
    with QueriedProductsRef {
  _QueriedProductsProviderElement(super.provider);

  @override
  String? get keywords => (origin as QueriedProductsProvider).keywords;
}

String _$searchHistoryHash() => r'ad2f73ac1667f5406a4ebd5e957ac6e2b6d698e2';

/// See also [searchHistory].
@ProviderFor(searchHistory)
final searchHistoryProvider = AutoDisposeFutureProvider<List<String>>.internal(
  searchHistory,
  name: r'searchHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef SearchHistoryRef = AutoDisposeFutureProviderRef<List<String>>;
String _$productSalesCountHash() => r'5aa6874e983170bc0e42be6a8e93a63bf44a2e45';

/// See also [productSalesCount].
@ProviderFor(productSalesCount)
const productSalesCountProvider = ProductSalesCountFamily();

/// See also [productSalesCount].
class ProductSalesCountFamily extends Family<AsyncValue<int>> {
  /// See also [productSalesCount].
  const ProductSalesCountFamily();

  /// See also [productSalesCount].
  ProductSalesCountProvider call(
    String productId,
  ) {
    return ProductSalesCountProvider(
      productId,
    );
  }

  @override
  ProductSalesCountProvider getProviderOverride(
    covariant ProductSalesCountProvider provider,
  ) {
    return call(
      provider.productId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productSalesCountProvider';
}

/// See also [productSalesCount].
class ProductSalesCountProvider extends AutoDisposeFutureProvider<int> {
  /// See also [productSalesCount].
  ProductSalesCountProvider(
    String productId,
  ) : this._internal(
          (ref) => productSalesCount(
            ref as ProductSalesCountRef,
            productId,
          ),
          from: productSalesCountProvider,
          name: r'productSalesCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productSalesCountHash,
          dependencies: ProductSalesCountFamily._dependencies,
          allTransitiveDependencies:
              ProductSalesCountFamily._allTransitiveDependencies,
          productId: productId,
        );

  ProductSalesCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<int> Function(ProductSalesCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductSalesCountProvider._internal(
        (ref) => create(ref as ProductSalesCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<int> createElement() {
    return _ProductSalesCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductSalesCountProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductSalesCountRef on AutoDisposeFutureProviderRef<int> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductSalesCountProviderElement
    extends AutoDisposeFutureProviderElement<int> with ProductSalesCountRef {
  _ProductSalesCountProviderElement(super.provider);

  @override
  String get productId => (origin as ProductSalesCountProvider).productId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
