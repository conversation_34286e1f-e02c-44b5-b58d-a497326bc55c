import 'dart:convert';
import 'dart:math';

import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/features/commerce/products/query/get_product_sales_count.dart';
import 'package:gomama/app/features/commerce/search/model/commerce_filter.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'product_providers.g.dart';

final searchFilterProvider = StateProvider.autoDispose<CommerceFilter>(
  (ref) => CommerceFilter(),
);

@riverpod
Future<Product?> product(
  ProductRef ref,
  String handle,
) async {
  final shopifyStore = ShopifyStore.instance;
  final response = await shopifyStore.getProductByHandle(handle);

  return response;
}

@riverpod
Future<List<Product>> products(ProductsRef ref) async {
  final shopifyStore = ShopifyStore.instance;

  return shopifyStore.getAllProducts();
}

@riverpod
Future<List<Product>> collectionProducts(
  CollectionProductsRef ref,
  String collectionId, {
  int? limit,
}) async {
  final shopifyStore = ShopifyStore.instance;

  final products =
      await shopifyStore.getAllProductsFromCollectionById(collectionId);

  // If limit is provided, trim the list to that size
  if (limit != null && products.length > limit) {
    return products.take(limit).toList();
  }

  return products;
}

@riverpod
Future<List<Product>?> queriedProducts(
  QueriedProductsRef ref,
  String? keywords,
) async {
  final shopifyStore = ShopifyStore.instance;
  final filter = ref.read(searchFilterProvider);

  // process & combine keywords with applied filters
  // keyword searches across title and vendor automatically
  final keywordQueryString = keywords?.isNotEmpty == true
      ? '(title:"*$keywords*" OR vendor:"*$keywords*")'
      : '';

  // reservedTag & ageGroup using tag
  // category using product_type
  final reservedTagQueryString = filter.reservedTags?.isNotEmpty == true
      ? 'tag:${filter.reservedTags}'
      : '';
  final ageGroupQueryString =
      filter.ageGroup?.isNotEmpty == true ? 'tag:${filter.ageGroup}' : '';
  final categoryQueryString = filter.category?.isNotEmpty == true
      ? 'product_type:${filter.category}'
      : '';
  final tagsConditions = [
    if (reservedTagQueryString.isNotEmpty) reservedTagQueryString,
    if (ageGroupQueryString.isNotEmpty) ageGroupQueryString,
    if (categoryQueryString.isNotEmpty) categoryQueryString,
  ];

  // final query string
  final tagsQueryString =
      tagsConditions.isNotEmpty ? '(${tagsConditions.join(' AND ')})' : '';
  final queryString = [
    if (keywordQueryString.isNotEmpty) keywordQueryString,
    if (tagsQueryString.isNotEmpty) tagsQueryString,
  ].join(' OR ');

  Groveman.debug(queryString);

  // NOTE: only use searchProduct when price range is provided
  try {
    // save keyword to search history
    if (keywords?.isNotEmpty == true) {
      final storage = ref.read(securedAppStorageProvider);
      final searchHistory = await storage.readValue('searchHistory') ?? '[]';
      final historyKeywords =
          List<String>.from(json.decode(searchHistory) as List<dynamic>)
            // Remove if keyword already exists (to move it to front)
            ..remove(keywords)
            // Add new keyword at the beginning
            ..insert(0, keywords!);
      // keep only last 5 keywords
      if (historyKeywords.length > 5) {
        historyKeywords.removeLast();
      }
      // update stored search history
      await storage.writeValue('searchHistory', json.encode(historyKeywords));
      ref.invalidate(searchHistoryProvider);
    }

    if (filter.priceRange != null) {
      final products = await shopifyStore.searchProducts(
        queryString,
        filters: {
          'price': {
            'min': filter.priceRange!.min ?? 0,
            'max': filter.priceRange!.max ?? 500,
          },
        },
      );

      Groveman
        ..debug(filter.priceRange!.min.toString())
        ..debug(filter.priceRange!.max.toString());

      return products;
    } else {
      final products = await shopifyStore.getAllProductsOnQuery(
        '',
        queryString,
        sortKey:
            queryString.isEmpty ? SortKeyProduct.BEST_SELLING : filter.sortKey,
        reverse: filter.isPriceReverse ?? false,
      );

      return products.sublist(
        0,
        queryString.isEmpty ? min(4, products.length) : products.length,
      );
    }
  } catch (e) {
    Groveman.error('queriedProducts', error: e);
    return [];
  }
}

@riverpod
Future<List<String>> searchHistory(SearchHistoryRef ref) async {
  final searchHistory =
      await ref.read(securedAppStorageProvider).readValue('searchHistory') ??
          '[]';
  return List<String>.from(json.decode(searchHistory) as List<dynamic>);
}

@riverpod
Future<int> productSalesCount(
  ProductSalesCountRef ref,
  String productId,
) async {
  try {
    final response = await ShopifyCustom.instance.customQuery(
      gqlQuery: getProductSalesCount,
      variables: {'id': productId},
    );
    
    final metafield = response?['product']?['metafield'];
    if (metafield != null && metafield['value'] != null) {
      return int.tryParse(metafield['value'] as String) ?? 0;
    }
    
    return 0;
  } catch (error, stackTrace) {
    Groveman.error('productSalesCount', error: error, stackTrace: stackTrace);
    return 0; // Return 0 as default in case of error
  }
}
