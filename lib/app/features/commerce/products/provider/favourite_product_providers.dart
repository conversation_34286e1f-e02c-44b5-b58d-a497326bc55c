import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/features/commerce/products/repository/favourite_product_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/models/src/product/product.dart';
import 'package:shopify_flutter/shopify/src/shopify_store.dart';

part 'favourite_product_providers.g.dart';

@riverpod
Future<List<String>> favouriteProductIds(
  FavouriteProductIdsRef ref, {
  bool? isLocal,
}) async {
  if (isLocal == true) {
    return List<String>.from(
      json.decode(
        await ref.read(securedAppStorageProvider).readValue('favouriteIds') ??
            '[]',
      ) as List<dynamic>,
    );
  }

  try {
    final response = await ref
        .watch(favouriteProductRepositoryProvider)
        .getFavouriteProducts();
    return response;
  } catch (error) {
    Groveman.error('favouriteProductIds', error: error);
    rethrow;
  }
}

@riverpod
Future<List<Product?>> favouriteProducts(FavouriteProductsRef ref) async {
  final shopifyStore = ShopifyStore.instance;
  final likedProductIds = await ref.watch(
    favouriteProductIdsProvider(isLocal: true).future,
  );

  if (likedProductIds.isEmpty) return [];

  final queryString = likedProductIds.map(
    (id) {
      final customId = id.split('/').last;
      return 'id:$customId';
    },
  ).join(' OR ');

  try {
    final products = await shopifyStore.searchProducts(queryString);
    return products ?? [];
  } catch (error) {
    Groveman.error('favouriteProducts', error: error);
    rethrow;
  }
}

@Riverpod(keepAlive: true)
class FavouriteProductController extends _$FavouriteProductController {
  @override
  FutureOr<void> build() async {
    // in case app is closed mid-debounce & before sync with remote
    // favouriteDebounce determine whether to sync local to remote on next app launch
    final favouriteDebounce = await ref
            .read(securedAppStorageProvider)
            .readValue('favouriteDebounce') ==
        'true';
    final localIds =
        await ref.read(favouriteProductIdsProvider(isLocal: true).future);
    final remoteIds = await ref.read(favouriteProductIdsProvider().future);

    if (listEquals(remoteIds, localIds)) {
      Groveman.info('FavouriteProductController - no update');
      return;
    }

    // if remote not empty && empty local storage -> update local storage
    if (remoteIds.isNotEmpty && localIds.isEmpty) {
      await ref
          .read(securedAppStorageProvider)
          .writeValue('favouriteIds', json.encode(remoteIds));
      Groveman.info('FavouriteProductController - update local storage');
    }
    // debounce true -> update remote storage
    else if (remoteIds.isNotEmpty &&
        localIds.isNotEmpty &&
        favouriteDebounce == true) {
      await syncLocalIdsToRemote();
      Groveman.info('FavouriteProductController - update remote storage');
    } else {
      await ref
          .read(securedAppStorageProvider)
          .writeValue('favouriteIds', json.encode([]));
      Groveman.info('FavouriteProductController - empty local storage');
    }

    return;
  }

  Future<void> toggleFavourite(String productId) async {
    try {
      final favouriteDebounce = await ref
              .read(securedAppStorageProvider)
              .readValue('favouriteDebounce') ==
          'true';
      final localIds =
          await ref.read(favouriteProductIdsProvider(isLocal: true).future);

      // toggle locally
      if (localIds.contains(productId)) {
        localIds.remove(productId);
      } else {
        localIds.add(productId);
      }

      await ref
          .read(securedAppStorageProvider)
          .writeValue('favouriteIds', json.encode(localIds));

      if (favouriteDebounce == false) {
        // set debounce flag to true for syncing with remote later
        await ref
            .read(securedAppStorageProvider)
            .writeValue('favouriteDebounce', 'true');

        // prevent multiple debounce
        // delayed update remote
        Timer(const Duration(seconds: 10), () async {
          await syncLocalIdsToRemote();
        });
      }
    } catch (error) {
      Groveman.error('toggleFavourite', error: error);
      rethrow;
    } finally {
      ref
        ..invalidate(favouriteProductIdsProvider)
        ..invalidate(favouriteProductsProvider);
    }
  }

  Future<void> syncLocalIdsToRemote() async {
    try {
      final localIds =
          await ref.read(favouriteProductIdsProvider(isLocal: true).future);

      await ref
          .watch(favouriteProductRepositoryProvider)
          .setFavourites(localIds);
    } catch (error) {
      Groveman.error('toggleFavourite', error: error);
      rethrow;
    } finally {
      // reset debounce flag
      await ref
          .read(securedAppStorageProvider)
          .writeValue('favouriteDebounce', 'false');
    }
  }
}
