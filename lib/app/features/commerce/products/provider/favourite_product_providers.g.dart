// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_product_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favouriteProductIdsHash() =>
    r'0cf99a97a29909cc02036f4f3922a7adfa2d421b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [favouriteProductIds].
@ProviderFor(favouriteProductIds)
const favouriteProductIdsProvider = FavouriteProductIdsFamily();

/// See also [favouriteProductIds].
class FavouriteProductIdsFamily extends Family<AsyncValue<List<String>>> {
  /// See also [favouriteProductIds].
  const FavouriteProductIdsFamily();

  /// See also [favouriteProductIds].
  FavouriteProductIdsProvider call({
    bool? isLocal,
  }) {
    return FavouriteProductIdsProvider(
      isLocal: isLocal,
    );
  }

  @override
  FavouriteProductIdsProvider getProviderOverride(
    covariant FavouriteProductIdsProvider provider,
  ) {
    return call(
      isLocal: provider.isLocal,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'favouriteProductIdsProvider';
}

/// See also [favouriteProductIds].
class FavouriteProductIdsProvider
    extends AutoDisposeFutureProvider<List<String>> {
  /// See also [favouriteProductIds].
  FavouriteProductIdsProvider({
    bool? isLocal,
  }) : this._internal(
          (ref) => favouriteProductIds(
            ref as FavouriteProductIdsRef,
            isLocal: isLocal,
          ),
          from: favouriteProductIdsProvider,
          name: r'favouriteProductIdsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$favouriteProductIdsHash,
          dependencies: FavouriteProductIdsFamily._dependencies,
          allTransitiveDependencies:
              FavouriteProductIdsFamily._allTransitiveDependencies,
          isLocal: isLocal,
        );

  FavouriteProductIdsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.isLocal,
  }) : super.internal();

  final bool? isLocal;

  @override
  Override overrideWith(
    FutureOr<List<String>> Function(FavouriteProductIdsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FavouriteProductIdsProvider._internal(
        (ref) => create(ref as FavouriteProductIdsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        isLocal: isLocal,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<String>> createElement() {
    return _FavouriteProductIdsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FavouriteProductIdsProvider && other.isLocal == isLocal;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, isLocal.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FavouriteProductIdsRef on AutoDisposeFutureProviderRef<List<String>> {
  /// The parameter `isLocal` of this provider.
  bool? get isLocal;
}

class _FavouriteProductIdsProviderElement
    extends AutoDisposeFutureProviderElement<List<String>>
    with FavouriteProductIdsRef {
  _FavouriteProductIdsProviderElement(super.provider);

  @override
  bool? get isLocal => (origin as FavouriteProductIdsProvider).isLocal;
}

String _$favouriteProductsHash() => r'4864f8fb60f87aa7e980b54186eb17dcce6f61d1';

/// See also [favouriteProducts].
@ProviderFor(favouriteProducts)
final favouriteProductsProvider =
    AutoDisposeFutureProvider<List<Product?>>.internal(
  favouriteProducts,
  name: r'favouriteProductsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favouriteProductsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FavouriteProductsRef = AutoDisposeFutureProviderRef<List<Product?>>;
String _$favouriteProductControllerHash() =>
    r'5b94e3ebf1e862c05ea4a4db682e29519ea31c41';

/// See also [FavouriteProductController].
@ProviderFor(FavouriteProductController)
final favouriteProductControllerProvider =
    AsyncNotifierProvider<FavouriteProductController, void>.internal(
  FavouriteProductController.new,
  name: r'favouriteProductControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favouriteProductControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavouriteProductController = AsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
