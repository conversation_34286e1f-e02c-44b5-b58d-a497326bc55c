import 'package:gomama/app/core/network/web_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'favourite_product_repository.g.dart';

@Riverpod(keepAlive: true)
FavouriteProductRepository favouriteProductRepository(
  FavouriteProductRepositoryRef ref,
) =>
    FavouriteProductRepository(ref);

class FavouriteProductRepository {
  FavouriteProductRepository(this.ref);
  final FavouriteProductRepositoryRef ref;

  Future<List<String>> getFavouriteProducts() async {
    try {
      final response = await ref
          .watch(repositoryProvider)
          .get<Json>('/me/products/favourites');
      return (response.data!['data'] as List).cast<String>();
    } catch (error) {
      Groveman.error('getFavouriteProducts', error: error);
      rethrow;
    }
  }

  Future<bool> toggleFavourite(String productId) async {
    try {
      final response = await ref.watch(repositoryProvider).post<Json>(
        '/me/products/favourites',
        data: {'product_id': productId},
      );
      return response.data?['success'] == true;
    } catch (error) {
      Groveman.error('toggleFavourite', error: error);
      rethrow;
    }
  }
  
  Future<bool> setFavourites(List<String> productIds) async {
    try {
      final response = await ref.watch(repositoryProvider).put<Json>(
        '/me/products/favourites',
        data: {'product_ids': productIds},
      );
      return response.data?['success'] == true;
    } catch (error) {
      Groveman.error('setFavourites', error: error);
      rethrow;
    }
  }
}
