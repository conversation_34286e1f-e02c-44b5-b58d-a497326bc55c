// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_product_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favouriteProductRepositoryHash() =>
    r'83bb495b493838888d46c926dacfa71bc319de1f';

/// See also [favouriteProductRepository].
@ProviderFor(favouriteProductRepository)
final favouriteProductRepositoryProvider =
    Provider<FavouriteProductRepository>.internal(
  favouriteProductRepository,
  name: r'favouriteProductRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favouriteProductRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FavouriteProductRepositoryRef = ProviderRef<FavouriteProductRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
