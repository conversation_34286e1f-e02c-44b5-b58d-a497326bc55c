import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_button.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PriceMatchView extends ConsumerWidget {
  const PriceMatchView({super.key});

  static const routeName = 'price-match';
  static const routePath = 'price-match';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      appBar: AppBar(
        elevation: 4,
        foregroundColor: CustomColors.primary,
        title: const Text('Price Match'),
        centerTitle: true,
      ),
      floatingActionButton: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: SizedBox(
            width: double.infinity,
            child: FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: () {
                const PriceMatchRequestRoute().push(context);
              },
              child: const Text('Price Match It!'),
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      body: Stack(
        children: [
          // banner
          Container(
            height: 400,
            padding: const EdgeInsets.fromLTRB(8, 12, 40, 0),
            alignment: Alignment.topCenter,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  CustomColors.backgroundGradientLight,
                  CustomColors.backgroundGradient,
                  CustomColors.backgroundGradientDark,
                ],
              ),
            ),
            child: Image.asset(
              'assets/images/price_match_banner.png',
              fit: BoxFit.contain,
            ),
          ),
          // FAQs
          BrandBottomSheet(
            maxHeight: 0,
            minHeight: mediaQuery(context).size.height - 400,
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 48, vertical: 32),
                  color: CustomColors.secondaryExtraLight,
                  child: Column(
                    children: [
                      Text(
                        'Guarantee Policy',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        "We want to ensure you get the best price on your purchases. If you find a lower price on an identical item from a qualifying competitor, we'll match it!",
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: CustomColors.primary),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Terms & Conditions',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _NumberedList(
                            1,
                            'Eligible Channels:',
                            'We will match prices from all local major retailers.',
                          ),
                          _NumberedList(
                            2,
                            'Product Eligibility:',
                            'The item must be identical in brand, model, size, and in brand new sealed condition.',
                          ),
                          _NumberedList(
                            3,
                            'Exclusions:',
                            'We do not match prices from clearance sales, bundle offers, platform or special promotions.',
                          ),
                          _NumberedList(
                            4,
                            'Time frame:',
                            'Requests must be made within 30 days of purchase.',
                          ),
                          _NumberedList(
                            5,
                            'Availability:',
                            'The item must be lower in price and ready in stock and not on a pre-order basis.',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _NumberedList extends StatelessWidget {
  const _NumberedList(this.number, this.title, this.content);

  final int number;
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$number.',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: CustomColors.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: ' '),
                  TextSpan(
                    text: content,
                  ),
                ],
              ),
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium!
                  .copyWith(color: CustomColors.primary),
            ),
          ),
        ],
      ),
    );
  }
}
