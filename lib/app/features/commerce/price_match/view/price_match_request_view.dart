import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/price_match/widget/form/additional_info_form.dart';
import 'package:gomama/app/features/commerce/price_match/widget/form/competition_info_form.dart';
import 'package:gomama/app/features/commerce/price_match/widget/form/customer_info_form.dart';
import 'package:gomama/app/features/commerce/price_match/widget/form/product_info_form.dart';
import 'package:gomama/app/features/commerce/price_match/widget/form_progress_indicator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final priceMatchPageControllerProvider =
    StateProvider.autoDispose<PageController?>((ref) => PageController());
final formProgressProvider = StateProvider.autoDispose<double?>((ref) => 0.2);

class PriceMatchRequestView extends ConsumerWidget {
  const PriceMatchRequestView({super.key});

  static const routeName = 'price-match-request';
  static const routePath = 'request';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = ref.watch(priceMatchPageControllerProvider);
    final formProgress = ref.watch(formProgressProvider);

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      appBar: AppBar(
        elevation: 4,
        foregroundColor: CustomColors.primary,
        title: const Text('Price Match Request'),
        centerTitle: true,
      ),
      body: (pageController == null || formProgress == null)
          ? const SizedBox.shrink()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: formProgress < 1.0
                  ? [
                      // form progress
                      const FormProgressIndicator(),
                      // form body
                      const _FormBody(),
                    ]
                  : [
                      const _SubmittedBody(),
                    ],
            ),
    );
  }
}

class _FormBody extends HookConsumerWidget {
  const _FormBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormBuilderState>());

    return FormBuilder(
      key: formKey.value,
      child: Expanded(
        child: Container(
          padding: const EdgeInsets.only(top: 32),
          decoration: const BoxDecoration(
            color: CustomColors.primary,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(36),
              topRight: Radius.circular(36),
            ),
          ),
          child: PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: ref.watch(priceMatchPageControllerProvider),
            children: [
              CustomerInfoForm(formKey.value),
              ProductInfoForm(formKey.value),
              CompetitionInfoForm(formKey.value),
              AdditionalInfoForm(formKey.value),
            ],
          ),
        ),
      ),
    );
  }
}

class _SubmittedBody extends ConsumerWidget {
  const _SubmittedBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(48),
        decoration: const BoxDecoration(
          color: CustomColors.primary,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(36),
            topRight: Radius.circular(36),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                height: 195,
                alignment: Alignment.topCenter,
                child: Image.asset(
                  'assets/images/goma_heart.png',
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Thank you for your submission!',
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: Colors.white,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Please allow 5 to 7 working days for our customer service officer to respond; we appreciate your patience. Go!Mama reserves the right to refuse any price match request, and the final decision regarding eligibility and price matching rests with us.',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.white,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Align(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  onPressed: () {
                    const CommerceRoute().go(context);
                  },
                  child: const Text('Back To Shop'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
