import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:gomama/app/features/commerce/price_match/provider/price_match_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

enum RequestStatus {
  inReview,
  approved,
  denied,
}

class MyRequestsView extends ConsumerWidget {
  const MyRequestsView({super.key});

  static const routeName = 'my-requests';
  static const routePath = 'my-requests';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final requestsInReview = ref.watch(priceMatchRequestsProvider);
    final requestsApproved = ref.watch(priceMatchRequestsProvider);
    final requestsDenied = ref.watch(priceMatchRequestsProvider);

    return DefaultTabController(
      length: 3,
      child: Scaffold(
        backgroundColor: CustomColors.primaries.shade50,
        appBar: AppBar(
          elevation: 4,
          foregroundColor: CustomColors.primary,
          title: const Text('My Requests'),
          bottom: const TabBar(
            isScrollable: true,
            labelPadding: EdgeInsets.symmetric(horizontal: 16),
            tabAlignment: TabAlignment.center,
            tabs: [
              Tab(text: 'In Review'),
              Tab(text: 'Approved'),
              Tab(text: 'Denied'),
            ],
          ),
          centerTitle: true,
        ),
        body: TabBarView(
          children: [
            _TabContent(
              asyncPriceMatch: requestsInReview,
              onLoadMore: () {},
            ),
            _TabContent(
              asyncPriceMatch: requestsApproved,
              onLoadMore: () {},
            ),
            _TabContent(
              asyncPriceMatch: requestsDenied,
              onLoadMore: () {},
            ),
          ],
        ),
      ),
    );
  }
}

class _TabContent extends ConsumerWidget {
  const _TabContent({required this.asyncPriceMatch, this.onLoadMore});

  final AsyncValue<List<PriceMatch>?> asyncPriceMatch;
  final void Function()? onLoadMore;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return asyncPriceMatch.when(
      data: (requests) => ListView.separated(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        itemCount: requests?.length ?? 0,
        itemBuilder: (context, index) {
          if (requests == null || requests.isEmpty == true) {
            return const SizedBox.shrink();
          }

          return _RequestCard(requests[index]);
        },
        separatorBuilder: (context, index) {
          return const SizedBox(height: 16);
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(child: Text(error.toString())),
    );
  }
}

class _RequestCard extends ConsumerWidget {
  const _RequestCard(this.request);

  final PriceMatch request;
  // final RequestStatus variant;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // String _getStatusString() {
    //   switch (variant) {
    //     case RequestStatus.inReview:
    //       return 'In Review';
    //     case RequestStatus.approved:
    //       return 'Approved';
    //     case RequestStatus.denied:
    //       return 'Denied';
    //   }
    // }

    Widget _buildDetailRow(String label, String value) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                label,
                style: textTheme(context).bodyMedium!.copyWith(
                      fontWeight: FontWeight.bold,
                      color: CustomColors.primary,
                    ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.right,
                style: textTheme(context).bodyMedium!.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
            ),
          ],
        ),
      );
    }

    void _showPriceMatchDetailsDialog() {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Price Match Request #${request.id}',
            style: textTheme(context).titleLarge!.copyWith(
                  color: CustomColors.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Status',
                      style: textTheme(context).titleMedium!.copyWith(
                            fontWeight: FontWeight.bold,
                            color: CustomColors.primary,
                          ),
                    ),
                    Text(
                      'In Review',
                      style: textTheme(context).titleMedium!.copyWith(
                            color: CustomColors.primary,
                          ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Submitted',
                      style: textTheme(context).titleMedium!.copyWith(
                            fontWeight: FontWeight.bold,
                            color: CustomColors.primary,
                          ),
                    ),
                    Text(
                      request.createdAt == null
                          ? '-'
                          : DateFormat('dd/MM/yyyy').format(request.createdAt!),
                      style: textTheme(context).titleMedium!.copyWith(
                            color: CustomColors.primary,
                          ),
                    ),
                  ],
                ),
                Divider(color: Colors.grey.shade300, height: 32),
                _buildDetailRow('Order Number', request.orderNumber ?? '-'),
                _buildDetailRow('Product Brand', request.productBrand),
                _buildDetailRow('Product Name', request.productName),
                _buildDetailRow('Product Model', request.productModelNumber),
                _buildDetailRow(
                  'Referenced Price',
                  '\$${request.price.toStringAsFixed(2)}',
                ),
                _buildDetailRow('Retailer/Platform', request.retailer),
                _buildDetailRow(
                  'Product Link',
                  request.productLink,
                ),
                Divider(color: Colors.grey.shade300, height: 32),
                Text(
                  'Comment',
                  style: textTheme(context).bodyMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                        color: CustomColors.primary,
                      ),
                ),
                Text(
                  request.comment ?? 'No additional comment',
                  style: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
              ],
            ),
          ),
          actions: [
            FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: () {
                context.pop();
              },
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.description),
              const SizedBox(width: 6),
              Text(
                'Price Match Request #${request.id}',
                style: textTheme(context).labelLarge!.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
              const Spacer(),
              Text(
                'In Review',
                style: textTheme(context).labelMedium!.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
            ],
          ),
          Divider(color: Colors.grey.shade300),
          _buildDetailRow('Product Brand', request.productBrand),
          _buildDetailRow('Product Name', request.productName),
          _buildDetailRow(
            'Referenced Price',
            '\$${request.price.toStringAsFixed(2)}',
          ),
          Divider(color: Colors.grey.shade300),
          GestureDetector(
            onTap: _showPriceMatchDetailsDialog,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Request Detail',
                  style: textTheme(context).labelMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                        color: CustomColors.primary,
                      ),
                ),
                const Icon(Icons.chevron_right),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
