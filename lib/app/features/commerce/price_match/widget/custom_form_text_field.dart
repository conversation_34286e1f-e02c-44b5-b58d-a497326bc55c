import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CustomFormTextField extends ConsumerWidget {
  const CustomFormTextField({
    super.key,
    required this.name,
    this.hintText,
    this.validator,
    this.keyboardType,
    this.maxLines,
    this.readOnly,
  });

  final String name;
  final String? hintText;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? maxLines;
  final bool? readOnly;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FormBuilderTextField(
      name: name,
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        errorStyle: Theme.of(context).textTheme.labelMedium!.copyWith(
              color: CustomColors.red,
            ),
        hintText: hintText,
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
      ),
      readOnly: readOnly ?? false,
    );
  }
}
