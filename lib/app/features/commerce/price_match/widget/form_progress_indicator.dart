import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FormProgressIndicator extends ConsumerWidget {
  const FormProgressIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final value = ref.watch(formProgressProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 4),
          Text(
            value == 0.2
                ? 'Customer\nInformation'
                : value == 0.5
                    ? 'Product\nInformation'
                    : value == 0.8
                        ? 'Competition\nInformation'
                        : 'Additional\nComments',
            style:
                Theme.of(context).textTheme.titleLarge!.copyWith(fontSize: 20),
            textAlign: TextAlign.start,
          ),
          const SizedBox(height: 12),
          Stack(
            children: [
              LinearProgressIndicator(
                value: value,
                backgroundColor: Colors.grey.shade500,
                minHeight: 6,
              ),
              Container(
                width:
                    (MediaQuery.of(context).size.width - 64) * (value ?? 0.2),
                height: 6,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      CustomColors.backgroundGradientDark,
                      CustomColors.backgroundGradient,
                      CustomColors.backgroundGradientLight,
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
