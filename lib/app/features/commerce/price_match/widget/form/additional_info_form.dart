import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:gomama/app/features/commerce/price_match/repository/price_match_repository.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:gomama/app/features/commerce/price_match/widget/custom_form_text_field.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

Map<String, dynamic> parseDoubles(Map<String, dynamic> input) {
  final result = <String, dynamic>{};

  input.forEach((key, value) {
    if (value is String) {
      // parce price to double
      try {
        final parsed = key == 'price' ? double.tryParse(value) : null;
        result[key] = parsed ?? value; // default if parsing fails
      } catch (e) {
        result[key] = value;
      }
    } else {
      // default for non-strings
      result[key] = value;
    }
  });

  return result;
}

class AdditionalInfoForm extends HookConsumerWidget {
  const AdditionalInfoForm(this.formKey, {super.key});

  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _isSubmitting = useState<bool>(false);

    Future<void> submitPriceMatch() async {
      if (formKey.currentContext == null) return;

      final success = formKey.currentState?.saveAndValidate();

      if (success != true) {
        return;
      }

      try {
        _isSubmitting.value = true;

        // process formstate into PriceMatch()
        final formValues = parseDoubles(formKey.currentState!.value);
        final processedBody = PriceMatchFormType.fromJson(formValues);

        // post api
        final response = await ref
            .read(priceMatchRepositoryProvider)
            .submitRequest(processedBody);

        _isSubmitting.value = false;

        if (response.success == true) {
          // go 1 if success
          ref.read(formProgressProvider.notifier).update((state) => 1);
        } else {
          if (context.mounted) {
            // popup if fail
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Something went wrong, try again later'),
              ),
            );
          }
        }
      } catch (error, stack) {
        Groveman.error('submitPriceMatch', error: error, stackTrace: stack);
        _isSubmitting.value = false;

        // popup if fail
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                error is AppNetworkResponseException
                    ? '${error.message}'
                    : 'Something went wrong, please try again later',
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Additional Comments (optional):',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            const CustomFormTextField(
              name: 'comment',
              hintText: 'Enter any additional comments or details',
              keyboardType: TextInputType.multiline,
              maxLines: 8,
            ),
            const SizedBox(height: 48),
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 48,
                        vertical: 16,
                      ),
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white, width: 2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.8);
                      ref.read(priceMatchPageControllerProvider)?.previousPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Back'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                      ),
                    ),
                    onPressed:
                        _isSubmitting.value == true ? null : submitPriceMatch,
                    child: Text(
                      _isSubmitting.value == true ? 'Submitting...' : 'Submit',
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
