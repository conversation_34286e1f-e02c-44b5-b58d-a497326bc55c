import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:gomama/app/features/commerce/price_match/widget/custom_form_text_field.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductInfoForm extends ConsumerWidget {
  const ProductInfoForm(this.formKey, {super.key});

  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Product Name:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'product_name',
              hintText: 'Enter product name',
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 24),
            const Text(
              'Brand:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'product_brand',
              hintText: 'Enter brand name',
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 24),
            const Text(
              'Model Number:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'product_model_number',
              hintText: 'Enter model number',
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 48),
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 48,
                        vertical: 16,
                      ),
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white, width: 2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.2);
                      ref.read(priceMatchPageControllerProvider)?.previousPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Back'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      // validate fields
                      final success = [
                        'product_name',
                        'product_brand',
                        'product_model_number',
                      ].every(
                        (field) =>
                            formKey.currentState?.fields[field]?.validate() ??
                            false,
                      );
                      if (success != true) return;
                  
                      // save the form after validate
                      formKey.currentState?.save();
                  
                      // next step
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.8);
                      ref.read(priceMatchPageControllerProvider)?.nextPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Next'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
