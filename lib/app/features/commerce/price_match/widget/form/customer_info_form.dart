import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:gomama/app/features/commerce/price_match/widget/custom_form_text_field.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CustomerInfoForm extends ConsumerWidget {
  const CustomerInfoForm(this.formKey, {super.key});

  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authControllerProvider).requireValue;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Email:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: '',
              hintText: user.emailAddress,
              readOnly: true,
            ),
            const SizedBox(height: 24),
            const Text(
              'Order Number (if applicable):',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            const CustomFormTextField(
              name: 'order_number',
              hintText: 'Enter your order number',
            ),
            const SizedBox(height: 48),
            Row(
              children: [
                const Expanded(child: SizedBox.shrink()),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      formKey.currentState?.save();

                      // next step
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.5);
                      ref.read(priceMatchPageControllerProvider)?.nextPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Next'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
