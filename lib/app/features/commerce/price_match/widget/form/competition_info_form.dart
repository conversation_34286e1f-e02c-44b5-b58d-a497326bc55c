import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/features/commerce/price_match/view/price_match_request_view.dart';
import 'package:gomama/app/features/commerce/price_match/widget/custom_form_text_field.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CompetitionInfoForm extends ConsumerWidget {
  const CompetitionInfoForm(this.formKey, {super.key});

  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Retailer/Platform Name:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'retailer',
              hintText: 'Enter retailer/platform name',
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 24),
            const Text(
              'Price:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'price',
              hintText: "Enter competitor's price",
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                FormBuilderValidators.numeric(),
              ]),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 24),
            const Text(
              'Product Link:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'product_link',
              hintText: 'Enter product link',
              validator: FormBuilderValidators.required(),
            ),
            const SizedBox(height: 48),
            Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 48,
                        vertical: 16,
                      ),
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white, width: 2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.5);
                      ref.read(priceMatchPageControllerProvider)?.previousPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Back'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      // validate fields
                      final success = [
                        'retailer',
                        'price',
                        'product_link',
                      ].every(
                        (field) =>
                            formKey.currentState?.fields[field]?.validate() ??
                            false,
                      );
                      if (success != true) return;
                  
                      // save the form after validate
                      formKey.currentState?.save();
                  
                      // next step
                      ref
                          .read(formProgressProvider.notifier)
                          .update((state) => 0.9);
                      ref.read(priceMatchPageControllerProvider)?.nextPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                    },
                    child: const Text('Next'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
