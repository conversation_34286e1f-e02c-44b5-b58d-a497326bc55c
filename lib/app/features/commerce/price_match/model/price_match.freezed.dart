// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'price_match.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PriceMatchFormType _$PriceMatchFormTypeFromJson(Map<String, dynamic> json) {
  return _PriceMatchFormType.fromJson(json);
}

/// @nodoc
mixin _$PriceMatchFormType {
  String? get orderNumber => throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  String get productBrand => throw _privateConstructorUsedError;
  String get productModelNumber => throw _privateConstructorUsedError;
  String get retailer => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String get productLink => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PriceMatchFormTypeCopyWith<PriceMatchFormType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriceMatchFormTypeCopyWith<$Res> {
  factory $PriceMatchFormTypeCopyWith(
          PriceMatchFormType value, $Res Function(PriceMatchFormType) then) =
      _$PriceMatchFormTypeCopyWithImpl<$Res, PriceMatchFormType>;
  @useResult
  $Res call(
      {String? orderNumber,
      String productName,
      String productBrand,
      String productModelNumber,
      String retailer,
      double price,
      String productLink,
      String? comment});
}

/// @nodoc
class _$PriceMatchFormTypeCopyWithImpl<$Res, $Val extends PriceMatchFormType>
    implements $PriceMatchFormTypeCopyWith<$Res> {
  _$PriceMatchFormTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNumber = freezed,
    Object? productName = null,
    Object? productBrand = null,
    Object? productModelNumber = null,
    Object? retailer = null,
    Object? price = null,
    Object? productLink = null,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productBrand: null == productBrand
          ? _value.productBrand
          : productBrand // ignore: cast_nullable_to_non_nullable
              as String,
      productModelNumber: null == productModelNumber
          ? _value.productModelNumber
          : productModelNumber // ignore: cast_nullable_to_non_nullable
              as String,
      retailer: null == retailer
          ? _value.retailer
          : retailer // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productLink: null == productLink
          ? _value.productLink
          : productLink // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PriceMatchFormTypeImplCopyWith<$Res>
    implements $PriceMatchFormTypeCopyWith<$Res> {
  factory _$$PriceMatchFormTypeImplCopyWith(_$PriceMatchFormTypeImpl value,
          $Res Function(_$PriceMatchFormTypeImpl) then) =
      __$$PriceMatchFormTypeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? orderNumber,
      String productName,
      String productBrand,
      String productModelNumber,
      String retailer,
      double price,
      String productLink,
      String? comment});
}

/// @nodoc
class __$$PriceMatchFormTypeImplCopyWithImpl<$Res>
    extends _$PriceMatchFormTypeCopyWithImpl<$Res, _$PriceMatchFormTypeImpl>
    implements _$$PriceMatchFormTypeImplCopyWith<$Res> {
  __$$PriceMatchFormTypeImplCopyWithImpl(_$PriceMatchFormTypeImpl _value,
      $Res Function(_$PriceMatchFormTypeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNumber = freezed,
    Object? productName = null,
    Object? productBrand = null,
    Object? productModelNumber = null,
    Object? retailer = null,
    Object? price = null,
    Object? productLink = null,
    Object? comment = freezed,
  }) {
    return _then(_$PriceMatchFormTypeImpl(
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productBrand: null == productBrand
          ? _value.productBrand
          : productBrand // ignore: cast_nullable_to_non_nullable
              as String,
      productModelNumber: null == productModelNumber
          ? _value.productModelNumber
          : productModelNumber // ignore: cast_nullable_to_non_nullable
              as String,
      retailer: null == retailer
          ? _value.retailer
          : retailer // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productLink: null == productLink
          ? _value.productLink
          : productLink // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PriceMatchFormTypeImpl implements _PriceMatchFormType {
  _$PriceMatchFormTypeImpl(
      {this.orderNumber,
      required this.productName,
      required this.productBrand,
      required this.productModelNumber,
      required this.retailer,
      required this.price,
      required this.productLink,
      this.comment});

  factory _$PriceMatchFormTypeImpl.fromJson(Map<String, dynamic> json) =>
      _$$PriceMatchFormTypeImplFromJson(json);

  @override
  final String? orderNumber;
  @override
  final String productName;
  @override
  final String productBrand;
  @override
  final String productModelNumber;
  @override
  final String retailer;
  @override
  final double price;
  @override
  final String productLink;
  @override
  final String? comment;

  @override
  String toString() {
    return 'PriceMatchFormType(orderNumber: $orderNumber, productName: $productName, productBrand: $productBrand, productModelNumber: $productModelNumber, retailer: $retailer, price: $price, productLink: $productLink, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriceMatchFormTypeImpl &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productBrand, productBrand) ||
                other.productBrand == productBrand) &&
            (identical(other.productModelNumber, productModelNumber) ||
                other.productModelNumber == productModelNumber) &&
            (identical(other.retailer, retailer) ||
                other.retailer == retailer) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.productLink, productLink) ||
                other.productLink == productLink) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, orderNumber, productName,
      productBrand, productModelNumber, retailer, price, productLink, comment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PriceMatchFormTypeImplCopyWith<_$PriceMatchFormTypeImpl> get copyWith =>
      __$$PriceMatchFormTypeImplCopyWithImpl<_$PriceMatchFormTypeImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PriceMatchFormTypeImplToJson(
      this,
    );
  }
}

abstract class _PriceMatchFormType implements PriceMatchFormType {
  factory _PriceMatchFormType(
      {final String? orderNumber,
      required final String productName,
      required final String productBrand,
      required final String productModelNumber,
      required final String retailer,
      required final double price,
      required final String productLink,
      final String? comment}) = _$PriceMatchFormTypeImpl;

  factory _PriceMatchFormType.fromJson(Map<String, dynamic> json) =
      _$PriceMatchFormTypeImpl.fromJson;

  @override
  String? get orderNumber;
  @override
  String get productName;
  @override
  String get productBrand;
  @override
  String get productModelNumber;
  @override
  String get retailer;
  @override
  double get price;
  @override
  String get productLink;
  @override
  String? get comment;
  @override
  @JsonKey(ignore: true)
  _$$PriceMatchFormTypeImplCopyWith<_$PriceMatchFormTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PriceMatch _$PriceMatchFromJson(Map<String, dynamic> json) {
  return _PriceMatch.fromJson(json);
}

/// @nodoc
mixin _$PriceMatch {
  int get id => throw _privateConstructorUsedError;
  String get customerName => throw _privateConstructorUsedError;
  String get customerEmail => throw _privateConstructorUsedError;
  String? get orderNumber => throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  String get productBrand => throw _privateConstructorUsedError;
  String get productModelNumber => throw _privateConstructorUsedError;
  String get retailer => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String get productLink => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PriceMatchCopyWith<PriceMatch> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriceMatchCopyWith<$Res> {
  factory $PriceMatchCopyWith(
          PriceMatch value, $Res Function(PriceMatch) then) =
      _$PriceMatchCopyWithImpl<$Res, PriceMatch>;
  @useResult
  $Res call(
      {int id,
      String customerName,
      String customerEmail,
      String? orderNumber,
      String productName,
      String productBrand,
      String productModelNumber,
      String retailer,
      double price,
      String productLink,
      String? comment,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$PriceMatchCopyWithImpl<$Res, $Val extends PriceMatch>
    implements $PriceMatchCopyWith<$Res> {
  _$PriceMatchCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerName = null,
    Object? customerEmail = null,
    Object? orderNumber = freezed,
    Object? productName = null,
    Object? productBrand = null,
    Object? productModelNumber = null,
    Object? retailer = null,
    Object? price = null,
    Object? productLink = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      customerEmail: null == customerEmail
          ? _value.customerEmail
          : customerEmail // ignore: cast_nullable_to_non_nullable
              as String,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productBrand: null == productBrand
          ? _value.productBrand
          : productBrand // ignore: cast_nullable_to_non_nullable
              as String,
      productModelNumber: null == productModelNumber
          ? _value.productModelNumber
          : productModelNumber // ignore: cast_nullable_to_non_nullable
              as String,
      retailer: null == retailer
          ? _value.retailer
          : retailer // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productLink: null == productLink
          ? _value.productLink
          : productLink // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PriceMatchImplCopyWith<$Res>
    implements $PriceMatchCopyWith<$Res> {
  factory _$$PriceMatchImplCopyWith(
          _$PriceMatchImpl value, $Res Function(_$PriceMatchImpl) then) =
      __$$PriceMatchImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String customerName,
      String customerEmail,
      String? orderNumber,
      String productName,
      String productBrand,
      String productModelNumber,
      String retailer,
      double price,
      String productLink,
      String? comment,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$PriceMatchImplCopyWithImpl<$Res>
    extends _$PriceMatchCopyWithImpl<$Res, _$PriceMatchImpl>
    implements _$$PriceMatchImplCopyWith<$Res> {
  __$$PriceMatchImplCopyWithImpl(
      _$PriceMatchImpl _value, $Res Function(_$PriceMatchImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerName = null,
    Object? customerEmail = null,
    Object? orderNumber = freezed,
    Object? productName = null,
    Object? productBrand = null,
    Object? productModelNumber = null,
    Object? retailer = null,
    Object? price = null,
    Object? productLink = null,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$PriceMatchImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      customerEmail: null == customerEmail
          ? _value.customerEmail
          : customerEmail // ignore: cast_nullable_to_non_nullable
              as String,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productBrand: null == productBrand
          ? _value.productBrand
          : productBrand // ignore: cast_nullable_to_non_nullable
              as String,
      productModelNumber: null == productModelNumber
          ? _value.productModelNumber
          : productModelNumber // ignore: cast_nullable_to_non_nullable
              as String,
      retailer: null == retailer
          ? _value.retailer
          : retailer // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productLink: null == productLink
          ? _value.productLink
          : productLink // ignore: cast_nullable_to_non_nullable
              as String,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$PriceMatchImpl implements _PriceMatch {
  _$PriceMatchImpl(
      {required this.id,
      required this.customerName,
      required this.customerEmail,
      this.orderNumber,
      required this.productName,
      required this.productBrand,
      required this.productModelNumber,
      required this.retailer,
      required this.price,
      required this.productLink,
      this.comment,
      this.createdAt,
      this.updatedAt});

  factory _$PriceMatchImpl.fromJson(Map<String, dynamic> json) =>
      _$$PriceMatchImplFromJson(json);

  @override
  final int id;
  @override
  final String customerName;
  @override
  final String customerEmail;
  @override
  final String? orderNumber;
  @override
  final String productName;
  @override
  final String productBrand;
  @override
  final String productModelNumber;
  @override
  final String retailer;
  @override
  final double price;
  @override
  final String productLink;
  @override
  final String? comment;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'PriceMatch(id: $id, customerName: $customerName, customerEmail: $customerEmail, orderNumber: $orderNumber, productName: $productName, productBrand: $productBrand, productModelNumber: $productModelNumber, retailer: $retailer, price: $price, productLink: $productLink, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriceMatchImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerEmail, customerEmail) ||
                other.customerEmail == customerEmail) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productBrand, productBrand) ||
                other.productBrand == productBrand) &&
            (identical(other.productModelNumber, productModelNumber) ||
                other.productModelNumber == productModelNumber) &&
            (identical(other.retailer, retailer) ||
                other.retailer == retailer) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.productLink, productLink) ||
                other.productLink == productLink) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      customerName,
      customerEmail,
      orderNumber,
      productName,
      productBrand,
      productModelNumber,
      retailer,
      price,
      productLink,
      comment,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PriceMatchImplCopyWith<_$PriceMatchImpl> get copyWith =>
      __$$PriceMatchImplCopyWithImpl<_$PriceMatchImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PriceMatchImplToJson(
      this,
    );
  }
}

abstract class _PriceMatch implements PriceMatch {
  factory _PriceMatch(
      {required final int id,
      required final String customerName,
      required final String customerEmail,
      final String? orderNumber,
      required final String productName,
      required final String productBrand,
      required final String productModelNumber,
      required final String retailer,
      required final double price,
      required final String productLink,
      final String? comment,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$PriceMatchImpl;

  factory _PriceMatch.fromJson(Map<String, dynamic> json) =
      _$PriceMatchImpl.fromJson;

  @override
  int get id;
  @override
  String get customerName;
  @override
  String get customerEmail;
  @override
  String? get orderNumber;
  @override
  String get productName;
  @override
  String get productBrand;
  @override
  String get productModelNumber;
  @override
  String get retailer;
  @override
  double get price;
  @override
  String get productLink;
  @override
  String? get comment;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$PriceMatchImplCopyWith<_$PriceMatchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PriceMatchResponse _$PriceMatchResponseFromJson(Map<String, dynamic> json) {
  return _PriceMatchResponse.fromJson(json);
}

/// @nodoc
mixin _$PriceMatchResponse {
  List<PriceMatch> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PriceMatchResponseCopyWith<PriceMatchResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriceMatchResponseCopyWith<$Res> {
  factory $PriceMatchResponseCopyWith(
          PriceMatchResponse value, $Res Function(PriceMatchResponse) then) =
      _$PriceMatchResponseCopyWithImpl<$Res, PriceMatchResponse>;
  @useResult
  $Res call({List<PriceMatch> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$PriceMatchResponseCopyWithImpl<$Res, $Val extends PriceMatchResponse>
    implements $PriceMatchResponseCopyWith<$Res> {
  _$PriceMatchResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<PriceMatch>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PriceMatchResponseImplCopyWith<$Res>
    implements $PriceMatchResponseCopyWith<$Res> {
  factory _$$PriceMatchResponseImplCopyWith(_$PriceMatchResponseImpl value,
          $Res Function(_$PriceMatchResponseImpl) then) =
      __$$PriceMatchResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PriceMatch> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$PriceMatchResponseImplCopyWithImpl<$Res>
    extends _$PriceMatchResponseCopyWithImpl<$Res, _$PriceMatchResponseImpl>
    implements _$$PriceMatchResponseImplCopyWith<$Res> {
  __$$PriceMatchResponseImplCopyWithImpl(_$PriceMatchResponseImpl _value,
      $Res Function(_$PriceMatchResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$PriceMatchResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<PriceMatch>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PriceMatchResponseImpl implements _PriceMatchResponse {
  _$PriceMatchResponseImpl(
      {required final List<PriceMatch> data, required this.meta})
      : _data = data;

  factory _$PriceMatchResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PriceMatchResponseImplFromJson(json);

  final List<PriceMatch> _data;
  @override
  List<PriceMatch> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'PriceMatchResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriceMatchResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PriceMatchResponseImplCopyWith<_$PriceMatchResponseImpl> get copyWith =>
      __$$PriceMatchResponseImplCopyWithImpl<_$PriceMatchResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PriceMatchResponseImplToJson(
      this,
    );
  }
}

abstract class _PriceMatchResponse implements PriceMatchResponse {
  factory _PriceMatchResponse(
      {required final List<PriceMatch> data,
      required final Pagination meta}) = _$PriceMatchResponseImpl;

  factory _PriceMatchResponse.fromJson(Map<String, dynamic> json) =
      _$PriceMatchResponseImpl.fromJson;

  @override
  List<PriceMatch> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$PriceMatchResponseImplCopyWith<_$PriceMatchResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
