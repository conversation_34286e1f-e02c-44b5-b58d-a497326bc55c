import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';

part 'price_match.freezed.dart';
part 'price_match.g.dart';

@freezed
class PriceMatchFormType with _$PriceMatchFormType {
  factory PriceMatchFormType({
    String? orderNumber,
    required String productName,
    required String productBrand,
    required String productModelNumber,
    required String retailer,
    required double price,
    required String productLink,
    String? comment,
  }) = _PriceMatchFormType;

  factory PriceMatchFormType.fromJson(Json json) =>
      _$PriceMatchFormTypeFromJson(json);
}

@freezed
class PriceMatch with _$PriceMatch {
  @CustomDateTimeConverter()
  factory PriceMatch({
    required int id,
    required String customerName,
    required String customerEmail,
    String? orderNumber,
    required String productName,
    required String productBrand,
    required String productModelNumber,
    required String retailer,
    required double price,
    required String productLink,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _PriceMatch;

  factory PriceMatch.fromJson(Json json) => _$PriceMatchFromJson(json);
}

@freezed
class PriceMatchResponse with _$PriceMatchResponse {
  factory PriceMatchResponse({
    required List<PriceMatch> data,
    required Pagination meta,
  }) = _PriceMatchResponse;

  factory PriceMatchResponse.fromJson(Json json) =>
      _$PriceMatchResponseFromJson(json);
}
