// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_match.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PriceMatchFormTypeImpl _$$PriceMatchFormTypeImplFromJson(
        Map<String, dynamic> json) =>
    _$PriceMatchFormTypeImpl(
      orderNumber: json['order_number'] as String?,
      productName: json['product_name'] as String,
      productBrand: json['product_brand'] as String,
      productModelNumber: json['product_model_number'] as String,
      retailer: json['retailer'] as String,
      price: (json['price'] as num).toDouble(),
      productLink: json['product_link'] as String,
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$PriceMatchFormTypeImplToJson(
        _$PriceMatchFormTypeImpl instance) =>
    <String, dynamic>{
      'order_number': instance.orderNumber,
      'product_name': instance.productName,
      'product_brand': instance.productBrand,
      'product_model_number': instance.productModelNumber,
      'retailer': instance.retailer,
      'price': instance.price,
      'product_link': instance.productLink,
      'comment': instance.comment,
    };

_$PriceMatchImpl _$$PriceMatchImplFromJson(Map<String, dynamic> json) =>
    _$PriceMatchImpl(
      id: (json['id'] as num).toInt(),
      customerName: json['customer_name'] as String,
      customerEmail: json['customer_email'] as String,
      orderNumber: json['order_number'] as String?,
      productName: json['product_name'] as String,
      productBrand: json['product_brand'] as String,
      productModelNumber: json['product_model_number'] as String,
      retailer: json['retailer'] as String,
      price: (json['price'] as num).toDouble(),
      productLink: json['product_link'] as String,
      comment: json['comment'] as String?,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$PriceMatchImplToJson(_$PriceMatchImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customer_name': instance.customerName,
      'customer_email': instance.customerEmail,
      'order_number': instance.orderNumber,
      'product_name': instance.productName,
      'product_brand': instance.productBrand,
      'product_model_number': instance.productModelNumber,
      'retailer': instance.retailer,
      'price': instance.price,
      'product_link': instance.productLink,
      'comment': instance.comment,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$PriceMatchResponseImpl _$$PriceMatchResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$PriceMatchResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => PriceMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PriceMatchResponseImplToJson(
        _$PriceMatchResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
