import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'price_match_repository.g.dart';

@Riverpod(keepAlive: true)
PriceMatchRepository priceMatchRepository(
  PriceMatchRepositoryRef ref,
) =>
    PriceMatchRepository(ref);

class PriceMatchRepository {
  PriceMatchRepository(this.ref);
  final PriceMatchRepositoryRef ref;

  Future<PriceMatchResponse> fetchRequests() async {
    try {
      final response = await ref
          .read(repositoryProvider)
          .get<Json>('/cms/price-match-requests');

      return PriceMatchResponse.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchRequests', error: error);
      rethrow;
    }
  }

  Future<PostResponse<void>> submitRequest(
    PriceMatchFormType body,
  ) async {
    try {
      final response = await ref
          .read(repositoryProvider)
          .post<Json>('/cms/price-match-requests', data: body.toJson());

      return PostResponse.fromJson(response.data!, (json) {});
    } catch (error) {
      Groveman.warning('submitRequest', error: error);
      rethrow;
    }
  }
}
