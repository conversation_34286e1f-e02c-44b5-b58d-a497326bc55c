// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_match_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$priceMatchRepositoryHash() =>
    r'fa188450ba1eba21378cfff3628040e2e6061edb';

/// See also [priceMatchRepository].
@ProviderFor(priceMatchRepository)
final priceMatchRepositoryProvider = Provider<PriceMatchRepository>.internal(
  priceMatchRepository,
  name: r'priceMatchRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$priceMatchRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PriceMatchRepositoryRef = ProviderRef<PriceMatchRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
