// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_match_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$priceMatchRequestsHash() =>
    r'aaabe5bc458ab0041c9ecdbf609377836cf68c85';

/// See also [priceMatchRequests].
@ProviderFor(priceMatchRequests)
final priceMatchRequestsProvider =
    AutoDisposeFutureProvider<List<PriceMatch>?>.internal(
  priceMatchRequests,
  name: r'priceMatchRequestsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$priceMatchRequestsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PriceMatchRequestsRef = AutoDisposeFutureProviderRef<List<PriceMatch>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
