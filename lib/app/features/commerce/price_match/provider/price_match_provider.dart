import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:gomama/app/features/commerce/price_match/repository/price_match_repository.dart';
import 'package:gomama/app/features/commerce/repository/commerce_banner_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'price_match_provider.g.dart';

@riverpod
Future<List<PriceMatch>?> priceMatchRequests(PriceMatchRequestsRef ref) async {
  try {
    final response =
        await ref.watch(priceMatchRepositoryProvider).fetchRequests();

    return response.data;
  } catch (e) {
    Groveman.error('priceMatchRequests', error: e);
    return null;
  }
}
