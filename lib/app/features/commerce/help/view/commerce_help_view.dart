import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

enum HelpOptionType {
  orders('Orders (Tracking, Delivery, Returns)'),
  payments('Payments & Refunds'),
  account('Account or Login Issues'),
  technical('App or Technical Problems'),
  product('Product Information or General Inquiry'),
  others('Others');

  const HelpOptionType(this.title);
  final String title;
}

class CommerceHelpView extends HookConsumerWidget {
  const CommerceHelpView({super.key});

  static const routeName = 'commerce-help';
  static const routePath = 'help';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nextStep = useState<bool>(false);
    final selectedOption = useState<HelpOptionType?>(null);
    final helpOptions = [
      HelpOptionType.orders,
      HelpOptionType.payments,
      HelpOptionType.account,
      HelpOptionType.technical,
      HelpOptionType.product,
      HelpOptionType.others,
    ];

    Widget _buildHelpOption(HelpOptionType option) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: GestureDetector(
          onTap: () => selectedOption.value = option,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: selectedOption.value == option
                  ? CustomColors.primary.withValues(alpha: 0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(36),
              border: Border.all(
                color: selectedOption.value == option
                    ? CustomColors.primary
                    : Colors.grey.shade300,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    option.title,
                    style: textTheme(context).bodyMedium!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                ),
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: selectedOption.value == option
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                      width: 2,
                    ),
                  ),
                  child: selectedOption.value == option
                      ? const Center(
                          child: Icon(
                            Icons.circle,
                            size: 12,
                            color: CustomColors.primary,
                          ),
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: CustomColors.primary,
        foregroundColor: Colors.white,
        title: const Text('Help & Support'),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: nextStep.value
          ? const SizedBox.shrink()
          : Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              width: double.infinity,
              child: FilledButton(
                onPressed: selectedOption.value == null
                    ? null
                    : () => nextStep.value = true,
                style: FilledButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: const Text(
                  'Next',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
      body: nextStep.value
          ? _ConnectWithSupportView(selectedOption.value)
          : Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 32),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'How can we help you today?',
                            style: textTheme(context).titleLarge!.copyWith(
                                  fontSize: 32,
                                  color: CustomColors.primary,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Please Choose One Option',
                            style: textTheme(context).labelMedium!.copyWith(
                                  color: CustomColors.primary,
                                  fontStyle: FontStyle.italic,
                                ),
                          ),
                          const SizedBox(height: 16),
                          ...helpOptions.map(_buildHelpOption),
                          const SizedBox(height: 64),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}

class _ConnectWithSupportView extends ConsumerWidget {
  const _ConnectWithSupportView(this.option);

  final HelpOptionType? option;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 48),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              height: 195,
              alignment: Alignment.topCenter,
              child: Image.asset(
                'assets/images/goma_heart.png',
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'We’re Here To Help!',
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Thank you for sharing your concern.',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: CustomColors.primary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 6),
            Text(
              'Tap the button below to connect with our support team. We’ll assist you right away!',
              style: Theme.of(context).textTheme.labelMedium!.copyWith(
                    color: CustomColors.primary,
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: () {
                ref.read(
                  sendWhatsAppMessageProvider(
                    'Hi GO!MAMA, I need some help on ${option != null ? option?.title : 'Others'}',
                    Environment.gomamaWhatsappPhone,
                  ),
                );
              },
              child: const Text('Connect With Support'),
            ),
          ],
        ),
      ),
    );
  }
}
