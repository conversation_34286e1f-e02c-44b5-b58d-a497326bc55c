import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/coin/widget/coin_check_in.dart';
import 'package:gomama/app/features/commerce/widget/commerce_swipeable_banner.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DailyCoinsView extends ConsumerWidget {
  const DailyCoinsView({super.key});

  static const routeName = 'daily-coins';
  static const routePath = 'daily-coins';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StarsBackground(
      child: MediaQuery.removePadding(
        context: context,
        // removeTop: true,
        removeBottom: true,
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              SliverOverlapAbsorber(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
                sliver: const SliverSafeArea(
                  top: false,
                  sliver: SliverAppBar(
                    title: Text('Collect Daily Coins'),
                    centerTitle: true,
                    foregroundColor: Colors.white,
                    collapsedHeight: kTextTabBarHeight,
                    toolbarHeight: kTextTabBarHeight,
                    expandedHeight: kTextTabBarHeight,
                    pinned: true,
                    scrolledUnderElevation: 0,
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ),
            ],
            body: const Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                MilkBackground(),
                Expanded(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: CustomColors.secondaryLight,
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: SingleChildScrollView(
                        // required to not clip negative positioned widgets
                        clipBehavior: Clip.none,
                        padding: EdgeInsets.only(bottom: 32),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            CoinCheckIn(),
                            SizedBox(height: 20),
                            CommerceSwipeableBanner(
                              bannerType: 'coin',
                              showSearch: false,
                              aspectRatio: 350 / 150,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(16)),
                              elevation: 4,
                              clipBehavior: Clip.hardEdge,
                              label: 'Earn More Coins',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
