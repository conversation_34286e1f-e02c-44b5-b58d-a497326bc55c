import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/repository/user_repository.dart';
import 'package:gomama/app/features/commerce/coin/model/commerce_coin.dart';
import 'package:gomama/app/features/commerce/coin/repository/coins_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'coins_providers.g.dart';

final coinsBusyProvider = StateProvider.autoDispose<bool>((ref) => false);
final coinsDiscountProvider = StateProvider.autoDispose<double?>((ref) => null);

@riverpod
Future<CommerceCoin?> coinBalance(CoinBalanceRef ref) async {
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return null;
  }

  try {
    return ref.read(coinsRepositoryProvider).getCoinsBalance();
  } catch (error) {
    Groveman.error('coinBalance', error: error);
    return null;
  }
}

@riverpod
Future<bool> checkInCoins(
  CheckInCoinsRef ref,
) async {
  try {
    return await ref.read(coinsRepositoryProvider).checkInCoins();
  } catch (error) {
    Groveman.error('checkInCoins', error: error);
    rethrow;
  }
}

@riverpod
Future<MaxRedeemableCoins> maxRedeemableCoins(
  MaxRedeemableCoinsRef ref,
) async {
  try {
    final response =
        await ref.read(coinsRepositoryProvider).maxRedeemableCoins();

    // update discount amount provider
    ref
        .read(coinsDiscountProvider.notifier)
        .update((state) => response.discountAmount);

    return response;
  } catch (error) {
    Groveman.error('maxRedeemableCoins', error: error);
    rethrow;
  }
}

@riverpod
Future<String> redeemCoins(
  RedeemCoinsRef ref,
  int points,
) async {
  try {
    return await ref.read(coinsRepositoryProvider).redeemCoins(points);
  } catch (error) {
    Groveman.error('redeemCoins', error: error);
    rethrow;
  }
}

@riverpod
Future<void> cancelRedeem(
  CancelRedeemRef ref,
) async {
  try {
    await ref.read(coinsRepositoryProvider).cancelCoinRedemption();
  } catch (error) {
    Groveman.error('cancelRedeem', error: error);
    rethrow;
  }
}
