// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coins_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$coinBalanceHash() => r'a7d19cfaee4d33cbdb91fd6f688a7d3d39fdf37b';

/// See also [coinBalance].
@ProviderFor(coinBalance)
final coinBalanceProvider = AutoDisposeFutureProvider<CommerceCoin?>.internal(
  coinBalance,
  name: r'coinBalanceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$coinBalanceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CoinBalanceRef = AutoDisposeFutureProviderRef<CommerceCoin?>;
String _$checkInCoinsHash() => r'81d7de829cc2d6a39b7305b86527ac940208236e';

/// See also [checkInCoins].
@ProviderFor(checkInCoins)
final checkInCoinsProvider = AutoDisposeFutureProvider<bool>.internal(
  checkInCoins,
  name: r'checkInCoinsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$checkInCoinsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CheckInCoinsRef = AutoDisposeFutureProviderRef<bool>;
String _$maxRedeemableCoinsHash() =>
    r'5cd901eb5e2cc1dece8e4296c3104a75c81e645f';

/// See also [maxRedeemableCoins].
@ProviderFor(maxRedeemableCoins)
final maxRedeemableCoinsProvider =
    AutoDisposeFutureProvider<MaxRedeemableCoins>.internal(
  maxRedeemableCoins,
  name: r'maxRedeemableCoinsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$maxRedeemableCoinsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MaxRedeemableCoinsRef
    = AutoDisposeFutureProviderRef<MaxRedeemableCoins>;
String _$redeemCoinsHash() => r'6a0176af4c6988238a5632ce0156ee248bab285e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [redeemCoins].
@ProviderFor(redeemCoins)
const redeemCoinsProvider = RedeemCoinsFamily();

/// See also [redeemCoins].
class RedeemCoinsFamily extends Family<AsyncValue<String>> {
  /// See also [redeemCoins].
  const RedeemCoinsFamily();

  /// See also [redeemCoins].
  RedeemCoinsProvider call(
    int points,
  ) {
    return RedeemCoinsProvider(
      points,
    );
  }

  @override
  RedeemCoinsProvider getProviderOverride(
    covariant RedeemCoinsProvider provider,
  ) {
    return call(
      provider.points,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'redeemCoinsProvider';
}

/// See also [redeemCoins].
class RedeemCoinsProvider extends AutoDisposeFutureProvider<String> {
  /// See also [redeemCoins].
  RedeemCoinsProvider(
    int points,
  ) : this._internal(
          (ref) => redeemCoins(
            ref as RedeemCoinsRef,
            points,
          ),
          from: redeemCoinsProvider,
          name: r'redeemCoinsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$redeemCoinsHash,
          dependencies: RedeemCoinsFamily._dependencies,
          allTransitiveDependencies:
              RedeemCoinsFamily._allTransitiveDependencies,
          points: points,
        );

  RedeemCoinsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.points,
  }) : super.internal();

  final int points;

  @override
  Override overrideWith(
    FutureOr<String> Function(RedeemCoinsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RedeemCoinsProvider._internal(
        (ref) => create(ref as RedeemCoinsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        points: points,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<String> createElement() {
    return _RedeemCoinsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RedeemCoinsProvider && other.points == points;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, points.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin RedeemCoinsRef on AutoDisposeFutureProviderRef<String> {
  /// The parameter `points` of this provider.
  int get points;
}

class _RedeemCoinsProviderElement
    extends AutoDisposeFutureProviderElement<String> with RedeemCoinsRef {
  _RedeemCoinsProviderElement(super.provider);

  @override
  int get points => (origin as RedeemCoinsProvider).points;
}

String _$cancelRedeemHash() => r'e73b0741ab14c8dbdcd6fe95949df5f590687dba';

/// See also [cancelRedeem].
@ProviderFor(cancelRedeem)
final cancelRedeemProvider = AutoDisposeFutureProvider<void>.internal(
  cancelRedeem,
  name: r'cancelRedeemProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$cancelRedeemHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CancelRedeemRef = AutoDisposeFutureProviderRef<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
