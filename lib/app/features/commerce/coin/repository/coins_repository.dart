import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/coin/model/commerce_coin.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'coins_repository.g.dart';

@Riverpod(keepAlive: true)
CoinsRepository coinsRepository(
  CoinsRepositoryRef ref,
) =>
    CoinsRepository(ref);

class CoinsRepository {
  CoinsRepository(this.ref);
  final CoinsRepositoryRef ref;

  Future<CommerceCoin> getCoinsBalance() async {
    try {
      final response =
          await ref.watch(repositoryProvider).get<Json>('/me/coins/balance');
      return CommerceCoin.fromJson(response.data!['data'] as Json);
    } catch (error) {
      Groveman.error('getCoinsBalance', error: error);
      rethrow;
    }
  }

  Future<bool> checkInCoins() async {
    try {
      final response =
          await ref.watch(repositoryProvider).post<Json>('/me/coins/check-in');
      return response.data!['data']['success'] == true;
    } catch (error) {
      Groveman.error('checkInCoins', error: error);
      rethrow;
    }
  }

  Future<MaxRedeemableCoins> maxRedeemableCoins() async {
    try {
      final cart = await ref.read(cartControllerProvider.future);
      final response = await ref.watch(repositoryProvider).post<Json>(
        '/me/coins/max-redeemable',
        data: {
          'cart_id': cart.id,
        },
      );
      return MaxRedeemableCoins.fromJson(response.data!['data'] as Json);
    } catch (error) {
      Groveman.error('maxRedeemableCoins', error: error);
      rethrow;
    }
  }

  Future<String> redeemCoins(int points) async {
    try {
      final cart = await ref.read(cartControllerProvider.future);
      final response = await ref.watch(repositoryProvider).post<Json>(
        '/me/coins/redeem',
        data: {
          'points': points,
          'cart_id': cart.id,
        },
      );
      return response.data!['data']['code'] as String;
    } catch (error) {
      Groveman.error('redeemCoins', error: error);
      rethrow;
    }
  }

  Future<void> cancelCoinRedemption() async {
    try {
      final coinBalance = await ref.read(coinBalanceProvider.future);

      if (coinBalance?.activeRedemption == null) return;

      await ref.watch(repositoryProvider).post<Json>(
        '/me/coins/cancel',
        data: {'redemption_id': coinBalance?.activeRedemption['code'] ?? ''},
      );
    } catch (error) {
      Groveman.error('cancelCoinRedemption', error: error);
      rethrow;
    }
  }
}
