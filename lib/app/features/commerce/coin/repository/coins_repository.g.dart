// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coins_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$coinsRepositoryHash() => r'bce5247458f6a9f3083bc0466ecf91bea616e437';

/// See also [coinsRepository].
@ProviderFor(coinsRepository)
final coinsRepositoryProvider = Provider<CoinsRepository>.internal(
  coinsRepository,
  name: r'coinsRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$coinsRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CoinsRepositoryRef = ProviderRef<CoinsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
