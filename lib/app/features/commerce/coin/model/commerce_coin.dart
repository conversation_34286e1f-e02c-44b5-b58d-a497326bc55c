import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'commerce_coin.freezed.dart';
part 'commerce_coin.g.dart';

@freezed
class CommerceCoin with _$CommerceCoin {
  factory CommerceCoin({
    required String externalId,
    required String email,
    required int totalPoints,
    required int totalEarned,
    required int eachCurrencySpend,
    required int eachPoint,
    required DailyLogin dailyLogin,
    dynamic activeRedemption,
  }) = _CommerceCoin;

  factory CommerceCoin.fromJson(Json json) => _$CommerceCoinFromJson(json);
}

@freezed
class DailyLogin with _$DailyLogin {
  factory DailyLogin({
    required bool hasLoggedInToday,
    required int currentStreak,
    required int highestStreak,
  }) = _DailyLogin;

  factory DailyLogin.fromJson(Json json) => _$DailyLoginFromJson(json);
}

@freezed
class MaxRedeemableCoins with _$MaxRedeemableCoins {
  factory MaxRedeemableCoins({
    required int actualMaxRedeemable,
    required int customerPointOnHand,
    double? discountAmount,
  }) = _MaxRedeemableCoins;

  factory MaxRedeemableCoins.fromJson(Json json) =>
      _$MaxRedeemableCoinsFromJson(json);
}
