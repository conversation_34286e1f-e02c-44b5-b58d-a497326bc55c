// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commerce_coin.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CommerceCoinImpl _$$CommerceCoinImplFromJson(Map<String, dynamic> json) =>
    _$CommerceCoinImpl(
      externalId: json['external_id'] as String,
      email: json['email'] as String,
      totalPoints: (json['total_points'] as num).toInt(),
      totalEarned: (json['total_earned'] as num).toInt(),
      eachCurrencySpend: (json['each_currency_spend'] as num).toInt(),
      eachPoint: (json['each_point'] as num).toInt(),
      dailyLogin:
          DailyLogin.fromJson(json['daily_login'] as Map<String, dynamic>),
      activeRedemption: json['active_redemption'],
    );

Map<String, dynamic> _$$CommerceCoinImplToJson(_$CommerceCoinImpl instance) =>
    <String, dynamic>{
      'external_id': instance.externalId,
      'email': instance.email,
      'total_points': instance.totalPoints,
      'total_earned': instance.totalEarned,
      'each_currency_spend': instance.eachCurrencySpend,
      'each_point': instance.eachPoint,
      'daily_login': instance.dailyLogin.toJson(),
      'active_redemption': instance.activeRedemption,
    };

_$DailyLoginImpl _$$DailyLoginImplFromJson(Map<String, dynamic> json) =>
    _$DailyLoginImpl(
      hasLoggedInToday: json['has_logged_in_today'] as bool,
      currentStreak: (json['current_streak'] as num).toInt(),
      highestStreak: (json['highest_streak'] as num).toInt(),
    );

Map<String, dynamic> _$$DailyLoginImplToJson(_$DailyLoginImpl instance) =>
    <String, dynamic>{
      'has_logged_in_today': instance.hasLoggedInToday,
      'current_streak': instance.currentStreak,
      'highest_streak': instance.highestStreak,
    };

_$MaxRedeemableCoinsImpl _$$MaxRedeemableCoinsImplFromJson(
        Map<String, dynamic> json) =>
    _$MaxRedeemableCoinsImpl(
      actualMaxRedeemable: (json['actual_max_redeemable'] as num).toInt(),
      customerPointOnHand: (json['customer_point_on_hand'] as num).toInt(),
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$MaxRedeemableCoinsImplToJson(
        _$MaxRedeemableCoinsImpl instance) =>
    <String, dynamic>{
      'actual_max_redeemable': instance.actualMaxRedeemable,
      'customer_point_on_hand': instance.customerPointOnHand,
      'discount_amount': instance.discountAmount,
    };
