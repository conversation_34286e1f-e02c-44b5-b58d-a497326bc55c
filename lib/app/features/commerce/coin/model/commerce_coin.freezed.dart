// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commerce_coin.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CommerceCoin _$CommerceCoinFromJson(Map<String, dynamic> json) {
  return _CommerceCoin.fromJson(json);
}

/// @nodoc
mixin _$CommerceCoin {
  String get externalId => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  int get totalPoints => throw _privateConstructorUsedError;
  int get totalEarned => throw _privateConstructorUsedError;
  int get eachCurrencySpend => throw _privateConstructorUsedError;
  int get eachPoint => throw _privateConstructorUsedError;
  DailyLogin get dailyLogin => throw _privateConstructorUsedError;
  dynamic get activeRedemption => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommerceCoinCopyWith<CommerceCoin> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommerceCoinCopyWith<$Res> {
  factory $CommerceCoinCopyWith(
          CommerceCoin value, $Res Function(CommerceCoin) then) =
      _$CommerceCoinCopyWithImpl<$Res, CommerceCoin>;
  @useResult
  $Res call(
      {String externalId,
      String email,
      int totalPoints,
      int totalEarned,
      int eachCurrencySpend,
      int eachPoint,
      DailyLogin dailyLogin,
      dynamic activeRedemption});

  $DailyLoginCopyWith<$Res> get dailyLogin;
}

/// @nodoc
class _$CommerceCoinCopyWithImpl<$Res, $Val extends CommerceCoin>
    implements $CommerceCoinCopyWith<$Res> {
  _$CommerceCoinCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? externalId = null,
    Object? email = null,
    Object? totalPoints = null,
    Object? totalEarned = null,
    Object? eachCurrencySpend = null,
    Object? eachPoint = null,
    Object? dailyLogin = null,
    Object? activeRedemption = freezed,
  }) {
    return _then(_value.copyWith(
      externalId: null == externalId
          ? _value.externalId
          : externalId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as int,
      eachCurrencySpend: null == eachCurrencySpend
          ? _value.eachCurrencySpend
          : eachCurrencySpend // ignore: cast_nullable_to_non_nullable
              as int,
      eachPoint: null == eachPoint
          ? _value.eachPoint
          : eachPoint // ignore: cast_nullable_to_non_nullable
              as int,
      dailyLogin: null == dailyLogin
          ? _value.dailyLogin
          : dailyLogin // ignore: cast_nullable_to_non_nullable
              as DailyLogin,
      activeRedemption: freezed == activeRedemption
          ? _value.activeRedemption
          : activeRedemption // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DailyLoginCopyWith<$Res> get dailyLogin {
    return $DailyLoginCopyWith<$Res>(_value.dailyLogin, (value) {
      return _then(_value.copyWith(dailyLogin: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CommerceCoinImplCopyWith<$Res>
    implements $CommerceCoinCopyWith<$Res> {
  factory _$$CommerceCoinImplCopyWith(
          _$CommerceCoinImpl value, $Res Function(_$CommerceCoinImpl) then) =
      __$$CommerceCoinImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String externalId,
      String email,
      int totalPoints,
      int totalEarned,
      int eachCurrencySpend,
      int eachPoint,
      DailyLogin dailyLogin,
      dynamic activeRedemption});

  @override
  $DailyLoginCopyWith<$Res> get dailyLogin;
}

/// @nodoc
class __$$CommerceCoinImplCopyWithImpl<$Res>
    extends _$CommerceCoinCopyWithImpl<$Res, _$CommerceCoinImpl>
    implements _$$CommerceCoinImplCopyWith<$Res> {
  __$$CommerceCoinImplCopyWithImpl(
      _$CommerceCoinImpl _value, $Res Function(_$CommerceCoinImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? externalId = null,
    Object? email = null,
    Object? totalPoints = null,
    Object? totalEarned = null,
    Object? eachCurrencySpend = null,
    Object? eachPoint = null,
    Object? dailyLogin = null,
    Object? activeRedemption = freezed,
  }) {
    return _then(_$CommerceCoinImpl(
      externalId: null == externalId
          ? _value.externalId
          : externalId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as int,
      eachCurrencySpend: null == eachCurrencySpend
          ? _value.eachCurrencySpend
          : eachCurrencySpend // ignore: cast_nullable_to_non_nullable
              as int,
      eachPoint: null == eachPoint
          ? _value.eachPoint
          : eachPoint // ignore: cast_nullable_to_non_nullable
              as int,
      dailyLogin: null == dailyLogin
          ? _value.dailyLogin
          : dailyLogin // ignore: cast_nullable_to_non_nullable
              as DailyLogin,
      activeRedemption: freezed == activeRedemption
          ? _value.activeRedemption
          : activeRedemption // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommerceCoinImpl implements _CommerceCoin {
  _$CommerceCoinImpl(
      {required this.externalId,
      required this.email,
      required this.totalPoints,
      required this.totalEarned,
      required this.eachCurrencySpend,
      required this.eachPoint,
      required this.dailyLogin,
      this.activeRedemption});

  factory _$CommerceCoinImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommerceCoinImplFromJson(json);

  @override
  final String externalId;
  @override
  final String email;
  @override
  final int totalPoints;
  @override
  final int totalEarned;
  @override
  final int eachCurrencySpend;
  @override
  final int eachPoint;
  @override
  final DailyLogin dailyLogin;
  @override
  final dynamic activeRedemption;

  @override
  String toString() {
    return 'CommerceCoin(externalId: $externalId, email: $email, totalPoints: $totalPoints, totalEarned: $totalEarned, eachCurrencySpend: $eachCurrencySpend, eachPoint: $eachPoint, dailyLogin: $dailyLogin, activeRedemption: $activeRedemption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommerceCoinImpl &&
            (identical(other.externalId, externalId) ||
                other.externalId == externalId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.totalEarned, totalEarned) ||
                other.totalEarned == totalEarned) &&
            (identical(other.eachCurrencySpend, eachCurrencySpend) ||
                other.eachCurrencySpend == eachCurrencySpend) &&
            (identical(other.eachPoint, eachPoint) ||
                other.eachPoint == eachPoint) &&
            (identical(other.dailyLogin, dailyLogin) ||
                other.dailyLogin == dailyLogin) &&
            const DeepCollectionEquality()
                .equals(other.activeRedemption, activeRedemption));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      externalId,
      email,
      totalPoints,
      totalEarned,
      eachCurrencySpend,
      eachPoint,
      dailyLogin,
      const DeepCollectionEquality().hash(activeRedemption));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CommerceCoinImplCopyWith<_$CommerceCoinImpl> get copyWith =>
      __$$CommerceCoinImplCopyWithImpl<_$CommerceCoinImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommerceCoinImplToJson(
      this,
    );
  }
}

abstract class _CommerceCoin implements CommerceCoin {
  factory _CommerceCoin(
      {required final String externalId,
      required final String email,
      required final int totalPoints,
      required final int totalEarned,
      required final int eachCurrencySpend,
      required final int eachPoint,
      required final DailyLogin dailyLogin,
      final dynamic activeRedemption}) = _$CommerceCoinImpl;

  factory _CommerceCoin.fromJson(Map<String, dynamic> json) =
      _$CommerceCoinImpl.fromJson;

  @override
  String get externalId;
  @override
  String get email;
  @override
  int get totalPoints;
  @override
  int get totalEarned;
  @override
  int get eachCurrencySpend;
  @override
  int get eachPoint;
  @override
  DailyLogin get dailyLogin;
  @override
  dynamic get activeRedemption;
  @override
  @JsonKey(ignore: true)
  _$$CommerceCoinImplCopyWith<_$CommerceCoinImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DailyLogin _$DailyLoginFromJson(Map<String, dynamic> json) {
  return _DailyLogin.fromJson(json);
}

/// @nodoc
mixin _$DailyLogin {
  bool get hasLoggedInToday => throw _privateConstructorUsedError;
  int get currentStreak => throw _privateConstructorUsedError;
  int get highestStreak => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DailyLoginCopyWith<DailyLogin> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DailyLoginCopyWith<$Res> {
  factory $DailyLoginCopyWith(
          DailyLogin value, $Res Function(DailyLogin) then) =
      _$DailyLoginCopyWithImpl<$Res, DailyLogin>;
  @useResult
  $Res call({bool hasLoggedInToday, int currentStreak, int highestStreak});
}

/// @nodoc
class _$DailyLoginCopyWithImpl<$Res, $Val extends DailyLogin>
    implements $DailyLoginCopyWith<$Res> {
  _$DailyLoginCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasLoggedInToday = null,
    Object? currentStreak = null,
    Object? highestStreak = null,
  }) {
    return _then(_value.copyWith(
      hasLoggedInToday: null == hasLoggedInToday
          ? _value.hasLoggedInToday
          : hasLoggedInToday // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      highestStreak: null == highestStreak
          ? _value.highestStreak
          : highestStreak // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DailyLoginImplCopyWith<$Res>
    implements $DailyLoginCopyWith<$Res> {
  factory _$$DailyLoginImplCopyWith(
          _$DailyLoginImpl value, $Res Function(_$DailyLoginImpl) then) =
      __$$DailyLoginImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool hasLoggedInToday, int currentStreak, int highestStreak});
}

/// @nodoc
class __$$DailyLoginImplCopyWithImpl<$Res>
    extends _$DailyLoginCopyWithImpl<$Res, _$DailyLoginImpl>
    implements _$$DailyLoginImplCopyWith<$Res> {
  __$$DailyLoginImplCopyWithImpl(
      _$DailyLoginImpl _value, $Res Function(_$DailyLoginImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasLoggedInToday = null,
    Object? currentStreak = null,
    Object? highestStreak = null,
  }) {
    return _then(_$DailyLoginImpl(
      hasLoggedInToday: null == hasLoggedInToday
          ? _value.hasLoggedInToday
          : hasLoggedInToday // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      highestStreak: null == highestStreak
          ? _value.highestStreak
          : highestStreak // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DailyLoginImpl implements _DailyLogin {
  _$DailyLoginImpl(
      {required this.hasLoggedInToday,
      required this.currentStreak,
      required this.highestStreak});

  factory _$DailyLoginImpl.fromJson(Map<String, dynamic> json) =>
      _$$DailyLoginImplFromJson(json);

  @override
  final bool hasLoggedInToday;
  @override
  final int currentStreak;
  @override
  final int highestStreak;

  @override
  String toString() {
    return 'DailyLogin(hasLoggedInToday: $hasLoggedInToday, currentStreak: $currentStreak, highestStreak: $highestStreak)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DailyLoginImpl &&
            (identical(other.hasLoggedInToday, hasLoggedInToday) ||
                other.hasLoggedInToday == hasLoggedInToday) &&
            (identical(other.currentStreak, currentStreak) ||
                other.currentStreak == currentStreak) &&
            (identical(other.highestStreak, highestStreak) ||
                other.highestStreak == highestStreak));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, hasLoggedInToday, currentStreak, highestStreak);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DailyLoginImplCopyWith<_$DailyLoginImpl> get copyWith =>
      __$$DailyLoginImplCopyWithImpl<_$DailyLoginImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DailyLoginImplToJson(
      this,
    );
  }
}

abstract class _DailyLogin implements DailyLogin {
  factory _DailyLogin(
      {required final bool hasLoggedInToday,
      required final int currentStreak,
      required final int highestStreak}) = _$DailyLoginImpl;

  factory _DailyLogin.fromJson(Map<String, dynamic> json) =
      _$DailyLoginImpl.fromJson;

  @override
  bool get hasLoggedInToday;
  @override
  int get currentStreak;
  @override
  int get highestStreak;
  @override
  @JsonKey(ignore: true)
  _$$DailyLoginImplCopyWith<_$DailyLoginImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MaxRedeemableCoins _$MaxRedeemableCoinsFromJson(Map<String, dynamic> json) {
  return _MaxRedeemableCoins.fromJson(json);
}

/// @nodoc
mixin _$MaxRedeemableCoins {
  int get actualMaxRedeemable => throw _privateConstructorUsedError;
  int get customerPointOnHand => throw _privateConstructorUsedError;
  double? get discountAmount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MaxRedeemableCoinsCopyWith<MaxRedeemableCoins> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MaxRedeemableCoinsCopyWith<$Res> {
  factory $MaxRedeemableCoinsCopyWith(
          MaxRedeemableCoins value, $Res Function(MaxRedeemableCoins) then) =
      _$MaxRedeemableCoinsCopyWithImpl<$Res, MaxRedeemableCoins>;
  @useResult
  $Res call(
      {int actualMaxRedeemable,
      int customerPointOnHand,
      double? discountAmount});
}

/// @nodoc
class _$MaxRedeemableCoinsCopyWithImpl<$Res, $Val extends MaxRedeemableCoins>
    implements $MaxRedeemableCoinsCopyWith<$Res> {
  _$MaxRedeemableCoinsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualMaxRedeemable = null,
    Object? customerPointOnHand = null,
    Object? discountAmount = freezed,
  }) {
    return _then(_value.copyWith(
      actualMaxRedeemable: null == actualMaxRedeemable
          ? _value.actualMaxRedeemable
          : actualMaxRedeemable // ignore: cast_nullable_to_non_nullable
              as int,
      customerPointOnHand: null == customerPointOnHand
          ? _value.customerPointOnHand
          : customerPointOnHand // ignore: cast_nullable_to_non_nullable
              as int,
      discountAmount: freezed == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MaxRedeemableCoinsImplCopyWith<$Res>
    implements $MaxRedeemableCoinsCopyWith<$Res> {
  factory _$$MaxRedeemableCoinsImplCopyWith(_$MaxRedeemableCoinsImpl value,
          $Res Function(_$MaxRedeemableCoinsImpl) then) =
      __$$MaxRedeemableCoinsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int actualMaxRedeemable,
      int customerPointOnHand,
      double? discountAmount});
}

/// @nodoc
class __$$MaxRedeemableCoinsImplCopyWithImpl<$Res>
    extends _$MaxRedeemableCoinsCopyWithImpl<$Res, _$MaxRedeemableCoinsImpl>
    implements _$$MaxRedeemableCoinsImplCopyWith<$Res> {
  __$$MaxRedeemableCoinsImplCopyWithImpl(_$MaxRedeemableCoinsImpl _value,
      $Res Function(_$MaxRedeemableCoinsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actualMaxRedeemable = null,
    Object? customerPointOnHand = null,
    Object? discountAmount = freezed,
  }) {
    return _then(_$MaxRedeemableCoinsImpl(
      actualMaxRedeemable: null == actualMaxRedeemable
          ? _value.actualMaxRedeemable
          : actualMaxRedeemable // ignore: cast_nullable_to_non_nullable
              as int,
      customerPointOnHand: null == customerPointOnHand
          ? _value.customerPointOnHand
          : customerPointOnHand // ignore: cast_nullable_to_non_nullable
              as int,
      discountAmount: freezed == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MaxRedeemableCoinsImpl implements _MaxRedeemableCoins {
  _$MaxRedeemableCoinsImpl(
      {required this.actualMaxRedeemable,
      required this.customerPointOnHand,
      this.discountAmount});

  factory _$MaxRedeemableCoinsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MaxRedeemableCoinsImplFromJson(json);

  @override
  final int actualMaxRedeemable;
  @override
  final int customerPointOnHand;
  @override
  final double? discountAmount;

  @override
  String toString() {
    return 'MaxRedeemableCoins(actualMaxRedeemable: $actualMaxRedeemable, customerPointOnHand: $customerPointOnHand, discountAmount: $discountAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MaxRedeemableCoinsImpl &&
            (identical(other.actualMaxRedeemable, actualMaxRedeemable) ||
                other.actualMaxRedeemable == actualMaxRedeemable) &&
            (identical(other.customerPointOnHand, customerPointOnHand) ||
                other.customerPointOnHand == customerPointOnHand) &&
            (identical(other.discountAmount, discountAmount) ||
                other.discountAmount == discountAmount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, actualMaxRedeemable, customerPointOnHand, discountAmount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MaxRedeemableCoinsImplCopyWith<_$MaxRedeemableCoinsImpl> get copyWith =>
      __$$MaxRedeemableCoinsImplCopyWithImpl<_$MaxRedeemableCoinsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MaxRedeemableCoinsImplToJson(
      this,
    );
  }
}

abstract class _MaxRedeemableCoins implements MaxRedeemableCoins {
  factory _MaxRedeemableCoins(
      {required final int actualMaxRedeemable,
      required final int customerPointOnHand,
      final double? discountAmount}) = _$MaxRedeemableCoinsImpl;

  factory _MaxRedeemableCoins.fromJson(Map<String, dynamic> json) =
      _$MaxRedeemableCoinsImpl.fromJson;

  @override
  int get actualMaxRedeemable;
  @override
  int get customerPointOnHand;
  @override
  double? get discountAmount;
  @override
  @JsonKey(ignore: true)
  _$$MaxRedeemableCoinsImplCopyWith<_$MaxRedeemableCoinsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
