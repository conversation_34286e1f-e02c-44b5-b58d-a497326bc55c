import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/coin/widget/day_card.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CoinCheckIn extends HookConsumerWidget {
  const CoinCheckIn({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coinBalance = ref.watch(coinBalanceProvider);

    final _isBusy = useState<bool>(false);

    // get amount of coin based on streak
    int _getAmount(int streak) {
      // Get the effective streak within the 7-day cycle (0-6)
      final effectiveStreak = streak % 7;

      if (effectiveStreak <= 2) {
        return effectiveStreak + 1;
      } else if (effectiveStreak == 6) {
        return 10;
      } else {
        return effectiveStreak + 2;
      }
    }

    return coinBalance.when(
      data: (coinBalance) {
        if (coinBalance == null) {
          return const SizedBox.shrink();
        }

        final amount = _getAmount(coinBalance.dailyLogin.currentStreak);

        return Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              top: -110,
              left: 0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Available Coins',
                        style: textTheme(context)
                            .titleMedium!
                            .copyWith(color: Colors.white),
                      ),
                      const SizedBox(width: 6),
                      const Icon(
                        Icons.help_outline,
                        size: 16,
                        color: Colors.white,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Image.asset(
                        'assets/images/coin.png',
                        width: 30,
                        height: 30,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        coinBalance.totalPoints.toString(),
                        style: textTheme(context)
                            .titleLarge!
                            .copyWith(color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              top: -75,
              right: 15,
              child: SizedBox(
                height: 95,
                child: AspectRatio(
                  aspectRatio: 1,
                  child: Image.asset(
                    'assets/images/goma_hold.png',
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            Material(
              borderRadius: const BorderRadius.all(Radius.circular(16)),
              elevation: 4,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(16),
                  ),
                ),
                child: Column(
                  children: [
                    GridView.count(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 7,
                      crossAxisSpacing: 8,
                      childAspectRatio: 0.35,
                      children: List.generate(
                        7,
                        (index) {
                          return DayCard(
                            index: index,
                            amount: _getAmount(index),
                            currentStreak: coinBalance.dailyLogin.currentStreak,
                            hasClaimed: coinBalance.dailyLogin.hasLoggedInToday,
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    FilledButton(
                      onPressed: (coinBalance.dailyLogin.hasLoggedInToday ==
                                  true ||
                              _isBusy.value == true)
                          ? null
                          : () async {
                              _isBusy.value = true;

                              try {
                                await ref.read(checkInCoinsProvider.future);
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        '$amount coin has been added to your balance',
                                      ),
                                    ),
                                  );
                                }
                              } catch (error) {
                                Groveman.error(
                                  'checkin failed',
                                  error: error,
                                );
                                if (error is AppNetworkResponseException &&
                                    error.errorCode == 'login.bonus.claimed') {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          error.data['error'] as String,
                                        ),
                                      ),
                                    );
                                  }
                                } else {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Something went wrong, please try again',
                                        ),
                                      ),
                                    );
                                  }
                                }
                              } finally {
                                ref.invalidate(coinBalanceProvider);
                                _isBusy.value = false;
                              }
                            },
                      child: Text(
                        coinBalance.dailyLogin.hasLoggedInToday == true
                            ? 'Coin Collected Today'
                            : _isBusy.value
                                ? 'Checking In...'
                                : 'Check-In Today For $amount Coin',
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
      error: (error, stackTrace) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text('Something went wrong, please try again'),
          ),
          FilledButton(
            style: FilledButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            onPressed: () {
              ref.invalidate(coinBalanceProvider);
            },
            child: const Text('Try again'),
          ),
        ],
      ),
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
