import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DayCard extends ConsumerWidget {
  const DayCard({
    super.key,
    required this.index,
    required this.currentStreak,
    required this.hasClaimed,
    this.amount = 1,
  });

  final int index;
  final int currentStreak;
  final bool hasClaimed;
  final int amount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // currentStreak value increases on claim,
    // For streaks >= 7, we cycle back using modulo
    // e.g., streak 7 maps to index 0, streak 8 to index 1, etc.
    final effectiveStreak = currentStreak % 7;
    final isPast = index < effectiveStreak;
    final isCurrent = effectiveStreak == index;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isPast
                ? CustomColors.primaries.shade100
                : CustomColors.primaries.shade50,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isCurrent && !hasClaimed
                  ? CustomColors.primary
                  : Colors.transparent,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '+$amount',
                style: textTheme(context).bodyMedium!.copyWith(
                      color: isCurrent && !hasClaimed
                          ? CustomColors.primary
                          : CustomColors.placeholder,
                    ),
              ),
              const SizedBox(height: 8),
              if (isPast)
                const Icon(
                  Icons.check_circle_outline_outlined,
                  size: 24,
                  color: CustomColors.primary,
                )
              else
                Image.asset(
                  'assets/images/coin.png',
                  width: 24,
                  height: 24,
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          // increases the index 6 card label according to streak
          index == 0
              ? 'Today'
              : index == 6 && currentStreak >= 7
                  ? 'Day ${hasClaimed ? currentStreak : currentStreak + 1}'
                  : 'Day ${index + 1}',
          style: textTheme(context).labelMedium!.copyWith(
                color: isCurrent && !hasClaimed
                    ? CustomColors.primary
                    : CustomColors.placeholder,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
