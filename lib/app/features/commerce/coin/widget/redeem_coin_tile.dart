import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class RedeemCoinTile extends HookConsumerWidget {
  const RedeemCoinTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final maxRedeemable = ref.watch(maxRedeemableCoinsProvider);

    final switchState = useState<bool>(false);

    return ListTileTheme(
      data: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        tileColor: CustomColors.primaries.shade100,
        iconColor: iconTheme(context).color,
        textColor: colorTheme(context).primary,
      ),
      child: maxRedeemable.when(
        data: (data) {
          return ListTile(
            dense: true,
            leading: const Icon(Icons.monetization_on_outlined),
            title: Text(
              'Claim ${data.actualMaxRedeemable} coins (\$${data.discountAmount?.toStringAsFixed(2)})',
            ),
            trailing: Switch(
              value: switchState.value,
              inactiveTrackColor: Colors.transparent,
              onChanged: data.actualMaxRedeemable < 1
                  ? null
                  : (value) {
                      switchState.value = value;
                      // set indicator of redeem coin for checkout process
                      ref
                          .read(isRedeemCoinProvider.notifier)
                          .update((state) => value);
                    },
            ),
            onTap: () {
              switchState.value = !switchState.value;
            },
          );
        },
        loading: () {
          return const ListTile(
            dense: true,
            leading: Icon(Icons.monetization_on_outlined),
            title: Text('Calculating redeemable coins...'),
            trailing: Switch(
              value: false,
              inactiveTrackColor: Colors.transparent,
              onChanged: null,
            ),
          );
        },
        error: (error, stackTrace) {
          Groveman.error(
            'redeemCoinTile',
            error: error,
            stackTrace: stackTrace,
          );

          return ListTile(
            dense: true,
            leading: const Icon(Icons.monetization_on_outlined),
            title: const Text('Failed to retrieve coins, please try again'),
            trailing: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref
                  ..invalidate(maxRedeemableCoinsProvider)
                  ..invalidate(cartControllerProvider);
              },
            ),
          );
        },
      ),
    );
  }
}
