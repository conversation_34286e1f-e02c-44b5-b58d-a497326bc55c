import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/coin/model/commerce_coin.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/orders/model/order_review_request.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'shopify_orders_repository.g.dart';

@Riverpod(keepAlive: true)
ShopifyOrdersRepository shopifyOrdersRepository(
  ShopifyOrdersRepositoryRef ref,
) =>
    ShopifyOrdersRepository(ref);

class ShopifyOrdersRepository {
  ShopifyOrdersRepository(this.ref);
  final ShopifyOrdersRepositoryRef ref;

  Future<CoreShopifyOrderResponse> fetchOrders({
    String? cursor,
    List<String>? financialStatus,
    List<String>? fulfillmentStatus,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, dynamic>{};
      queryParams['limit'] = kPageLimit;
      if (cursor != null) queryParams['cursor'] = cursor;
      if (financialStatus != null) {
        queryParams['financial_status'] = financialStatus;
      }
      if (fulfillmentStatus != null) {
        queryParams['fulfillment_status'] = fulfillmentStatus;
      }

      final response = await ref.watch(repositoryProvider).get<Json>(
            '/me/orders',
            queryParameters: queryParams,
          );

      final data = response.data!['data'] as Json;

      // Map the edges/node structure
      final pageInfo =
          CoreShopifyPageInfo.fromJson(data['orders']['pageInfo'] as Json);
      final orders = (data['orders']['edges'] as List).map((edge) {
        final node = edge['node'] as Json;
        // Add cursor to the order object for pagination
        node['cursor'] = edge['cursor'];
        return CoreShopifyOrder.fromJson(node);
      }).toList();
      final orderResponse = CoreShopifyOrderResponse(
        orders: orders,
        pageInfo: pageInfo,
      );

      return orderResponse;
    } catch (error, stackTrace) {
      Groveman.warning('fetchOrders', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<OrderReviewRequestResponse> fetchOrderReviewRequests({
    int? page,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, dynamic>{};
      queryParams['limit'] = kPageLimit;
      queryParams['page'] = page ?? 1;

      final response = await ref.watch(repositoryProvider).get<Json>(
            '/me/orders/reviews',
            queryParameters: queryParams,
          );
      return OrderReviewRequestResponse.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchOrderReviewRequests', error: error);
      rethrow;
    }
  }

  Future<bool> updateOrderReceived(String orderId) async {
    try {
      final response = await ref.watch(repositoryProvider).put<Json>(
            '/me/orders/$orderId/mark-as-received',
          );

      return response.data!['success'] == true;
    } catch (error) {
      Groveman.warning('updateOrderReceived', error: error);
      rethrow;
    }
  }
}
