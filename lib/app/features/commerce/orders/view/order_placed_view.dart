import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';

class OrderPlacedView extends ConsumerWidget {
  const OrderPlacedView({super.key});

  static const routeName = 'order-placed';
  static const routePath = 'order-placed';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      appBar: AppBar(
        foregroundColor: CustomColors.primary,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(48),
              child: Column(
                children: [
                  Container(
                    height: 195,
                    alignment: Alignment.center,
                    child: Image.asset(
                      'assets/images/goma_heart.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'Order Placed!',
                    style: textTheme(context).titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your order has been successfully placed. Thank you for shopping with us!',
                    textAlign: TextAlign.center,
                    style: textTheme(context).titleMedium!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                  const SizedBox(height: 48),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () =>
                              const PurchaseHistoryRoute('to_ship').go(context),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('My Orders'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: FilledButton(
                          onPressed: () => const CommerceRoute().go(context),
                          style: FilledButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: const Text('Home'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
