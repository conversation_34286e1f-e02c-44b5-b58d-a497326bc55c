import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/orders/provider/shopify_orders_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/models/src/order/line_item_order/line_item_order.dart';
import 'package:shopify_flutter/models/src/order/shipping_address/shipping_address.dart';
import 'package:shopify_flutter/models/src/order/successful_fulfillment/successful_fullfilment.dart';
import 'package:shopify_flutter/models/src/product/price_v_2/price_v_2.dart';
import 'package:url_launcher/url_launcher_string.dart';

class OrderDetailsView extends ConsumerWidget {
  const OrderDetailsView({
    super.key,
    required this.orderId,
    required this.isCompleted,
  });

  final String orderId;
  final bool isCompleted;
  static const routeName = 'order-detail-view';
  static const routePath = 'order-detail/:orderId';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncOrder = ref.watch(OrderByIdProvider(orderId));

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      appBar: AppBar(
        centerTitle: true,
        title: const Text('Order Detail'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: asyncOrder.when(
          data: (order) {
            if (order == null) return const SizedBox.shrink();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (order.shippingAddress != null)
                  _ShippingAddress(order.shippingAddress!),
                const SizedBox(height: 12),
                _ShippingInformation(
                  financialStatus: order.financialStatus,
                  fulfillmentStatus: order.fulfillmentStatus,
                  fulfillment: order.successfulFulfillments?.firstOrNull,
                  isCompleted: isCompleted,
                ),
                const SizedBox(height: 12),
                _OrderProductDetails(order.lineItems.lineItemOrderList),
                const SizedBox(height: 12),
                _PaymentDetails(
                  totalPrice: order.totalPriceV2,
                  totalShippingPrice: order.totalShippingPriceV2,
                  totalTax: order.totalTaxV2,
                  lineItemOrderList: order.lineItems.lineItemOrderList,
                ),
              ],
            );
          },
          error: (error, stackTrace) {
            return Center(
              child: Column(
                children: [
                  const SizedBox(height: 64),
                  Image.asset(
                    'assets/images/goma_sad.png',
                    height: 160,
                  ),
                  Text(
                    'Unable to retrieve info, please try again later',
                    style: textTheme(context).titleMedium,
                  ),
                  const SizedBox(height: 16),
                  FilledButton(
                    style: FilledButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () => ref.invalidate(orderByIdProvider),
                    child: const Text('Try again'),
                  ),
                ],
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }
}

class _ShippingAddress extends ConsumerWidget {
  const _ShippingAddress(this.shippingAddress);

  final ShippingAddress shippingAddress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.location_on, color: CustomColors.primary),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${shippingAddress.firstName} ${shippingAddress.lastName} ${shippingAddress.phone != null ? '| ${shippingAddress.phone}' : ''}',
                style: textTheme(context).bodyMedium?.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                shippingAddress.address1,
                style: textTheme(context).labelLarge?.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
              if (shippingAddress.address2?.isEmpty == false)
                Text(
                  '${shippingAddress.address2}',
                  style: textTheme(context).labelLarge?.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
              Text(
                shippingAddress.city,
                style: textTheme(context).labelLarge?.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
              Text(
                shippingAddress.province?.isEmpty == false
                    ? '${shippingAddress.province}, ${shippingAddress.country}, ${shippingAddress.zip}'
                    : '${shippingAddress.country}, ${shippingAddress.zip}',
                style: textTheme(context).labelLarge?.copyWith(
                      color: CustomColors.primary,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _OrderProductDetails extends StatelessWidget {
  const _OrderProductDetails(this.lineItemOrders);

  final List<LineItemOrder> lineItemOrders;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.shopping_bag_sharp, color: CustomColors.primary),
              const SizedBox(width: 12),
              Text(
                'Products information',
                style: textTheme(context).bodyMedium?.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final lineItemOrder = lineItemOrders[index];
              final itemVariant = lineItemOrder.variant;

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: CachedNetworkImage(
                        imageUrl: itemVariant?.image?.originalSrc ?? '',
                        imageBuilder: (context, imageProvider) {
                          return DecoratedBox(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          );
                        },
                        errorWidget: (context, url, error) =>
                            const Icon(CustomIcon.error),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          itemVariant?.product?.title ?? '',
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: CustomColors.primary,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32),
                            color: Colors.grey.shade200,
                          ),
                          child: Text(
                            itemVariant?.title ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .labelMedium!
                                .copyWith(color: Colors.grey),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              lineItemOrder.discountedTotalPrice.formattedPrice,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(
                                    color: CustomColors.primary,
                                  ),
                            ),
                            Text(
                              'x${lineItemOrder.quantity}',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelMedium!
                                  .copyWith(color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
            separatorBuilder: (context, index) => const SizedBox(height: 24),
            itemCount: lineItemOrders.length,
          ),
        ],
      ),
    );
  }
}

class _PaymentDetails extends ConsumerWidget {
  const _PaymentDetails({
    required this.totalTax,
    required this.totalShippingPrice,
    required this.totalPrice,
    required this.lineItemOrderList,
  });

  final PriceV2 totalTax;
  final PriceV2 totalShippingPrice;
  final PriceV2 totalPrice;
  final List<LineItemOrder> lineItemOrderList;

  num get totalDiscountAmount => lineItemOrderList.fold<num>(
        0,
        (sum, lineItemOrder) =>
            sum +
            lineItemOrder.discountAllocations.fold<num>(
              0,
              (discountSum, discount) =>
                  discountSum + (discount.allocatedAmount?.amount ?? 0),
            ),
      );

  num get merchandiseSubTotal => lineItemOrderList.fold<num>(
        0,
        (sum, lineItemOrder) => sum + lineItemOrder.discountedTotalPrice.amount,
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget _buildPaymentRow(
      String label,
      String amountString, {
      bool isNegative = false,
    }) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: textTheme(context).bodyMedium?.copyWith(
                    color: CustomColors.primary,
                  ),
            ),
            Text(
              isNegative ? '-$amountString' : amountString,
              style: textTheme(context).bodyMedium?.copyWith(
                    color: CustomColors.primary,
                  ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.receipt, color: CustomColors.primary),
              const SizedBox(width: 12),
              Text(
                'Payment Details',
                style: textTheme(context).bodyMedium?.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          _buildPaymentRow(
            'Merchandise Subtotal',
            merchandiseSubTotal.toStringAsFixed(2),
          ),
          _buildPaymentRow(
            'Shipping Subtotal',
            totalShippingPrice.formattedPrice,
          ),
          // TODO: differentiate between platform & coin discount
          // _buildPaymentRow(
          //   context,
          //   'Platform Discount Voucher',
          //   -platformDiscountVoucher,
          //   isNegative: true,
          // ),
          if (totalDiscountAmount > 0)
            _buildPaymentRow(
              'Credit Coins Redeemed',
              totalDiscountAmount.toStringAsFixed(2),
              isNegative: true,
            ),
          Divider(height: 16, color: Colors.grey.shade300),
          _buildPaymentRow(
            'Total Payment',
            totalPrice.formattedPrice,
          ),
        ],
      ),
    );
  }
}

class _ShippingInformation extends ConsumerWidget {
  const _ShippingInformation({
    required this.financialStatus,
    required this.fulfillmentStatus,
    this.fulfillment,
    this.isCompleted,
  });

  final String financialStatus;
  final String fulfillmentStatus;
  final SuccessfulFullfilment? fulfillment;
  final bool? isCompleted;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final trackingUrl = fulfillment?.trackingInfo?.firstOrNull?.url;
    String _getStatusString() {
      if (isCompleted == true) {
        return 'Your Order Is Completed';
      } else if (financialStatus == 'PAID' &&
          fulfillmentStatus == 'UNFULFILLED') {
        return 'Order Processing';
      } else if (financialStatus == 'PAID' &&
          fulfillmentStatus == 'FULFILLED') {
        return 'Out For Delivery';
      } else if (financialStatus == 'REFUNDED') {
        return 'Order Refunded';
      } else {
        return 'Payment Pending';
      }
    }

    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: CustomColors.primary, // Purple color from image
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Text(
              _getStatusString(),
              style: textTheme(context).bodyMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Stack(
            children: [
              GestureDetector(
                onTap: (trackingUrl == null)
                    ? null
                    : () async {
                        try {
                          if (await canLaunchUrlString(trackingUrl)) {
                            await launchUrlString(
                              trackingUrl,
                              mode: LaunchMode.externalApplication,
                            );
                          }
                        } catch (e) {
                          debugPrint('Error launching URL: $e');
                        }
                      },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.local_shipping,
                        color: CustomColors.primary,
                      ),
                      const SizedBox(width: 16),
                      Text(
                        trackingUrl == null
                            ? 'Tracking Unavailable'
                            : 'Shipping Information',
                        style: textTheme(context).titleMedium?.copyWith(
                              color: CustomColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const Spacer(),
                      Text(
                        'View',
                        style: textTheme(context).bodyMedium?.copyWith(
                              color: CustomColors.primary,
                            ),
                      ),
                      const Icon(Icons.chevron_right),
                    ],
                  ),
                ),
              ),
              // block shipping info if order not yet fulfilled
              if (fulfillmentStatus != 'FULFILLED' || trackingUrl == null)
                Positioned.fill(
                  child: ColoredBox(
                    color: Colors.grey.shade300.withOpacity(0.6),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
