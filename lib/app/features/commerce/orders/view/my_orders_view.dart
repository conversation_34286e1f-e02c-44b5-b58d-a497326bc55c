import 'package:defer_pointer/defer_pointer.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/widget/commerce_swipeable_banner.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyOrdersView extends ConsumerWidget {
  const MyOrdersView({super.key});

  static const routeName = 'my-orders';
  static const routePath = 'my-orders';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StarsBackground(
      child: MediaQuery.removePadding(
        context: context,
        // removeTop: true,
        removeBottom: true,
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: NestedScrollView(
            physics: const NeverScrollableScrollPhysics(),
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              SliverOverlapAbsorber(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
                sliver: const SliverSafeArea(
                  top: false,
                  sliver: SliverAppBar(
                    title: Text('My Orders'),
                    centerTitle: true,
                    foregroundColor: Colors.white,
                    collapsedHeight: kTextTabBarHeight,
                    toolbarHeight: kTextTabBarHeight,
                    expandedHeight: kTextTabBarHeight,
                    pinned: true,
                    scrolledUnderElevation: 0,
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ),
            ],
            body: ClipRect(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const MilkBackground(),
                  Expanded(
                    child: DeferredPointerHandler(
                      child: DecoratedBox(
                        decoration: const BoxDecoration(
                          color: CustomColors.secondaryLight,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: SingleChildScrollView(
                            // required to not clip negative positioned widgets
                            clipBehavior: Clip.none,
                            padding: const EdgeInsets.only(bottom: 32),
                            child: Column(
                              children: [
                                const _PurchaseHistoryNavigationPanel(),
                                const SizedBox(height: 12),
                                SizedBox(
                                  height: 220,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      Expanded(
                                        flex: 5,
                                        child: _MyOrderCards(
                                          label: 'My Likes',
                                          icon: Expanded(
                                            child: Center(
                                              child: Image.asset(
                                                'assets/images/goma_heart.png',
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                          ),
                                          handleTap: () => const MyLikesRoute()
                                              .push(context),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        flex: 6,
                                        child: Column(
                                          children: [
                                            Expanded(
                                              child: _MyOrderCards(
                                                label: 'My Vouchers',
                                                icon: const Icon(
                                                  Icons.percent,
                                                  size: 28,
                                                ),
                                                handleTap: () {},
                                              ),
                                            ),
                                            const SizedBox(height: 12),
                                            Expanded(
                                              child: _MyOrderCards(
                                                label: 'Help & Support',
                                                icon: const Icon(
                                                  Icons.chat_bubble,
                                                  size: 28,
                                                ),
                                                handleTap: () =>
                                                    const CommerceHelpRoute()
                                                        .push(context),
                                              ),
                                            ),
                                            // Expanded(
                                            //   child: _MyOrderCards(
                                            //     label: 'My Requests',
                                            //     icon: const Icon(
                                            //       Icons.chat_bubble,
                                            //       size: 28,
                                            //     ),
                                            //     handleTap: () =>
                                            //         const MyRequestsRoute()
                                            //             .push(context),
                                            //   ),
                                            // ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 12),
                                const CommerceSwipeableBanner(
                                  bannerType: 'order',
                                  showSearch: false,
                                  aspectRatio: 350 / 150,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(16)),
                                  elevation: 4,
                                  clipBehavior: Clip.hardEdge,
                                ),
                                const SizedBox(height: 24),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _PurchaseHistoryNavigationPanel extends ConsumerWidget {
  const _PurchaseHistoryNavigationPanel();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Material(
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      elevation: 4,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -75,
            right: 15,
            child: SizedBox(
              height: 95,
              child: AspectRatio(
                aspectRatio: 1,
                child: Image.asset(
                  'assets/images/goma_hold.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                DeferPointer(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      const PurchaseHistoryRoute('to_pay').push(context);
                    },
                    child: Row(
                      children: [
                        Text(
                          'Purchase History',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(color: CustomColors.primary),
                        ),
                        const Spacer(),
                        Text(
                          'View All',
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall!
                              .copyWith(color: CustomColors.primary),
                        ),
                        const Icon(Icons.chevron_right),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    DeferPointer(
                      child: InkWell(
                        onTap: () {
                          const PurchaseHistoryRoute('to_pay').push(context);
                        },
                        child: Column(
                          children: [
                            const Icon(Icons.account_balance_wallet),
                            Text(
                              'To Pay',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: CustomColors.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DeferPointer(
                      child: InkWell(
                        onTap: () {
                          const PurchaseHistoryRoute('to_ship').push(context);
                        },
                        child: Column(
                          children: [
                            const Icon(Icons.inventory_2),
                            Text(
                              'To Ship',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: CustomColors.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DeferPointer(
                      child: InkWell(
                        onTap: () {
                          const PurchaseHistoryRoute('to_receive')
                              .push(context);
                        },
                        child: Column(
                          children: [
                            const Icon(Icons.local_shipping),
                            Text(
                              'To Receive',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: CustomColors.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DeferPointer(
                      child: InkWell(
                        onTap: () {
                          const PurchaseHistoryRoute('to_review').push(context);
                        },
                        child: Column(
                          children: [
                            const Icon(Icons.chat_bubble),
                            Text(
                              'Review',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: CustomColors.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DeferPointer(
                      child: InkWell(
                        onTap: () {
                          const PurchaseHistoryRoute('to_refund').push(context);
                        },
                        child: Column(
                          children: [
                            const Icon(Icons.attach_money),
                            Text(
                              'Refund',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: CustomColors.primary),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _MyOrderCards extends ConsumerWidget {
  const _MyOrderCards({
    required this.label,
    required this.icon,
    this.handleTap,
  });

  final String label;
  final Widget icon;
  final void Function()? handleTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DeferPointer(
      child: GestureDetector(
        onTap: handleTap,
        child: Material(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          elevation: 4,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(16),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context)
                          .textTheme
                          .labelLarge!
                          .copyWith(color: CustomColors.primary),
                    ),
                    const Icon(Icons.chevron_right),
                  ],
                ),
                icon,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
