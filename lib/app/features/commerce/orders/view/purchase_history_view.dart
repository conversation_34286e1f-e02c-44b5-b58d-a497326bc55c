import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:gomama/app/features/commerce/orders/provider/shopify_orders_providers.dart';
import 'package:gomama/app/features/commerce/orders/widget/order_review_request_list.dart';
import 'package:gomama/app/features/commerce/orders/widget/purchase_history_items_group.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:gomama/app/features/commerce/review/widget/my_product_reviews.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PurchaseHistoryView extends ConsumerWidget {
  const PurchaseHistoryView({
    super.key,
    this.initialTab = 'to_pay',
  });

  static const routeName = 'purchase-history';
  static const routePath = 'history';
  final String initialTab;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(ordersLoadingprovider);

    final ordersToPay = ref.watch(ordersToPayControllerProvider);
    final ordersToShip = ref.watch(ordersToShipControllerProvider);
    final ordersToReceive = ref.watch(ordersToReceiveControllerProvider);
    final ordersCompleted = ref.watch(ordersCompletedControllerProvider);
    final ordersRefunded = ref.watch(ordersRefundedControllerProvider);

    return DefaultTabController(
      length: 7,
      initialIndex: initialTab == 'to_pay'
          ? 0
          : initialTab == 'to_ship'
              ? 1
              : initialTab == 'to_receive'
                  ? 2
                  : initialTab == 'to_review'
                      ? 3
                      : initialTab == 'to_refund'
                          ? 5
                          : 0,
      child: Scaffold(
        backgroundColor: CustomColors.primaries.shade50,
        appBar: AppBar(
          centerTitle: true,
          foregroundColor: CustomColors.primary,
          title: const Text('Purchase History'),
          bottom: _ReadOnlyTabBar(
            ignoring: isLoading,
            child: TabBar(
              isScrollable: true,
              labelPadding: const EdgeInsets.symmetric(horizontal: 16),
              tabAlignment: TabAlignment.center,
              onTap: (index) {
                // invalidates each tab's provider on tab change
                switch (index) {
                  case 0:
                    ref.invalidate(ordersToPayControllerProvider);
                    break;
                  case 1:
                    ref.invalidate(ordersToShipControllerProvider);
                    break;
                  case 2:
                    ref.invalidate(ordersToReceiveControllerProvider);
                    break;
                  case 3:
                    ref.invalidate(ordersToReviewControllerProvider);
                    break;
                  case 4:
                    ref.invalidate(ordersCompletedControllerProvider);
                    break;
                  case 5:
                    ref.invalidate(ordersRefundedControllerProvider);
                    break;
                  case 6:
                    ref.invalidate(productReviewsProvider);
                    break;
                }
              },
              tabs: const [
                Tab(text: 'To Pay'),
                Tab(text: 'To Ship'),
                Tab(text: 'To Receive'),
                Tab(text: 'To Review'),
                Tab(text: 'Completed'),
                Tab(text: 'Refunded'),
                Tab(text: 'Review'),
              ],
            ),
          ),
        ),
        body: TabBarView(
          physics: isLoading ? const NeverScrollableScrollPhysics() : null,
          children: [
            // to pay
            _TabContent(
              asyncOrderResponse: ordersToPay,
              variant: ItemStatus.toPay,
              onLoadMore: () {
                ref.read(ordersToPayControllerProvider.notifier).fetchMore();
              },
              onRefresh: () {
                ref.invalidate(ordersToPayControllerProvider);
              },
            ),
            // to ship
            _TabContent(
              asyncOrderResponse: ordersToShip,
              variant: ItemStatus.toShip,
              onLoadMore: () {
                ref.read(ordersToShipControllerProvider.notifier).fetchMore();
              },
              onRefresh: () {
                ref.invalidate(ordersToShipControllerProvider);
              },
            ),
            // to receive
            _TabContent(
              asyncOrderResponse: ordersToReceive,
              variant: ItemStatus.toReceive,
              onLoadMore: () {
                ref
                    .read(ordersToReceiveControllerProvider.notifier)
                    .fetchMore();
              },
              onRefresh: () {
                ref.invalidate(ordersToReceiveControllerProvider);
              },
            ),
            // to review
            const OrderReviewRequestList(),
            // completed
            _TabContent(
              asyncOrderResponse: ordersCompleted,
              variant: ItemStatus.completed,
              onLoadMore: () {
                ref
                    .read(ordersCompletedControllerProvider.notifier)
                    .fetchMore();
              },
              onRefresh: () {
                ref.invalidate(ordersCompletedControllerProvider);
              },
            ),
            // refunded
            _TabContent(
              asyncOrderResponse: ordersRefunded,
              variant: ItemStatus.refunded,
              onLoadMore: () {
                ref.read(ordersRefundedControllerProvider.notifier).fetchMore();
              },
              onRefresh: () {
                ref.invalidate(ordersRefundedControllerProvider);
              },
            ),
            // my reviews
            const MyProductReviews(),
          ],
        ),
      ),
    );
  }
}

class _TabContent extends ConsumerWidget {
  const _TabContent({
    required this.asyncOrderResponse,
    required this.variant,
    this.onLoadMore,
    this.onRefresh,
  });

  final AsyncValue<CoreShopifyOrderResponse?> asyncOrderResponse;
  final ItemStatus variant;
  final void Function()? onLoadMore;
  final void Function()? onRefresh;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(ordersLoadingprovider);

    return asyncOrderResponse.when(
      data: (orderResponse) {
        if (orderResponse == null || orderResponse.orders.isEmpty == true) {
          return const SizedBox.shrink();
        }

        final orders = orderResponse.orders;
        final pageInfo = orderResponse.pageInfo;

        return ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
          itemCount: orders.length + (pageInfo?.hasNextPage == true ? 1 : 0),
          itemBuilder: (context, index) {
            // load more button
            if (index == orders.length && pageInfo?.hasNextPage == true) {
              return isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : FilledButton(
                      style: FilledButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      onPressed: onLoadMore,
                      child: const Text('Load more'),
                    );
            }

            final order = orders[index];

            // history group
            return PurchaseHistoryItemsGroup(
              order: order,
              variant: variant,
            );
          },
          separatorBuilder: (context, index) => const SizedBox(height: 16),
        );
      },
      error: (error, stackTrace) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text('Something went wrong, please try again'),
            ),
            FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: onRefresh,
              child: const Text('Try again'),
            ),
          ],
        );
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}

class _ReadOnlyTabBar extends ConsumerWidget implements PreferredSizeWidget {
  const _ReadOnlyTabBar({required this.child, required this.ignoring});

  final TabBar child;
  final bool ignoring;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IgnorePointer(
      ignoring: ignoring,
      child: child,
    );
  }

  @override
  Size get preferredSize => child.preferredSize;
}
