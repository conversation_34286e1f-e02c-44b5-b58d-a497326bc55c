// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shopify_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CoreShopifyOrderResponse _$CoreShopifyOrderResponseFromJson(
    Map<String, dynamic> json) {
  return _CoreShopifyOrderResponse.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyOrderResponse {
  List<CoreShopifyOrder> get orders => throw _privateConstructorUsedError;
  CoreShopifyPageInfo? get pageInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyOrderResponseCopyWith<CoreShopifyOrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyOrderResponseCopyWith<$Res> {
  factory $CoreShopifyOrderResponseCopyWith(CoreShopifyOrderResponse value,
          $Res Function(CoreShopifyOrderResponse) then) =
      _$CoreShopifyOrderResponseCopyWithImpl<$Res, CoreShopifyOrderResponse>;
  @useResult
  $Res call({List<CoreShopifyOrder> orders, CoreShopifyPageInfo? pageInfo});

  $CoreShopifyPageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class _$CoreShopifyOrderResponseCopyWithImpl<$Res,
        $Val extends CoreShopifyOrderResponse>
    implements $CoreShopifyOrderResponseCopyWith<$Res> {
  _$CoreShopifyOrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
    Object? pageInfo = freezed,
  }) {
    return _then(_value.copyWith(
      orders: null == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<CoreShopifyOrder>,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPageInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPageInfoCopyWith<$Res>? get pageInfo {
    if (_value.pageInfo == null) {
      return null;
    }

    return $CoreShopifyPageInfoCopyWith<$Res>(_value.pageInfo!, (value) {
      return _then(_value.copyWith(pageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyOrderResponseImplCopyWith<$Res>
    implements $CoreShopifyOrderResponseCopyWith<$Res> {
  factory _$$CoreShopifyOrderResponseImplCopyWith(
          _$CoreShopifyOrderResponseImpl value,
          $Res Function(_$CoreShopifyOrderResponseImpl) then) =
      __$$CoreShopifyOrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CoreShopifyOrder> orders, CoreShopifyPageInfo? pageInfo});

  @override
  $CoreShopifyPageInfoCopyWith<$Res>? get pageInfo;
}

/// @nodoc
class __$$CoreShopifyOrderResponseImplCopyWithImpl<$Res>
    extends _$CoreShopifyOrderResponseCopyWithImpl<$Res,
        _$CoreShopifyOrderResponseImpl>
    implements _$$CoreShopifyOrderResponseImplCopyWith<$Res> {
  __$$CoreShopifyOrderResponseImplCopyWithImpl(
      _$CoreShopifyOrderResponseImpl _value,
      $Res Function(_$CoreShopifyOrderResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
    Object? pageInfo = freezed,
  }) {
    return _then(_$CoreShopifyOrderResponseImpl(
      orders: null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<CoreShopifyOrder>,
      pageInfo: freezed == pageInfo
          ? _value.pageInfo
          : pageInfo // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPageInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyOrderResponseImpl implements _CoreShopifyOrderResponse {
  _$CoreShopifyOrderResponseImpl(
      {required final List<CoreShopifyOrder> orders, this.pageInfo})
      : _orders = orders;

  factory _$CoreShopifyOrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyOrderResponseImplFromJson(json);

  final List<CoreShopifyOrder> _orders;
  @override
  List<CoreShopifyOrder> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  final CoreShopifyPageInfo? pageInfo;

  @override
  String toString() {
    return 'CoreShopifyOrderResponse(orders: $orders, pageInfo: $pageInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyOrderResponseImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.pageInfo, pageInfo) ||
                other.pageInfo == pageInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_orders), pageInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyOrderResponseImplCopyWith<_$CoreShopifyOrderResponseImpl>
      get copyWith => __$$CoreShopifyOrderResponseImplCopyWithImpl<
          _$CoreShopifyOrderResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyOrderResponseImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyOrderResponse implements CoreShopifyOrderResponse {
  factory _CoreShopifyOrderResponse(
      {required final List<CoreShopifyOrder> orders,
      final CoreShopifyPageInfo? pageInfo}) = _$CoreShopifyOrderResponseImpl;

  factory _CoreShopifyOrderResponse.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyOrderResponseImpl.fromJson;

  @override
  List<CoreShopifyOrder> get orders;
  @override
  CoreShopifyPageInfo? get pageInfo;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyOrderResponseImplCopyWith<_$CoreShopifyOrderResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyPageInfo _$CoreShopifyPageInfoFromJson(Map<String, dynamic> json) {
  return _CoreShopifyPageInfo.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyPageInfo {
  @JsonKey(name: 'endCursor')
  String? get endCursor => throw _privateConstructorUsedError;
  @JsonKey(name: 'hasNextPage')
  bool? get hasNextPage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyPageInfoCopyWith<CoreShopifyPageInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyPageInfoCopyWith<$Res> {
  factory $CoreShopifyPageInfoCopyWith(
          CoreShopifyPageInfo value, $Res Function(CoreShopifyPageInfo) then) =
      _$CoreShopifyPageInfoCopyWithImpl<$Res, CoreShopifyPageInfo>;
  @useResult
  $Res call(
      {@JsonKey(name: 'endCursor') String? endCursor,
      @JsonKey(name: 'hasNextPage') bool? hasNextPage});
}

/// @nodoc
class _$CoreShopifyPageInfoCopyWithImpl<$Res, $Val extends CoreShopifyPageInfo>
    implements $CoreShopifyPageInfoCopyWith<$Res> {
  _$CoreShopifyPageInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endCursor = freezed,
    Object? hasNextPage = freezed,
  }) {
    return _then(_value.copyWith(
      endCursor: freezed == endCursor
          ? _value.endCursor
          : endCursor // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNextPage: freezed == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyPageInfoImplCopyWith<$Res>
    implements $CoreShopifyPageInfoCopyWith<$Res> {
  factory _$$CoreShopifyPageInfoImplCopyWith(_$CoreShopifyPageInfoImpl value,
          $Res Function(_$CoreShopifyPageInfoImpl) then) =
      __$$CoreShopifyPageInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'endCursor') String? endCursor,
      @JsonKey(name: 'hasNextPage') bool? hasNextPage});
}

/// @nodoc
class __$$CoreShopifyPageInfoImplCopyWithImpl<$Res>
    extends _$CoreShopifyPageInfoCopyWithImpl<$Res, _$CoreShopifyPageInfoImpl>
    implements _$$CoreShopifyPageInfoImplCopyWith<$Res> {
  __$$CoreShopifyPageInfoImplCopyWithImpl(_$CoreShopifyPageInfoImpl _value,
      $Res Function(_$CoreShopifyPageInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endCursor = freezed,
    Object? hasNextPage = freezed,
  }) {
    return _then(_$CoreShopifyPageInfoImpl(
      endCursor: freezed == endCursor
          ? _value.endCursor
          : endCursor // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNextPage: freezed == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyPageInfoImpl implements _CoreShopifyPageInfo {
  _$CoreShopifyPageInfoImpl(
      {@JsonKey(name: 'endCursor') this.endCursor,
      @JsonKey(name: 'hasNextPage') this.hasNextPage});

  factory _$CoreShopifyPageInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyPageInfoImplFromJson(json);

  @override
  @JsonKey(name: 'endCursor')
  final String? endCursor;
  @override
  @JsonKey(name: 'hasNextPage')
  final bool? hasNextPage;

  @override
  String toString() {
    return 'CoreShopifyPageInfo(endCursor: $endCursor, hasNextPage: $hasNextPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyPageInfoImpl &&
            (identical(other.endCursor, endCursor) ||
                other.endCursor == endCursor) &&
            (identical(other.hasNextPage, hasNextPage) ||
                other.hasNextPage == hasNextPage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, endCursor, hasNextPage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyPageInfoImplCopyWith<_$CoreShopifyPageInfoImpl> get copyWith =>
      __$$CoreShopifyPageInfoImplCopyWithImpl<_$CoreShopifyPageInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyPageInfoImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyPageInfo implements CoreShopifyPageInfo {
  factory _CoreShopifyPageInfo(
          {@JsonKey(name: 'endCursor') final String? endCursor,
          @JsonKey(name: 'hasNextPage') final bool? hasNextPage}) =
      _$CoreShopifyPageInfoImpl;

  factory _CoreShopifyPageInfo.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyPageInfoImpl.fromJson;

  @override
  @JsonKey(name: 'endCursor')
  String? get endCursor;
  @override
  @JsonKey(name: 'hasNextPage')
  bool? get hasNextPage;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyPageInfoImplCopyWith<_$CoreShopifyPageInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CoreShopifyOrder _$CoreShopifyOrderFromJson(Map<String, dynamic> json) {
  return _CoreShopifyOrder.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyOrder {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'currencyCode')
  String get currencyCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'lineItems')
  CoreShopifyLineItems get lineItems =>
      throw _privateConstructorUsedError; // Changed to handle edges structure
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'processedAt')
  String get processedAt =>
      throw _privateConstructorUsedError; // @JsonKey(name: 'shippingAddress') ShippingAddress? shippingAddress,
// @JsonKey(name: 'billingAddress') ShippingAddress? billingAddress,
  @JsonKey(name: 'statusPageUrl')
  String get statusPageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'subtotalPriceSet')
  CoreShopifyPriceSet get subtotalPriceSet =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'totalPriceSet')
  CoreShopifyPriceSet get totalPriceSet => throw _privateConstructorUsedError;
  @JsonKey(name: 'totalShippingPriceSet')
  CoreShopifyPriceSet get totalShippingPriceSet =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'totalTaxSet')
  CoreShopifyPriceSet get totalTaxSet => throw _privateConstructorUsedError;
  @JsonKey(name: 'displayFinancialStatus')
  String get displayFinancialStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'displayFulfillmentStatus')
  String get displayFulfillmentStatus => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  CoreShopifyMetafield? get metafield => throw _privateConstructorUsedError;
  String get cursor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyOrderCopyWith<CoreShopifyOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyOrderCopyWith<$Res> {
  factory $CoreShopifyOrderCopyWith(
          CoreShopifyOrder value, $Res Function(CoreShopifyOrder) then) =
      _$CoreShopifyOrderCopyWithImpl<$Res, CoreShopifyOrder>;
  @useResult
  $Res call(
      {String id,
      String email,
      @JsonKey(name: 'currencyCode') String currencyCode,
      @JsonKey(name: 'lineItems') CoreShopifyLineItems lineItems,
      String name,
      @JsonKey(name: 'processedAt') String processedAt,
      @JsonKey(name: 'statusPageUrl') String statusPageUrl,
      @JsonKey(name: 'subtotalPriceSet') CoreShopifyPriceSet subtotalPriceSet,
      @JsonKey(name: 'totalPriceSet') CoreShopifyPriceSet totalPriceSet,
      @JsonKey(name: 'totalShippingPriceSet')
      CoreShopifyPriceSet totalShippingPriceSet,
      @JsonKey(name: 'totalTaxSet') CoreShopifyPriceSet totalTaxSet,
      @JsonKey(name: 'displayFinancialStatus') String displayFinancialStatus,
      @JsonKey(name: 'displayFulfillmentStatus')
      String displayFulfillmentStatus,
      String? phone,
      CoreShopifyMetafield? metafield,
      String cursor});

  $CoreShopifyLineItemsCopyWith<$Res> get lineItems;
  $CoreShopifyPriceSetCopyWith<$Res> get subtotalPriceSet;
  $CoreShopifyPriceSetCopyWith<$Res> get totalPriceSet;
  $CoreShopifyPriceSetCopyWith<$Res> get totalShippingPriceSet;
  $CoreShopifyPriceSetCopyWith<$Res> get totalTaxSet;
  $CoreShopifyMetafieldCopyWith<$Res>? get metafield;
}

/// @nodoc
class _$CoreShopifyOrderCopyWithImpl<$Res, $Val extends CoreShopifyOrder>
    implements $CoreShopifyOrderCopyWith<$Res> {
  _$CoreShopifyOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? currencyCode = null,
    Object? lineItems = null,
    Object? name = null,
    Object? processedAt = null,
    Object? statusPageUrl = null,
    Object? subtotalPriceSet = null,
    Object? totalPriceSet = null,
    Object? totalShippingPriceSet = null,
    Object? totalTaxSet = null,
    Object? displayFinancialStatus = null,
    Object? displayFulfillmentStatus = null,
    Object? phone = freezed,
    Object? metafield = freezed,
    Object? cursor = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      lineItems: null == lineItems
          ? _value.lineItems
          : lineItems // ignore: cast_nullable_to_non_nullable
              as CoreShopifyLineItems,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      processedAt: null == processedAt
          ? _value.processedAt
          : processedAt // ignore: cast_nullable_to_non_nullable
              as String,
      statusPageUrl: null == statusPageUrl
          ? _value.statusPageUrl
          : statusPageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subtotalPriceSet: null == subtotalPriceSet
          ? _value.subtotalPriceSet
          : subtotalPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalPriceSet: null == totalPriceSet
          ? _value.totalPriceSet
          : totalPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalShippingPriceSet: null == totalShippingPriceSet
          ? _value.totalShippingPriceSet
          : totalShippingPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalTaxSet: null == totalTaxSet
          ? _value.totalTaxSet
          : totalTaxSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      displayFinancialStatus: null == displayFinancialStatus
          ? _value.displayFinancialStatus
          : displayFinancialStatus // ignore: cast_nullable_to_non_nullable
              as String,
      displayFulfillmentStatus: null == displayFulfillmentStatus
          ? _value.displayFulfillmentStatus
          : displayFulfillmentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      metafield: freezed == metafield
          ? _value.metafield
          : metafield // ignore: cast_nullable_to_non_nullable
              as CoreShopifyMetafield?,
      cursor: null == cursor
          ? _value.cursor
          : cursor // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyLineItemsCopyWith<$Res> get lineItems {
    return $CoreShopifyLineItemsCopyWith<$Res>(_value.lineItems, (value) {
      return _then(_value.copyWith(lineItems: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get subtotalPriceSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.subtotalPriceSet, (value) {
      return _then(_value.copyWith(subtotalPriceSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get totalPriceSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.totalPriceSet, (value) {
      return _then(_value.copyWith(totalPriceSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get totalShippingPriceSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.totalShippingPriceSet,
        (value) {
      return _then(_value.copyWith(totalShippingPriceSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get totalTaxSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.totalTaxSet, (value) {
      return _then(_value.copyWith(totalTaxSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyMetafieldCopyWith<$Res>? get metafield {
    if (_value.metafield == null) {
      return null;
    }

    return $CoreShopifyMetafieldCopyWith<$Res>(_value.metafield!, (value) {
      return _then(_value.copyWith(metafield: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyOrderImplCopyWith<$Res>
    implements $CoreShopifyOrderCopyWith<$Res> {
  factory _$$CoreShopifyOrderImplCopyWith(_$CoreShopifyOrderImpl value,
          $Res Function(_$CoreShopifyOrderImpl) then) =
      __$$CoreShopifyOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      @JsonKey(name: 'currencyCode') String currencyCode,
      @JsonKey(name: 'lineItems') CoreShopifyLineItems lineItems,
      String name,
      @JsonKey(name: 'processedAt') String processedAt,
      @JsonKey(name: 'statusPageUrl') String statusPageUrl,
      @JsonKey(name: 'subtotalPriceSet') CoreShopifyPriceSet subtotalPriceSet,
      @JsonKey(name: 'totalPriceSet') CoreShopifyPriceSet totalPriceSet,
      @JsonKey(name: 'totalShippingPriceSet')
      CoreShopifyPriceSet totalShippingPriceSet,
      @JsonKey(name: 'totalTaxSet') CoreShopifyPriceSet totalTaxSet,
      @JsonKey(name: 'displayFinancialStatus') String displayFinancialStatus,
      @JsonKey(name: 'displayFulfillmentStatus')
      String displayFulfillmentStatus,
      String? phone,
      CoreShopifyMetafield? metafield,
      String cursor});

  @override
  $CoreShopifyLineItemsCopyWith<$Res> get lineItems;
  @override
  $CoreShopifyPriceSetCopyWith<$Res> get subtotalPriceSet;
  @override
  $CoreShopifyPriceSetCopyWith<$Res> get totalPriceSet;
  @override
  $CoreShopifyPriceSetCopyWith<$Res> get totalShippingPriceSet;
  @override
  $CoreShopifyPriceSetCopyWith<$Res> get totalTaxSet;
  @override
  $CoreShopifyMetafieldCopyWith<$Res>? get metafield;
}

/// @nodoc
class __$$CoreShopifyOrderImplCopyWithImpl<$Res>
    extends _$CoreShopifyOrderCopyWithImpl<$Res, _$CoreShopifyOrderImpl>
    implements _$$CoreShopifyOrderImplCopyWith<$Res> {
  __$$CoreShopifyOrderImplCopyWithImpl(_$CoreShopifyOrderImpl _value,
      $Res Function(_$CoreShopifyOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? currencyCode = null,
    Object? lineItems = null,
    Object? name = null,
    Object? processedAt = null,
    Object? statusPageUrl = null,
    Object? subtotalPriceSet = null,
    Object? totalPriceSet = null,
    Object? totalShippingPriceSet = null,
    Object? totalTaxSet = null,
    Object? displayFinancialStatus = null,
    Object? displayFulfillmentStatus = null,
    Object? phone = freezed,
    Object? metafield = freezed,
    Object? cursor = null,
  }) {
    return _then(_$CoreShopifyOrderImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      lineItems: null == lineItems
          ? _value.lineItems
          : lineItems // ignore: cast_nullable_to_non_nullable
              as CoreShopifyLineItems,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      processedAt: null == processedAt
          ? _value.processedAt
          : processedAt // ignore: cast_nullable_to_non_nullable
              as String,
      statusPageUrl: null == statusPageUrl
          ? _value.statusPageUrl
          : statusPageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subtotalPriceSet: null == subtotalPriceSet
          ? _value.subtotalPriceSet
          : subtotalPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalPriceSet: null == totalPriceSet
          ? _value.totalPriceSet
          : totalPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalShippingPriceSet: null == totalShippingPriceSet
          ? _value.totalShippingPriceSet
          : totalShippingPriceSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      totalTaxSet: null == totalTaxSet
          ? _value.totalTaxSet
          : totalTaxSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      displayFinancialStatus: null == displayFinancialStatus
          ? _value.displayFinancialStatus
          : displayFinancialStatus // ignore: cast_nullable_to_non_nullable
              as String,
      displayFulfillmentStatus: null == displayFulfillmentStatus
          ? _value.displayFulfillmentStatus
          : displayFulfillmentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      metafield: freezed == metafield
          ? _value.metafield
          : metafield // ignore: cast_nullable_to_non_nullable
              as CoreShopifyMetafield?,
      cursor: null == cursor
          ? _value.cursor
          : cursor // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyOrderImpl implements _CoreShopifyOrder {
  _$CoreShopifyOrderImpl(
      {required this.id,
      required this.email,
      @JsonKey(name: 'currencyCode') required this.currencyCode,
      @JsonKey(name: 'lineItems') required this.lineItems,
      required this.name,
      @JsonKey(name: 'processedAt') required this.processedAt,
      @JsonKey(name: 'statusPageUrl') required this.statusPageUrl,
      @JsonKey(name: 'subtotalPriceSet') required this.subtotalPriceSet,
      @JsonKey(name: 'totalPriceSet') required this.totalPriceSet,
      @JsonKey(name: 'totalShippingPriceSet')
      required this.totalShippingPriceSet,
      @JsonKey(name: 'totalTaxSet') required this.totalTaxSet,
      @JsonKey(name: 'displayFinancialStatus')
      required this.displayFinancialStatus,
      @JsonKey(name: 'displayFulfillmentStatus')
      required this.displayFulfillmentStatus,
      this.phone,
      this.metafield,
      required this.cursor});

  factory _$CoreShopifyOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyOrderImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  @JsonKey(name: 'currencyCode')
  final String currencyCode;
  @override
  @JsonKey(name: 'lineItems')
  final CoreShopifyLineItems lineItems;
// Changed to handle edges structure
  @override
  final String name;
  @override
  @JsonKey(name: 'processedAt')
  final String processedAt;
// @JsonKey(name: 'shippingAddress') ShippingAddress? shippingAddress,
// @JsonKey(name: 'billingAddress') ShippingAddress? billingAddress,
  @override
  @JsonKey(name: 'statusPageUrl')
  final String statusPageUrl;
  @override
  @JsonKey(name: 'subtotalPriceSet')
  final CoreShopifyPriceSet subtotalPriceSet;
  @override
  @JsonKey(name: 'totalPriceSet')
  final CoreShopifyPriceSet totalPriceSet;
  @override
  @JsonKey(name: 'totalShippingPriceSet')
  final CoreShopifyPriceSet totalShippingPriceSet;
  @override
  @JsonKey(name: 'totalTaxSet')
  final CoreShopifyPriceSet totalTaxSet;
  @override
  @JsonKey(name: 'displayFinancialStatus')
  final String displayFinancialStatus;
  @override
  @JsonKey(name: 'displayFulfillmentStatus')
  final String displayFulfillmentStatus;
  @override
  final String? phone;
  @override
  final CoreShopifyMetafield? metafield;
  @override
  final String cursor;

  @override
  String toString() {
    return 'CoreShopifyOrder(id: $id, email: $email, currencyCode: $currencyCode, lineItems: $lineItems, name: $name, processedAt: $processedAt, statusPageUrl: $statusPageUrl, subtotalPriceSet: $subtotalPriceSet, totalPriceSet: $totalPriceSet, totalShippingPriceSet: $totalShippingPriceSet, totalTaxSet: $totalTaxSet, displayFinancialStatus: $displayFinancialStatus, displayFulfillmentStatus: $displayFulfillmentStatus, phone: $phone, metafield: $metafield, cursor: $cursor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.lineItems, lineItems) ||
                other.lineItems == lineItems) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.statusPageUrl, statusPageUrl) ||
                other.statusPageUrl == statusPageUrl) &&
            (identical(other.subtotalPriceSet, subtotalPriceSet) ||
                other.subtotalPriceSet == subtotalPriceSet) &&
            (identical(other.totalPriceSet, totalPriceSet) ||
                other.totalPriceSet == totalPriceSet) &&
            (identical(other.totalShippingPriceSet, totalShippingPriceSet) ||
                other.totalShippingPriceSet == totalShippingPriceSet) &&
            (identical(other.totalTaxSet, totalTaxSet) ||
                other.totalTaxSet == totalTaxSet) &&
            (identical(other.displayFinancialStatus, displayFinancialStatus) ||
                other.displayFinancialStatus == displayFinancialStatus) &&
            (identical(
                    other.displayFulfillmentStatus, displayFulfillmentStatus) ||
                other.displayFulfillmentStatus == displayFulfillmentStatus) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.metafield, metafield) ||
                other.metafield == metafield) &&
            (identical(other.cursor, cursor) || other.cursor == cursor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      email,
      currencyCode,
      lineItems,
      name,
      processedAt,
      statusPageUrl,
      subtotalPriceSet,
      totalPriceSet,
      totalShippingPriceSet,
      totalTaxSet,
      displayFinancialStatus,
      displayFulfillmentStatus,
      phone,
      metafield,
      cursor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyOrderImplCopyWith<_$CoreShopifyOrderImpl> get copyWith =>
      __$$CoreShopifyOrderImplCopyWithImpl<_$CoreShopifyOrderImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyOrderImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyOrder implements CoreShopifyOrder {
  factory _CoreShopifyOrder(
      {required final String id,
      required final String email,
      @JsonKey(name: 'currencyCode') required final String currencyCode,
      @JsonKey(name: 'lineItems') required final CoreShopifyLineItems lineItems,
      required final String name,
      @JsonKey(name: 'processedAt') required final String processedAt,
      @JsonKey(name: 'statusPageUrl') required final String statusPageUrl,
      @JsonKey(name: 'subtotalPriceSet')
      required final CoreShopifyPriceSet subtotalPriceSet,
      @JsonKey(name: 'totalPriceSet')
      required final CoreShopifyPriceSet totalPriceSet,
      @JsonKey(name: 'totalShippingPriceSet')
      required final CoreShopifyPriceSet totalShippingPriceSet,
      @JsonKey(name: 'totalTaxSet')
      required final CoreShopifyPriceSet totalTaxSet,
      @JsonKey(name: 'displayFinancialStatus')
      required final String displayFinancialStatus,
      @JsonKey(name: 'displayFulfillmentStatus')
      required final String displayFulfillmentStatus,
      final String? phone,
      final CoreShopifyMetafield? metafield,
      required final String cursor}) = _$CoreShopifyOrderImpl;

  factory _CoreShopifyOrder.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyOrderImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  @JsonKey(name: 'currencyCode')
  String get currencyCode;
  @override
  @JsonKey(name: 'lineItems')
  CoreShopifyLineItems get lineItems;
  @override // Changed to handle edges structure
  String get name;
  @override
  @JsonKey(name: 'processedAt')
  String get processedAt;
  @override // @JsonKey(name: 'shippingAddress') ShippingAddress? shippingAddress,
// @JsonKey(name: 'billingAddress') ShippingAddress? billingAddress,
  @JsonKey(name: 'statusPageUrl')
  String get statusPageUrl;
  @override
  @JsonKey(name: 'subtotalPriceSet')
  CoreShopifyPriceSet get subtotalPriceSet;
  @override
  @JsonKey(name: 'totalPriceSet')
  CoreShopifyPriceSet get totalPriceSet;
  @override
  @JsonKey(name: 'totalShippingPriceSet')
  CoreShopifyPriceSet get totalShippingPriceSet;
  @override
  @JsonKey(name: 'totalTaxSet')
  CoreShopifyPriceSet get totalTaxSet;
  @override
  @JsonKey(name: 'displayFinancialStatus')
  String get displayFinancialStatus;
  @override
  @JsonKey(name: 'displayFulfillmentStatus')
  String get displayFulfillmentStatus;
  @override
  String? get phone;
  @override
  CoreShopifyMetafield? get metafield;
  @override
  String get cursor;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyOrderImplCopyWith<_$CoreShopifyOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CoreShopifyLineItems _$CoreShopifyLineItemsFromJson(Map<String, dynamic> json) {
  return _CoreShopifyLineItems.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyLineItems {
  List<CoreShopifyLineItemEdge> get edges => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyLineItemsCopyWith<CoreShopifyLineItems> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyLineItemsCopyWith<$Res> {
  factory $CoreShopifyLineItemsCopyWith(CoreShopifyLineItems value,
          $Res Function(CoreShopifyLineItems) then) =
      _$CoreShopifyLineItemsCopyWithImpl<$Res, CoreShopifyLineItems>;
  @useResult
  $Res call({List<CoreShopifyLineItemEdge> edges});
}

/// @nodoc
class _$CoreShopifyLineItemsCopyWithImpl<$Res,
        $Val extends CoreShopifyLineItems>
    implements $CoreShopifyLineItemsCopyWith<$Res> {
  _$CoreShopifyLineItemsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = null,
  }) {
    return _then(_value.copyWith(
      edges: null == edges
          ? _value.edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<CoreShopifyLineItemEdge>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyLineItemsImplCopyWith<$Res>
    implements $CoreShopifyLineItemsCopyWith<$Res> {
  factory _$$CoreShopifyLineItemsImplCopyWith(_$CoreShopifyLineItemsImpl value,
          $Res Function(_$CoreShopifyLineItemsImpl) then) =
      __$$CoreShopifyLineItemsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CoreShopifyLineItemEdge> edges});
}

/// @nodoc
class __$$CoreShopifyLineItemsImplCopyWithImpl<$Res>
    extends _$CoreShopifyLineItemsCopyWithImpl<$Res, _$CoreShopifyLineItemsImpl>
    implements _$$CoreShopifyLineItemsImplCopyWith<$Res> {
  __$$CoreShopifyLineItemsImplCopyWithImpl(_$CoreShopifyLineItemsImpl _value,
      $Res Function(_$CoreShopifyLineItemsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? edges = null,
  }) {
    return _then(_$CoreShopifyLineItemsImpl(
      edges: null == edges
          ? _value._edges
          : edges // ignore: cast_nullable_to_non_nullable
              as List<CoreShopifyLineItemEdge>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyLineItemsImpl implements _CoreShopifyLineItems {
  _$CoreShopifyLineItemsImpl(
      {required final List<CoreShopifyLineItemEdge> edges})
      : _edges = edges;

  factory _$CoreShopifyLineItemsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyLineItemsImplFromJson(json);

  final List<CoreShopifyLineItemEdge> _edges;
  @override
  List<CoreShopifyLineItemEdge> get edges {
    if (_edges is EqualUnmodifiableListView) return _edges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_edges);
  }

  @override
  String toString() {
    return 'CoreShopifyLineItems(edges: $edges)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyLineItemsImpl &&
            const DeepCollectionEquality().equals(other._edges, _edges));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_edges));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyLineItemsImplCopyWith<_$CoreShopifyLineItemsImpl>
      get copyWith =>
          __$$CoreShopifyLineItemsImplCopyWithImpl<_$CoreShopifyLineItemsImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyLineItemsImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyLineItems implements CoreShopifyLineItems {
  factory _CoreShopifyLineItems(
          {required final List<CoreShopifyLineItemEdge> edges}) =
      _$CoreShopifyLineItemsImpl;

  factory _CoreShopifyLineItems.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyLineItemsImpl.fromJson;

  @override
  List<CoreShopifyLineItemEdge> get edges;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyLineItemsImplCopyWith<_$CoreShopifyLineItemsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyLineItemEdge _$CoreShopifyLineItemEdgeFromJson(
    Map<String, dynamic> json) {
  return _CoreShopifyLineItemEdge.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyLineItemEdge {
  CoreShopifyLineItem get node => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyLineItemEdgeCopyWith<CoreShopifyLineItemEdge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyLineItemEdgeCopyWith<$Res> {
  factory $CoreShopifyLineItemEdgeCopyWith(CoreShopifyLineItemEdge value,
          $Res Function(CoreShopifyLineItemEdge) then) =
      _$CoreShopifyLineItemEdgeCopyWithImpl<$Res, CoreShopifyLineItemEdge>;
  @useResult
  $Res call({CoreShopifyLineItem node});

  $CoreShopifyLineItemCopyWith<$Res> get node;
}

/// @nodoc
class _$CoreShopifyLineItemEdgeCopyWithImpl<$Res,
        $Val extends CoreShopifyLineItemEdge>
    implements $CoreShopifyLineItemEdgeCopyWith<$Res> {
  _$CoreShopifyLineItemEdgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_value.copyWith(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as CoreShopifyLineItem,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyLineItemCopyWith<$Res> get node {
    return $CoreShopifyLineItemCopyWith<$Res>(_value.node, (value) {
      return _then(_value.copyWith(node: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyLineItemEdgeImplCopyWith<$Res>
    implements $CoreShopifyLineItemEdgeCopyWith<$Res> {
  factory _$$CoreShopifyLineItemEdgeImplCopyWith(
          _$CoreShopifyLineItemEdgeImpl value,
          $Res Function(_$CoreShopifyLineItemEdgeImpl) then) =
      __$$CoreShopifyLineItemEdgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CoreShopifyLineItem node});

  @override
  $CoreShopifyLineItemCopyWith<$Res> get node;
}

/// @nodoc
class __$$CoreShopifyLineItemEdgeImplCopyWithImpl<$Res>
    extends _$CoreShopifyLineItemEdgeCopyWithImpl<$Res,
        _$CoreShopifyLineItemEdgeImpl>
    implements _$$CoreShopifyLineItemEdgeImplCopyWith<$Res> {
  __$$CoreShopifyLineItemEdgeImplCopyWithImpl(
      _$CoreShopifyLineItemEdgeImpl _value,
      $Res Function(_$CoreShopifyLineItemEdgeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? node = null,
  }) {
    return _then(_$CoreShopifyLineItemEdgeImpl(
      node: null == node
          ? _value.node
          : node // ignore: cast_nullable_to_non_nullable
              as CoreShopifyLineItem,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyLineItemEdgeImpl implements _CoreShopifyLineItemEdge {
  _$CoreShopifyLineItemEdgeImpl({required this.node});

  factory _$CoreShopifyLineItemEdgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyLineItemEdgeImplFromJson(json);

  @override
  final CoreShopifyLineItem node;

  @override
  String toString() {
    return 'CoreShopifyLineItemEdge(node: $node)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyLineItemEdgeImpl &&
            (identical(other.node, node) || other.node == node));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, node);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyLineItemEdgeImplCopyWith<_$CoreShopifyLineItemEdgeImpl>
      get copyWith => __$$CoreShopifyLineItemEdgeImplCopyWithImpl<
          _$CoreShopifyLineItemEdgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyLineItemEdgeImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyLineItemEdge implements CoreShopifyLineItemEdge {
  factory _CoreShopifyLineItemEdge({required final CoreShopifyLineItem node}) =
      _$CoreShopifyLineItemEdgeImpl;

  factory _CoreShopifyLineItemEdge.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyLineItemEdgeImpl.fromJson;

  @override
  CoreShopifyLineItem get node;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyLineItemEdgeImplCopyWith<_$CoreShopifyLineItemEdgeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyLineItem _$CoreShopifyLineItemFromJson(Map<String, dynamic> json) {
  return _CoreShopifyLineItem.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyLineItem {
  String get title => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'originalTotalSet')
  CoreShopifyPriceSet get originalTotalSet =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'discountedTotalSet')
  CoreShopifyPriceSet get discountedTotalSet =>
      throw _privateConstructorUsedError;
  CoreShopifyProductVariant? get variant => throw _privateConstructorUsedError;
  CoreShopifyProductImage? get image => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyLineItemCopyWith<CoreShopifyLineItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyLineItemCopyWith<$Res> {
  factory $CoreShopifyLineItemCopyWith(
          CoreShopifyLineItem value, $Res Function(CoreShopifyLineItem) then) =
      _$CoreShopifyLineItemCopyWithImpl<$Res, CoreShopifyLineItem>;
  @useResult
  $Res call(
      {String title,
      int quantity,
      @JsonKey(name: 'originalTotalSet') CoreShopifyPriceSet originalTotalSet,
      @JsonKey(name: 'discountedTotalSet')
      CoreShopifyPriceSet discountedTotalSet,
      CoreShopifyProductVariant? variant,
      CoreShopifyProductImage? image});

  $CoreShopifyPriceSetCopyWith<$Res> get originalTotalSet;
  $CoreShopifyPriceSetCopyWith<$Res> get discountedTotalSet;
  $CoreShopifyProductVariantCopyWith<$Res>? get variant;
  $CoreShopifyProductImageCopyWith<$Res>? get image;
}

/// @nodoc
class _$CoreShopifyLineItemCopyWithImpl<$Res, $Val extends CoreShopifyLineItem>
    implements $CoreShopifyLineItemCopyWith<$Res> {
  _$CoreShopifyLineItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? quantity = null,
    Object? originalTotalSet = null,
    Object? discountedTotalSet = null,
    Object? variant = freezed,
    Object? image = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      originalTotalSet: null == originalTotalSet
          ? _value.originalTotalSet
          : originalTotalSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      discountedTotalSet: null == discountedTotalSet
          ? _value.discountedTotalSet
          : discountedTotalSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      variant: freezed == variant
          ? _value.variant
          : variant // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductVariant?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductImage?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get originalTotalSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.originalTotalSet, (value) {
      return _then(_value.copyWith(originalTotalSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPriceSetCopyWith<$Res> get discountedTotalSet {
    return $CoreShopifyPriceSetCopyWith<$Res>(_value.discountedTotalSet,
        (value) {
      return _then(_value.copyWith(discountedTotalSet: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyProductVariantCopyWith<$Res>? get variant {
    if (_value.variant == null) {
      return null;
    }

    return $CoreShopifyProductVariantCopyWith<$Res>(_value.variant!, (value) {
      return _then(_value.copyWith(variant: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyProductImageCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $CoreShopifyProductImageCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyLineItemImplCopyWith<$Res>
    implements $CoreShopifyLineItemCopyWith<$Res> {
  factory _$$CoreShopifyLineItemImplCopyWith(_$CoreShopifyLineItemImpl value,
          $Res Function(_$CoreShopifyLineItemImpl) then) =
      __$$CoreShopifyLineItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      int quantity,
      @JsonKey(name: 'originalTotalSet') CoreShopifyPriceSet originalTotalSet,
      @JsonKey(name: 'discountedTotalSet')
      CoreShopifyPriceSet discountedTotalSet,
      CoreShopifyProductVariant? variant,
      CoreShopifyProductImage? image});

  @override
  $CoreShopifyPriceSetCopyWith<$Res> get originalTotalSet;
  @override
  $CoreShopifyPriceSetCopyWith<$Res> get discountedTotalSet;
  @override
  $CoreShopifyProductVariantCopyWith<$Res>? get variant;
  @override
  $CoreShopifyProductImageCopyWith<$Res>? get image;
}

/// @nodoc
class __$$CoreShopifyLineItemImplCopyWithImpl<$Res>
    extends _$CoreShopifyLineItemCopyWithImpl<$Res, _$CoreShopifyLineItemImpl>
    implements _$$CoreShopifyLineItemImplCopyWith<$Res> {
  __$$CoreShopifyLineItemImplCopyWithImpl(_$CoreShopifyLineItemImpl _value,
      $Res Function(_$CoreShopifyLineItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? quantity = null,
    Object? originalTotalSet = null,
    Object? discountedTotalSet = null,
    Object? variant = freezed,
    Object? image = freezed,
  }) {
    return _then(_$CoreShopifyLineItemImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      originalTotalSet: null == originalTotalSet
          ? _value.originalTotalSet
          : originalTotalSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      discountedTotalSet: null == discountedTotalSet
          ? _value.discountedTotalSet
          : discountedTotalSet // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPriceSet,
      variant: freezed == variant
          ? _value.variant
          : variant // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductVariant?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductImage?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyLineItemImpl implements _CoreShopifyLineItem {
  _$CoreShopifyLineItemImpl(
      {required this.title,
      required this.quantity,
      @JsonKey(name: 'originalTotalSet') required this.originalTotalSet,
      @JsonKey(name: 'discountedTotalSet') required this.discountedTotalSet,
      this.variant,
      this.image});

  factory _$CoreShopifyLineItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyLineItemImplFromJson(json);

  @override
  final String title;
  @override
  final int quantity;
  @override
  @JsonKey(name: 'originalTotalSet')
  final CoreShopifyPriceSet originalTotalSet;
  @override
  @JsonKey(name: 'discountedTotalSet')
  final CoreShopifyPriceSet discountedTotalSet;
  @override
  final CoreShopifyProductVariant? variant;
  @override
  final CoreShopifyProductImage? image;

  @override
  String toString() {
    return 'CoreShopifyLineItem(title: $title, quantity: $quantity, originalTotalSet: $originalTotalSet, discountedTotalSet: $discountedTotalSet, variant: $variant, image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyLineItemImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.originalTotalSet, originalTotalSet) ||
                other.originalTotalSet == originalTotalSet) &&
            (identical(other.discountedTotalSet, discountedTotalSet) ||
                other.discountedTotalSet == discountedTotalSet) &&
            (identical(other.variant, variant) || other.variant == variant) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, quantity,
      originalTotalSet, discountedTotalSet, variant, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyLineItemImplCopyWith<_$CoreShopifyLineItemImpl> get copyWith =>
      __$$CoreShopifyLineItemImplCopyWithImpl<_$CoreShopifyLineItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyLineItemImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyLineItem implements CoreShopifyLineItem {
  factory _CoreShopifyLineItem(
      {required final String title,
      required final int quantity,
      @JsonKey(name: 'originalTotalSet')
      required final CoreShopifyPriceSet originalTotalSet,
      @JsonKey(name: 'discountedTotalSet')
      required final CoreShopifyPriceSet discountedTotalSet,
      final CoreShopifyProductVariant? variant,
      final CoreShopifyProductImage? image}) = _$CoreShopifyLineItemImpl;

  factory _CoreShopifyLineItem.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyLineItemImpl.fromJson;

  @override
  String get title;
  @override
  int get quantity;
  @override
  @JsonKey(name: 'originalTotalSet')
  CoreShopifyPriceSet get originalTotalSet;
  @override
  @JsonKey(name: 'discountedTotalSet')
  CoreShopifyPriceSet get discountedTotalSet;
  @override
  CoreShopifyProductVariant? get variant;
  @override
  CoreShopifyProductImage? get image;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyLineItemImplCopyWith<_$CoreShopifyLineItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CoreShopifyMetafield _$CoreShopifyMetafieldFromJson(Map<String, dynamic> json) {
  return _CoreShopifyMetafield.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyMetafield {
  String? get namespace => throw _privateConstructorUsedError;
  String? get key => throw _privateConstructorUsedError;
  String? get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyMetafieldCopyWith<CoreShopifyMetafield> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyMetafieldCopyWith<$Res> {
  factory $CoreShopifyMetafieldCopyWith(CoreShopifyMetafield value,
          $Res Function(CoreShopifyMetafield) then) =
      _$CoreShopifyMetafieldCopyWithImpl<$Res, CoreShopifyMetafield>;
  @useResult
  $Res call({String? namespace, String? key, String? value});
}

/// @nodoc
class _$CoreShopifyMetafieldCopyWithImpl<$Res,
        $Val extends CoreShopifyMetafield>
    implements $CoreShopifyMetafieldCopyWith<$Res> {
  _$CoreShopifyMetafieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? namespace = freezed,
    Object? key = freezed,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      namespace: freezed == namespace
          ? _value.namespace
          : namespace // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyMetafieldImplCopyWith<$Res>
    implements $CoreShopifyMetafieldCopyWith<$Res> {
  factory _$$CoreShopifyMetafieldImplCopyWith(_$CoreShopifyMetafieldImpl value,
          $Res Function(_$CoreShopifyMetafieldImpl) then) =
      __$$CoreShopifyMetafieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? namespace, String? key, String? value});
}

/// @nodoc
class __$$CoreShopifyMetafieldImplCopyWithImpl<$Res>
    extends _$CoreShopifyMetafieldCopyWithImpl<$Res, _$CoreShopifyMetafieldImpl>
    implements _$$CoreShopifyMetafieldImplCopyWith<$Res> {
  __$$CoreShopifyMetafieldImplCopyWithImpl(_$CoreShopifyMetafieldImpl _value,
      $Res Function(_$CoreShopifyMetafieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? namespace = freezed,
    Object? key = freezed,
    Object? value = freezed,
  }) {
    return _then(_$CoreShopifyMetafieldImpl(
      namespace: freezed == namespace
          ? _value.namespace
          : namespace // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyMetafieldImpl implements _CoreShopifyMetafield {
  _$CoreShopifyMetafieldImpl({this.namespace, this.key, this.value});

  factory _$CoreShopifyMetafieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyMetafieldImplFromJson(json);

  @override
  final String? namespace;
  @override
  final String? key;
  @override
  final String? value;

  @override
  String toString() {
    return 'CoreShopifyMetafield(namespace: $namespace, key: $key, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyMetafieldImpl &&
            (identical(other.namespace, namespace) ||
                other.namespace == namespace) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, namespace, key, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyMetafieldImplCopyWith<_$CoreShopifyMetafieldImpl>
      get copyWith =>
          __$$CoreShopifyMetafieldImplCopyWithImpl<_$CoreShopifyMetafieldImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyMetafieldImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyMetafield implements CoreShopifyMetafield {
  factory _CoreShopifyMetafield(
      {final String? namespace,
      final String? key,
      final String? value}) = _$CoreShopifyMetafieldImpl;

  factory _CoreShopifyMetafield.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyMetafieldImpl.fromJson;

  @override
  String? get namespace;
  @override
  String? get key;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyMetafieldImplCopyWith<_$CoreShopifyMetafieldImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyPriceSet _$CoreShopifyPriceSetFromJson(Map<String, dynamic> json) {
  return _CoreShopifyPriceSet.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyPriceSet {
  @JsonKey(name: 'presentmentMoney')
  CoreShopifyPresentmentMoney get presentmentMoney =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyPriceSetCopyWith<CoreShopifyPriceSet> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyPriceSetCopyWith<$Res> {
  factory $CoreShopifyPriceSetCopyWith(
          CoreShopifyPriceSet value, $Res Function(CoreShopifyPriceSet) then) =
      _$CoreShopifyPriceSetCopyWithImpl<$Res, CoreShopifyPriceSet>;
  @useResult
  $Res call(
      {@JsonKey(name: 'presentmentMoney')
      CoreShopifyPresentmentMoney presentmentMoney});

  $CoreShopifyPresentmentMoneyCopyWith<$Res> get presentmentMoney;
}

/// @nodoc
class _$CoreShopifyPriceSetCopyWithImpl<$Res, $Val extends CoreShopifyPriceSet>
    implements $CoreShopifyPriceSetCopyWith<$Res> {
  _$CoreShopifyPriceSetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presentmentMoney = null,
  }) {
    return _then(_value.copyWith(
      presentmentMoney: null == presentmentMoney
          ? _value.presentmentMoney
          : presentmentMoney // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPresentmentMoney,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyPresentmentMoneyCopyWith<$Res> get presentmentMoney {
    return $CoreShopifyPresentmentMoneyCopyWith<$Res>(_value.presentmentMoney,
        (value) {
      return _then(_value.copyWith(presentmentMoney: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyPriceSetImplCopyWith<$Res>
    implements $CoreShopifyPriceSetCopyWith<$Res> {
  factory _$$CoreShopifyPriceSetImplCopyWith(_$CoreShopifyPriceSetImpl value,
          $Res Function(_$CoreShopifyPriceSetImpl) then) =
      __$$CoreShopifyPriceSetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'presentmentMoney')
      CoreShopifyPresentmentMoney presentmentMoney});

  @override
  $CoreShopifyPresentmentMoneyCopyWith<$Res> get presentmentMoney;
}

/// @nodoc
class __$$CoreShopifyPriceSetImplCopyWithImpl<$Res>
    extends _$CoreShopifyPriceSetCopyWithImpl<$Res, _$CoreShopifyPriceSetImpl>
    implements _$$CoreShopifyPriceSetImplCopyWith<$Res> {
  __$$CoreShopifyPriceSetImplCopyWithImpl(_$CoreShopifyPriceSetImpl _value,
      $Res Function(_$CoreShopifyPriceSetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presentmentMoney = null,
  }) {
    return _then(_$CoreShopifyPriceSetImpl(
      presentmentMoney: null == presentmentMoney
          ? _value.presentmentMoney
          : presentmentMoney // ignore: cast_nullable_to_non_nullable
              as CoreShopifyPresentmentMoney,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyPriceSetImpl implements _CoreShopifyPriceSet {
  _$CoreShopifyPriceSetImpl(
      {@JsonKey(name: 'presentmentMoney') required this.presentmentMoney});

  factory _$CoreShopifyPriceSetImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyPriceSetImplFromJson(json);

  @override
  @JsonKey(name: 'presentmentMoney')
  final CoreShopifyPresentmentMoney presentmentMoney;

  @override
  String toString() {
    return 'CoreShopifyPriceSet(presentmentMoney: $presentmentMoney)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyPriceSetImpl &&
            (identical(other.presentmentMoney, presentmentMoney) ||
                other.presentmentMoney == presentmentMoney));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, presentmentMoney);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyPriceSetImplCopyWith<_$CoreShopifyPriceSetImpl> get copyWith =>
      __$$CoreShopifyPriceSetImplCopyWithImpl<_$CoreShopifyPriceSetImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyPriceSetImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyPriceSet implements CoreShopifyPriceSet {
  factory _CoreShopifyPriceSet(
          {@JsonKey(name: 'presentmentMoney')
          required final CoreShopifyPresentmentMoney presentmentMoney}) =
      _$CoreShopifyPriceSetImpl;

  factory _CoreShopifyPriceSet.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyPriceSetImpl.fromJson;

  @override
  @JsonKey(name: 'presentmentMoney')
  CoreShopifyPresentmentMoney get presentmentMoney;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyPriceSetImplCopyWith<_$CoreShopifyPriceSetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CoreShopifyPresentmentMoney _$CoreShopifyPresentmentMoneyFromJson(
    Map<String, dynamic> json) {
  return _CoreShopifyPresentmentMoney.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyPresentmentMoney {
  String get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'currencyCode')
  String get currencyCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyPresentmentMoneyCopyWith<CoreShopifyPresentmentMoney>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyPresentmentMoneyCopyWith<$Res> {
  factory $CoreShopifyPresentmentMoneyCopyWith(
          CoreShopifyPresentmentMoney value,
          $Res Function(CoreShopifyPresentmentMoney) then) =
      _$CoreShopifyPresentmentMoneyCopyWithImpl<$Res,
          CoreShopifyPresentmentMoney>;
  @useResult
  $Res call(
      {String amount, @JsonKey(name: 'currencyCode') String currencyCode});
}

/// @nodoc
class _$CoreShopifyPresentmentMoneyCopyWithImpl<$Res,
        $Val extends CoreShopifyPresentmentMoney>
    implements $CoreShopifyPresentmentMoneyCopyWith<$Res> {
  _$CoreShopifyPresentmentMoneyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currencyCode = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyPresentmentMoneyImplCopyWith<$Res>
    implements $CoreShopifyPresentmentMoneyCopyWith<$Res> {
  factory _$$CoreShopifyPresentmentMoneyImplCopyWith(
          _$CoreShopifyPresentmentMoneyImpl value,
          $Res Function(_$CoreShopifyPresentmentMoneyImpl) then) =
      __$$CoreShopifyPresentmentMoneyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String amount, @JsonKey(name: 'currencyCode') String currencyCode});
}

/// @nodoc
class __$$CoreShopifyPresentmentMoneyImplCopyWithImpl<$Res>
    extends _$CoreShopifyPresentmentMoneyCopyWithImpl<$Res,
        _$CoreShopifyPresentmentMoneyImpl>
    implements _$$CoreShopifyPresentmentMoneyImplCopyWith<$Res> {
  __$$CoreShopifyPresentmentMoneyImplCopyWithImpl(
      _$CoreShopifyPresentmentMoneyImpl _value,
      $Res Function(_$CoreShopifyPresentmentMoneyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currencyCode = null,
  }) {
    return _then(_$CoreShopifyPresentmentMoneyImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyPresentmentMoneyImpl
    implements _CoreShopifyPresentmentMoney {
  _$CoreShopifyPresentmentMoneyImpl(
      {required this.amount,
      @JsonKey(name: 'currencyCode') required this.currencyCode});

  factory _$CoreShopifyPresentmentMoneyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CoreShopifyPresentmentMoneyImplFromJson(json);

  @override
  final String amount;
  @override
  @JsonKey(name: 'currencyCode')
  final String currencyCode;

  @override
  String toString() {
    return 'CoreShopifyPresentmentMoney(amount: $amount, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyPresentmentMoneyImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, amount, currencyCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyPresentmentMoneyImplCopyWith<_$CoreShopifyPresentmentMoneyImpl>
      get copyWith => __$$CoreShopifyPresentmentMoneyImplCopyWithImpl<
          _$CoreShopifyPresentmentMoneyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyPresentmentMoneyImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyPresentmentMoney
    implements CoreShopifyPresentmentMoney {
  factory _CoreShopifyPresentmentMoney(
          {required final String amount,
          @JsonKey(name: 'currencyCode') required final String currencyCode}) =
      _$CoreShopifyPresentmentMoneyImpl;

  factory _CoreShopifyPresentmentMoney.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyPresentmentMoneyImpl.fromJson;

  @override
  String get amount;
  @override
  @JsonKey(name: 'currencyCode')
  String get currencyCode;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyPresentmentMoneyImplCopyWith<_$CoreShopifyPresentmentMoneyImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyProductVariant _$CoreShopifyProductVariantFromJson(
    Map<String, dynamic> json) {
  return _CoreShopifyProductVariant.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyProductVariant {
  String get title => throw _privateConstructorUsedError;
  CoreShopifyProduct get product => throw _privateConstructorUsedError;
  CoreShopifyProductImage? get image => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyProductVariantCopyWith<CoreShopifyProductVariant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyProductVariantCopyWith<$Res> {
  factory $CoreShopifyProductVariantCopyWith(CoreShopifyProductVariant value,
          $Res Function(CoreShopifyProductVariant) then) =
      _$CoreShopifyProductVariantCopyWithImpl<$Res, CoreShopifyProductVariant>;
  @useResult
  $Res call(
      {String title,
      CoreShopifyProduct product,
      CoreShopifyProductImage? image});

  $CoreShopifyProductCopyWith<$Res> get product;
  $CoreShopifyProductImageCopyWith<$Res>? get image;
}

/// @nodoc
class _$CoreShopifyProductVariantCopyWithImpl<$Res,
        $Val extends CoreShopifyProductVariant>
    implements $CoreShopifyProductVariantCopyWith<$Res> {
  _$CoreShopifyProductVariantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? product = null,
    Object? image = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProduct,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductImage?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyProductCopyWith<$Res> get product {
    return $CoreShopifyProductCopyWith<$Res>(_value.product, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CoreShopifyProductImageCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $CoreShopifyProductImageCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreShopifyProductVariantImplCopyWith<$Res>
    implements $CoreShopifyProductVariantCopyWith<$Res> {
  factory _$$CoreShopifyProductVariantImplCopyWith(
          _$CoreShopifyProductVariantImpl value,
          $Res Function(_$CoreShopifyProductVariantImpl) then) =
      __$$CoreShopifyProductVariantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      CoreShopifyProduct product,
      CoreShopifyProductImage? image});

  @override
  $CoreShopifyProductCopyWith<$Res> get product;
  @override
  $CoreShopifyProductImageCopyWith<$Res>? get image;
}

/// @nodoc
class __$$CoreShopifyProductVariantImplCopyWithImpl<$Res>
    extends _$CoreShopifyProductVariantCopyWithImpl<$Res,
        _$CoreShopifyProductVariantImpl>
    implements _$$CoreShopifyProductVariantImplCopyWith<$Res> {
  __$$CoreShopifyProductVariantImplCopyWithImpl(
      _$CoreShopifyProductVariantImpl _value,
      $Res Function(_$CoreShopifyProductVariantImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? product = null,
    Object? image = freezed,
  }) {
    return _then(_$CoreShopifyProductVariantImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProduct,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as CoreShopifyProductImage?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyProductVariantImpl implements _CoreShopifyProductVariant {
  _$CoreShopifyProductVariantImpl(
      {required this.title, required this.product, this.image});

  factory _$CoreShopifyProductVariantImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyProductVariantImplFromJson(json);

  @override
  final String title;
  @override
  final CoreShopifyProduct product;
  @override
  final CoreShopifyProductImage? image;

  @override
  String toString() {
    return 'CoreShopifyProductVariant(title: $title, product: $product, image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyProductVariantImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, product, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyProductVariantImplCopyWith<_$CoreShopifyProductVariantImpl>
      get copyWith => __$$CoreShopifyProductVariantImplCopyWithImpl<
          _$CoreShopifyProductVariantImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyProductVariantImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyProductVariant implements CoreShopifyProductVariant {
  factory _CoreShopifyProductVariant(
      {required final String title,
      required final CoreShopifyProduct product,
      final CoreShopifyProductImage? image}) = _$CoreShopifyProductVariantImpl;

  factory _CoreShopifyProductVariant.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyProductVariantImpl.fromJson;

  @override
  String get title;
  @override
  CoreShopifyProduct get product;
  @override
  CoreShopifyProductImage? get image;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyProductVariantImplCopyWith<_$CoreShopifyProductVariantImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CoreShopifyProduct _$CoreShopifyProductFromJson(Map<String, dynamic> json) {
  return _CoreShopifyProduct.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyProduct {
  String get id => throw _privateConstructorUsedError;
  String get vendor => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyProductCopyWith<CoreShopifyProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyProductCopyWith<$Res> {
  factory $CoreShopifyProductCopyWith(
          CoreShopifyProduct value, $Res Function(CoreShopifyProduct) then) =
      _$CoreShopifyProductCopyWithImpl<$Res, CoreShopifyProduct>;
  @useResult
  $Res call({String id, String vendor, String title});
}

/// @nodoc
class _$CoreShopifyProductCopyWithImpl<$Res, $Val extends CoreShopifyProduct>
    implements $CoreShopifyProductCopyWith<$Res> {
  _$CoreShopifyProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? vendor = null,
    Object? title = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      vendor: null == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyProductImplCopyWith<$Res>
    implements $CoreShopifyProductCopyWith<$Res> {
  factory _$$CoreShopifyProductImplCopyWith(_$CoreShopifyProductImpl value,
          $Res Function(_$CoreShopifyProductImpl) then) =
      __$$CoreShopifyProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String vendor, String title});
}

/// @nodoc
class __$$CoreShopifyProductImplCopyWithImpl<$Res>
    extends _$CoreShopifyProductCopyWithImpl<$Res, _$CoreShopifyProductImpl>
    implements _$$CoreShopifyProductImplCopyWith<$Res> {
  __$$CoreShopifyProductImplCopyWithImpl(_$CoreShopifyProductImpl _value,
      $Res Function(_$CoreShopifyProductImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? vendor = null,
    Object? title = null,
  }) {
    return _then(_$CoreShopifyProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      vendor: null == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyProductImpl implements _CoreShopifyProduct {
  _$CoreShopifyProductImpl(
      {required this.id, required this.vendor, required this.title});

  factory _$CoreShopifyProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyProductImplFromJson(json);

  @override
  final String id;
  @override
  final String vendor;
  @override
  final String title;

  @override
  String toString() {
    return 'CoreShopifyProduct(id: $id, vendor: $vendor, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.vendor, vendor) || other.vendor == vendor) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, vendor, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyProductImplCopyWith<_$CoreShopifyProductImpl> get copyWith =>
      __$$CoreShopifyProductImplCopyWithImpl<_$CoreShopifyProductImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyProductImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyProduct implements CoreShopifyProduct {
  factory _CoreShopifyProduct(
      {required final String id,
      required final String vendor,
      required final String title}) = _$CoreShopifyProductImpl;

  factory _CoreShopifyProduct.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyProductImpl.fromJson;

  @override
  String get id;
  @override
  String get vendor;
  @override
  String get title;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyProductImplCopyWith<_$CoreShopifyProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CoreShopifyProductImage _$CoreShopifyProductImageFromJson(
    Map<String, dynamic> json) {
  return _CoreShopifyProductImage.fromJson(json);
}

/// @nodoc
mixin _$CoreShopifyProductImage {
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CoreShopifyProductImageCopyWith<CoreShopifyProductImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreShopifyProductImageCopyWith<$Res> {
  factory $CoreShopifyProductImageCopyWith(CoreShopifyProductImage value,
          $Res Function(CoreShopifyProductImage) then) =
      _$CoreShopifyProductImageCopyWithImpl<$Res, CoreShopifyProductImage>;
  @useResult
  $Res call({String? url});
}

/// @nodoc
class _$CoreShopifyProductImageCopyWithImpl<$Res,
        $Val extends CoreShopifyProductImage>
    implements $CoreShopifyProductImageCopyWith<$Res> {
  _$CoreShopifyProductImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CoreShopifyProductImageImplCopyWith<$Res>
    implements $CoreShopifyProductImageCopyWith<$Res> {
  factory _$$CoreShopifyProductImageImplCopyWith(
          _$CoreShopifyProductImageImpl value,
          $Res Function(_$CoreShopifyProductImageImpl) then) =
      __$$CoreShopifyProductImageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? url});
}

/// @nodoc
class __$$CoreShopifyProductImageImplCopyWithImpl<$Res>
    extends _$CoreShopifyProductImageCopyWithImpl<$Res,
        _$CoreShopifyProductImageImpl>
    implements _$$CoreShopifyProductImageImplCopyWith<$Res> {
  __$$CoreShopifyProductImageImplCopyWithImpl(
      _$CoreShopifyProductImageImpl _value,
      $Res Function(_$CoreShopifyProductImageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_$CoreShopifyProductImageImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CoreShopifyProductImageImpl implements _CoreShopifyProductImage {
  _$CoreShopifyProductImageImpl({this.url});

  factory _$CoreShopifyProductImageImpl.fromJson(Map<String, dynamic> json) =>
      _$$CoreShopifyProductImageImplFromJson(json);

  @override
  final String? url;

  @override
  String toString() {
    return 'CoreShopifyProductImage(url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreShopifyProductImageImpl &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreShopifyProductImageImplCopyWith<_$CoreShopifyProductImageImpl>
      get copyWith => __$$CoreShopifyProductImageImplCopyWithImpl<
          _$CoreShopifyProductImageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CoreShopifyProductImageImplToJson(
      this,
    );
  }
}

abstract class _CoreShopifyProductImage implements CoreShopifyProductImage {
  factory _CoreShopifyProductImage({final String? url}) =
      _$CoreShopifyProductImageImpl;

  factory _CoreShopifyProductImage.fromJson(Map<String, dynamic> json) =
      _$CoreShopifyProductImageImpl.fromJson;

  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$CoreShopifyProductImageImplCopyWith<_$CoreShopifyProductImageImpl>
      get copyWith => throw _privateConstructorUsedError;
}
