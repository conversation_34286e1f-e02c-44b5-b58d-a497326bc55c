import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:shopify_flutter/models/src/order/line_items_order/line_items_order.dart';

part 'shopify_order.freezed.dart';
part 'shopify_order.g.dart';

@freezed
class CoreShopifyOrderResponse with _$CoreShopifyOrderResponse {
  /// The order constructor
  factory CoreShopifyOrderResponse({
    required List<CoreShopifyOrder> orders,
    CoreShopifyPageInfo? pageInfo,
  }) = _CoreShopifyOrderResponse;

  factory CoreShopifyOrderResponse.fromJson(<PERSON>son json) =>
      _$CoreShopifyOrderResponseFromJson(json);
}

@freezed
class CoreShopifyPageInfo with _$CoreShopifyPageInfo {
  /// The page info constructor
  factory CoreShopifyPageInfo({
    @JsonKey(name: 'endCursor') String? endCursor,
    @JsonKey(name: 'hasNextPage') bool? hasNextPage,
  }) = _CoreShopifyPageInfo;

  factory CoreShopifyPageInfo.fromJson(Json json) =>
      _$CoreShopifyPageInfoFromJson(json);
}

@freezed
class CoreShopifyOrder with _$CoreShopifyOrder {
  factory CoreShopifyOrder({
    required String id,
    required String email,
    @JsonKey(name: 'currencyCode') required String currencyCode,
    @JsonKey(name: 'lineItems')
    required CoreShopifyLineItems
        lineItems, // Changed to handle edges structure
    required String name,
    @JsonKey(name: 'processedAt') required String processedAt,
    // @JsonKey(name: 'shippingAddress') ShippingAddress? shippingAddress,
    // @JsonKey(name: 'billingAddress') ShippingAddress? billingAddress,
    @JsonKey(name: 'statusPageUrl') required String statusPageUrl,
    @JsonKey(name: 'subtotalPriceSet')
    required CoreShopifyPriceSet subtotalPriceSet,
    @JsonKey(name: 'totalPriceSet') required CoreShopifyPriceSet totalPriceSet,
    @JsonKey(name: 'totalShippingPriceSet')
    required CoreShopifyPriceSet totalShippingPriceSet,
    @JsonKey(name: 'totalTaxSet') required CoreShopifyPriceSet totalTaxSet,
    @JsonKey(name: 'displayFinancialStatus')
    required String displayFinancialStatus,
    @JsonKey(name: 'displayFulfillmentStatus')
    required String displayFulfillmentStatus,
    String? phone,
    CoreShopifyMetafield? metafield,
    required String cursor,
  }) = _CoreShopifyOrder;

  factory CoreShopifyOrder.fromJson(Json json) =>
      _$CoreShopifyOrderFromJson(json);
}

@freezed
class CoreShopifyLineItems with _$CoreShopifyLineItems {
  factory CoreShopifyLineItems({
    required List<CoreShopifyLineItemEdge> edges,
  }) = _CoreShopifyLineItems;

  factory CoreShopifyLineItems.fromJson(Json json) =>
      _$CoreShopifyLineItemsFromJson(json);
}

@freezed
class CoreShopifyLineItemEdge with _$CoreShopifyLineItemEdge {
  factory CoreShopifyLineItemEdge({
    required CoreShopifyLineItem node,
  }) = _CoreShopifyLineItemEdge;

  factory CoreShopifyLineItemEdge.fromJson(Json json) =>
      _$CoreShopifyLineItemEdgeFromJson(json);
}

@freezed
class CoreShopifyLineItem with _$CoreShopifyLineItem {
  factory CoreShopifyLineItem({
    required String title,
    required int quantity,
    @JsonKey(name: 'originalTotalSet')
    required CoreShopifyPriceSet originalTotalSet,
    @JsonKey(name: 'discountedTotalSet')
    required CoreShopifyPriceSet discountedTotalSet,
    CoreShopifyProductVariant? variant,
    CoreShopifyProductImage? image,
  }) = _CoreShopifyLineItem;

  factory CoreShopifyLineItem.fromJson(Json json) =>
      _$CoreShopifyLineItemFromJson(json);
}

@freezed
class CoreShopifyMetafield with _$CoreShopifyMetafield {
  factory CoreShopifyMetafield({
    String? namespace,
    String? key,
    String? value,
  }) = _CoreShopifyMetafield;

  factory CoreShopifyMetafield.fromJson(Json json) =>
      _$CoreShopifyMetafieldFromJson(json);
}

@freezed
class CoreShopifyPriceSet with _$CoreShopifyPriceSet {
  factory CoreShopifyPriceSet({
    @JsonKey(name: 'presentmentMoney')
    required CoreShopifyPresentmentMoney presentmentMoney,
  }) = _CoreShopifyPriceSet;

  factory CoreShopifyPriceSet.fromJson(Json json) =>
      _$CoreShopifyPriceSetFromJson(json);
}

@freezed
class CoreShopifyPresentmentMoney with _$CoreShopifyPresentmentMoney {
  factory CoreShopifyPresentmentMoney({
    required String amount,
    @JsonKey(name: 'currencyCode') required String currencyCode,
  }) = _CoreShopifyPresentmentMoney;

  factory CoreShopifyPresentmentMoney.fromJson(Json json) =>
      _$CoreShopifyPresentmentMoneyFromJson(json);
}

@freezed
class CoreShopifyProductVariant with _$CoreShopifyProductVariant {
  factory CoreShopifyProductVariant({
    required String title,
    required CoreShopifyProduct product,
    CoreShopifyProductImage? image,
  }) = _CoreShopifyProductVariant;

  factory CoreShopifyProductVariant.fromJson(Json json) =>
      _$CoreShopifyProductVariantFromJson(json);
}

@freezed
class CoreShopifyProduct with _$CoreShopifyProduct {
  factory CoreShopifyProduct({
    required String id,
    required String vendor,
    required String title,
  }) = _CoreShopifyProduct;

  factory CoreShopifyProduct.fromJson(Json json) =>
      _$CoreShopifyProductFromJson(json);
}

@freezed
class CoreShopifyProductImage with _$CoreShopifyProductImage {
  factory CoreShopifyProductImage({
    String? url,
  }) = _CoreShopifyProductImage;

  factory CoreShopifyProductImage.fromJson(Json json) =>
      _$CoreShopifyProductImageFromJson(json);
}
