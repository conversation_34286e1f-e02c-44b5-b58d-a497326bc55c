// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_review_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReviewRequestProductImpl _$$ReviewRequestProductImplFromJson(
        Map<String, dynamic> json) =>
    _$ReviewRequestProductImpl(
      id: (json['id'] as num).toInt(),
      customId: json['custom_id'] as String,
      name: json['name'] as String?,
      vendor: json['vendor'] as String?,
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$$ReviewRequestProductImplToJson(
        _$ReviewRequestProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'custom_id': instance.customId,
      'name': instance.name,
      'vendor': instance.vendor,
      'image_url': instance.imageUrl,
    };

_$ReviewRequestVariantImpl _$$ReviewRequestVariantImplFromJson(
        Map<String, dynamic> json) =>
    _$ReviewRequestVariantImpl(
      id: (json['id'] as num).toInt(),
      customId: json['custom_id'] as String,
      name: json['name'] as String?,
      imageUrl: json['image_url'] as String?,
      price: (json['price'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ReviewRequestVariantImplToJson(
        _$ReviewRequestVariantImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'custom_id': instance.customId,
      'name': instance.name,
      'image_url': instance.imageUrl,
      'price': instance.price,
    };

_$OrderReviewRequestImpl _$$OrderReviewRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderReviewRequestImpl(
      id: (json['id'] as num).toInt(),
      externalOrderId: json['external_order_id'] as String,
      product: ReviewRequestProduct.fromJson(
          json['product'] as Map<String, dynamic>),
      externalOrderName: json['external_order_name'] as String?,
      variants: (json['variants'] as List<dynamic>?)
          ?.map((e) => ReviewRequestVariant.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$OrderReviewRequestImplToJson(
        _$OrderReviewRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'external_order_id': instance.externalOrderId,
      'product': instance.product.toJson(),
      'external_order_name': instance.externalOrderName,
      'variants': instance.variants?.map((e) => e.toJson()).toList(),
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$OrderReviewRequestResponseImpl _$$OrderReviewRequestResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderReviewRequestResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => OrderReviewRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrderReviewRequestResponseImplToJson(
        _$OrderReviewRequestResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
