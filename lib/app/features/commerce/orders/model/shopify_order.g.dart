// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopify_order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CoreShopifyOrderResponseImpl _$$CoreShopifyOrderResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyOrderResponseImpl(
      orders: (json['orders'] as List<dynamic>)
          .map((e) => CoreShopifyOrder.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['page_info'] == null
          ? null
          : CoreShopifyPageInfo.fromJson(
              json['page_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CoreShopifyOrderResponseImplToJson(
        _$CoreShopifyOrderResponseImpl instance) =>
    <String, dynamic>{
      'orders': instance.orders.map((e) => e.toJson()).toList(),
      'page_info': instance.pageInfo?.toJson(),
    };

_$CoreShopifyPageInfoImpl _$$CoreShopifyPageInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyPageInfoImpl(
      endCursor: json['endCursor'] as String?,
      hasNextPage: json['hasNextPage'] as bool?,
    );

Map<String, dynamic> _$$CoreShopifyPageInfoImplToJson(
        _$CoreShopifyPageInfoImpl instance) =>
    <String, dynamic>{
      'endCursor': instance.endCursor,
      'hasNextPage': instance.hasNextPage,
    };

_$CoreShopifyOrderImpl _$$CoreShopifyOrderImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyOrderImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      currencyCode: json['currencyCode'] as String,
      lineItems: CoreShopifyLineItems.fromJson(
          json['lineItems'] as Map<String, dynamic>),
      name: json['name'] as String,
      processedAt: json['processedAt'] as String,
      statusPageUrl: json['statusPageUrl'] as String,
      subtotalPriceSet: CoreShopifyPriceSet.fromJson(
          json['subtotalPriceSet'] as Map<String, dynamic>),
      totalPriceSet: CoreShopifyPriceSet.fromJson(
          json['totalPriceSet'] as Map<String, dynamic>),
      totalShippingPriceSet: CoreShopifyPriceSet.fromJson(
          json['totalShippingPriceSet'] as Map<String, dynamic>),
      totalTaxSet: CoreShopifyPriceSet.fromJson(
          json['totalTaxSet'] as Map<String, dynamic>),
      displayFinancialStatus: json['displayFinancialStatus'] as String,
      displayFulfillmentStatus: json['displayFulfillmentStatus'] as String,
      phone: json['phone'] as String?,
      metafield: json['metafield'] == null
          ? null
          : CoreShopifyMetafield.fromJson(
              json['metafield'] as Map<String, dynamic>),
      cursor: json['cursor'] as String,
    );

Map<String, dynamic> _$$CoreShopifyOrderImplToJson(
        _$CoreShopifyOrderImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'currencyCode': instance.currencyCode,
      'lineItems': instance.lineItems.toJson(),
      'name': instance.name,
      'processedAt': instance.processedAt,
      'statusPageUrl': instance.statusPageUrl,
      'subtotalPriceSet': instance.subtotalPriceSet.toJson(),
      'totalPriceSet': instance.totalPriceSet.toJson(),
      'totalShippingPriceSet': instance.totalShippingPriceSet.toJson(),
      'totalTaxSet': instance.totalTaxSet.toJson(),
      'displayFinancialStatus': instance.displayFinancialStatus,
      'displayFulfillmentStatus': instance.displayFulfillmentStatus,
      'phone': instance.phone,
      'metafield': instance.metafield?.toJson(),
      'cursor': instance.cursor,
    };

_$CoreShopifyLineItemsImpl _$$CoreShopifyLineItemsImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyLineItemsImpl(
      edges: (json['edges'] as List<dynamic>)
          .map((e) =>
              CoreShopifyLineItemEdge.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CoreShopifyLineItemsImplToJson(
        _$CoreShopifyLineItemsImpl instance) =>
    <String, dynamic>{
      'edges': instance.edges.map((e) => e.toJson()).toList(),
    };

_$CoreShopifyLineItemEdgeImpl _$$CoreShopifyLineItemEdgeImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyLineItemEdgeImpl(
      node: CoreShopifyLineItem.fromJson(json['node'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CoreShopifyLineItemEdgeImplToJson(
        _$CoreShopifyLineItemEdgeImpl instance) =>
    <String, dynamic>{
      'node': instance.node.toJson(),
    };

_$CoreShopifyLineItemImpl _$$CoreShopifyLineItemImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyLineItemImpl(
      title: json['title'] as String,
      quantity: (json['quantity'] as num).toInt(),
      originalTotalSet: CoreShopifyPriceSet.fromJson(
          json['originalTotalSet'] as Map<String, dynamic>),
      discountedTotalSet: CoreShopifyPriceSet.fromJson(
          json['discountedTotalSet'] as Map<String, dynamic>),
      variant: json['variant'] == null
          ? null
          : CoreShopifyProductVariant.fromJson(
              json['variant'] as Map<String, dynamic>),
      image: json['image'] == null
          ? null
          : CoreShopifyProductImage.fromJson(
              json['image'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CoreShopifyLineItemImplToJson(
        _$CoreShopifyLineItemImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'quantity': instance.quantity,
      'originalTotalSet': instance.originalTotalSet.toJson(),
      'discountedTotalSet': instance.discountedTotalSet.toJson(),
      'variant': instance.variant?.toJson(),
      'image': instance.image?.toJson(),
    };

_$CoreShopifyMetafieldImpl _$$CoreShopifyMetafieldImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyMetafieldImpl(
      namespace: json['namespace'] as String?,
      key: json['key'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$$CoreShopifyMetafieldImplToJson(
        _$CoreShopifyMetafieldImpl instance) =>
    <String, dynamic>{
      'namespace': instance.namespace,
      'key': instance.key,
      'value': instance.value,
    };

_$CoreShopifyPriceSetImpl _$$CoreShopifyPriceSetImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyPriceSetImpl(
      presentmentMoney: CoreShopifyPresentmentMoney.fromJson(
          json['presentmentMoney'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CoreShopifyPriceSetImplToJson(
        _$CoreShopifyPriceSetImpl instance) =>
    <String, dynamic>{
      'presentmentMoney': instance.presentmentMoney.toJson(),
    };

_$CoreShopifyPresentmentMoneyImpl _$$CoreShopifyPresentmentMoneyImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyPresentmentMoneyImpl(
      amount: json['amount'] as String,
      currencyCode: json['currencyCode'] as String,
    );

Map<String, dynamic> _$$CoreShopifyPresentmentMoneyImplToJson(
        _$CoreShopifyPresentmentMoneyImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'currencyCode': instance.currencyCode,
    };

_$CoreShopifyProductVariantImpl _$$CoreShopifyProductVariantImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyProductVariantImpl(
      title: json['title'] as String,
      product:
          CoreShopifyProduct.fromJson(json['product'] as Map<String, dynamic>),
      image: json['image'] == null
          ? null
          : CoreShopifyProductImage.fromJson(
              json['image'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CoreShopifyProductVariantImplToJson(
        _$CoreShopifyProductVariantImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'product': instance.product.toJson(),
      'image': instance.image?.toJson(),
    };

_$CoreShopifyProductImpl _$$CoreShopifyProductImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyProductImpl(
      id: json['id'] as String,
      vendor: json['vendor'] as String,
      title: json['title'] as String,
    );

Map<String, dynamic> _$$CoreShopifyProductImplToJson(
        _$CoreShopifyProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'vendor': instance.vendor,
      'title': instance.title,
    };

_$CoreShopifyProductImageImpl _$$CoreShopifyProductImageImplFromJson(
        Map<String, dynamic> json) =>
    _$CoreShopifyProductImageImpl(
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$CoreShopifyProductImageImplToJson(
        _$CoreShopifyProductImageImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
    };
