// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_review_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReviewRequestProduct _$ReviewRequestProductFromJson(Map<String, dynamic> json) {
  return _ReviewRequestProduct.fromJson(json);
}

/// @nodoc
mixin _$ReviewRequestProduct {
  int get id => throw _privateConstructorUsedError;
  String get customId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get vendor => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReviewRequestProductCopyWith<ReviewRequestProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewRequestProductCopyWith<$Res> {
  factory $ReviewRequestProductCopyWith(ReviewRequestProduct value,
          $Res Function(ReviewRequestProduct) then) =
      _$ReviewRequestProductCopyWithImpl<$Res, ReviewRequestProduct>;
  @useResult
  $Res call(
      {int id,
      String customId,
      String? name,
      String? vendor,
      String? imageUrl});
}

/// @nodoc
class _$ReviewRequestProductCopyWithImpl<$Res,
        $Val extends ReviewRequestProduct>
    implements $ReviewRequestProductCopyWith<$Res> {
  _$ReviewRequestProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = freezed,
    Object? vendor = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReviewRequestProductImplCopyWith<$Res>
    implements $ReviewRequestProductCopyWith<$Res> {
  factory _$$ReviewRequestProductImplCopyWith(_$ReviewRequestProductImpl value,
          $Res Function(_$ReviewRequestProductImpl) then) =
      __$$ReviewRequestProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String customId,
      String? name,
      String? vendor,
      String? imageUrl});
}

/// @nodoc
class __$$ReviewRequestProductImplCopyWithImpl<$Res>
    extends _$ReviewRequestProductCopyWithImpl<$Res, _$ReviewRequestProductImpl>
    implements _$$ReviewRequestProductImplCopyWith<$Res> {
  __$$ReviewRequestProductImplCopyWithImpl(_$ReviewRequestProductImpl _value,
      $Res Function(_$ReviewRequestProductImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = freezed,
    Object? vendor = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_$ReviewRequestProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewRequestProductImpl implements _ReviewRequestProduct {
  _$ReviewRequestProductImpl(
      {required this.id,
      required this.customId,
      this.name,
      this.vendor,
      this.imageUrl});

  factory _$ReviewRequestProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewRequestProductImplFromJson(json);

  @override
  final int id;
  @override
  final String customId;
  @override
  final String? name;
  @override
  final String? vendor;
  @override
  final String? imageUrl;

  @override
  String toString() {
    return 'ReviewRequestProduct(id: $id, customId: $customId, name: $name, vendor: $vendor, imageUrl: $imageUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewRequestProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customId, customId) ||
                other.customId == customId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.vendor, vendor) || other.vendor == vendor) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, customId, name, vendor, imageUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewRequestProductImplCopyWith<_$ReviewRequestProductImpl>
      get copyWith =>
          __$$ReviewRequestProductImplCopyWithImpl<_$ReviewRequestProductImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewRequestProductImplToJson(
      this,
    );
  }
}

abstract class _ReviewRequestProduct implements ReviewRequestProduct {
  factory _ReviewRequestProduct(
      {required final int id,
      required final String customId,
      final String? name,
      final String? vendor,
      final String? imageUrl}) = _$ReviewRequestProductImpl;

  factory _ReviewRequestProduct.fromJson(Map<String, dynamic> json) =
      _$ReviewRequestProductImpl.fromJson;

  @override
  int get id;
  @override
  String get customId;
  @override
  String? get name;
  @override
  String? get vendor;
  @override
  String? get imageUrl;
  @override
  @JsonKey(ignore: true)
  _$$ReviewRequestProductImplCopyWith<_$ReviewRequestProductImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ReviewRequestVariant _$ReviewRequestVariantFromJson(Map<String, dynamic> json) {
  return _ReviewRequestVariant.fromJson(json);
}

/// @nodoc
mixin _$ReviewRequestVariant {
  int get id => throw _privateConstructorUsedError;
  String get customId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReviewRequestVariantCopyWith<ReviewRequestVariant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewRequestVariantCopyWith<$Res> {
  factory $ReviewRequestVariantCopyWith(ReviewRequestVariant value,
          $Res Function(ReviewRequestVariant) then) =
      _$ReviewRequestVariantCopyWithImpl<$Res, ReviewRequestVariant>;
  @useResult
  $Res call(
      {int id, String customId, String? name, String? imageUrl, int? price});
}

/// @nodoc
class _$ReviewRequestVariantCopyWithImpl<$Res,
        $Val extends ReviewRequestVariant>
    implements $ReviewRequestVariantCopyWith<$Res> {
  _$ReviewRequestVariantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? price = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReviewRequestVariantImplCopyWith<$Res>
    implements $ReviewRequestVariantCopyWith<$Res> {
  factory _$$ReviewRequestVariantImplCopyWith(_$ReviewRequestVariantImpl value,
          $Res Function(_$ReviewRequestVariantImpl) then) =
      __$$ReviewRequestVariantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id, String customId, String? name, String? imageUrl, int? price});
}

/// @nodoc
class __$$ReviewRequestVariantImplCopyWithImpl<$Res>
    extends _$ReviewRequestVariantCopyWithImpl<$Res, _$ReviewRequestVariantImpl>
    implements _$$ReviewRequestVariantImplCopyWith<$Res> {
  __$$ReviewRequestVariantImplCopyWithImpl(_$ReviewRequestVariantImpl _value,
      $Res Function(_$ReviewRequestVariantImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? price = freezed,
  }) {
    return _then(_$ReviewRequestVariantImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewRequestVariantImpl implements _ReviewRequestVariant {
  _$ReviewRequestVariantImpl(
      {required this.id,
      required this.customId,
      this.name,
      this.imageUrl,
      this.price});

  factory _$ReviewRequestVariantImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewRequestVariantImplFromJson(json);

  @override
  final int id;
  @override
  final String customId;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final int? price;

  @override
  String toString() {
    return 'ReviewRequestVariant(id: $id, customId: $customId, name: $name, imageUrl: $imageUrl, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewRequestVariantImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customId, customId) ||
                other.customId == customId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, customId, name, imageUrl, price);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewRequestVariantImplCopyWith<_$ReviewRequestVariantImpl>
      get copyWith =>
          __$$ReviewRequestVariantImplCopyWithImpl<_$ReviewRequestVariantImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewRequestVariantImplToJson(
      this,
    );
  }
}

abstract class _ReviewRequestVariant implements ReviewRequestVariant {
  factory _ReviewRequestVariant(
      {required final int id,
      required final String customId,
      final String? name,
      final String? imageUrl,
      final int? price}) = _$ReviewRequestVariantImpl;

  factory _ReviewRequestVariant.fromJson(Map<String, dynamic> json) =
      _$ReviewRequestVariantImpl.fromJson;

  @override
  int get id;
  @override
  String get customId;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  int? get price;
  @override
  @JsonKey(ignore: true)
  _$$ReviewRequestVariantImplCopyWith<_$ReviewRequestVariantImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OrderReviewRequest _$OrderReviewRequestFromJson(Map<String, dynamic> json) {
  return _OrderReviewRequest.fromJson(json);
}

/// @nodoc
mixin _$OrderReviewRequest {
  int get id => throw _privateConstructorUsedError;
  String get externalOrderId => throw _privateConstructorUsedError;
  ReviewRequestProduct get product => throw _privateConstructorUsedError;
  String? get externalOrderName => throw _privateConstructorUsedError;
  List<ReviewRequestVariant>? get variants =>
      throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderReviewRequestCopyWith<OrderReviewRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderReviewRequestCopyWith<$Res> {
  factory $OrderReviewRequestCopyWith(
          OrderReviewRequest value, $Res Function(OrderReviewRequest) then) =
      _$OrderReviewRequestCopyWithImpl<$Res, OrderReviewRequest>;
  @useResult
  $Res call(
      {int id,
      String externalOrderId,
      ReviewRequestProduct product,
      String? externalOrderName,
      List<ReviewRequestVariant>? variants,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ReviewRequestProductCopyWith<$Res> get product;
}

/// @nodoc
class _$OrderReviewRequestCopyWithImpl<$Res, $Val extends OrderReviewRequest>
    implements $OrderReviewRequestCopyWith<$Res> {
  _$OrderReviewRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? externalOrderId = null,
    Object? product = null,
    Object? externalOrderName = freezed,
    Object? variants = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      externalOrderId: null == externalOrderId
          ? _value.externalOrderId
          : externalOrderId // ignore: cast_nullable_to_non_nullable
              as String,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ReviewRequestProduct,
      externalOrderName: freezed == externalOrderName
          ? _value.externalOrderName
          : externalOrderName // ignore: cast_nullable_to_non_nullable
              as String?,
      variants: freezed == variants
          ? _value.variants
          : variants // ignore: cast_nullable_to_non_nullable
              as List<ReviewRequestVariant>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ReviewRequestProductCopyWith<$Res> get product {
    return $ReviewRequestProductCopyWith<$Res>(_value.product, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderReviewRequestImplCopyWith<$Res>
    implements $OrderReviewRequestCopyWith<$Res> {
  factory _$$OrderReviewRequestImplCopyWith(_$OrderReviewRequestImpl value,
          $Res Function(_$OrderReviewRequestImpl) then) =
      __$$OrderReviewRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String externalOrderId,
      ReviewRequestProduct product,
      String? externalOrderName,
      List<ReviewRequestVariant>? variants,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ReviewRequestProductCopyWith<$Res> get product;
}

/// @nodoc
class __$$OrderReviewRequestImplCopyWithImpl<$Res>
    extends _$OrderReviewRequestCopyWithImpl<$Res, _$OrderReviewRequestImpl>
    implements _$$OrderReviewRequestImplCopyWith<$Res> {
  __$$OrderReviewRequestImplCopyWithImpl(_$OrderReviewRequestImpl _value,
      $Res Function(_$OrderReviewRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? externalOrderId = null,
    Object? product = null,
    Object? externalOrderName = freezed,
    Object? variants = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$OrderReviewRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      externalOrderId: null == externalOrderId
          ? _value.externalOrderId
          : externalOrderId // ignore: cast_nullable_to_non_nullable
              as String,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ReviewRequestProduct,
      externalOrderName: freezed == externalOrderName
          ? _value.externalOrderName
          : externalOrderName // ignore: cast_nullable_to_non_nullable
              as String?,
      variants: freezed == variants
          ? _value._variants
          : variants // ignore: cast_nullable_to_non_nullable
              as List<ReviewRequestVariant>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$OrderReviewRequestImpl implements _OrderReviewRequest {
  _$OrderReviewRequestImpl(
      {required this.id,
      required this.externalOrderId,
      required this.product,
      this.externalOrderName,
      final List<ReviewRequestVariant>? variants,
      this.createdAt,
      this.updatedAt})
      : _variants = variants;

  factory _$OrderReviewRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderReviewRequestImplFromJson(json);

  @override
  final int id;
  @override
  final String externalOrderId;
  @override
  final ReviewRequestProduct product;
  @override
  final String? externalOrderName;
  final List<ReviewRequestVariant>? _variants;
  @override
  List<ReviewRequestVariant>? get variants {
    final value = _variants;
    if (value == null) return null;
    if (_variants is EqualUnmodifiableListView) return _variants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'OrderReviewRequest(id: $id, externalOrderId: $externalOrderId, product: $product, externalOrderName: $externalOrderName, variants: $variants, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderReviewRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.externalOrderId, externalOrderId) ||
                other.externalOrderId == externalOrderId) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.externalOrderName, externalOrderName) ||
                other.externalOrderName == externalOrderName) &&
            const DeepCollectionEquality().equals(other._variants, _variants) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      externalOrderId,
      product,
      externalOrderName,
      const DeepCollectionEquality().hash(_variants),
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderReviewRequestImplCopyWith<_$OrderReviewRequestImpl> get copyWith =>
      __$$OrderReviewRequestImplCopyWithImpl<_$OrderReviewRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderReviewRequestImplToJson(
      this,
    );
  }
}

abstract class _OrderReviewRequest implements OrderReviewRequest {
  factory _OrderReviewRequest(
      {required final int id,
      required final String externalOrderId,
      required final ReviewRequestProduct product,
      final String? externalOrderName,
      final List<ReviewRequestVariant>? variants,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$OrderReviewRequestImpl;

  factory _OrderReviewRequest.fromJson(Map<String, dynamic> json) =
      _$OrderReviewRequestImpl.fromJson;

  @override
  int get id;
  @override
  String get externalOrderId;
  @override
  ReviewRequestProduct get product;
  @override
  String? get externalOrderName;
  @override
  List<ReviewRequestVariant>? get variants;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$OrderReviewRequestImplCopyWith<_$OrderReviewRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderReviewRequestResponse _$OrderReviewRequestResponseFromJson(
    Map<String, dynamic> json) {
  return _OrderReviewRequestResponse.fromJson(json);
}

/// @nodoc
mixin _$OrderReviewRequestResponse {
  List<OrderReviewRequest> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderReviewRequestResponseCopyWith<OrderReviewRequestResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderReviewRequestResponseCopyWith<$Res> {
  factory $OrderReviewRequestResponseCopyWith(OrderReviewRequestResponse value,
          $Res Function(OrderReviewRequestResponse) then) =
      _$OrderReviewRequestResponseCopyWithImpl<$Res,
          OrderReviewRequestResponse>;
  @useResult
  $Res call({List<OrderReviewRequest> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$OrderReviewRequestResponseCopyWithImpl<$Res,
        $Val extends OrderReviewRequestResponse>
    implements $OrderReviewRequestResponseCopyWith<$Res> {
  _$OrderReviewRequestResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<OrderReviewRequest>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderReviewRequestResponseImplCopyWith<$Res>
    implements $OrderReviewRequestResponseCopyWith<$Res> {
  factory _$$OrderReviewRequestResponseImplCopyWith(
          _$OrderReviewRequestResponseImpl value,
          $Res Function(_$OrderReviewRequestResponseImpl) then) =
      __$$OrderReviewRequestResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OrderReviewRequest> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$OrderReviewRequestResponseImplCopyWithImpl<$Res>
    extends _$OrderReviewRequestResponseCopyWithImpl<$Res,
        _$OrderReviewRequestResponseImpl>
    implements _$$OrderReviewRequestResponseImplCopyWith<$Res> {
  __$$OrderReviewRequestResponseImplCopyWithImpl(
      _$OrderReviewRequestResponseImpl _value,
      $Res Function(_$OrderReviewRequestResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$OrderReviewRequestResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<OrderReviewRequest>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderReviewRequestResponseImpl implements _OrderReviewRequestResponse {
  _$OrderReviewRequestResponseImpl(
      {required final List<OrderReviewRequest> data, required this.meta})
      : _data = data;

  factory _$OrderReviewRequestResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$OrderReviewRequestResponseImplFromJson(json);

  final List<OrderReviewRequest> _data;
  @override
  List<OrderReviewRequest> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'OrderReviewRequestResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderReviewRequestResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderReviewRequestResponseImplCopyWith<_$OrderReviewRequestResponseImpl>
      get copyWith => __$$OrderReviewRequestResponseImplCopyWithImpl<
          _$OrderReviewRequestResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderReviewRequestResponseImplToJson(
      this,
    );
  }
}

abstract class _OrderReviewRequestResponse
    implements OrderReviewRequestResponse {
  factory _OrderReviewRequestResponse(
      {required final List<OrderReviewRequest> data,
      required final Pagination meta}) = _$OrderReviewRequestResponseImpl;

  factory _OrderReviewRequestResponse.fromJson(Map<String, dynamic> json) =
      _$OrderReviewRequestResponseImpl.fromJson;

  @override
  List<OrderReviewRequest> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$OrderReviewRequestResponseImplCopyWith<_$OrderReviewRequestResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
