import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:shopify_flutter/models/src/order/line_items_order/line_items_order.dart';

part 'order_review_request.freezed.dart';
part 'order_review_request.g.dart';

@freezed
class ReviewRequestProduct with _$ReviewRequestProduct {
  factory ReviewRequestProduct({
    required int id,
    required String customId,
    String? name,
    String? vendor,
    String? imageUrl,
  }) = _ReviewRequestProduct;

  factory ReviewRequestProduct.fromJson(Json json) =>
      _$ReviewRequestProductFromJson(json);
}

@freezed
class ReviewRequestVariant with _$ReviewRequestVariant {
  factory ReviewRequestVariant({
    required int id,
    required String customId,
    String? name,
    String? imageUrl,
    int? price,
  }) = _ReviewRequestVariant;

  factory ReviewRequestVariant.from<PERSON>son(Json json) =>
      _$ReviewRequestVariantFromJson(json);
}

@freezed
class OrderReviewRequest with _$OrderReviewRequest {
  @CustomDateTimeConverter()
  factory OrderReviewRequest({
    required int id,
    required String externalOrderId,
    required ReviewRequestProduct product,
    String? externalOrderName,
    List<ReviewRequestVariant>? variants,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _OrderReviewRequest;

  factory OrderReviewRequest.fromJson(Json json) =>
      _$OrderReviewRequestFromJson(json);
}

@freezed
class OrderReviewRequestResponse with _$OrderReviewRequestResponse {
  factory OrderReviewRequestResponse({
    required List<OrderReviewRequest> data,
    required Pagination meta,
  }) = _OrderReviewRequestResponse;

  factory OrderReviewRequestResponse.fromJson(Json json) =>
      _$OrderReviewRequestResponseFromJson(json);
}
