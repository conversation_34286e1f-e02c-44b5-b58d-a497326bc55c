import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PurchaseHistoryItemTile extends HookConsumerWidget {
  const PurchaseHistoryItemTile({
    super.key,
    required this.lineItemOrder,
  });

  final CoreShopifyLineItem lineItemOrder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productVariant = lineItemOrder.variant;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      horizontalTitleGap: 0,
      title: SizedBox(
        height: 120,
        child: Row(
          children: [
            AspectRatio(
              aspectRatio: 1,
              child: CachedNetworkImage(
                imageUrl: lineItemOrder.image?.url ?? productVariant?.image?.url ?? '',
                imageBuilder: (context, imageProvider) {
                  return DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
                errorWidget: (context, url, error) =>
                    const Icon(CustomIcon.error),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productVariant?.product.title ?? '',
                    style: textTheme(context).bodyLarge!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(32),
                      color: Colors.grey.shade200,
                    ),
                    child: Text(
                      productVariant?.title ?? '',
                      style: Theme.of(context)
                          .textTheme
                          .labelMedium!
                          .copyWith(color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${double.parse(lineItemOrder.discountedTotalSet.presentmentMoney.amount).toStringAsFixed(2)}',
                    style: textTheme(context).bodyLarge!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
