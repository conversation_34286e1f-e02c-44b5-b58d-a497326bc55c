import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:gomama/app/features/commerce/orders/widget/confirm_mark_as_receive.dart';
import 'package:gomama/app/features/commerce/orders/widget/purchase_history_item_tile.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

enum ItemStatus {
  toPay,
  toShip,
  toReceive,
  completed,
  refunded,
}

class PurchaseHistoryItemsGroup extends HookConsumerWidget {
  const PurchaseHistoryItemsGroup({
    super.key,
    required this.order,
    required this.variant,
  });

  final CoreShopifyOrder order;
  final ItemStatus variant;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String _getStatusString() {
      switch (variant) {
        case ItemStatus.toPay:
          return 'To Pay';
        case ItemStatus.toShip:
          return 'To Ship';
        case ItemStatus.toReceive:
          return 'To Receive';
        case ItemStatus.completed:
          return 'Completed';
        case ItemStatus.refunded:
          return 'Refunded';
      }
    }

    // group lineItemOrders by vendor
    final groupedLineItemOrders =
        order.lineItems.edges.fold<Map<String, List<CoreShopifyLineItem>>>(
      {},
      (map, lineItemOrder) {
        final key = lineItemOrder.node.variant?.product.vendor ?? '';
        map[key] = [...(map[key] ?? []), lineItemOrder.node];
        return map;
      },
    );

    return GestureDetector(
      onTap: () {
        final parts = order.id.split('/');
        final customId = parts.isNotEmpty ? parts.last : '';
        OrderDetailsRoute(
          customId,
          isCompleted: variant == ItemStatus.completed,
        ).push(context);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // order name
            Row(
              children: [
                const Icon(Icons.receipt_long),
                const SizedBox(width: 6),
                Text(
                  order.name,
                  style: textTheme(context).labelLarge!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
                const Spacer(),
                Text(
                  _getStatusString(),
                  style: textTheme(context).labelMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
              ],
            ),
            Divider(
              color: Colors.grey.shade300,
              height: 24,
            ),
            // store groups
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                // map by vendor/collection
                final vendor = groupedLineItemOrders.keys.elementAt(index);
                final lineItemOrders = groupedLineItemOrders[vendor] ?? [];

                return _OrderVendorGroup(
                  vendor: vendor,
                  lineItemOrders: lineItemOrders,
                );
              },
              separatorBuilder: (context, idnex) => Divider(
                color: Colors.grey.shade300,
                height: 24,
              ),
              itemCount: groupedLineItemOrders.length,
            ),

            if (variant == ItemStatus.toReceive)
              FilledButton(
                style: FilledButton.styleFrom(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                onPressed: () {
                  confirmMarkAsReceived(
                    context: context,
                    ref: ref,
                    orderId: order.id,
                  );
                },
                child: const Text('Order Received'),
              ),
          ],
        ),
      ),
    );
  }
}

class _OrderVendorGroup extends StatelessWidget {
  const _OrderVendorGroup({
    required this.vendor,
    required this.lineItemOrders,
  });

  final String vendor;
  final List<CoreShopifyLineItem> lineItemOrders;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            const Icon(Icons.store),
            const SizedBox(width: 6),
            Text(
              vendor,
              style: textTheme(context).bodyMedium!.copyWith(
                    color: CustomColors.primary,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // items row
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final lineItemOrder = lineItemOrders[index];

            return PurchaseHistoryItemTile(lineItemOrder: lineItemOrder);
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 12);
          },
          itemCount: lineItemOrders.length,
        ),
      ],
    );
  }
}
