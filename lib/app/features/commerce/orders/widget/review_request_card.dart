import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/orders/model/order_review_request.dart';
import 'package:gomama/app/features/commerce/provider/shopify_token_provider.dart';
import 'package:gomama/app/features/commerce/review/widget/product_review_dialog.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ReviewRequestCard extends HookConsumerWidget {
  const ReviewRequestCard({
    super.key,
    required this.reviewRequest,
  });

  final OrderReviewRequest reviewRequest;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        OrderDetailsRoute(
          reviewRequest.externalOrderId,
          isCompleted: true,
        ).push(context);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // order
            Row(
              children: [
                const Icon(Icons.receipt_long),
                const SizedBox(width: 6),
                Text(
                  reviewRequest.externalOrderName ?? '-',
                  style: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
                const Spacer(),
                Text(
                  'To Review',
                  style: textTheme(context).labelMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // vendor
            Row(
              children: [
                const Icon(Icons.store),
                const SizedBox(width: 6),
                Text(
                  reviewRequest.product.vendor ?? '-',
                  style: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
                const Spacer(),
              ],
            ),
            Divider(
              color: Colors.grey.shade300,
              height: 24,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  reviewRequest.product.name ?? '-',
                  textAlign: TextAlign.left,
                  style: textTheme(context).bodyLarge!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
                const SizedBox(height: 8),
                if (reviewRequest.variants?.isNotEmpty == true)
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return _VariantTile(reviewRequest.variants![index]);
                    },
                    separatorBuilder: (context, index) {
                      return const SizedBox(height: 12);
                    },
                    itemCount: reviewRequest.variants!.length,
                  ),
              ],
            ),
            // review button
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: () async {
                final user = ref.watch(authControllerProvider).requireValue;
                final shopifyProfile = user.shopifyProfile;

                try {
                  if (shopifyProfile == null) {
                    throw Exception('Shopify profile not found');
                  }

                  await getShopifyAccessToken(
                    email: shopifyProfile.email,
                    password: shopifyProfile.password!,
                  );

                  if (context.mounted) {
                    productReviewDialog(
                      context: context,
                      productId: reviewRequest.product.customId,
                      orderId: reviewRequest.externalOrderId,
                    );
                  }
                } catch (error) {
                  Groveman.error('review button', error: error);
                  return;
                }
              },
              child: const Text('Review'),
            ),
          ],
        ),
      ),
    );
  }
}

class _VariantTile extends ConsumerWidget {
  const _VariantTile(this.productVariant);

  final ReviewRequestVariant productVariant;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      horizontalTitleGap: 0,
      title: SizedBox(
        height: 120,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 1,
              child: CachedNetworkImage(
                imageUrl: productVariant.imageUrl ?? '',
                imageBuilder: (context, imageProvider) {
                  return DecoratedBox(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
                errorWidget: (context, url, error) =>
                    const Icon(CustomIcon.error),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productVariant.name ?? '',
                    style: textTheme(context).bodyLarge!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                  Text(
                    '\$${(productVariant.price ?? 0).toStringAsFixed(2)}',
                    textAlign: TextAlign.right,
                    style: textTheme(context).bodyLarge!.copyWith(
                          color: CustomColors.primary,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
