import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/orders/provider/shopify_orders_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

void confirmMarkAsReceived({
  required BuildContext context,
  required WidgetRef ref,
  required String orderId,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return _DialogContent(orderId);
    },
  );
}

class _DialogContent extends HookConsumerWidget {
  const _DialogContent(this.orderId);

  final String orderId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _isBusy = useState<bool>(false);

    return AlertDialog(
      title: const Text('Confirm Mark Order as Receive?'),
      content: const Text(
        'Are you sure you want to mark this order as received?',
      ),
      actions: [
        OutlinedButton(
          style: OutlinedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          onPressed: () {
            context.pop();
          },
          child: const Text('Back'),
        ),
        FilledButton(
          style: FilledButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          onPressed: () async {
            _isBusy.value = true;

            try {
              final customOrderId = orderId.split('/').last;
              final success = await ref.read(
                markOrderAsReceivedProvider(customOrderId).future,
              );

              if (success == false) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Something went wrong, please try again later.',
                      ),
                    ),
                  );
                }
              }
            } catch (error) {
              Groveman.error('orderReceivedButton', error: error);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      error.toString(),
                    ),
                  ),
                );
              }
            } finally {
              _isBusy.value = false;
              // refresh orders 'to receive' & 'to review'
              ref
                ..invalidate(ordersToReceiveControllerProvider)
                // TODO: 'to review' not refreshed
                ..invalidate(ordersToReviewControllerProvider);

              if (context.mounted) {
                context.pop();
              }
            }
          },
          child: Text(
            _isBusy.value == true ? 'Loading...' : 'Confirm',
          ),
        ),
      ],
    );
  }
}
