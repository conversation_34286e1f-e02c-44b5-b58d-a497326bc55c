import 'package:flutter/material.dart';
import 'package:gomama/app/features/commerce/orders/provider/shopify_orders_providers.dart';
import 'package:gomama/app/features/commerce/orders/widget/review_request_card.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OrderReviewRequestList extends HookConsumerWidget {
  const OrderReviewRequestList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncResponse = ref.watch(ordersToReviewControllerProvider);
    final isLoading = ref.watch(ordersLoadingprovider);

    return asyncResponse.when(
      data: (response) {
        if (response == null) {
          return const SizedBox.shrink();
        }

        final reviewRequests = response.data;
        final pagination = response.meta;

        return ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          itemCount: reviewRequests.length +
              (pagination.currentPage < pagination.lastPage ? 1 : 0),
          itemBuilder: (context, index) {
            // load more button
            if (index == reviewRequests.length &&
                pagination.currentPage < pagination.lastPage) {
              return isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : FilledButton(
                      style: FilledButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      onPressed: () {
                        ref
                            .read(ordersToReviewControllerProvider.notifier)
                            .fetchMore();
                      },
                      child: const Text('Load more'),
                    );
            }

            return ReviewRequestCard(reviewRequest: reviewRequests[index]);
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 16);
          },
        );
      },
      error: (error, stackTrace) {
        return const SizedBox.shrink();
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}
