import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/orders/model/order_review_request.dart';
import 'package:gomama/app/features/commerce/orders/model/shopify_order.dart';
import 'package:gomama/app/features/commerce/orders/queries/get_order_on_query_query.dart';
import 'package:gomama/app/features/commerce/orders/repository/shopify_orders_repository.dart';
import 'package:gomama/app/features/commerce/provider/shopify_token_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'shopify_orders_providers.g.dart';

final ordersLoadingprovider = StateProvider.autoDispose<bool>((ref) => false);

@riverpod
Future<Order?> orderById(OrderByIdRef ref, String id) async {
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return null;
  }

  try {
    final accessToken = await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );

    final response = await ShopifyCustom.instance.customQuery(
      gqlQuery: getOrderOnQueryQuery,
      variables: {
        'accessToken': accessToken,
        'query': 'id:$id',
      },
    );

    // NOTE: Order.fromGraphJson unable to direcly parse single object
    // therefore fetch list then process into single order
    final orderEdges = response?['customer']?['orders'];
    if (orderEdges == null) return null;

    final orders = Orders.fromGraphJson(orderEdges as Map<String, dynamic>);
    if (orders.orderList.isEmpty == true) return null;
    final order = orders.orderList.first;
    return order;
  } catch (error, stackTrace) {
    Groveman.error('orderById', error: error, stackTrace: stackTrace);
    return null;
  }
}

/// NOTE: retrieve orders from core backend
@riverpod
Future<CoreShopifyOrderResponse?> orders(OrdersRef ref) async {
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return null;
  }

  try {
    return ref.read(shopifyOrdersRepositoryProvider).fetchOrders();
  } catch (error) {
    Groveman.error('orders', error: error);
    return null;
  }
}

@riverpod
class OrdersToPayController extends _$OrdersToPayController {
  @override
  Future<CoreShopifyOrderResponse?> build() async {
    try {
      final response = ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PENDING'],
        fulfillmentStatus: ['UNFULFILLED'],
      );
      return response;
    } catch (error) {
      Groveman.error('ordersToShip', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null) return;

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PENDING'],
        fulfillmentStatus: ['UNFULFILLED'],
        cursor: prevOrders.pageInfo?.endCursor,
      );

      final updatedOrders = CoreShopifyOrderResponse(
        orders: [...prevOrders.orders, ...nextOrders.orders],
        pageInfo: nextOrders.pageInfo,
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersToPay', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
class OrdersToShipController extends _$OrdersToShipController {
  @override
  Future<CoreShopifyOrderResponse?> build() async {
    try {
      final response = ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['UNFULFILLED'],
      );
      return response;
    } catch (error) {
      Groveman.error('ordersToShip', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null) return;

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['UNFULFILLED'],
        cursor: prevOrders.pageInfo?.endCursor,
      );

      final updatedOrders = CoreShopifyOrderResponse(
        orders: [...prevOrders.orders, ...nextOrders.orders],
        pageInfo: nextOrders.pageInfo,
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersToShip', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
class OrdersToReceiveController extends _$OrdersToReceiveController {
  @override
  Future<CoreShopifyOrderResponse?> build() async {
    try {
      final orderResponse =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['FULFILLED,SHIPPED'],
      );
      // filter out orders that are already received
      final filteredOrders = orderResponse.orders
          .where((order) => order.metafield?.value != 'true')
          .toList();
      final updatedResponse = CoreShopifyOrderResponse(
        orders: filteredOrders,
        pageInfo: orderResponse.pageInfo,
      );
      return updatedResponse;
    } catch (error) {
      Groveman.error('ordersToReceive', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null) return;

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['FULFILLED,SHIPPED'],
        cursor: prevOrders.pageInfo?.endCursor,
      );
      // filter out orders that are already received
      final filteredNextOrders = nextOrders.orders
          .where((order) => order.metafield?.value != 'true')
          .toList();
      final updatedOrders = CoreShopifyOrderResponse(
        orders: [...prevOrders.orders, ...filteredNextOrders],
        pageInfo: nextOrders.pageInfo,
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersToReceive', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
class OrdersCompletedController extends _$OrdersCompletedController {
  @override
  Future<CoreShopifyOrderResponse?> build() async {
    try {
      final orderResponse =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['FULFILLED,SHIPPED'],
      );
      // filter out orders that are not received
      final filteredOrders = orderResponse.orders
          .where((order) => order.metafield?.value == 'true')
          .toList();
      final updatedResponse = CoreShopifyOrderResponse(
        orders: filteredOrders,
        pageInfo: orderResponse.pageInfo,
      );
      return updatedResponse;
    } catch (error) {
      Groveman.error('ordersCompleted', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null) return;

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['PAID'],
        fulfillmentStatus: ['FULFILLED,SHIPPED'],
        cursor: prevOrders.pageInfo?.endCursor,
      );
      // filter out orders that are not received
      final filteredNextOrders = nextOrders.orders
          .where((order) => order.metafield?.value == 'true')
          .toList();
      final updatedOrders = CoreShopifyOrderResponse(
        orders: [...prevOrders.orders, ...filteredNextOrders],
        pageInfo: nextOrders.pageInfo,
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersCompleted', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
class OrdersToReviewController extends _$OrdersToReviewController {
  @override
  Future<OrderReviewRequestResponse?> build() async {
    try {
      return await ref
          .read(shopifyOrdersRepositoryProvider)
          .fetchOrderReviewRequests();
    } catch (error) {
      Groveman.error('ordersToReview', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null ||
        prevOrders.meta.currentPage == prevOrders.meta.lastPage) {
      return;
    }

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders = await ref
          .read(shopifyOrdersRepositoryProvider)
          .fetchOrderReviewRequests(
            page: prevOrders.meta.currentPage + 1,
          );

      final updatedOrders = OrderReviewRequestResponse(
        meta: nextOrders.meta,
        data: [...prevOrders.data, ...nextOrders.data],
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersToReview', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
class OrdersRefundedController extends _$OrdersRefundedController {
  @override
  Future<CoreShopifyOrderResponse?> build() async {
    try {
      final response =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['REFUNDED'],
        fulfillmentStatus: ['UNFULFILLED'],
      );
      return response;
    } catch (error) {
      Groveman.error('ordersRefunded', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevOrders = state.value;
    if (prevOrders == null) return;

    try {
      ref.read(ordersLoadingprovider.notifier).update((state) => true);
      final nextOrders =
          await ref.read(shopifyOrdersRepositoryProvider).fetchOrders(
        financialStatus: ['REFUNDED'],
        fulfillmentStatus: ['UNFULFILLED'],
        cursor: prevOrders.pageInfo?.endCursor,
      );

      final updatedOrders = CoreShopifyOrderResponse(
        orders: [...prevOrders.orders, ...nextOrders.orders],
        pageInfo: nextOrders.pageInfo,
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore OrdersRefunded', error: error);
    } finally {
      ref.read(ordersLoadingprovider.notifier).update((state) => false);
    }
  }
}

@riverpod
Future<bool> markOrderAsReceived(
  MarkOrderAsReceivedRef ref,
  String orderId,
) async {
  try {
    return await ref
        .read(shopifyOrdersRepositoryProvider)
        .updateOrderReceived(orderId);
  } catch (error) {
    Groveman.error('markOrderAsReceived', error: error);
    return false;
  }
}
