// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopify_orders_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderByIdHash() => r'df89506ac928b419b63f7dc00aeb6381ff4ffd4f';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [orderById].
@ProviderFor(orderById)
const orderByIdProvider = OrderByIdFamily();

/// See also [orderById].
class OrderByIdFamily extends Family<AsyncValue<Order?>> {
  /// See also [orderById].
  const OrderByIdFamily();

  /// See also [orderById].
  OrderByIdProvider call(
    String id,
  ) {
    return OrderByIdProvider(
      id,
    );
  }

  @override
  OrderByIdProvider getProviderOverride(
    covariant OrderByIdProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderByIdProvider';
}

/// See also [orderById].
class OrderByIdProvider extends AutoDisposeFutureProvider<Order?> {
  /// See also [orderById].
  OrderByIdProvider(
    String id,
  ) : this._internal(
          (ref) => orderById(
            ref as OrderByIdRef,
            id,
          ),
          from: orderByIdProvider,
          name: r'orderByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$orderByIdHash,
          dependencies: OrderByIdFamily._dependencies,
          allTransitiveDependencies: OrderByIdFamily._allTransitiveDependencies,
          id: id,
        );

  OrderByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<Order?> Function(OrderByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderByIdProvider._internal(
        (ref) => create(ref as OrderByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Order?> createElement() {
    return _OrderByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin OrderByIdRef on AutoDisposeFutureProviderRef<Order?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _OrderByIdProviderElement extends AutoDisposeFutureProviderElement<Order?>
    with OrderByIdRef {
  _OrderByIdProviderElement(super.provider);

  @override
  String get id => (origin as OrderByIdProvider).id;
}

String _$ordersHash() => r'9dab1add4bf848170e98a5fd744d526cc068ada7';

/// NOTE: retrieve orders from core backend
///
/// Copied from [orders].
@ProviderFor(orders)
final ordersProvider =
    AutoDisposeFutureProvider<CoreShopifyOrderResponse?>.internal(
  orders,
  name: r'ordersProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$ordersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OrdersRef = AutoDisposeFutureProviderRef<CoreShopifyOrderResponse?>;
String _$markOrderAsReceivedHash() =>
    r'e5cd00f0b0f83848659f5376ee675228cd823762';

/// See also [markOrderAsReceived].
@ProviderFor(markOrderAsReceived)
const markOrderAsReceivedProvider = MarkOrderAsReceivedFamily();

/// See also [markOrderAsReceived].
class MarkOrderAsReceivedFamily extends Family<AsyncValue<bool>> {
  /// See also [markOrderAsReceived].
  const MarkOrderAsReceivedFamily();

  /// See also [markOrderAsReceived].
  MarkOrderAsReceivedProvider call(
    String orderId,
  ) {
    return MarkOrderAsReceivedProvider(
      orderId,
    );
  }

  @override
  MarkOrderAsReceivedProvider getProviderOverride(
    covariant MarkOrderAsReceivedProvider provider,
  ) {
    return call(
      provider.orderId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'markOrderAsReceivedProvider';
}

/// See also [markOrderAsReceived].
class MarkOrderAsReceivedProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [markOrderAsReceived].
  MarkOrderAsReceivedProvider(
    String orderId,
  ) : this._internal(
          (ref) => markOrderAsReceived(
            ref as MarkOrderAsReceivedRef,
            orderId,
          ),
          from: markOrderAsReceivedProvider,
          name: r'markOrderAsReceivedProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$markOrderAsReceivedHash,
          dependencies: MarkOrderAsReceivedFamily._dependencies,
          allTransitiveDependencies:
              MarkOrderAsReceivedFamily._allTransitiveDependencies,
          orderId: orderId,
        );

  MarkOrderAsReceivedProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(MarkOrderAsReceivedRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MarkOrderAsReceivedProvider._internal(
        (ref) => create(ref as MarkOrderAsReceivedRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _MarkOrderAsReceivedProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MarkOrderAsReceivedProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin MarkOrderAsReceivedRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _MarkOrderAsReceivedProviderElement
    extends AutoDisposeFutureProviderElement<bool> with MarkOrderAsReceivedRef {
  _MarkOrderAsReceivedProviderElement(super.provider);

  @override
  String get orderId => (origin as MarkOrderAsReceivedProvider).orderId;
}

String _$ordersToPayControllerHash() =>
    r'a0e04cd10c72c830128ba00e92203646696a3d96';

/// See also [OrdersToPayController].
@ProviderFor(OrdersToPayController)
final ordersToPayControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersToPayController, CoreShopifyOrderResponse?>.internal(
  OrdersToPayController.new,
  name: r'ordersToPayControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersToPayControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersToPayController
    = AutoDisposeAsyncNotifier<CoreShopifyOrderResponse?>;
String _$ordersToShipControllerHash() =>
    r'd3d8e15b84e859c18e0e252365935b24761986c8';

/// See also [OrdersToShipController].
@ProviderFor(OrdersToShipController)
final ordersToShipControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersToShipController, CoreShopifyOrderResponse?>.internal(
  OrdersToShipController.new,
  name: r'ordersToShipControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersToShipControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersToShipController
    = AutoDisposeAsyncNotifier<CoreShopifyOrderResponse?>;
String _$ordersToReceiveControllerHash() =>
    r'3a501ac4a82d6c187015545dabab53a4c0e9d17d';

/// See also [OrdersToReceiveController].
@ProviderFor(OrdersToReceiveController)
final ordersToReceiveControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersToReceiveController, CoreShopifyOrderResponse?>.internal(
  OrdersToReceiveController.new,
  name: r'ordersToReceiveControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersToReceiveControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersToReceiveController
    = AutoDisposeAsyncNotifier<CoreShopifyOrderResponse?>;
String _$ordersCompletedControllerHash() =>
    r'25f79d3587d7e5ecb3e0336d8419f44580d2e20c';

/// See also [OrdersCompletedController].
@ProviderFor(OrdersCompletedController)
final ordersCompletedControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersCompletedController, CoreShopifyOrderResponse?>.internal(
  OrdersCompletedController.new,
  name: r'ordersCompletedControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersCompletedControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersCompletedController
    = AutoDisposeAsyncNotifier<CoreShopifyOrderResponse?>;
String _$ordersToReviewControllerHash() =>
    r'572afa84a9ab89696cab929a570f44ebda93e210';

/// See also [OrdersToReviewController].
@ProviderFor(OrdersToReviewController)
final ordersToReviewControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersToReviewController, OrderReviewRequestResponse?>.internal(
  OrdersToReviewController.new,
  name: r'ordersToReviewControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersToReviewControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersToReviewController
    = AutoDisposeAsyncNotifier<OrderReviewRequestResponse?>;
String _$ordersRefundedControllerHash() =>
    r'978644bd6f91af52aff3e25ca00fb5ad32725195';

/// See also [OrdersRefundedController].
@ProviderFor(OrdersRefundedController)
final ordersRefundedControllerProvider = AutoDisposeAsyncNotifierProvider<
    OrdersRefundedController, CoreShopifyOrderResponse?>.internal(
  OrdersRefundedController.new,
  name: r'ordersRefundedControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersRefundedControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrdersRefundedController
    = AutoDisposeAsyncNotifier<CoreShopifyOrderResponse?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
