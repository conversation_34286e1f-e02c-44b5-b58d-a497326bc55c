// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_review_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productReviewRepositoryHash() =>
    r'3a2bf3641f692fd9cb673714c8de303b7923abdc';

/// See also [productReviewRepository].
@ProviderFor(productReviewRepository)
final productReviewRepositoryProvider =
    Provider<ProductReviewRepository>.internal(
  productReviewRepository,
  name: r'productReviewRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$productReviewRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ProductReviewRepositoryRef = ProviderRef<ProductReviewRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
