import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/commerce/orders/provider/shopify_orders_providers.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_review_repository.g.dart';

@Riverpod(keepAlive: true)
ProductReviewRepository productReviewRepository(
  ProductReviewRepositoryRef ref,
) =>
    ProductReviewRepository(ref);

class ProductReviewRepository {
  ProductReviewRepository(this.ref);
  final ProductReviewRepositoryRef ref;

  Future<ProductReviewResponse> fetchReviews({
    String? productId,
    String? email,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParameters = {
        if (productId != null) 'products': productId,
        if (email != null) 'email': email,
        'page': page ?? 1,
        'limit': limit ?? 100,
      };

      final response = await ref
          .read(zestRepositoryProvider)
          .get<Json>('/reviews', queryParameters: queryParameters);

      return ProductReviewResponse.fromJson(response.data!);
    } catch (error, stackTrace) {
      Groveman.warning('fetchReviews', error: error, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<ProductReview> fetchReview({
    required String reviewId,
  }) async {
    try {
      final response = await ref
          .read(zestRepositoryProvider)
          .get<Json>('/reviews/$reviewId');

      return ProductReview.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchReview', error: error);
      rethrow;
    }
  }

  Future<ReviewOverview> fetchOverview({
    required String productId,
  }) async {
    try {
      final response = await ref
          .read(zestRepositoryProvider)
          .get<Json>('/products/$productId/overview');

      return ReviewOverview.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchOverview', error: error);
      rethrow;
    }
  }

  Future<String> submitReview(
    ProductReviewFormType body,
  ) async {
    try {
      final response = await ref
          .read(repositoryProvider)
          .post<Json>('/me/orders/reviews', data: body.toJson());

      return response.data!['message'] as String;
    } catch (error) {
      Groveman.warning('submitReview', error: error);
      rethrow;
    } finally {
      ref
        ..invalidate(productReviewsProvider)
        ..invalidate(ordersToReviewControllerProvider);
    }
  }
}
