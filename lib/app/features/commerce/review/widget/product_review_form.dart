import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/price_match/widget/custom_form_text_field.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:gomama/app/features/commerce/review/repository/product_review_repository.dart';
import 'package:gomama/app/widgets/rating_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ProductReviewForm extends HookConsumerWidget {
  const ProductReviewForm({
    super.key,
    required this.productId,
    required this.orderId,
  });

  final String productId;
  final String orderId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormBuilderState>());
    final _isSubmitting = useState<bool>(false);

    Future<void> submitReview() async {
      final user = ref.watch(authControllerProvider).requireValue;
      final shopifyProfile = user.shopifyProfile;
      final success = formKey.value.currentState?.saveAndValidate();
      if (success != true) return;

      try {
        if (shopifyProfile == null) {
          throw Exception('Shopify profile not found');
        }

        _isSubmitting.value = true;

        final body = ProductReviewFormType(
          productId: productId,
          shopifyOrderId: orderId,
          content: formKey.value.currentState!.value['content'].toString(),
          score: formKey.value.currentState!.value['score'] as int,
        );

        final response =
            await ref.read(productReviewRepositoryProvider).submitReview(body);

        if (response == 'Review submitted successfully') {
          if (context.mounted) {
            context.pop();
            // popup if fail
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Review submitted successfully'),
              ),
            );
          }
        } else {
          if (context.mounted) {
            context.pop();
            // popup if fail
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Something went wrong, try again later'),
              ),
            );
          }
        }
      } catch (error, stack) {
        Groveman.error('submitReview', error: error, stackTrace: stack);
        _isSubmitting.value = false;

        // popup if fail
        if (context.mounted) {
          context.pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                error is AppNetworkResponseException
                    ? '${error.message}'
                    : 'Something went wrong, please try again later',
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } finally {
        _isSubmitting.value = false;
        ref.invalidate(productReviewsProvider);
      }
    }

    return FormBuilder(
      key: formKey.value,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Product Rating',
              style: textTheme(context).titleMedium,
            ),
            const SizedBox(height: 8),
            Align(
              child: FormBuilderField(
                name: 'score',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 36,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Review',
              style: textTheme(context).titleMedium,
            ),
            const SizedBox(height: 8),
            CustomFormTextField(
              name: 'content',
              hintText:
                  'We’re here to improve your experience. Share your thoughts!',
              maxLines: 5,
              validator: FormBuilderValidators.required(),
            ),
            const Spacer(),
            FilledButton(
              style: FilledButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isSubmitting.value == true ? null : submitReview,
              child: Text(
                _isSubmitting.value == true ? 'Submitting...' : 'Submit Review',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
