import 'package:flutter/material.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/review/provider/product_review_provider.dart';
import 'package:gomama/app/features/commerce/review/widget/product_review_card.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyProductReviews extends ConsumerWidget {
  const MyProductReviews({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authControllerProvider).requireValue;
    final shopifyProfile = user.shopifyProfile;
    final reviews = ref.watch(
      productReviewsProvider(email: shopifyProfile!.email),
    );

    return reviews.when(
      data: (reviews) {
        if (reviews == null || reviews.isEmpty == true) {
          return const SizedBox.shrink();
        }

        return ListView.separated(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            final review = reviews[index];

            return ProductReviewCard(review);
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 12);
          },
          itemCount: reviews.length,
        );
      },
      error: (error, stackTrace) {
        return const SizedBox.shrink();
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}
