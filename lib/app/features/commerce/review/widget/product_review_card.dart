import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/widgets/rating_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class ProductReviewCard extends ConsumerWidget {
  const ProductReviewCard(this.productReview, {super.key});

  final ProductReview productReview;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                height: 50,
                width: 50,
                child: CachedNetworkImage(
                  imageUrl: productReview.product.imageUrl ?? '',
                  imageBuilder: (context, imageProvider) {
                    return DecoratedBox(
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                  errorWidget: (context, url, error) =>
                      const Icon(CustomIcon.error),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productReview.product.name,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: textTheme(context).bodyLarge,
                    ),
                    Row(
                      children: [
                        for (int i = 0; i < productReview.score; i++)
                          const Icon(
                            CustomIcon.star,
                            color: CustomColors.primary,
                            size: 10,
                          ),
                        const SizedBox(width: 2),
                        Text(
                          productReview.score.toStringAsFixed(1),
                          style: textTheme(context)
                              .labelSmall!
                              .copyWith(color: CustomColors.primary),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                DateFormat('dd/MM/yyyy').format(productReview.createdAt),
                style: textTheme(context).labelSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(productReview.content),
        ],
      ),
    );
  }
}
