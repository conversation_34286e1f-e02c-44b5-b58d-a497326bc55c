import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/review/widget/product_review_form.dart';

void productReviewDialog({
  required BuildContext context,
  required String productId,
  required String orderId,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: CustomColors.secondaryLight,
    builder: (BuildContext context) {
      return Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.45,
          child: ProductReviewForm(
            productId: productId,
            orderId: orderId,
          ),
        ),
      );
    },
  );
}
