import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:shopify_flutter/models/src/order/order.dart';

part 'product_review.freezed.dart';
part 'product_review.g.dart';

@freezed
class ProductReviewFormType with _$ProductReviewFormType {
  const factory ProductReviewFormType({
    required String productId,
    required String shopifyOrderId,
    required int score,
    required String content,
    // @JsonKey(includeFromJson: false) List<File>? attachments,
  }) = _ProductReviewFormType;

  factory ProductReviewFormType.fromJson(Json json) =>
      _$ProductReviewFormTypeFromJson(json);
}

@freezed
class ProductReview with _$ProductReview {
  @CustomDateTimeConverter()
  const factory ProductReview({
    required int id,
    required String title,
    required String content,
    required String name,
    required String email,
    // required String appClientId,
    required int score,
    required int productId,
    // DateTime? importedAt,
    required DateTime createdAt,
    // String? externalImportId,
    // String? languageCode,
    // required String translation,
    // bool? verifiedEmail,
    required ReviewProduct product,
    required int upVotes,
    required int downVotes,
  }) = _ProductReview;

  factory ProductReview.fromJson(Json json) => _$ProductReviewFromJson(json);
}

@freezed
class ReviewProduct with _$ReviewProduct {
  const factory ReviewProduct({
    required int id,
    required String customId,
    required String name,
    String? imageUrl,
    String? slug,
  }) = _ReviewProduct;

  factory ReviewProduct.fromJson(Json json) => _$ReviewProductFromJson(json);
}

@freezed
class ReviewOverview with _$ReviewOverview {
  const factory ReviewOverview({
    int? maxRating,
    int? minRating,
    String? avgRating,
    required int total,
    required List<ReviewScoreGroup> grouped,
  }) = _ReviewOverview;

  factory ReviewOverview.fromJson(Json json) => _$ReviewOverviewFromJson(json);
}

@freezed
class ReviewScoreGroup with _$ReviewScoreGroup {
  const factory ReviewScoreGroup({
    required int score,
    required int total,
  }) = _ReviewScoreGroup;

  factory ReviewScoreGroup.fromJson(Json json) =>
      _$ReviewScoreGroupFromJson(json);
}

@freezed
class ProductReviewResponse with _$ProductReviewResponse {
  factory ProductReviewResponse({
    required List<ProductReview> data,
    required Pagination meta,
  }) = _ProductReviewResponse;

  factory ProductReviewResponse.fromJson(Json json) =>
      _$ProductReviewResponseFromJson(json);
}

@freezed
class OrderWithMetafield with _$OrderWithMetafield {
  const factory OrderWithMetafield({
    Order? order,
    OrderMetafield? metafield,
  }) = _OrderWithMetafield;

  factory OrderWithMetafield.fromJson(Json json) =>
      _$OrderWithMetafieldFromJson(json);
}

@freezed
class OrderMetafield with _$OrderMetafield {
  const factory OrderMetafield({
    required String id,
    bool? isReceived,
  }) = _OrderMetafield;

  factory OrderMetafield.fromJson(Json json) => _$OrderMetafieldFromJson(json);
}
