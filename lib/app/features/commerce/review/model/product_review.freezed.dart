// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_review.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductReviewFormType _$ProductReviewFormTypeFromJson(
    Map<String, dynamic> json) {
  return _ProductReviewFormType.fromJson(json);
}

/// @nodoc
mixin _$ProductReviewFormType {
  String get productId => throw _privateConstructorUsedError;
  String get shopifyOrderId => throw _privateConstructorUsedError;
  int get score => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProductReviewFormTypeCopyWith<ProductReviewFormType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductReviewFormTypeCopyWith<$Res> {
  factory $ProductReviewFormTypeCopyWith(ProductReviewFormType value,
          $Res Function(ProductReviewFormType) then) =
      _$ProductReviewFormTypeCopyWithImpl<$Res, ProductReviewFormType>;
  @useResult
  $Res call(
      {String productId, String shopifyOrderId, int score, String content});
}

/// @nodoc
class _$ProductReviewFormTypeCopyWithImpl<$Res,
        $Val extends ProductReviewFormType>
    implements $ProductReviewFormTypeCopyWith<$Res> {
  _$ProductReviewFormTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? shopifyOrderId = null,
    Object? score = null,
    Object? content = null,
  }) {
    return _then(_value.copyWith(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      shopifyOrderId: null == shopifyOrderId
          ? _value.shopifyOrderId
          : shopifyOrderId // ignore: cast_nullable_to_non_nullable
              as String,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductReviewFormTypeImplCopyWith<$Res>
    implements $ProductReviewFormTypeCopyWith<$Res> {
  factory _$$ProductReviewFormTypeImplCopyWith(
          _$ProductReviewFormTypeImpl value,
          $Res Function(_$ProductReviewFormTypeImpl) then) =
      __$$ProductReviewFormTypeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String productId, String shopifyOrderId, int score, String content});
}

/// @nodoc
class __$$ProductReviewFormTypeImplCopyWithImpl<$Res>
    extends _$ProductReviewFormTypeCopyWithImpl<$Res,
        _$ProductReviewFormTypeImpl>
    implements _$$ProductReviewFormTypeImplCopyWith<$Res> {
  __$$ProductReviewFormTypeImplCopyWithImpl(_$ProductReviewFormTypeImpl _value,
      $Res Function(_$ProductReviewFormTypeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? shopifyOrderId = null,
    Object? score = null,
    Object? content = null,
  }) {
    return _then(_$ProductReviewFormTypeImpl(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      shopifyOrderId: null == shopifyOrderId
          ? _value.shopifyOrderId
          : shopifyOrderId // ignore: cast_nullable_to_non_nullable
              as String,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductReviewFormTypeImpl implements _ProductReviewFormType {
  const _$ProductReviewFormTypeImpl(
      {required this.productId,
      required this.shopifyOrderId,
      required this.score,
      required this.content});

  factory _$ProductReviewFormTypeImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductReviewFormTypeImplFromJson(json);

  @override
  final String productId;
  @override
  final String shopifyOrderId;
  @override
  final int score;
  @override
  final String content;

  @override
  String toString() {
    return 'ProductReviewFormType(productId: $productId, shopifyOrderId: $shopifyOrderId, score: $score, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductReviewFormTypeImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.shopifyOrderId, shopifyOrderId) ||
                other.shopifyOrderId == shopifyOrderId) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, productId, shopifyOrderId, score, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductReviewFormTypeImplCopyWith<_$ProductReviewFormTypeImpl>
      get copyWith => __$$ProductReviewFormTypeImplCopyWithImpl<
          _$ProductReviewFormTypeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductReviewFormTypeImplToJson(
      this,
    );
  }
}

abstract class _ProductReviewFormType implements ProductReviewFormType {
  const factory _ProductReviewFormType(
      {required final String productId,
      required final String shopifyOrderId,
      required final int score,
      required final String content}) = _$ProductReviewFormTypeImpl;

  factory _ProductReviewFormType.fromJson(Map<String, dynamic> json) =
      _$ProductReviewFormTypeImpl.fromJson;

  @override
  String get productId;
  @override
  String get shopifyOrderId;
  @override
  int get score;
  @override
  String get content;
  @override
  @JsonKey(ignore: true)
  _$$ProductReviewFormTypeImplCopyWith<_$ProductReviewFormTypeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductReview _$ProductReviewFromJson(Map<String, dynamic> json) {
  return _ProductReview.fromJson(json);
}

/// @nodoc
mixin _$ProductReview {
  int get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email =>
      throw _privateConstructorUsedError; // required String appClientId,
  int get score => throw _privateConstructorUsedError;
  int get productId =>
      throw _privateConstructorUsedError; // DateTime? importedAt,
  DateTime get createdAt =>
      throw _privateConstructorUsedError; // String? externalImportId,
// String? languageCode,
// required String translation,
// bool? verifiedEmail,
  ReviewProduct get product => throw _privateConstructorUsedError;
  int get upVotes => throw _privateConstructorUsedError;
  int get downVotes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProductReviewCopyWith<ProductReview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductReviewCopyWith<$Res> {
  factory $ProductReviewCopyWith(
          ProductReview value, $Res Function(ProductReview) then) =
      _$ProductReviewCopyWithImpl<$Res, ProductReview>;
  @useResult
  $Res call(
      {int id,
      String title,
      String content,
      String name,
      String email,
      int score,
      int productId,
      DateTime createdAt,
      ReviewProduct product,
      int upVotes,
      int downVotes});

  $ReviewProductCopyWith<$Res> get product;
}

/// @nodoc
class _$ProductReviewCopyWithImpl<$Res, $Val extends ProductReview>
    implements $ProductReviewCopyWith<$Res> {
  _$ProductReviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? name = null,
    Object? email = null,
    Object? score = null,
    Object? productId = null,
    Object? createdAt = null,
    Object? product = null,
    Object? upVotes = null,
    Object? downVotes = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ReviewProduct,
      upVotes: null == upVotes
          ? _value.upVotes
          : upVotes // ignore: cast_nullable_to_non_nullable
              as int,
      downVotes: null == downVotes
          ? _value.downVotes
          : downVotes // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ReviewProductCopyWith<$Res> get product {
    return $ReviewProductCopyWith<$Res>(_value.product, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductReviewImplCopyWith<$Res>
    implements $ProductReviewCopyWith<$Res> {
  factory _$$ProductReviewImplCopyWith(
          _$ProductReviewImpl value, $Res Function(_$ProductReviewImpl) then) =
      __$$ProductReviewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String title,
      String content,
      String name,
      String email,
      int score,
      int productId,
      DateTime createdAt,
      ReviewProduct product,
      int upVotes,
      int downVotes});

  @override
  $ReviewProductCopyWith<$Res> get product;
}

/// @nodoc
class __$$ProductReviewImplCopyWithImpl<$Res>
    extends _$ProductReviewCopyWithImpl<$Res, _$ProductReviewImpl>
    implements _$$ProductReviewImplCopyWith<$Res> {
  __$$ProductReviewImplCopyWithImpl(
      _$ProductReviewImpl _value, $Res Function(_$ProductReviewImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? name = null,
    Object? email = null,
    Object? score = null,
    Object? productId = null,
    Object? createdAt = null,
    Object? product = null,
    Object? upVotes = null,
    Object? downVotes = null,
  }) {
    return _then(_$ProductReviewImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      product: null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ReviewProduct,
      upVotes: null == upVotes
          ? _value.upVotes
          : upVotes // ignore: cast_nullable_to_non_nullable
              as int,
      downVotes: null == downVotes
          ? _value.downVotes
          : downVotes // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ProductReviewImpl implements _ProductReview {
  const _$ProductReviewImpl(
      {required this.id,
      required this.title,
      required this.content,
      required this.name,
      required this.email,
      required this.score,
      required this.productId,
      required this.createdAt,
      required this.product,
      required this.upVotes,
      required this.downVotes});

  factory _$ProductReviewImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductReviewImplFromJson(json);

  @override
  final int id;
  @override
  final String title;
  @override
  final String content;
  @override
  final String name;
  @override
  final String email;
// required String appClientId,
  @override
  final int score;
  @override
  final int productId;
// DateTime? importedAt,
  @override
  final DateTime createdAt;
// String? externalImportId,
// String? languageCode,
// required String translation,
// bool? verifiedEmail,
  @override
  final ReviewProduct product;
  @override
  final int upVotes;
  @override
  final int downVotes;

  @override
  String toString() {
    return 'ProductReview(id: $id, title: $title, content: $content, name: $name, email: $email, score: $score, productId: $productId, createdAt: $createdAt, product: $product, upVotes: $upVotes, downVotes: $downVotes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductReviewImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.upVotes, upVotes) || other.upVotes == upVotes) &&
            (identical(other.downVotes, downVotes) ||
                other.downVotes == downVotes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, content, name, email,
      score, productId, createdAt, product, upVotes, downVotes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductReviewImplCopyWith<_$ProductReviewImpl> get copyWith =>
      __$$ProductReviewImplCopyWithImpl<_$ProductReviewImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductReviewImplToJson(
      this,
    );
  }
}

abstract class _ProductReview implements ProductReview {
  const factory _ProductReview(
      {required final int id,
      required final String title,
      required final String content,
      required final String name,
      required final String email,
      required final int score,
      required final int productId,
      required final DateTime createdAt,
      required final ReviewProduct product,
      required final int upVotes,
      required final int downVotes}) = _$ProductReviewImpl;

  factory _ProductReview.fromJson(Map<String, dynamic> json) =
      _$ProductReviewImpl.fromJson;

  @override
  int get id;
  @override
  String get title;
  @override
  String get content;
  @override
  String get name;
  @override
  String get email;
  @override // required String appClientId,
  int get score;
  @override
  int get productId;
  @override // DateTime? importedAt,
  DateTime get createdAt;
  @override // String? externalImportId,
// String? languageCode,
// required String translation,
// bool? verifiedEmail,
  ReviewProduct get product;
  @override
  int get upVotes;
  @override
  int get downVotes;
  @override
  @JsonKey(ignore: true)
  _$$ProductReviewImplCopyWith<_$ProductReviewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReviewProduct _$ReviewProductFromJson(Map<String, dynamic> json) {
  return _ReviewProduct.fromJson(json);
}

/// @nodoc
mixin _$ReviewProduct {
  int get id => throw _privateConstructorUsedError;
  String get customId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get slug => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReviewProductCopyWith<ReviewProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewProductCopyWith<$Res> {
  factory $ReviewProductCopyWith(
          ReviewProduct value, $Res Function(ReviewProduct) then) =
      _$ReviewProductCopyWithImpl<$Res, ReviewProduct>;
  @useResult
  $Res call(
      {int id, String customId, String name, String? imageUrl, String? slug});
}

/// @nodoc
class _$ReviewProductCopyWithImpl<$Res, $Val extends ReviewProduct>
    implements $ReviewProductCopyWith<$Res> {
  _$ReviewProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = null,
    Object? imageUrl = freezed,
    Object? slug = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      slug: freezed == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReviewProductImplCopyWith<$Res>
    implements $ReviewProductCopyWith<$Res> {
  factory _$$ReviewProductImplCopyWith(
          _$ReviewProductImpl value, $Res Function(_$ReviewProductImpl) then) =
      __$$ReviewProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id, String customId, String name, String? imageUrl, String? slug});
}

/// @nodoc
class __$$ReviewProductImplCopyWithImpl<$Res>
    extends _$ReviewProductCopyWithImpl<$Res, _$ReviewProductImpl>
    implements _$$ReviewProductImplCopyWith<$Res> {
  __$$ReviewProductImplCopyWithImpl(
      _$ReviewProductImpl _value, $Res Function(_$ReviewProductImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customId = null,
    Object? name = null,
    Object? imageUrl = freezed,
    Object? slug = freezed,
  }) {
    return _then(_$ReviewProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      customId: null == customId
          ? _value.customId
          : customId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      slug: freezed == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewProductImpl implements _ReviewProduct {
  const _$ReviewProductImpl(
      {required this.id,
      required this.customId,
      required this.name,
      this.imageUrl,
      this.slug});

  factory _$ReviewProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewProductImplFromJson(json);

  @override
  final int id;
  @override
  final String customId;
  @override
  final String name;
  @override
  final String? imageUrl;
  @override
  final String? slug;

  @override
  String toString() {
    return 'ReviewProduct(id: $id, customId: $customId, name: $name, imageUrl: $imageUrl, slug: $slug)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customId, customId) ||
                other.customId == customId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.slug, slug) || other.slug == slug));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, customId, name, imageUrl, slug);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewProductImplCopyWith<_$ReviewProductImpl> get copyWith =>
      __$$ReviewProductImplCopyWithImpl<_$ReviewProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewProductImplToJson(
      this,
    );
  }
}

abstract class _ReviewProduct implements ReviewProduct {
  const factory _ReviewProduct(
      {required final int id,
      required final String customId,
      required final String name,
      final String? imageUrl,
      final String? slug}) = _$ReviewProductImpl;

  factory _ReviewProduct.fromJson(Map<String, dynamic> json) =
      _$ReviewProductImpl.fromJson;

  @override
  int get id;
  @override
  String get customId;
  @override
  String get name;
  @override
  String? get imageUrl;
  @override
  String? get slug;
  @override
  @JsonKey(ignore: true)
  _$$ReviewProductImplCopyWith<_$ReviewProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReviewOverview _$ReviewOverviewFromJson(Map<String, dynamic> json) {
  return _ReviewOverview.fromJson(json);
}

/// @nodoc
mixin _$ReviewOverview {
  int? get maxRating => throw _privateConstructorUsedError;
  int? get minRating => throw _privateConstructorUsedError;
  String? get avgRating => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;
  List<ReviewScoreGroup> get grouped => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReviewOverviewCopyWith<ReviewOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewOverviewCopyWith<$Res> {
  factory $ReviewOverviewCopyWith(
          ReviewOverview value, $Res Function(ReviewOverview) then) =
      _$ReviewOverviewCopyWithImpl<$Res, ReviewOverview>;
  @useResult
  $Res call(
      {int? maxRating,
      int? minRating,
      String? avgRating,
      int total,
      List<ReviewScoreGroup> grouped});
}

/// @nodoc
class _$ReviewOverviewCopyWithImpl<$Res, $Val extends ReviewOverview>
    implements $ReviewOverviewCopyWith<$Res> {
  _$ReviewOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxRating = freezed,
    Object? minRating = freezed,
    Object? avgRating = freezed,
    Object? total = null,
    Object? grouped = null,
  }) {
    return _then(_value.copyWith(
      maxRating: freezed == maxRating
          ? _value.maxRating
          : maxRating // ignore: cast_nullable_to_non_nullable
              as int?,
      minRating: freezed == minRating
          ? _value.minRating
          : minRating // ignore: cast_nullable_to_non_nullable
              as int?,
      avgRating: freezed == avgRating
          ? _value.avgRating
          : avgRating // ignore: cast_nullable_to_non_nullable
              as String?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      grouped: null == grouped
          ? _value.grouped
          : grouped // ignore: cast_nullable_to_non_nullable
              as List<ReviewScoreGroup>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReviewOverviewImplCopyWith<$Res>
    implements $ReviewOverviewCopyWith<$Res> {
  factory _$$ReviewOverviewImplCopyWith(_$ReviewOverviewImpl value,
          $Res Function(_$ReviewOverviewImpl) then) =
      __$$ReviewOverviewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? maxRating,
      int? minRating,
      String? avgRating,
      int total,
      List<ReviewScoreGroup> grouped});
}

/// @nodoc
class __$$ReviewOverviewImplCopyWithImpl<$Res>
    extends _$ReviewOverviewCopyWithImpl<$Res, _$ReviewOverviewImpl>
    implements _$$ReviewOverviewImplCopyWith<$Res> {
  __$$ReviewOverviewImplCopyWithImpl(
      _$ReviewOverviewImpl _value, $Res Function(_$ReviewOverviewImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxRating = freezed,
    Object? minRating = freezed,
    Object? avgRating = freezed,
    Object? total = null,
    Object? grouped = null,
  }) {
    return _then(_$ReviewOverviewImpl(
      maxRating: freezed == maxRating
          ? _value.maxRating
          : maxRating // ignore: cast_nullable_to_non_nullable
              as int?,
      minRating: freezed == minRating
          ? _value.minRating
          : minRating // ignore: cast_nullable_to_non_nullable
              as int?,
      avgRating: freezed == avgRating
          ? _value.avgRating
          : avgRating // ignore: cast_nullable_to_non_nullable
              as String?,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      grouped: null == grouped
          ? _value._grouped
          : grouped // ignore: cast_nullable_to_non_nullable
              as List<ReviewScoreGroup>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewOverviewImpl implements _ReviewOverview {
  const _$ReviewOverviewImpl(
      {this.maxRating,
      this.minRating,
      this.avgRating,
      required this.total,
      required final List<ReviewScoreGroup> grouped})
      : _grouped = grouped;

  factory _$ReviewOverviewImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewOverviewImplFromJson(json);

  @override
  final int? maxRating;
  @override
  final int? minRating;
  @override
  final String? avgRating;
  @override
  final int total;
  final List<ReviewScoreGroup> _grouped;
  @override
  List<ReviewScoreGroup> get grouped {
    if (_grouped is EqualUnmodifiableListView) return _grouped;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_grouped);
  }

  @override
  String toString() {
    return 'ReviewOverview(maxRating: $maxRating, minRating: $minRating, avgRating: $avgRating, total: $total, grouped: $grouped)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewOverviewImpl &&
            (identical(other.maxRating, maxRating) ||
                other.maxRating == maxRating) &&
            (identical(other.minRating, minRating) ||
                other.minRating == minRating) &&
            (identical(other.avgRating, avgRating) ||
                other.avgRating == avgRating) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._grouped, _grouped));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, maxRating, minRating, avgRating,
      total, const DeepCollectionEquality().hash(_grouped));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewOverviewImplCopyWith<_$ReviewOverviewImpl> get copyWith =>
      __$$ReviewOverviewImplCopyWithImpl<_$ReviewOverviewImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewOverviewImplToJson(
      this,
    );
  }
}

abstract class _ReviewOverview implements ReviewOverview {
  const factory _ReviewOverview(
      {final int? maxRating,
      final int? minRating,
      final String? avgRating,
      required final int total,
      required final List<ReviewScoreGroup> grouped}) = _$ReviewOverviewImpl;

  factory _ReviewOverview.fromJson(Map<String, dynamic> json) =
      _$ReviewOverviewImpl.fromJson;

  @override
  int? get maxRating;
  @override
  int? get minRating;
  @override
  String? get avgRating;
  @override
  int get total;
  @override
  List<ReviewScoreGroup> get grouped;
  @override
  @JsonKey(ignore: true)
  _$$ReviewOverviewImplCopyWith<_$ReviewOverviewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReviewScoreGroup _$ReviewScoreGroupFromJson(Map<String, dynamic> json) {
  return _ReviewScoreGroup.fromJson(json);
}

/// @nodoc
mixin _$ReviewScoreGroup {
  int get score => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReviewScoreGroupCopyWith<ReviewScoreGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReviewScoreGroupCopyWith<$Res> {
  factory $ReviewScoreGroupCopyWith(
          ReviewScoreGroup value, $Res Function(ReviewScoreGroup) then) =
      _$ReviewScoreGroupCopyWithImpl<$Res, ReviewScoreGroup>;
  @useResult
  $Res call({int score, int total});
}

/// @nodoc
class _$ReviewScoreGroupCopyWithImpl<$Res, $Val extends ReviewScoreGroup>
    implements $ReviewScoreGroupCopyWith<$Res> {
  _$ReviewScoreGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? score = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReviewScoreGroupImplCopyWith<$Res>
    implements $ReviewScoreGroupCopyWith<$Res> {
  factory _$$ReviewScoreGroupImplCopyWith(_$ReviewScoreGroupImpl value,
          $Res Function(_$ReviewScoreGroupImpl) then) =
      __$$ReviewScoreGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int score, int total});
}

/// @nodoc
class __$$ReviewScoreGroupImplCopyWithImpl<$Res>
    extends _$ReviewScoreGroupCopyWithImpl<$Res, _$ReviewScoreGroupImpl>
    implements _$$ReviewScoreGroupImplCopyWith<$Res> {
  __$$ReviewScoreGroupImplCopyWithImpl(_$ReviewScoreGroupImpl _value,
      $Res Function(_$ReviewScoreGroupImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? score = null,
    Object? total = null,
  }) {
    return _then(_$ReviewScoreGroupImpl(
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReviewScoreGroupImpl implements _ReviewScoreGroup {
  const _$ReviewScoreGroupImpl({required this.score, required this.total});

  factory _$ReviewScoreGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReviewScoreGroupImplFromJson(json);

  @override
  final int score;
  @override
  final int total;

  @override
  String toString() {
    return 'ReviewScoreGroup(score: $score, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReviewScoreGroupImpl &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, score, total);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReviewScoreGroupImplCopyWith<_$ReviewScoreGroupImpl> get copyWith =>
      __$$ReviewScoreGroupImplCopyWithImpl<_$ReviewScoreGroupImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReviewScoreGroupImplToJson(
      this,
    );
  }
}

abstract class _ReviewScoreGroup implements ReviewScoreGroup {
  const factory _ReviewScoreGroup(
      {required final int score,
      required final int total}) = _$ReviewScoreGroupImpl;

  factory _ReviewScoreGroup.fromJson(Map<String, dynamic> json) =
      _$ReviewScoreGroupImpl.fromJson;

  @override
  int get score;
  @override
  int get total;
  @override
  @JsonKey(ignore: true)
  _$$ReviewScoreGroupImplCopyWith<_$ReviewScoreGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductReviewResponse _$ProductReviewResponseFromJson(
    Map<String, dynamic> json) {
  return _ProductReviewResponse.fromJson(json);
}

/// @nodoc
mixin _$ProductReviewResponse {
  List<ProductReview> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProductReviewResponseCopyWith<ProductReviewResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductReviewResponseCopyWith<$Res> {
  factory $ProductReviewResponseCopyWith(ProductReviewResponse value,
          $Res Function(ProductReviewResponse) then) =
      _$ProductReviewResponseCopyWithImpl<$Res, ProductReviewResponse>;
  @useResult
  $Res call({List<ProductReview> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$ProductReviewResponseCopyWithImpl<$Res,
        $Val extends ProductReviewResponse>
    implements $ProductReviewResponseCopyWith<$Res> {
  _$ProductReviewResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductReview>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductReviewResponseImplCopyWith<$Res>
    implements $ProductReviewResponseCopyWith<$Res> {
  factory _$$ProductReviewResponseImplCopyWith(
          _$ProductReviewResponseImpl value,
          $Res Function(_$ProductReviewResponseImpl) then) =
      __$$ProductReviewResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ProductReview> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$ProductReviewResponseImplCopyWithImpl<$Res>
    extends _$ProductReviewResponseCopyWithImpl<$Res,
        _$ProductReviewResponseImpl>
    implements _$$ProductReviewResponseImplCopyWith<$Res> {
  __$$ProductReviewResponseImplCopyWithImpl(_$ProductReviewResponseImpl _value,
      $Res Function(_$ProductReviewResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$ProductReviewResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductReview>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductReviewResponseImpl implements _ProductReviewResponse {
  _$ProductReviewResponseImpl(
      {required final List<ProductReview> data, required this.meta})
      : _data = data;

  factory _$ProductReviewResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductReviewResponseImplFromJson(json);

  final List<ProductReview> _data;
  @override
  List<ProductReview> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'ProductReviewResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductReviewResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductReviewResponseImplCopyWith<_$ProductReviewResponseImpl>
      get copyWith => __$$ProductReviewResponseImplCopyWithImpl<
          _$ProductReviewResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductReviewResponseImplToJson(
      this,
    );
  }
}

abstract class _ProductReviewResponse implements ProductReviewResponse {
  factory _ProductReviewResponse(
      {required final List<ProductReview> data,
      required final Pagination meta}) = _$ProductReviewResponseImpl;

  factory _ProductReviewResponse.fromJson(Map<String, dynamic> json) =
      _$ProductReviewResponseImpl.fromJson;

  @override
  List<ProductReview> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$ProductReviewResponseImplCopyWith<_$ProductReviewResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OrderWithMetafield _$OrderWithMetafieldFromJson(Map<String, dynamic> json) {
  return _OrderWithMetafield.fromJson(json);
}

/// @nodoc
mixin _$OrderWithMetafield {
  Order? get order => throw _privateConstructorUsedError;
  OrderMetafield? get metafield => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderWithMetafieldCopyWith<OrderWithMetafield> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderWithMetafieldCopyWith<$Res> {
  factory $OrderWithMetafieldCopyWith(
          OrderWithMetafield value, $Res Function(OrderWithMetafield) then) =
      _$OrderWithMetafieldCopyWithImpl<$Res, OrderWithMetafield>;
  @useResult
  $Res call({Order? order, OrderMetafield? metafield});

  $OrderCopyWith<$Res>? get order;
  $OrderMetafieldCopyWith<$Res>? get metafield;
}

/// @nodoc
class _$OrderWithMetafieldCopyWithImpl<$Res, $Val extends OrderWithMetafield>
    implements $OrderWithMetafieldCopyWith<$Res> {
  _$OrderWithMetafieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? metafield = freezed,
  }) {
    return _then(_value.copyWith(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as Order?,
      metafield: freezed == metafield
          ? _value.metafield
          : metafield // ignore: cast_nullable_to_non_nullable
              as OrderMetafield?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OrderCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $OrderCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $OrderMetafieldCopyWith<$Res>? get metafield {
    if (_value.metafield == null) {
      return null;
    }

    return $OrderMetafieldCopyWith<$Res>(_value.metafield!, (value) {
      return _then(_value.copyWith(metafield: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderWithMetafieldImplCopyWith<$Res>
    implements $OrderWithMetafieldCopyWith<$Res> {
  factory _$$OrderWithMetafieldImplCopyWith(_$OrderWithMetafieldImpl value,
          $Res Function(_$OrderWithMetafieldImpl) then) =
      __$$OrderWithMetafieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Order? order, OrderMetafield? metafield});

  @override
  $OrderCopyWith<$Res>? get order;
  @override
  $OrderMetafieldCopyWith<$Res>? get metafield;
}

/// @nodoc
class __$$OrderWithMetafieldImplCopyWithImpl<$Res>
    extends _$OrderWithMetafieldCopyWithImpl<$Res, _$OrderWithMetafieldImpl>
    implements _$$OrderWithMetafieldImplCopyWith<$Res> {
  __$$OrderWithMetafieldImplCopyWithImpl(_$OrderWithMetafieldImpl _value,
      $Res Function(_$OrderWithMetafieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? metafield = freezed,
  }) {
    return _then(_$OrderWithMetafieldImpl(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as Order?,
      metafield: freezed == metafield
          ? _value.metafield
          : metafield // ignore: cast_nullable_to_non_nullable
              as OrderMetafield?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderWithMetafieldImpl implements _OrderWithMetafield {
  const _$OrderWithMetafieldImpl({this.order, this.metafield});

  factory _$OrderWithMetafieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderWithMetafieldImplFromJson(json);

  @override
  final Order? order;
  @override
  final OrderMetafield? metafield;

  @override
  String toString() {
    return 'OrderWithMetafield(order: $order, metafield: $metafield)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderWithMetafieldImpl &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.metafield, metafield) ||
                other.metafield == metafield));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, order, metafield);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderWithMetafieldImplCopyWith<_$OrderWithMetafieldImpl> get copyWith =>
      __$$OrderWithMetafieldImplCopyWithImpl<_$OrderWithMetafieldImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderWithMetafieldImplToJson(
      this,
    );
  }
}

abstract class _OrderWithMetafield implements OrderWithMetafield {
  const factory _OrderWithMetafield(
      {final Order? order,
      final OrderMetafield? metafield}) = _$OrderWithMetafieldImpl;

  factory _OrderWithMetafield.fromJson(Map<String, dynamic> json) =
      _$OrderWithMetafieldImpl.fromJson;

  @override
  Order? get order;
  @override
  OrderMetafield? get metafield;
  @override
  @JsonKey(ignore: true)
  _$$OrderWithMetafieldImplCopyWith<_$OrderWithMetafieldImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderMetafield _$OrderMetafieldFromJson(Map<String, dynamic> json) {
  return _OrderMetafield.fromJson(json);
}

/// @nodoc
mixin _$OrderMetafield {
  String get id => throw _privateConstructorUsedError;
  bool? get isReceived => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderMetafieldCopyWith<OrderMetafield> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderMetafieldCopyWith<$Res> {
  factory $OrderMetafieldCopyWith(
          OrderMetafield value, $Res Function(OrderMetafield) then) =
      _$OrderMetafieldCopyWithImpl<$Res, OrderMetafield>;
  @useResult
  $Res call({String id, bool? isReceived});
}

/// @nodoc
class _$OrderMetafieldCopyWithImpl<$Res, $Val extends OrderMetafield>
    implements $OrderMetafieldCopyWith<$Res> {
  _$OrderMetafieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isReceived = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      isReceived: freezed == isReceived
          ? _value.isReceived
          : isReceived // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderMetafieldImplCopyWith<$Res>
    implements $OrderMetafieldCopyWith<$Res> {
  factory _$$OrderMetafieldImplCopyWith(_$OrderMetafieldImpl value,
          $Res Function(_$OrderMetafieldImpl) then) =
      __$$OrderMetafieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, bool? isReceived});
}

/// @nodoc
class __$$OrderMetafieldImplCopyWithImpl<$Res>
    extends _$OrderMetafieldCopyWithImpl<$Res, _$OrderMetafieldImpl>
    implements _$$OrderMetafieldImplCopyWith<$Res> {
  __$$OrderMetafieldImplCopyWithImpl(
      _$OrderMetafieldImpl _value, $Res Function(_$OrderMetafieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isReceived = freezed,
  }) {
    return _then(_$OrderMetafieldImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      isReceived: freezed == isReceived
          ? _value.isReceived
          : isReceived // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderMetafieldImpl implements _OrderMetafield {
  const _$OrderMetafieldImpl({required this.id, this.isReceived});

  factory _$OrderMetafieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderMetafieldImplFromJson(json);

  @override
  final String id;
  @override
  final bool? isReceived;

  @override
  String toString() {
    return 'OrderMetafield(id: $id, isReceived: $isReceived)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderMetafieldImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isReceived, isReceived) ||
                other.isReceived == isReceived));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, isReceived);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderMetafieldImplCopyWith<_$OrderMetafieldImpl> get copyWith =>
      __$$OrderMetafieldImplCopyWithImpl<_$OrderMetafieldImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderMetafieldImplToJson(
      this,
    );
  }
}

abstract class _OrderMetafield implements OrderMetafield {
  const factory _OrderMetafield(
      {required final String id,
      final bool? isReceived}) = _$OrderMetafieldImpl;

  factory _OrderMetafield.fromJson(Map<String, dynamic> json) =
      _$OrderMetafieldImpl.fromJson;

  @override
  String get id;
  @override
  bool? get isReceived;
  @override
  @JsonKey(ignore: true)
  _$$OrderMetafieldImplCopyWith<_$OrderMetafieldImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
