// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_review.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductReviewFormTypeImpl _$$ProductReviewFormTypeImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductReviewFormTypeImpl(
      productId: json['product_id'] as String,
      shopifyOrderId: json['shopify_order_id'] as String,
      score: (json['score'] as num).toInt(),
      content: json['content'] as String,
    );

Map<String, dynamic> _$$ProductReviewFormTypeImplToJson(
        _$ProductReviewFormTypeImpl instance) =>
    <String, dynamic>{
      'product_id': instance.productId,
      'shopify_order_id': instance.shopifyOrderId,
      'score': instance.score,
      'content': instance.content,
    };

_$ProductReviewImpl _$$ProductReviewImplFromJson(Map<String, dynamic> json) =>
    _$ProductReviewImpl(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      content: json['content'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      score: (json['score'] as num).toInt(),
      productId: (json['product_id'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      product: ReviewProduct.fromJson(json['product'] as Map<String, dynamic>),
      upVotes: (json['up_votes'] as num).toInt(),
      downVotes: (json['down_votes'] as num).toInt(),
    );

Map<String, dynamic> _$$ProductReviewImplToJson(_$ProductReviewImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'name': instance.name,
      'email': instance.email,
      'score': instance.score,
      'product_id': instance.productId,
      'created_at': instance.createdAt.toIso8601String(),
      'product': instance.product.toJson(),
      'up_votes': instance.upVotes,
      'down_votes': instance.downVotes,
    };

_$ReviewProductImpl _$$ReviewProductImplFromJson(Map<String, dynamic> json) =>
    _$ReviewProductImpl(
      id: (json['id'] as num).toInt(),
      customId: json['custom_id'] as String,
      name: json['name'] as String,
      imageUrl: json['image_url'] as String?,
      slug: json['slug'] as String?,
    );

Map<String, dynamic> _$$ReviewProductImplToJson(_$ReviewProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'custom_id': instance.customId,
      'name': instance.name,
      'image_url': instance.imageUrl,
      'slug': instance.slug,
    };

_$ReviewOverviewImpl _$$ReviewOverviewImplFromJson(Map<String, dynamic> json) =>
    _$ReviewOverviewImpl(
      maxRating: (json['max_rating'] as num?)?.toInt(),
      minRating: (json['min_rating'] as num?)?.toInt(),
      avgRating: json['avg_rating'] as String?,
      total: (json['total'] as num).toInt(),
      grouped: (json['grouped'] as List<dynamic>)
          .map((e) => ReviewScoreGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ReviewOverviewImplToJson(
        _$ReviewOverviewImpl instance) =>
    <String, dynamic>{
      'max_rating': instance.maxRating,
      'min_rating': instance.minRating,
      'avg_rating': instance.avgRating,
      'total': instance.total,
      'grouped': instance.grouped.map((e) => e.toJson()).toList(),
    };

_$ReviewScoreGroupImpl _$$ReviewScoreGroupImplFromJson(
        Map<String, dynamic> json) =>
    _$ReviewScoreGroupImpl(
      score: (json['score'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$ReviewScoreGroupImplToJson(
        _$ReviewScoreGroupImpl instance) =>
    <String, dynamic>{
      'score': instance.score,
      'total': instance.total,
    };

_$ProductReviewResponseImpl _$$ProductReviewResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductReviewResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => ProductReview.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProductReviewResponseImplToJson(
        _$ProductReviewResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };

_$OrderWithMetafieldImpl _$$OrderWithMetafieldImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderWithMetafieldImpl(
      order: json['order'] == null
          ? null
          : Order.fromJson(json['order'] as Map<String, dynamic>),
      metafield: json['metafield'] == null
          ? null
          : OrderMetafield.fromJson(json['metafield'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OrderWithMetafieldImplToJson(
        _$OrderWithMetafieldImpl instance) =>
    <String, dynamic>{
      'order': instance.order?.toJson(),
      'metafield': instance.metafield?.toJson(),
    };

_$OrderMetafieldImpl _$$OrderMetafieldImplFromJson(Map<String, dynamic> json) =>
    _$OrderMetafieldImpl(
      id: json['id'] as String,
      isReceived: json['is_received'] as bool?,
    );

Map<String, dynamic> _$$OrderMetafieldImplToJson(
        _$OrderMetafieldImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'is_received': instance.isReceived,
    };
