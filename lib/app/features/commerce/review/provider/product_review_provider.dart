import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/price_match/model/price_match.dart';
import 'package:gomama/app/features/commerce/price_match/repository/price_match_repository.dart';
import 'package:gomama/app/features/commerce/repository/commerce_banner_repository.dart';
import 'package:gomama/app/features/commerce/review/model/product_review.dart';
import 'package:gomama/app/features/commerce/review/repository/product_review_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'product_review_provider.g.dart';

final productReviewIsBusyProvider =
    StateProvider.autoDispose<bool>((ref) => false);

@riverpod
Future<List<ProductReview>?> productReviews(
  ProductReviewsRef ref, {
  int? page,
  int? limit,
  String? productId,
  String? email,
}) async {
  try {
    final response =
        await ref.watch(productReviewRepositoryProvider).fetchReviews(
              page: page,
              limit: limit,
              productId: productId,
              email: email,
            );

    return response.data;
  } catch (e) {
    Groveman.error('productReviews', error: e);
    return null;
  }
}

@riverpod
Future<ProductReview?> productReview(
  ProductReviewRef ref, {
  required String reviewId,
}) async {
  try {
    final response =
        await ref.watch(productReviewRepositoryProvider).fetchReview(
              reviewId: reviewId,
            );

    return response;
  } catch (e) {
    Groveman.error('productReview', error: e);
    return null;
  }
}

@riverpod
Future<ReviewOverview?> productOverview(
  ProductOverviewRef ref, {
  required String productId,
}) async {
  try {
    final response =
        await ref.watch(productReviewRepositoryProvider).fetchOverview(
              productId: productId,
            );

    return response;
  } catch (e) {
    Groveman.error('productOverview', error: e);
    return null;
  }
}

@riverpod
class ProductReviewsController extends _$ProductReviewsController {
  @override
  Future<ProductReviewResponse?> build({required String productId}) async {
    try {
      return await ref.watch(productReviewRepositoryProvider).fetchReviews(
            productId: productId,
            limit: kPageLimit,
          );
    } catch (error) {
      Groveman.error('ProductReviewsController', error: error);
      return null;
    }
  }

  Future<void> fetchMore() async {
    final prevReviews = state.value;
    if (prevReviews == null ||
        prevReviews.meta.currentPage == prevReviews.meta.lastPage) {
      return;
    }

    try {
      ref.read(productReviewIsBusyProvider.notifier).update((state) => true);
      final nextOrders =
          await ref.watch(productReviewRepositoryProvider).fetchReviews(
                productId: productId,
                page: prevReviews.meta.currentPage + 1,
                limit: kPageLimit,
              );

      final updatedOrders = ProductReviewResponse(
        meta: nextOrders.meta,
        data: [...prevReviews.data, ...nextOrders.data],
      );
      state = AsyncValue.data(updatedOrders);
    } catch (error) {
      Groveman.error('fetchMore ProductReviewsController', error: error);
    } finally {
      ref.read(productReviewIsBusyProvider.notifier).update((state) => false);
    }
  }
}
