// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_review_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productReviewsHash() => r'7a5464526ee517304be479749d9c670f595cad17';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [productReviews].
@ProviderFor(productReviews)
const productReviewsProvider = ProductReviewsFamily();

/// See also [productReviews].
class ProductReviewsFamily extends Family<AsyncValue<List<ProductReview>?>> {
  /// See also [productReviews].
  const ProductReviewsFamily();

  /// See also [productReviews].
  ProductReviewsProvider call({
    int? page,
    int? limit,
    String? productId,
    String? email,
  }) {
    return ProductReviewsProvider(
      page: page,
      limit: limit,
      productId: productId,
      email: email,
    );
  }

  @override
  ProductReviewsProvider getProviderOverride(
    covariant ProductReviewsProvider provider,
  ) {
    return call(
      page: provider.page,
      limit: provider.limit,
      productId: provider.productId,
      email: provider.email,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productReviewsProvider';
}

/// See also [productReviews].
class ProductReviewsProvider
    extends AutoDisposeFutureProvider<List<ProductReview>?> {
  /// See also [productReviews].
  ProductReviewsProvider({
    int? page,
    int? limit,
    String? productId,
    String? email,
  }) : this._internal(
          (ref) => productReviews(
            ref as ProductReviewsRef,
            page: page,
            limit: limit,
            productId: productId,
            email: email,
          ),
          from: productReviewsProvider,
          name: r'productReviewsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productReviewsHash,
          dependencies: ProductReviewsFamily._dependencies,
          allTransitiveDependencies:
              ProductReviewsFamily._allTransitiveDependencies,
          page: page,
          limit: limit,
          productId: productId,
          email: email,
        );

  ProductReviewsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.page,
    required this.limit,
    required this.productId,
    required this.email,
  }) : super.internal();

  final int? page;
  final int? limit;
  final String? productId;
  final String? email;

  @override
  Override overrideWith(
    FutureOr<List<ProductReview>?> Function(ProductReviewsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductReviewsProvider._internal(
        (ref) => create(ref as ProductReviewsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        page: page,
        limit: limit,
        productId: productId,
        email: email,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductReview>?> createElement() {
    return _ProductReviewsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductReviewsProvider &&
        other.page == page &&
        other.limit == limit &&
        other.productId == productId &&
        other.email == email;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, limit.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);
    hash = _SystemHash.combine(hash, email.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductReviewsRef on AutoDisposeFutureProviderRef<List<ProductReview>?> {
  /// The parameter `page` of this provider.
  int? get page;

  /// The parameter `limit` of this provider.
  int? get limit;

  /// The parameter `productId` of this provider.
  String? get productId;

  /// The parameter `email` of this provider.
  String? get email;
}

class _ProductReviewsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductReview>?>
    with ProductReviewsRef {
  _ProductReviewsProviderElement(super.provider);

  @override
  int? get page => (origin as ProductReviewsProvider).page;
  @override
  int? get limit => (origin as ProductReviewsProvider).limit;
  @override
  String? get productId => (origin as ProductReviewsProvider).productId;
  @override
  String? get email => (origin as ProductReviewsProvider).email;
}

String _$productReviewHash() => r'ae1c817682effb53fd053be02e32155718d3cb13';

/// See also [productReview].
@ProviderFor(productReview)
const productReviewProvider = ProductReviewFamily();

/// See also [productReview].
class ProductReviewFamily extends Family<AsyncValue<ProductReview?>> {
  /// See also [productReview].
  const ProductReviewFamily();

  /// See also [productReview].
  ProductReviewProvider call({
    required String reviewId,
  }) {
    return ProductReviewProvider(
      reviewId: reviewId,
    );
  }

  @override
  ProductReviewProvider getProviderOverride(
    covariant ProductReviewProvider provider,
  ) {
    return call(
      reviewId: provider.reviewId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productReviewProvider';
}

/// See also [productReview].
class ProductReviewProvider extends AutoDisposeFutureProvider<ProductReview?> {
  /// See also [productReview].
  ProductReviewProvider({
    required String reviewId,
  }) : this._internal(
          (ref) => productReview(
            ref as ProductReviewRef,
            reviewId: reviewId,
          ),
          from: productReviewProvider,
          name: r'productReviewProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productReviewHash,
          dependencies: ProductReviewFamily._dependencies,
          allTransitiveDependencies:
              ProductReviewFamily._allTransitiveDependencies,
          reviewId: reviewId,
        );

  ProductReviewProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.reviewId,
  }) : super.internal();

  final String reviewId;

  @override
  Override overrideWith(
    FutureOr<ProductReview?> Function(ProductReviewRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductReviewProvider._internal(
        (ref) => create(ref as ProductReviewRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        reviewId: reviewId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductReview?> createElement() {
    return _ProductReviewProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductReviewProvider && other.reviewId == reviewId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, reviewId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductReviewRef on AutoDisposeFutureProviderRef<ProductReview?> {
  /// The parameter `reviewId` of this provider.
  String get reviewId;
}

class _ProductReviewProviderElement
    extends AutoDisposeFutureProviderElement<ProductReview?>
    with ProductReviewRef {
  _ProductReviewProviderElement(super.provider);

  @override
  String get reviewId => (origin as ProductReviewProvider).reviewId;
}

String _$productOverviewHash() => r'843fdbdcd2400df4442684719a2c25e25510b7ac';

/// See also [productOverview].
@ProviderFor(productOverview)
const productOverviewProvider = ProductOverviewFamily();

/// See also [productOverview].
class ProductOverviewFamily extends Family<AsyncValue<ReviewOverview?>> {
  /// See also [productOverview].
  const ProductOverviewFamily();

  /// See also [productOverview].
  ProductOverviewProvider call({
    required String productId,
  }) {
    return ProductOverviewProvider(
      productId: productId,
    );
  }

  @override
  ProductOverviewProvider getProviderOverride(
    covariant ProductOverviewProvider provider,
  ) {
    return call(
      productId: provider.productId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productOverviewProvider';
}

/// See also [productOverview].
class ProductOverviewProvider
    extends AutoDisposeFutureProvider<ReviewOverview?> {
  /// See also [productOverview].
  ProductOverviewProvider({
    required String productId,
  }) : this._internal(
          (ref) => productOverview(
            ref as ProductOverviewRef,
            productId: productId,
          ),
          from: productOverviewProvider,
          name: r'productOverviewProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productOverviewHash,
          dependencies: ProductOverviewFamily._dependencies,
          allTransitiveDependencies:
              ProductOverviewFamily._allTransitiveDependencies,
          productId: productId,
        );

  ProductOverviewProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<ReviewOverview?> Function(ProductOverviewRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductOverviewProvider._internal(
        (ref) => create(ref as ProductOverviewRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ReviewOverview?> createElement() {
    return _ProductOverviewProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductOverviewProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductOverviewRef on AutoDisposeFutureProviderRef<ReviewOverview?> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductOverviewProviderElement
    extends AutoDisposeFutureProviderElement<ReviewOverview?>
    with ProductOverviewRef {
  _ProductOverviewProviderElement(super.provider);

  @override
  String get productId => (origin as ProductOverviewProvider).productId;
}

String _$productReviewsControllerHash() =>
    r'c6cd61dd0392abd7dae1d3ab351b30e40ca08ddf';

abstract class _$ProductReviewsController
    extends BuildlessAutoDisposeAsyncNotifier<ProductReviewResponse?> {
  late final String productId;

  FutureOr<ProductReviewResponse?> build({
    required String productId,
  });
}

/// See also [ProductReviewsController].
@ProviderFor(ProductReviewsController)
const productReviewsControllerProvider = ProductReviewsControllerFamily();

/// See also [ProductReviewsController].
class ProductReviewsControllerFamily
    extends Family<AsyncValue<ProductReviewResponse?>> {
  /// See also [ProductReviewsController].
  const ProductReviewsControllerFamily();

  /// See also [ProductReviewsController].
  ProductReviewsControllerProvider call({
    required String productId,
  }) {
    return ProductReviewsControllerProvider(
      productId: productId,
    );
  }

  @override
  ProductReviewsControllerProvider getProviderOverride(
    covariant ProductReviewsControllerProvider provider,
  ) {
    return call(
      productId: provider.productId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productReviewsControllerProvider';
}

/// See also [ProductReviewsController].
class ProductReviewsControllerProvider
    extends AutoDisposeAsyncNotifierProviderImpl<ProductReviewsController,
        ProductReviewResponse?> {
  /// See also [ProductReviewsController].
  ProductReviewsControllerProvider({
    required String productId,
  }) : this._internal(
          () => ProductReviewsController()..productId = productId,
          from: productReviewsControllerProvider,
          name: r'productReviewsControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$productReviewsControllerHash,
          dependencies: ProductReviewsControllerFamily._dependencies,
          allTransitiveDependencies:
              ProductReviewsControllerFamily._allTransitiveDependencies,
          productId: productId,
        );

  ProductReviewsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  FutureOr<ProductReviewResponse?> runNotifierBuild(
    covariant ProductReviewsController notifier,
  ) {
    return notifier.build(
      productId: productId,
    );
  }

  @override
  Override overrideWith(ProductReviewsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ProductReviewsControllerProvider._internal(
        () => create()..productId = productId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<ProductReviewsController,
      ProductReviewResponse?> createElement() {
    return _ProductReviewsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductReviewsControllerProvider &&
        other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProductReviewsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ProductReviewResponse?> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductReviewsControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<ProductReviewsController,
        ProductReviewResponse?> with ProductReviewsControllerRef {
  _ProductReviewsControllerProviderElement(super.provider);

  @override
  String get productId =>
      (origin as ProductReviewsControllerProvider).productId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
