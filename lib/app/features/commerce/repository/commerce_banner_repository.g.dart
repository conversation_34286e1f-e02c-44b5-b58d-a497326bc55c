// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commerce_banner_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$commerceBannerRepositoryHash() =>
    r'e09957e3691c4a79c75d6f521dec2e9ae8f3f2fe';

/// See also [commerceBannerRepository].
@ProviderFor(commerceBannerRepository)
final commerceBannerRepositoryProvider =
    Provider<CommerceBannerRepository>.internal(
  commerceBannerRepository,
  name: r'commerceBannerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$commerceBannerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CommerceBannerRepositoryRef = ProviderRef<CommerceBannerRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
