import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'commerce_banner_repository.g.dart';

@Riverpod(keepAlive: true)
CommerceBannerRepository commerceBannerRepository(
  CommerceBannerRepositoryRef ref,
) =>
    CommerceBannerRepository(ref);

class CommerceBannerRepository {
  CommerceBannerRepository(this.ref);
  final CommerceBannerRepositoryRef ref;

  Future<CommerceBannerResponse> fetchBanners(String? name) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
        '/cms/shop-banners',
        queryParameters: {
          'banner_name': name,
        },
      );
      return CommerceBannerResponse.fromJson(response.data!);
    } catch (error, stack) {
      Groveman.warning('fetchBanners', error: error, stackTrace: stack);
      rethrow;
    }
  }
}
