// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collection_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$collectionsHash() => r'c7462083479f1dfbb76022399f0751f201a79f4d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [collections].
@ProviderFor(collections)
const collectionsProvider = CollectionsFamily();

/// See also [collections].
class CollectionsFamily
    extends Family<AsyncValue<List<CollectionWithMetafield>>> {
  /// See also [collections].
  const CollectionsFamily();

  /// See also [collections].
  CollectionsProvider call({
    bool? includeEmpty = true,
  }) {
    return CollectionsProvider(
      includeEmpty: includeEmpty,
    );
  }

  @override
  CollectionsProvider getProviderOverride(
    covariant CollectionsProvider provider,
  ) {
    return call(
      includeEmpty: provider.includeEmpty,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'collectionsProvider';
}

/// See also [collections].
class CollectionsProvider
    extends AutoDisposeFutureProvider<List<CollectionWithMetafield>> {
  /// See also [collections].
  CollectionsProvider({
    bool? includeEmpty = true,
  }) : this._internal(
          (ref) => collections(
            ref as CollectionsRef,
            includeEmpty: includeEmpty,
          ),
          from: collectionsProvider,
          name: r'collectionsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$collectionsHash,
          dependencies: CollectionsFamily._dependencies,
          allTransitiveDependencies:
              CollectionsFamily._allTransitiveDependencies,
          includeEmpty: includeEmpty,
        );

  CollectionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.includeEmpty,
  }) : super.internal();

  final bool? includeEmpty;

  @override
  Override overrideWith(
    FutureOr<List<CollectionWithMetafield>> Function(CollectionsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CollectionsProvider._internal(
        (ref) => create(ref as CollectionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        includeEmpty: includeEmpty,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CollectionWithMetafield>>
      createElement() {
    return _CollectionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CollectionsProvider && other.includeEmpty == includeEmpty;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, includeEmpty.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CollectionsRef
    on AutoDisposeFutureProviderRef<List<CollectionWithMetafield>> {
  /// The parameter `includeEmpty` of this provider.
  bool? get includeEmpty;
}

class _CollectionsProviderElement
    extends AutoDisposeFutureProviderElement<List<CollectionWithMetafield>>
    with CollectionsRef {
  _CollectionsProviderElement(super.provider);

  @override
  bool? get includeEmpty => (origin as CollectionsProvider).includeEmpty;
}

String _$collectionByIdHash() => r'4378e3a71bc7d5c653bc14bf3a491701edd28a26';

/// See also [collectionById].
@ProviderFor(collectionById)
const collectionByIdProvider = CollectionByIdFamily();

/// See also [collectionById].
class CollectionByIdFamily extends Family<AsyncValue<Collection?>> {
  /// See also [collectionById].
  const CollectionByIdFamily();

  /// See also [collectionById].
  CollectionByIdProvider call(
    String collectionId,
  ) {
    return CollectionByIdProvider(
      collectionId,
    );
  }

  @override
  CollectionByIdProvider getProviderOverride(
    covariant CollectionByIdProvider provider,
  ) {
    return call(
      provider.collectionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'collectionByIdProvider';
}

/// See also [collectionById].
class CollectionByIdProvider extends AutoDisposeFutureProvider<Collection?> {
  /// See also [collectionById].
  CollectionByIdProvider(
    String collectionId,
  ) : this._internal(
          (ref) => collectionById(
            ref as CollectionByIdRef,
            collectionId,
          ),
          from: collectionByIdProvider,
          name: r'collectionByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$collectionByIdHash,
          dependencies: CollectionByIdFamily._dependencies,
          allTransitiveDependencies:
              CollectionByIdFamily._allTransitiveDependencies,
          collectionId: collectionId,
        );

  CollectionByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.collectionId,
  }) : super.internal();

  final String collectionId;

  @override
  Override overrideWith(
    FutureOr<Collection?> Function(CollectionByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CollectionByIdProvider._internal(
        (ref) => create(ref as CollectionByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        collectionId: collectionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Collection?> createElement() {
    return _CollectionByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CollectionByIdProvider &&
        other.collectionId == collectionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, collectionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CollectionByIdRef on AutoDisposeFutureProviderRef<Collection?> {
  /// The parameter `collectionId` of this provider.
  String get collectionId;
}

class _CollectionByIdProviderElement
    extends AutoDisposeFutureProviderElement<Collection?>
    with CollectionByIdRef {
  _CollectionByIdProviderElement(super.provider);

  @override
  String get collectionId => (origin as CollectionByIdProvider).collectionId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
