import 'package:gomama/app/features/commerce/collections/model/collection_with_metafield.dart';
import 'package:gomama/app/features/commerce/collections/model/collections_with_metafield.dart';
import 'package:gomama/app/features/commerce/collections/query/get_all_collections_with_metafield.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'collection_providers.g.dart';

// selected collection title
final selectedCollectionProvider =
    StateProvider.autoDispose<String?>((ref) => null);

@riverpod
Future<List<CollectionWithMetafield>> collections(
  CollectionsRef ref, {
  bool? includeEmpty = true,
}) async {
  try {
    final response = await ShopifyCustom.instance.customQuery(
      gqlQuery: getAllCollectionsWithMetafieldsQuery,
    );
    final tempCollections = CollectionsWithMetafield.fromGraphJson(
      response?['collections'] as Map<String, dynamic>,
    );

    // Filter collections based on criteria
    final filteredCollections =
        tempCollections.collectionList.where((collectionWithMeta) {
      // Always filter out hidden collections
      if (collectionWithMeta.hidden == true) {
        return false;
      }

      // If includeEmpty is false, only include collections with products
      if (includeEmpty == false) {
        return (collectionWithMeta.productsCount ?? 0) > 0;
      }

      // Otherwise include all non-hidden collections
      return true;
    }).toList()
          // Finally sort by rank (null ranks go last)
          ..sort((a, b) {
            // If both have ranks, compare them
            if (a.rank != null && b.rank != null) {
              return a.rank!.compareTo(b.rank!);
            }
            // If a has rank and b doesn't, a goes first
            if (a.rank != null) {
              return -1;
            }
            // If b has rank and a doesn't, b goes first
            if (b.rank != null) {
              return 1;
            }
            // If both are null, keep original order
            return 0;
          });

    return filteredCollections;
  } catch (error, stackTrace) {
    Groveman.error('collections', error: error, stackTrace: stackTrace);
    rethrow;
  }
}

@riverpod
Future<Collection?> collectionById(
  CollectionByIdRef ref,
  String collectionId,
) async {
  final shopifyStore = ShopifyStore.instance;
  try {
    return await shopifyStore.getCollectionById(collectionId);
  } catch (error) {
    Groveman.error('collectionById', error: error);
    rethrow;
  }
}
