// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collection_with_metafield.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CollectionWithMetafieldImpl _$$CollectionWithMetafieldImplFromJson(
        Map<String, dynamic> json) =>
    _$CollectionWithMetafieldImpl(
      collection:
          Collection.fromJson(json['collection'] as Map<String, dynamic>),
      hidden: json['hidden'] as bool?,
      rank: (json['rank'] as num?)?.toInt(),
      productsCount: (json['products_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CollectionWithMetafieldImplToJson(
        _$CollectionWithMetafieldImpl instance) =>
    <String, dynamic>{
      'collection': instance.collection.toJson(),
      'hidden': instance.hidden,
      'rank': instance.rank,
      'products_count': instance.productsCount,
    };
