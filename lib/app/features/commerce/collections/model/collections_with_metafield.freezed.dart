// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'collections_with_metafield.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CollectionsWithMetafield _$CollectionsWithMetafieldFromJson(
    Map<String, dynamic> json) {
  return _CollectionsWithMetafield.fromJson(json);
}

/// @nodoc
mixin _$CollectionsWithMetafield {
  List<CollectionWithMetafield> get collectionList =>
      throw _privateConstructorUsedError;
  bool get hasNextPage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CollectionsWithMetafieldCopyWith<CollectionsWithMetafield> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CollectionsWithMetafieldCopyWith<$Res> {
  factory $CollectionsWithMetafieldCopyWith(CollectionsWithMetafield value,
          $Res Function(CollectionsWithMetafield) then) =
      _$CollectionsWithMetafieldCopyWithImpl<$Res, CollectionsWithMetafield>;
  @useResult
  $Res call({List<CollectionWithMetafield> collectionList, bool hasNextPage});
}

/// @nodoc
class _$CollectionsWithMetafieldCopyWithImpl<$Res,
        $Val extends CollectionsWithMetafield>
    implements $CollectionsWithMetafieldCopyWith<$Res> {
  _$CollectionsWithMetafieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collectionList = null,
    Object? hasNextPage = null,
  }) {
    return _then(_value.copyWith(
      collectionList: null == collectionList
          ? _value.collectionList
          : collectionList // ignore: cast_nullable_to_non_nullable
              as List<CollectionWithMetafield>,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CollectionsWithMetafieldImplCopyWith<$Res>
    implements $CollectionsWithMetafieldCopyWith<$Res> {
  factory _$$CollectionsWithMetafieldImplCopyWith(
          _$CollectionsWithMetafieldImpl value,
          $Res Function(_$CollectionsWithMetafieldImpl) then) =
      __$$CollectionsWithMetafieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CollectionWithMetafield> collectionList, bool hasNextPage});
}

/// @nodoc
class __$$CollectionsWithMetafieldImplCopyWithImpl<$Res>
    extends _$CollectionsWithMetafieldCopyWithImpl<$Res,
        _$CollectionsWithMetafieldImpl>
    implements _$$CollectionsWithMetafieldImplCopyWith<$Res> {
  __$$CollectionsWithMetafieldImplCopyWithImpl(
      _$CollectionsWithMetafieldImpl _value,
      $Res Function(_$CollectionsWithMetafieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collectionList = null,
    Object? hasNextPage = null,
  }) {
    return _then(_$CollectionsWithMetafieldImpl(
      collectionList: null == collectionList
          ? _value._collectionList
          : collectionList // ignore: cast_nullable_to_non_nullable
              as List<CollectionWithMetafield>,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CollectionsWithMetafieldImpl implements _CollectionsWithMetafield {
  _$CollectionsWithMetafieldImpl(
      {required final List<CollectionWithMetafield> collectionList,
      required this.hasNextPage})
      : _collectionList = collectionList;

  factory _$CollectionsWithMetafieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$CollectionsWithMetafieldImplFromJson(json);

  final List<CollectionWithMetafield> _collectionList;
  @override
  List<CollectionWithMetafield> get collectionList {
    if (_collectionList is EqualUnmodifiableListView) return _collectionList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_collectionList);
  }

  @override
  final bool hasNextPage;

  @override
  String toString() {
    return 'CollectionsWithMetafield(collectionList: $collectionList, hasNextPage: $hasNextPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CollectionsWithMetafieldImpl &&
            const DeepCollectionEquality()
                .equals(other._collectionList, _collectionList) &&
            (identical(other.hasNextPage, hasNextPage) ||
                other.hasNextPage == hasNextPage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_collectionList), hasNextPage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CollectionsWithMetafieldImplCopyWith<_$CollectionsWithMetafieldImpl>
      get copyWith => __$$CollectionsWithMetafieldImplCopyWithImpl<
          _$CollectionsWithMetafieldImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CollectionsWithMetafieldImplToJson(
      this,
    );
  }
}

abstract class _CollectionsWithMetafield implements CollectionsWithMetafield {
  factory _CollectionsWithMetafield(
      {required final List<CollectionWithMetafield> collectionList,
      required final bool hasNextPage}) = _$CollectionsWithMetafieldImpl;

  factory _CollectionsWithMetafield.fromJson(Map<String, dynamic> json) =
      _$CollectionsWithMetafieldImpl.fromJson;

  @override
  List<CollectionWithMetafield> get collectionList;
  @override
  bool get hasNextPage;
  @override
  @JsonKey(ignore: true)
  _$$CollectionsWithMetafieldImplCopyWith<_$CollectionsWithMetafieldImpl>
      get copyWith => throw _privateConstructorUsedError;
}
