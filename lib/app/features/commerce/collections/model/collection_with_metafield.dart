import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shopify_flutter/models/models.dart';

part 'collection_with_metafield.freezed.dart';
part 'collection_with_metafield.g.dart';

@freezed
class CollectionWithMetafield with _$CollectionWithMetafield {
  factory CollectionWithMetafield({
    required Collection collection,
    bool? hidden,
    int? rank,
    int? productsCount,
  }) = _CollectionWithMetafield;

  factory CollectionWithMetafield.fromGraphJson(Map<String, dynamic> json) {
    final collection = Collection.fromGraphJson(json);
    final nodeJson = json['node'] as Map<String, dynamic>;

    bool? hidden;
    int? rank;
    int? productsCount;

    if (nodeJson['hidden'] != null) {
      hidden =
          (nodeJson['hidden']['value']?.toString() ?? 'false').toLowerCase() ==
              'true';
    }
    if (nodeJson['rank'] != null) {
      rank = int.tryParse(nodeJson['rank']['value']?.toString() ?? '0');
    }
    if (nodeJson['products'] != null) {
      final productsJson = nodeJson['products'] as Map<String, dynamic>;
      if (productsJson['nodes'] != null) {
        final nodesJson = productsJson['nodes'] as List<dynamic>;
        productsCount = nodesJson.length;
      }
    }

    return CollectionWithMetafield(
      collection: collection,
      hidden: hidden,
      rank: rank,
      productsCount: productsCount,
    );
  }

  factory CollectionWithMetafield.fromJson(Map<String, dynamic> json) =>
      _$CollectionWithMetafieldFromJson(json);
}
