import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/features/commerce/collections/model/collection_with_metafield.dart';

part 'collections_with_metafield.freezed.dart';
part 'collections_with_metafield.g.dart';

@freezed
class CollectionsWithMetafield with _$CollectionsWithMetafield {
  factory CollectionsWithMetafield({
    required List<CollectionWithMetafield> collectionList,
    required bool hasNextPage,
  }) = _CollectionsWithMetafield;

  factory CollectionsWithMetafield.fromJson(Map<String, dynamic> json) =>
      _$CollectionsWithMetafieldFromJson(json);

  factory CollectionsWithMetafield.fromGraphJson(Map<String, dynamic> json) {
    final collectionList = <CollectionWithMetafield>[];

    json['edges']?.forEach((e) {
      if (e != null) {
        collectionList.add(
          CollectionWithMetafield.fromGraphJson(e as Map<String, dynamic>),
        );
      }
    });

    return CollectionsWithMetafield(
      collectionList: collectionList,
      hasNextPage:
          (json['pageInfo'] as Map<String, dynamic>)['hasNextPage'] == true,
    );
  }
}
