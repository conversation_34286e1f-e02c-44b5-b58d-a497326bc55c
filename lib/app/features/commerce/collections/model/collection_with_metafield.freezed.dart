// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'collection_with_metafield.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CollectionWithMetafield _$CollectionWithMetafieldFromJson(
    Map<String, dynamic> json) {
  return _CollectionWithMetafield.fromJson(json);
}

/// @nodoc
mixin _$CollectionWithMetafield {
  Collection get collection => throw _privateConstructorUsedError;
  bool? get hidden => throw _privateConstructorUsedError;
  int? get rank => throw _privateConstructorUsedError;
  int? get productsCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CollectionWithMetafieldCopyWith<CollectionWithMetafield> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CollectionWithMetafieldCopyWith<$Res> {
  factory $CollectionWithMetafieldCopyWith(CollectionWithMetafield value,
          $Res Function(CollectionWithMetafield) then) =
      _$CollectionWithMetafieldCopyWithImpl<$Res, CollectionWithMetafield>;
  @useResult
  $Res call(
      {Collection collection, bool? hidden, int? rank, int? productsCount});

  $CollectionCopyWith<$Res> get collection;
}

/// @nodoc
class _$CollectionWithMetafieldCopyWithImpl<$Res,
        $Val extends CollectionWithMetafield>
    implements $CollectionWithMetafieldCopyWith<$Res> {
  _$CollectionWithMetafieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collection = null,
    Object? hidden = freezed,
    Object? rank = freezed,
    Object? productsCount = freezed,
  }) {
    return _then(_value.copyWith(
      collection: null == collection
          ? _value.collection
          : collection // ignore: cast_nullable_to_non_nullable
              as Collection,
      hidden: freezed == hidden
          ? _value.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      rank: freezed == rank
          ? _value.rank
          : rank // ignore: cast_nullable_to_non_nullable
              as int?,
      productsCount: freezed == productsCount
          ? _value.productsCount
          : productsCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CollectionCopyWith<$Res> get collection {
    return $CollectionCopyWith<$Res>(_value.collection, (value) {
      return _then(_value.copyWith(collection: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CollectionWithMetafieldImplCopyWith<$Res>
    implements $CollectionWithMetafieldCopyWith<$Res> {
  factory _$$CollectionWithMetafieldImplCopyWith(
          _$CollectionWithMetafieldImpl value,
          $Res Function(_$CollectionWithMetafieldImpl) then) =
      __$$CollectionWithMetafieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Collection collection, bool? hidden, int? rank, int? productsCount});

  @override
  $CollectionCopyWith<$Res> get collection;
}

/// @nodoc
class __$$CollectionWithMetafieldImplCopyWithImpl<$Res>
    extends _$CollectionWithMetafieldCopyWithImpl<$Res,
        _$CollectionWithMetafieldImpl>
    implements _$$CollectionWithMetafieldImplCopyWith<$Res> {
  __$$CollectionWithMetafieldImplCopyWithImpl(
      _$CollectionWithMetafieldImpl _value,
      $Res Function(_$CollectionWithMetafieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collection = null,
    Object? hidden = freezed,
    Object? rank = freezed,
    Object? productsCount = freezed,
  }) {
    return _then(_$CollectionWithMetafieldImpl(
      collection: null == collection
          ? _value.collection
          : collection // ignore: cast_nullable_to_non_nullable
              as Collection,
      hidden: freezed == hidden
          ? _value.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      rank: freezed == rank
          ? _value.rank
          : rank // ignore: cast_nullable_to_non_nullable
              as int?,
      productsCount: freezed == productsCount
          ? _value.productsCount
          : productsCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CollectionWithMetafieldImpl implements _CollectionWithMetafield {
  _$CollectionWithMetafieldImpl(
      {required this.collection, this.hidden, this.rank, this.productsCount});

  factory _$CollectionWithMetafieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$CollectionWithMetafieldImplFromJson(json);

  @override
  final Collection collection;
  @override
  final bool? hidden;
  @override
  final int? rank;
  @override
  final int? productsCount;

  @override
  String toString() {
    return 'CollectionWithMetafield(collection: $collection, hidden: $hidden, rank: $rank, productsCount: $productsCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CollectionWithMetafieldImpl &&
            (identical(other.collection, collection) ||
                other.collection == collection) &&
            (identical(other.hidden, hidden) || other.hidden == hidden) &&
            (identical(other.rank, rank) || other.rank == rank) &&
            (identical(other.productsCount, productsCount) ||
                other.productsCount == productsCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, collection, hidden, rank, productsCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CollectionWithMetafieldImplCopyWith<_$CollectionWithMetafieldImpl>
      get copyWith => __$$CollectionWithMetafieldImplCopyWithImpl<
          _$CollectionWithMetafieldImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CollectionWithMetafieldImplToJson(
      this,
    );
  }
}

abstract class _CollectionWithMetafield implements CollectionWithMetafield {
  factory _CollectionWithMetafield(
      {required final Collection collection,
      final bool? hidden,
      final int? rank,
      final int? productsCount}) = _$CollectionWithMetafieldImpl;

  factory _CollectionWithMetafield.fromJson(Map<String, dynamic> json) =
      _$CollectionWithMetafieldImpl.fromJson;

  @override
  Collection get collection;
  @override
  bool? get hidden;
  @override
  int? get rank;
  @override
  int? get productsCount;
  @override
  @JsonKey(ignore: true)
  _$$CollectionWithMetafieldImplCopyWith<_$CollectionWithMetafieldImpl>
      get copyWith => throw _privateConstructorUsedError;
}
