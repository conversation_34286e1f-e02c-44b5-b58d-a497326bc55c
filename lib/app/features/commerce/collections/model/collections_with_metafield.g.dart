// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collections_with_metafield.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CollectionsWithMetafieldImpl _$$CollectionsWithMetafieldImplFromJson(
        Map<String, dynamic> json) =>
    _$CollectionsWithMetafieldImpl(
      collectionList: (json['collection_list'] as List<dynamic>)
          .map((e) =>
              CollectionWithMetafield.fromJson(e as Map<String, dynamic>))
          .toList(),
      hasNextPage: json['has_next_page'] as bool,
    );

Map<String, dynamic> _$$CollectionsWithMetafieldImplToJson(
        _$CollectionsWithMetafieldImpl instance) =>
    <String, dynamic>{
      'collection_list':
          instance.collectionList.map((e) => e.toJson()).toList(),
      'has_next_page': instance.hasNextPage,
    };
