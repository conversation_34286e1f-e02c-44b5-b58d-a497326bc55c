import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_button.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_card.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CollectionView extends ConsumerWidget {
  const CollectionView(this.id, {super.key});

  final String id;
  static const routeName = 'collection-view';
  static const routePath = 'collections/:id';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final collection = ref.watch(collectionByIdProvider(id));
    final products = ref.watch(collectionProductsProvider(id));

    return collection.when(
      data: (collection) {
        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: CustomColors.primary,
            foregroundColor: Colors.white,
            title: Text(collection?.title ?? ''),
          ),
          body: products.when(
            data: (products) {
              return GridView.count(
                shrinkWrap: true,
                padding: const EdgeInsets.fromLTRB(6, 24, 6, 36),
                crossAxisCount: 2,
                childAspectRatio: 180 / 290,
                crossAxisSpacing: 10,
                mainAxisSpacing: 14,
                children: products.map((product) {
                  return ProductCard(product);
                }).toList(),
              );
            },
            error: (error, stackTrace) => Center(
              child: Text(
                'This collection is empty',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
          ),
        );
      },
      error: (error, stackTrace) {
        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            backgroundColor: CustomColors.primary,
            foregroundColor: Colors.white,
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "Sorry, we couldn't find this collection",
                ),
                const SizedBox(height: 16),
                FilledButton(
                  style: FilledButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  onPressed: () => ref.invalidate(collectionByIdProvider(id)),
                  child: const Text('Try again'),
                ),
              ],
            ),
          ),
        );
      },
      loading: () {
        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            backgroundColor: CustomColors.primary,
            foregroundColor: Colors.white,
          ),
          body: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}
