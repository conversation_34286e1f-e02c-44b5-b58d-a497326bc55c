import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/collections/model/collection_with_metafield.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_card.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// A skeleton container widget that shows a shimmer loading effect
class _SkeletonContainer extends StatefulWidget {
  const _SkeletonContainer({
    this.height,
    this.width = double.infinity,
    this.borderRadius,
  });

  final double? height;
  final double width;
  final BorderRadius? borderRadius;

  @override
  State<_SkeletonContainer> createState() => _SkeletonContainerState();
}

class _SkeletonContainerState extends State<_SkeletonContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = Tween<double>(begin: -2, end: 2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          height: widget.height,
          width: widget.width < 1
              ? MediaQuery.of(context).size.width * widget.width
              : widget.width,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: [
                _animation.value.clamp(0.0, 1.0) - 1,
                _animation.value.clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0) + 1,
              ],
            ),
          ),
        );
      },
    );
  }
}

class CollectionProductList extends ConsumerWidget {
  const CollectionProductList(this.collectionWithMetafield, {super.key});

  final CollectionWithMetafield collectionWithMetafield;

  // Build a skeleton loading card for products
  Widget _buildProductCardSkeleton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image skeleton
          AspectRatio(
            aspectRatio: 1,
            child: _SkeletonContainer(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product title skeleton - two lines
                _SkeletonContainer(height: 16),
                SizedBox(height: 4),
                _SkeletonContainer(height: 16, width: 0.7),
                SizedBox(height: 8),
                // Product price skeleton
                _SkeletonContainer(height: 20, width: 0.4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final collection = collectionWithMetafield.collection;
    // Use the provider with the default limit of 4 products
    final products =
        ref.watch(collectionProductsProvider(collection.id, limit: 4));

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: products.when(
        data: (products) {
          if (products.isEmpty) return const SizedBox.shrink();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        collection.title,
                        style: textTheme(context)
                            .titleLarge!
                            .copyWith(color: CustomColors.primary),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        CollectionRoute(collection.id).push(context);
                      },
                      child: Row(
                        children: [
                          Text(
                            'Explore More',
                            style: textTheme(context).labelLarge!.copyWith(
                                  color: Colors.grey,
                                  decoration: TextDecoration.underline,
                                ),
                          ),
                          const Icon(
                            Icons.chevron_right,
                            color: Colors.grey,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 14),
              // product list
              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 2,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                childAspectRatio: 180 / 290,
                crossAxisSpacing: 10,
                mainAxisSpacing: 14,
                children: products.map((product) {
                  return ProductCard(product);
                }).toList(),
              ),
              const SizedBox(height: 24),
            ],
          );
        },
        error: (error, stackTrace) {
          return const SizedBox.shrink();
        },
        loading: () {
          // Skeleton loading view that matches the product grid layout
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header skeleton
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Collection title skeleton
                    _SkeletonContainer(height: 24, width: 120),
                    // Explore more skeleton
                    Row(
                      children: [
                        _SkeletonContainer(height: 16, width: 80),
                        SizedBox(width: 4),
                        _SkeletonContainer(
                            height: 16,
                            width: 16,
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 14),
              // Product grid skeleton
              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 2,
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                childAspectRatio: 180 / 290,
                crossAxisSpacing: 10,
                mainAxisSpacing: 14,
                children:
                    List.generate(4, (index) => _buildProductCardSkeleton()),
              ),
              const SizedBox(height: 24),
            ],
          );
        },
      ),
    );
  }
}
