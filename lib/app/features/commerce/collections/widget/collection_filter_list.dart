import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/collections/model/collection_with_metafield.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CollectionFilterList extends ConsumerWidget {
  const CollectionFilterList({
    required this.collections,
    super.key,
  });

  final List<CollectionWithMetafield> collections;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ScrollController();
    final selectedCollection = ref.watch(selectedCollectionProvider);

    return SizedBox(
      height: 64, // Increased height to accommodate scrollbar
      child: RawScrollbar(
        controller: controller,
        padding: const EdgeInsets.symmetric(horizontal: 128),
        thickness: 8,
        trackVisibility: true,
        trackColor: CustomColors.primary.withValues(alpha: 0.2),
        trackRadius: const Radius.circular(3),
        thumbVisibility: true,
        thumbColor: CustomColors.primary,
        minThumbLength: 36,
        radius: const Radius.circular(3),
        interactive: true,
        child: ListView.builder(
          controller: controller,
          scrollDirection: Axis.horizontal,
          // adjust bottom padding to control spacing between scrollbar
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
          // Add 1 to itemCount for the 'All' chip
          itemCount: collections.length + 1,
          itemBuilder: (context, index) {
            // Special case for the 'All' chip at index 0
            if (index == 0) {
              final isSelected = selectedCollection == null;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  selected: isSelected,
                  showCheckmark: false,
                  backgroundColor: Colors.white,
                  selectedColor: CustomColors.primary,
                  side: const BorderSide(color: CustomColors.primary),
                  shadowColor: Colors.black.withOpacity(0.3),
                  elevation: 3,
                  label: Text(
                    'All',
                    style: TextStyle(
                      color: isSelected ? Colors.white : CustomColors.primary,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  onSelected: (_) {
                    // Set filter to null to show all collections
                    ref.read(selectedCollectionProvider.notifier).state = null;
                  },
                ),
              );
            }

            // Adjust index for the collection items (subtract 1 because of the 'All' chip)
            final collection = collections[index - 1].collection;
            final isSelected = selectedCollection == collection.id;

            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                selected: isSelected,
                showCheckmark: false,
                backgroundColor: Colors.white,
                selectedColor: CustomColors.primary,
                side: const BorderSide(color: CustomColors.primary),
                shadowColor: Colors.black.withValues(alpha: 0.8),
                elevation: 3,
                label: Text(
                  collection.title,
                  style: TextStyle(
                    color: isSelected ? Colors.white : CustomColors.primary,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                onSelected: (selected) {
                  // Toggle selection
                  if (selected) {
                    ref.read(selectedCollectionProvider.notifier).state =
                        collection.id;
                  } else if (selectedCollection == collection.id) {
                    // Clear selection if tapping the already selected item
                    ref.read(selectedCollectionProvider.notifier).state = null;
                  }
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
