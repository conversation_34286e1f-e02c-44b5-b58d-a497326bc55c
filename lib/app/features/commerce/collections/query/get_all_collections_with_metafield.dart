/// Query to get all collections with metafield
const String getAllCollectionsWithMetafieldsQuery = r'''
query ($cursor: String, $sortKey: CollectionSortKeys, $reverse: Boolean) {
  collections(
    first: 250
    after: $cursor
    sortKey: $sortKey
    reverse: $reverse
  ) {
    pageInfo {
      hasNextPage
    }
    edges {
      cursor
      node {
        title
        description
        descriptionHtml
        handle
        id
        updatedAt
        image {
          altText
          id
          originalSrc
        }
      	hidden: metafield(key: "hidden", namespace: "custom") {
          value
        }
        rank: metafield(key: "rank", namespace: "custom") {
          value
        }
        products(first: 4) {
          nodes {
            id
          }
        }
      }
    }
  }
}
''';
