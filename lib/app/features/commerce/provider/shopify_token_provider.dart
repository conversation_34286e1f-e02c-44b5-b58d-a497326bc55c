import 'package:groveman/groveman.dart';
import 'package:shopify_flutter/shopify/src/shopify_auth.dart';

// return shopify access token
// also login with email and password if token is expired
Future<String?> getShopifyAccessToken({
  required String email,
  required String password,
}) async {
  final shopifyAuth = ShopifyAuth.instance;

  try {
    final isTokenExpired = await shopifyAuth.isAccessTokenExpired;
    if (isTokenExpired == true) {
      await shopifyAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    }
    return await shopifyAuth.currentCustomerAccessToken;
  } catch (error) {
    Groveman.warning('getShopifyAccessToken', error: error);
    rethrow;
  }
}
