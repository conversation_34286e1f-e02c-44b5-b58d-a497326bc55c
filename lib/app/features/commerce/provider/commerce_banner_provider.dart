import 'package:gomama/app/features/commerce/model/commerce_banner.dart';
import 'package:gomama/app/features/commerce/repository/commerce_banner_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'commerce_banner_provider.g.dart';

@riverpod
Future<List<CommerceBanner>?> commerceBanners(
  CommerceBannersRef ref,
  String? bannerType,
) async {
  try {
    final response = await ref
        .watch(commerceBannerRepositoryProvider)
        .fetchBanners(bannerType);
    return response.data;
  } catch (e) {
    Groveman.error('commerceBanner', error: e);
    return null;
  }
}
