// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commerce_banner_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$commerceBannersHash() => r'fc4fa2c2f56ab35c621af4c8090a2760e0039ae7';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [commerceBanners].
@ProviderFor(commerceBanners)
const commerceBannersProvider = CommerceBannersFamily();

/// See also [commerceBanners].
class CommerceBannersFamily extends Family<AsyncValue<List<CommerceBanner>?>> {
  /// See also [commerceBanners].
  const CommerceBannersFamily();

  /// See also [commerceBanners].
  CommerceBannersProvider call(
    String? bannerType,
  ) {
    return CommerceBannersProvider(
      bannerType,
    );
  }

  @override
  CommerceBannersProvider getProviderOverride(
    covariant CommerceBannersProvider provider,
  ) {
    return call(
      provider.bannerType,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'commerceBannersProvider';
}

/// See also [commerceBanners].
class CommerceBannersProvider
    extends AutoDisposeFutureProvider<List<CommerceBanner>?> {
  /// See also [commerceBanners].
  CommerceBannersProvider(
    String? bannerType,
  ) : this._internal(
          (ref) => commerceBanners(
            ref as CommerceBannersRef,
            bannerType,
          ),
          from: commerceBannersProvider,
          name: r'commerceBannersProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$commerceBannersHash,
          dependencies: CommerceBannersFamily._dependencies,
          allTransitiveDependencies:
              CommerceBannersFamily._allTransitiveDependencies,
          bannerType: bannerType,
        );

  CommerceBannersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.bannerType,
  }) : super.internal();

  final String? bannerType;

  @override
  Override overrideWith(
    FutureOr<List<CommerceBanner>?> Function(CommerceBannersRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CommerceBannersProvider._internal(
        (ref) => create(ref as CommerceBannersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        bannerType: bannerType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CommerceBanner>?> createElement() {
    return _CommerceBannersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CommerceBannersProvider && other.bannerType == bannerType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, bannerType.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CommerceBannersRef
    on AutoDisposeFutureProviderRef<List<CommerceBanner>?> {
  /// The parameter `bannerType` of this provider.
  String? get bannerType;
}

class _CommerceBannersProviderElement
    extends AutoDisposeFutureProviderElement<List<CommerceBanner>?>
    with CommerceBannersRef {
  _CommerceBannersProviderElement(super.provider);

  @override
  String? get bannerType => (origin as CommerceBannersProvider).bannerType;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
