import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/search/constants/filter_constants.dart';
import 'package:gomama/app/features/commerce/search/model/commerce_filter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FilterButton extends HookConsumerWidget {
  const FilterButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(searchFilterProvider);
    final priceRange = useState<FilterPriceRange?>(filters.priceRange);

    return IconButton(
      onPressed: () {
        showModalBottomSheet<void>(
          context: context,
          showDragHandle: true,
          isScrollControlled: true,
          backgroundColor: CustomColors.secondaryLight,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
          ),
          builder: (BuildContext context) {
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      'Filter',
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                            color: CustomColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    _AgeGroupSection(
                      label: 'Age',
                      options: FilterConstants.ageGroups
                          .map(
                            (label) => FilterOption(label: label, value: label),
                          )
                          .toList(),
                    ),
                    const SizedBox(height: 16),
                    _CategorySection(
                      label: 'Categories',
                      options: FilterConstants.categories
                          .map(
                            (label) => FilterOption(label: label, value: label),
                          )
                          .toList(),
                    ),
                    const SizedBox(height: 16),
                    _PriceRangeSection(priceRange),
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SizedBox(
                            width: 100,
                            child: OutlinedButton(
                              style: OutlinedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                              ),
                              onPressed: () {
                                priceRange.value = null;
                                ref.read(searchFilterProvider.notifier).update(
                                      (state) => state.copyWith(
                                        ageGroup: null,
                                        category: null,
                                        priceRange: null,
                                      ),
                                    );
                                ref.invalidate(queriedProductsProvider);
                                context.pop();
                              },
                              child: const Text('Clear'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          SizedBox(
                            width: 100,
                            child: FilledButton(
                              style: FilledButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                              ),
                              onPressed: () {
                                ref.read(searchFilterProvider.notifier).update(
                                      (state) => state.copyWith(
                                        priceRange: priceRange.value,
                                      ),
                                    );
                                ref.invalidate(queriedProductsProvider);
                                context.pop();
                              },
                              child: const Text('Apply'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
      icon: const Row(
        children: [
          Icon(Icons.tune, size: 16),
          SizedBox(width: 4),
          Text('Filter'),
        ],
      ),
    );
  }
}

class _AgeGroupSection extends ConsumerWidget {
  const _AgeGroupSection({required this.label, required this.options});

  final String label;
  final List<FilterOption> options;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(searchFilterProvider);

    return ExpansionTile(
      title: Text(
        label,
        style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: CustomColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
      initiallyExpanded: true,
      childrenPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      shape: const Border(),
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: options.map((option) {
                return ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: filters.ageGroup == option.value
                        ? CustomColors.primary
                        : Colors.transparent,
                    foregroundColor: filters.ageGroup == option.value
                        ? CustomColors.secondaryLight
                        : CustomColors.primary,
                    side: BorderSide(
                      color: filters.ageGroup == option.value
                          ? Colors.transparent
                          : CustomColors.primary,
                    ),
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                      horizontal: 16,
                    ),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    ref.read(searchFilterProvider.notifier).update(
                          (state) => state.copyWith(ageGroup: option.value),
                        );
                  },
                  child: Text(option.label),
                );
              }).toList(),
            ),
          ],
        ),
      ],
    );
  }
}

class _CategorySection extends ConsumerWidget {
  const _CategorySection({required this.label, required this.options});

  final String label;
  final List<FilterOption> options;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(searchFilterProvider);

    return ExpansionTile(
      title: Text(
        label,
        style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: CustomColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
      initiallyExpanded: true,
      childrenPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      shape: const Border(),
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: options.map((option) {
                return ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: filters.category == option.value
                        ? CustomColors.primary
                        : Colors.transparent,
                    foregroundColor: filters.category == option.value
                        ? CustomColors.secondaryLight
                        : CustomColors.primary,
                    side: BorderSide(
                      color: filters.category == option.value
                          ? Colors.transparent
                          : CustomColors.primary,
                    ),
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                      horizontal: 16,
                    ),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    ref.read(searchFilterProvider.notifier).update(
                          (state) => state.copyWith(category: option.value),
                        );
                  },
                  child: Text(option.label),
                );
              }).toList(),
            ),
          ],
        ),
      ],
    );
  }
}

class _PriceRangeSection extends HookConsumerWidget {
  const _PriceRangeSection(this.priceRange);

  final ValueNotifier<FilterPriceRange?> priceRange;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ExpansionTile(
      title: Text(
        'Price',
        style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: CustomColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
      initiallyExpanded: true,
      childrenPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      shape: const Border(),
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: priceRange.value?.min?.toString() ?? '',
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'Minimum',
                  hintStyle: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.placeholder,
                      ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 6,
                    horizontal: 16,
                  ),
                ),
                onChanged: (value) {
                  priceRange.value = priceRange.value == null
                      ? FilterPriceRange(min: double.parse(value))
                      : priceRange.value?.copyWith(min: double.parse(value));
                },
              ),
            ),
            SizedBox(
              width: 36,
              child: Divider(
                thickness: 2,
                color: Colors.grey.shade400,
                indent: 6,
                endIndent: 6,
              ),
            ),
            Expanded(
              child: TextFormField(
                initialValue: priceRange.value?.max?.toString() ?? '',
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'Maximum',
                  hintStyle: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.placeholder,
                      ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(32),
                    borderSide: const BorderSide(
                      color: CustomColors.placeholder,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 6,
                    horizontal: 16,
                  ),
                ),
                onChanged: (value) {
                  priceRange.value = priceRange.value == null
                      ? FilterPriceRange(max: double.parse(value))
                      : priceRange.value?.copyWith(max: double.parse(value));
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
