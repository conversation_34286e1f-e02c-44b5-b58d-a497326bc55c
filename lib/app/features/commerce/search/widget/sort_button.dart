import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/search/model/commerce_filter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/enums/src/sort_key_product.dart';

class SortButton extends ConsumerWidget {
  const SortButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(searchFilterProvider);
    // final sortTags = [
    //   'Popularity', // best selling
    //   // 'Top Rated', // TBD
    //   'Best Match', // relevance (default)
    //   'Go!Mama Certification', // 'verified' tag from shopify?
    //   // 'Recommended', // TBD
    //   'Featured', // 'featured' tag from shopify?
    // ];

    return IconButton(
      onPressed: () {
        showModalBottomSheet<void>(
          context: context,
          showDragHandle: true,
          backgroundColor: CustomColors.secondaryLight,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.5,
          ),
          builder: (BuildContext context) {
            return SizedBox(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    'Sort',
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          color: CustomColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        // no filters applied
                        ListTile(
                          title: Text(
                            'Best Match (Relevance)',
                            style: Theme.of(context)
                                .textTheme
                                .labelLarge!
                                .copyWith(
                                  color: CustomColors.primary,
                                ),
                          ),
                          leading: Icon(
                            filters.reservedTags == null &&
                                    (filters.priceRange != null ||
                                        filters.sortKey == null ||
                                        filters.sortKey ==
                                            SortKeyProduct.PRICE ||
                                        (filters.priceRange == null &&
                                            filters.sortKey ==
                                                SortKeyProduct.RELEVANCE))
                                ? Icons.radio_button_checked
                                : Icons.radio_button_off,
                            color: CustomColors.primary,
                          ),
                          visualDensity: VisualDensity.compact,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          onTap: () {
                            ref.read(searchFilterProvider.notifier).update(
                                  (state) => CommerceFilter(
                                    sortKey: SortKeyProduct.RELEVANCE,
                                  ),
                                );
                            ref.invalidate(queriedProductsProvider);
                            context.pop();
                          },
                        ),
                        // best selling
                        ListTile(
                          title: Text(
                            'Popularity',
                            style: Theme.of(context)
                                .textTheme
                                .labelLarge!
                                .copyWith(
                                  color: CustomColors.primary,
                                ),
                          ),
                          leading: Icon(
                            filters.reservedTags == null &&
                                    filters.priceRange == null &&
                                    filters.sortKey ==
                                        SortKeyProduct.BEST_SELLING
                                ? Icons.radio_button_checked
                                : Icons.radio_button_off,
                            color: CustomColors.primary,
                          ),
                          visualDensity: VisualDensity.compact,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          onTap: () {
                            ref.read(searchFilterProvider.notifier).update(
                                  (state) => CommerceFilter(
                                    sortKey: SortKeyProduct.BEST_SELLING,
                                  ),
                                );
                            ref.invalidate(queriedProductsProvider);
                            context.pop();
                          },
                        ),
                        // // certified
                        // ListTile(
                        //   title: Text(
                        //     'Go!Mama Certification',
                        //     style: Theme.of(context)
                        //         .textTheme
                        //         .labelLarge!
                        //         .copyWith(
                        //           color: CustomColors.primary,
                        //         ),
                        //   ),
                        //   leading: Icon(
                        //     filters.sortKey == null &&
                        //             filters.reservedTags == 'Go!Mama certified'
                        //         ? Icons.radio_button_checked
                        //         : Icons.radio_button_off,
                        //     color: CustomColors.primary,
                        //   ),
                        //   visualDensity: VisualDensity.compact,
                        //   contentPadding: const EdgeInsets.symmetric(
                        //     horizontal: 16,
                        //   ),
                        //   onTap: () {
                        //     ref.read(searchFilterProvider.notifier).update(
                        //           (state) => CommerceFilter(
                        //             reservedTags: 'Go!Mama certified',
                        //           ),
                        //         );
                        //     ref.invalidate(queriedProductsProvider);
                        //     context.pop();
                        //   },
                        // ),
                        // featured
                        ListTile(
                          title: Text(
                            'Featured',
                            style: Theme.of(context)
                                .textTheme
                                .labelLarge!
                                .copyWith(
                                  color: CustomColors.primary,
                                ),
                          ),
                          leading: Icon(
                            filters.sortKey == null &&
                                    filters.reservedTags == 'featured'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_off,
                            color: CustomColors.primary,
                          ),
                          visualDensity: VisualDensity.compact,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          onTap: () {
                            ref.read(searchFilterProvider.notifier).update(
                                  (state) => CommerceFilter(
                                    reservedTags: 'featured',
                                  ),
                                );
                            ref.invalidate(queriedProductsProvider);
                            context.pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
      icon: const Row(
        children: [
          Icon(Icons.arrow_drop_up),
          SizedBox(width: 4),
          Text('Sort'),
        ],
      ),
    );
  }
}
