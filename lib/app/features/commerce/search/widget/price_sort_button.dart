import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/search/model/commerce_filter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/enums/src/sort_key_product.dart';

class PriceSortButton extends HookConsumerWidget {
  const PriceSortButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isReverse = useState<bool?>(null);

    return IconButton(
      onPressed: () {
        if (isReverse.value == false) {
          isReverse.value = null;
          ref.read(searchFilterProvider.notifier).update(
                (state) => state.copyWith(
                  sortKey: null,
                  isPriceReverse: false,
                ),
              );
        } else {
          if (isReverse.value == null) {
            isReverse.value = true;
          } else if (isReverse.value == true) {
            isReverse.value = false;
          }
          ref.read(searchFilterProvider.notifier).update(
                (state) => state.copyWith(
                  sortKey: SortKeyProduct.PRICE,
                  isPriceReverse: isReverse.value,
                ),
              );
        }
        ref.invalidate(queriedProductsProvider);
      },
      icon: Row(
        children: [
          Icon(
            isReverse.value == null
                ? Icons.unfold_more
                : isReverse.value == true
                    ? Icons.expand_more
                    : Icons.expand_less,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            isReverse.value == null
                ? 'Price'
                : isReverse.value == true
                    ? 'High'
                    : 'Low',
          ),
        ],
      ),
    );
  }
}
