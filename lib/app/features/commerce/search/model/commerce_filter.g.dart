// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commerce_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FilterOptionImpl _$$FilterOptionImplFromJson(Map<String, dynamic> json) =>
    _$FilterOptionImpl(
      label: json['label'] as String,
      value: json['value'] as String,
    );

Map<String, dynamic> _$$FilterOptionImplToJson(_$FilterOptionImpl instance) =>
    <String, dynamic>{
      'label': instance.label,
      'value': instance.value,
    };

_$FilterPriceRangeImpl _$$FilterPriceRangeImplFromJson(
        Map<String, dynamic> json) =>
    _$FilterPriceRangeImpl(
      min: (json['min'] as num?)?.toDouble(),
      max: (json['max'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$FilterPriceRangeImplToJson(
        _$FilterPriceRangeImpl instance) =>
    <String, dynamic>{
      'min': instance.min,
      'max': instance.max,
    };

_$CommerceFilterImpl _$$CommerceFilterImplFromJson(Map<String, dynamic> json) =>
    _$CommerceFilterImpl(
      ageGroup: json['age_group'] as String?,
      category: json['category'] as String?,
      reservedTags: json['reserved_tags'] as String?,
      sortKey: $enumDecodeNullable(_$SortKeyProductEnumMap, json['sort_key']),
      priceRange: json['price_range'] == null
          ? null
          : FilterPriceRange.fromJson(
              json['price_range'] as Map<String, dynamic>),
      isPriceReverse: json['is_price_reverse'] as bool?,
    );

Map<String, dynamic> _$$CommerceFilterImplToJson(
        _$CommerceFilterImpl instance) =>
    <String, dynamic>{
      'age_group': instance.ageGroup,
      'category': instance.category,
      'reserved_tags': instance.reservedTags,
      'sort_key': _$SortKeyProductEnumMap[instance.sortKey],
      'price_range': instance.priceRange?.toJson(),
      'is_price_reverse': instance.isPriceReverse,
    };

const _$SortKeyProductEnumMap = {
  SortKeyProduct.TITLE: 'TITLE',
  SortKeyProduct.PRODUCT_TYPE: 'PRODUCT_TYPE',
  SortKeyProduct.VENDOR: 'VENDOR',
  SortKeyProduct.UPDATED_AT: 'UPDATED_AT',
  SortKeyProduct.CREATED_AT: 'CREATED_AT',
  SortKeyProduct.BEST_SELLING: 'BEST_SELLING',
  SortKeyProduct.PRICE: 'PRICE',
  SortKeyProduct.ID: 'ID',
  SortKeyProduct.RELEVANCE: 'RELEVANCE',
};
