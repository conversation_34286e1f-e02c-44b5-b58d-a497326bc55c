import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:shopify_flutter/enums/src/sort_key_product.dart';

part 'commerce_filter.freezed.dart';
part 'commerce_filter.g.dart';

@freezed
class FilterOption with _$FilterOption {
  factory FilterOption({
    required String label,
    required String value,
  }) = _FilterOption;

  factory FilterOption.fromJson(Json json) => _$FilterOptionFromJson(json);
}

@freezed
class FilterPriceRange with _$FilterPriceRange {
  factory FilterPriceRange({
    double? min,
    double? max,
  }) = _FilterPriceRange;

  factory FilterPriceRange.fromJson(Json json) =>
      _$FilterPriceRangeFromJson(json);
}

@freezed
class CommerceFilter with _$CommerceFilter {
  factory CommerceFilter({
    String? ageGroup,
    String? category,
    String? reservedTags,
    SortKeyProduct? sortKey,
    FilterPriceRange? priceRange,
    bool? isPriceReverse,
  }) = _CommerceFilter;

  factory CommerceFilter.fromJson(Json json) => _$CommerceFilterFromJson(json);
}
