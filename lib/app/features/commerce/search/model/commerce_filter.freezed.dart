// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commerce_filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FilterOption _$FilterOptionFromJson(Map<String, dynamic> json) {
  return _FilterOption.fromJson(json);
}

/// @nodoc
mixin _$FilterOption {
  String get label => throw _privateConstructorUsedError;
  String get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FilterOptionCopyWith<FilterOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterOptionCopyWith<$Res> {
  factory $FilterOptionCopyWith(
          FilterOption value, $Res Function(FilterOption) then) =
      _$FilterOptionCopyWithImpl<$Res, FilterOption>;
  @useResult
  $Res call({String label, String value});
}

/// @nodoc
class _$FilterOptionCopyWithImpl<$Res, $Val extends FilterOption>
    implements $FilterOptionCopyWith<$Res> {
  _$FilterOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? label = null,
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilterOptionImplCopyWith<$Res>
    implements $FilterOptionCopyWith<$Res> {
  factory _$$FilterOptionImplCopyWith(
          _$FilterOptionImpl value, $Res Function(_$FilterOptionImpl) then) =
      __$$FilterOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String label, String value});
}

/// @nodoc
class __$$FilterOptionImplCopyWithImpl<$Res>
    extends _$FilterOptionCopyWithImpl<$Res, _$FilterOptionImpl>
    implements _$$FilterOptionImplCopyWith<$Res> {
  __$$FilterOptionImplCopyWithImpl(
      _$FilterOptionImpl _value, $Res Function(_$FilterOptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? label = null,
    Object? value = null,
  }) {
    return _then(_$FilterOptionImpl(
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FilterOptionImpl implements _FilterOption {
  _$FilterOptionImpl({required this.label, required this.value});

  factory _$FilterOptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$FilterOptionImplFromJson(json);

  @override
  final String label;
  @override
  final String value;

  @override
  String toString() {
    return 'FilterOption(label: $label, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterOptionImpl &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, label, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterOptionImplCopyWith<_$FilterOptionImpl> get copyWith =>
      __$$FilterOptionImplCopyWithImpl<_$FilterOptionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FilterOptionImplToJson(
      this,
    );
  }
}

abstract class _FilterOption implements FilterOption {
  factory _FilterOption(
      {required final String label,
      required final String value}) = _$FilterOptionImpl;

  factory _FilterOption.fromJson(Map<String, dynamic> json) =
      _$FilterOptionImpl.fromJson;

  @override
  String get label;
  @override
  String get value;
  @override
  @JsonKey(ignore: true)
  _$$FilterOptionImplCopyWith<_$FilterOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FilterPriceRange _$FilterPriceRangeFromJson(Map<String, dynamic> json) {
  return _FilterPriceRange.fromJson(json);
}

/// @nodoc
mixin _$FilterPriceRange {
  double? get min => throw _privateConstructorUsedError;
  double? get max => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FilterPriceRangeCopyWith<FilterPriceRange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterPriceRangeCopyWith<$Res> {
  factory $FilterPriceRangeCopyWith(
          FilterPriceRange value, $Res Function(FilterPriceRange) then) =
      _$FilterPriceRangeCopyWithImpl<$Res, FilterPriceRange>;
  @useResult
  $Res call({double? min, double? max});
}

/// @nodoc
class _$FilterPriceRangeCopyWithImpl<$Res, $Val extends FilterPriceRange>
    implements $FilterPriceRangeCopyWith<$Res> {
  _$FilterPriceRangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? min = freezed,
    Object? max = freezed,
  }) {
    return _then(_value.copyWith(
      min: freezed == min
          ? _value.min
          : min // ignore: cast_nullable_to_non_nullable
              as double?,
      max: freezed == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilterPriceRangeImplCopyWith<$Res>
    implements $FilterPriceRangeCopyWith<$Res> {
  factory _$$FilterPriceRangeImplCopyWith(_$FilterPriceRangeImpl value,
          $Res Function(_$FilterPriceRangeImpl) then) =
      __$$FilterPriceRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? min, double? max});
}

/// @nodoc
class __$$FilterPriceRangeImplCopyWithImpl<$Res>
    extends _$FilterPriceRangeCopyWithImpl<$Res, _$FilterPriceRangeImpl>
    implements _$$FilterPriceRangeImplCopyWith<$Res> {
  __$$FilterPriceRangeImplCopyWithImpl(_$FilterPriceRangeImpl _value,
      $Res Function(_$FilterPriceRangeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? min = freezed,
    Object? max = freezed,
  }) {
    return _then(_$FilterPriceRangeImpl(
      min: freezed == min
          ? _value.min
          : min // ignore: cast_nullable_to_non_nullable
              as double?,
      max: freezed == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FilterPriceRangeImpl implements _FilterPriceRange {
  _$FilterPriceRangeImpl({this.min, this.max});

  factory _$FilterPriceRangeImpl.fromJson(Map<String, dynamic> json) =>
      _$$FilterPriceRangeImplFromJson(json);

  @override
  final double? min;
  @override
  final double? max;

  @override
  String toString() {
    return 'FilterPriceRange(min: $min, max: $max)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterPriceRangeImpl &&
            (identical(other.min, min) || other.min == min) &&
            (identical(other.max, max) || other.max == max));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, min, max);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterPriceRangeImplCopyWith<_$FilterPriceRangeImpl> get copyWith =>
      __$$FilterPriceRangeImplCopyWithImpl<_$FilterPriceRangeImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FilterPriceRangeImplToJson(
      this,
    );
  }
}

abstract class _FilterPriceRange implements FilterPriceRange {
  factory _FilterPriceRange({final double? min, final double? max}) =
      _$FilterPriceRangeImpl;

  factory _FilterPriceRange.fromJson(Map<String, dynamic> json) =
      _$FilterPriceRangeImpl.fromJson;

  @override
  double? get min;
  @override
  double? get max;
  @override
  @JsonKey(ignore: true)
  _$$FilterPriceRangeImplCopyWith<_$FilterPriceRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CommerceFilter _$CommerceFilterFromJson(Map<String, dynamic> json) {
  return _CommerceFilter.fromJson(json);
}

/// @nodoc
mixin _$CommerceFilter {
  String? get ageGroup => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;
  String? get reservedTags => throw _privateConstructorUsedError;
  SortKeyProduct? get sortKey => throw _privateConstructorUsedError;
  FilterPriceRange? get priceRange => throw _privateConstructorUsedError;
  bool? get isPriceReverse => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommerceFilterCopyWith<CommerceFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommerceFilterCopyWith<$Res> {
  factory $CommerceFilterCopyWith(
          CommerceFilter value, $Res Function(CommerceFilter) then) =
      _$CommerceFilterCopyWithImpl<$Res, CommerceFilter>;
  @useResult
  $Res call(
      {String? ageGroup,
      String? category,
      String? reservedTags,
      SortKeyProduct? sortKey,
      FilterPriceRange? priceRange,
      bool? isPriceReverse});

  $FilterPriceRangeCopyWith<$Res>? get priceRange;
}

/// @nodoc
class _$CommerceFilterCopyWithImpl<$Res, $Val extends CommerceFilter>
    implements $CommerceFilterCopyWith<$Res> {
  _$CommerceFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ageGroup = freezed,
    Object? category = freezed,
    Object? reservedTags = freezed,
    Object? sortKey = freezed,
    Object? priceRange = freezed,
    Object? isPriceReverse = freezed,
  }) {
    return _then(_value.copyWith(
      ageGroup: freezed == ageGroup
          ? _value.ageGroup
          : ageGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      reservedTags: freezed == reservedTags
          ? _value.reservedTags
          : reservedTags // ignore: cast_nullable_to_non_nullable
              as String?,
      sortKey: freezed == sortKey
          ? _value.sortKey
          : sortKey // ignore: cast_nullable_to_non_nullable
              as SortKeyProduct?,
      priceRange: freezed == priceRange
          ? _value.priceRange
          : priceRange // ignore: cast_nullable_to_non_nullable
              as FilterPriceRange?,
      isPriceReverse: freezed == isPriceReverse
          ? _value.isPriceReverse
          : isPriceReverse // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FilterPriceRangeCopyWith<$Res>? get priceRange {
    if (_value.priceRange == null) {
      return null;
    }

    return $FilterPriceRangeCopyWith<$Res>(_value.priceRange!, (value) {
      return _then(_value.copyWith(priceRange: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CommerceFilterImplCopyWith<$Res>
    implements $CommerceFilterCopyWith<$Res> {
  factory _$$CommerceFilterImplCopyWith(_$CommerceFilterImpl value,
          $Res Function(_$CommerceFilterImpl) then) =
      __$$CommerceFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? ageGroup,
      String? category,
      String? reservedTags,
      SortKeyProduct? sortKey,
      FilterPriceRange? priceRange,
      bool? isPriceReverse});

  @override
  $FilterPriceRangeCopyWith<$Res>? get priceRange;
}

/// @nodoc
class __$$CommerceFilterImplCopyWithImpl<$Res>
    extends _$CommerceFilterCopyWithImpl<$Res, _$CommerceFilterImpl>
    implements _$$CommerceFilterImplCopyWith<$Res> {
  __$$CommerceFilterImplCopyWithImpl(
      _$CommerceFilterImpl _value, $Res Function(_$CommerceFilterImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ageGroup = freezed,
    Object? category = freezed,
    Object? reservedTags = freezed,
    Object? sortKey = freezed,
    Object? priceRange = freezed,
    Object? isPriceReverse = freezed,
  }) {
    return _then(_$CommerceFilterImpl(
      ageGroup: freezed == ageGroup
          ? _value.ageGroup
          : ageGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      reservedTags: freezed == reservedTags
          ? _value.reservedTags
          : reservedTags // ignore: cast_nullable_to_non_nullable
              as String?,
      sortKey: freezed == sortKey
          ? _value.sortKey
          : sortKey // ignore: cast_nullable_to_non_nullable
              as SortKeyProduct?,
      priceRange: freezed == priceRange
          ? _value.priceRange
          : priceRange // ignore: cast_nullable_to_non_nullable
              as FilterPriceRange?,
      isPriceReverse: freezed == isPriceReverse
          ? _value.isPriceReverse
          : isPriceReverse // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommerceFilterImpl implements _CommerceFilter {
  _$CommerceFilterImpl(
      {this.ageGroup,
      this.category,
      this.reservedTags,
      this.sortKey,
      this.priceRange,
      this.isPriceReverse});

  factory _$CommerceFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommerceFilterImplFromJson(json);

  @override
  final String? ageGroup;
  @override
  final String? category;
  @override
  final String? reservedTags;
  @override
  final SortKeyProduct? sortKey;
  @override
  final FilterPriceRange? priceRange;
  @override
  final bool? isPriceReverse;

  @override
  String toString() {
    return 'CommerceFilter(ageGroup: $ageGroup, category: $category, reservedTags: $reservedTags, sortKey: $sortKey, priceRange: $priceRange, isPriceReverse: $isPriceReverse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommerceFilterImpl &&
            (identical(other.ageGroup, ageGroup) ||
                other.ageGroup == ageGroup) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.reservedTags, reservedTags) ||
                other.reservedTags == reservedTags) &&
            (identical(other.sortKey, sortKey) || other.sortKey == sortKey) &&
            (identical(other.priceRange, priceRange) ||
                other.priceRange == priceRange) &&
            (identical(other.isPriceReverse, isPriceReverse) ||
                other.isPriceReverse == isPriceReverse));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, ageGroup, category, reservedTags,
      sortKey, priceRange, isPriceReverse);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CommerceFilterImplCopyWith<_$CommerceFilterImpl> get copyWith =>
      __$$CommerceFilterImplCopyWithImpl<_$CommerceFilterImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommerceFilterImplToJson(
      this,
    );
  }
}

abstract class _CommerceFilter implements CommerceFilter {
  factory _CommerceFilter(
      {final String? ageGroup,
      final String? category,
      final String? reservedTags,
      final SortKeyProduct? sortKey,
      final FilterPriceRange? priceRange,
      final bool? isPriceReverse}) = _$CommerceFilterImpl;

  factory _CommerceFilter.fromJson(Map<String, dynamic> json) =
      _$CommerceFilterImpl.fromJson;

  @override
  String? get ageGroup;
  @override
  String? get category;
  @override
  String? get reservedTags;
  @override
  SortKeyProduct? get sortKey;
  @override
  FilterPriceRange? get priceRange;
  @override
  bool? get isPriceReverse;
  @override
  @JsonKey(ignore: true)
  _$$CommerceFilterImplCopyWith<_$CommerceFilterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
