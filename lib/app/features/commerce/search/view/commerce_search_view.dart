import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/search/widget/suggestion_card.dart';
import 'package:gomama/app/features/commerce/widget/sliver_app_bar_delegate.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommerceSearchView extends ConsumerWidget {
  const CommerceSearchView({super.key});

  static const routeName = 'commerce-search';
  static const routePath = 'commerce-search';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bestSellingProducts = ref.watch(queriedProductsProvider(''));

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverAppBarDelegate(
              maxHeight: kToolbarHeight +
                  12 +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              minHeight: kToolbarHeight +
                  12 +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                color: CustomColors.primaries.shade50,
                child: Column(
                  children: [
                    SizedBox(
                      height: max(
                        mediaQuery(context).viewPadding.top,
                        kTextTabBarHeight,
                      ),
                    ),
                    const _Searchbar(''),
                  ],
                ),
              ),
            ),
          ),
          SliverList.list(
            children: [
              const _SearchHistory(),
              Divider(
                thickness: 2,
                height: 32,
                color: Colors.grey.shade400,
              ),
              // search suggestion (fetch top selling prods)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Search Suggestions',
                      style: textTheme(context).titleMedium!.copyWith(
                            color: CustomColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 12),
                    bestSellingProducts.when(
                      data: (products) {
                        if (products?.isEmpty == true) {
                          return const SizedBox.shrink();
                        }

                        return GridView.count(
                          shrinkWrap: true,
                          crossAxisCount: 2,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          childAspectRatio: 175 / 225,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 14,
                          children: products!.map((product) {
                            return SuggestionCard(product);
                          }).toList(),
                        );
                      },
                      error: (error, stack) {
                        return const SizedBox.shrink();
                      },
                      loading: () {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
            ],
          ),
        ],
      ),
    );
  }
}

// TODO: add auto search with debounced
// refer to search_view.dart
class _Searchbar extends HookConsumerWidget {
  const _Searchbar(this.initialQuery);
  final String initialQuery;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = useTextEditingController(text: initialQuery);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Hero(
        tag: 'commerce-searchbar',
        child: SizedBox(
          height: 40,
          child: Material(
            elevation: 4,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            child: TextField(
              controller: _controller,
              onSubmitted: (value) {
                if (value.isEmpty) {
                  return;
                }

                // go listing page with keyword
                CommerceSearchListingRoute(value).push(context);
              },
              autofocus: true,
              autocorrect: false,
              decoration: InputDecoration(
                prefixIcon: const BackButton(
                  style: ButtonStyle(
                    iconSize: WidgetStatePropertyAll(20),
                    iconColor: WidgetStatePropertyAll(CustomColors.primaries),
                  ),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      visualDensity: VisualDensity.compact,
                      icon: const Icon(
                        Icons.search,
                        size: 20,
                        color: CustomColors.primaries,
                      ),
                      onPressed: () {
                        if (_controller.value.text.isEmpty) {
                          return;
                        }

                        // go listing page with keyword
                        CommerceSearchListingRoute(
                          _controller.value.text,
                        ).push(context);
                      },
                    ),
                  ],
                ),
                hintText: 'Search products',
                hintStyle: textTheme(context)
                    .bodyMedium!
                    .copyWith(color: CustomColors.placeholder),
                contentPadding: const EdgeInsets.fromLTRB(0, 3.5, 0, 0),
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _SearchHistory extends ConsumerWidget {
  const _SearchHistory();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchHistory = ref.watch(searchHistoryProvider);

    return searchHistory.when(
      data: (searchHistory) {
        if (searchHistory.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Search History',
                style: textTheme(context).titleMedium!.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 4,
                runSpacing: 6,
                children: searchHistory
                    .map(
                      (label) => GestureDetector(
                        onTap: () =>
                            CommerceSearchListingRoute(label).push(context),
                        child: Chip(
                          label: Text(label),
                          visualDensity: VisualDensity.compact,
                          side: BorderSide(color: Colors.grey.shade400),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(32),
                          ),
                        ),
                      ),
                    )
                    .toList()
                    .cast<Widget>(),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}
