import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_button.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/products/widget/product_card.dart';
import 'package:gomama/app/features/commerce/search/model/commerce_filter.dart';
import 'package:gomama/app/features/commerce/search/widget/filter_button.dart';
import 'package:gomama/app/features/commerce/search/widget/price_sort_button.dart';
import 'package:gomama/app/features/commerce/search/widget/sort_button.dart';
import 'package:gomama/app/features/commerce/widget/sliver_app_bar_delegate.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/enums/src/sort_key_product.dart';

class CommerceSearchListingView extends HookConsumerWidget {
  const CommerceSearchListingView(this.keywords, {super.key});

  final String keywords;
  static const routeName = 'commerce-search-listing';
  static const routePath = ':keywords';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchKeyword = useState<String?>(keywords);
    final products = ref.watch(queriedProductsProvider(searchKeyword.value));

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverAppBarDelegate(
              maxHeight: kToolbarHeight +
                  52 +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              minHeight: kToolbarHeight +
                  52 +
                  max(mediaQuery(context).viewPadding.top, kTextTabBarHeight),
              child: Material(
                elevation: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  color: CustomColors.primaries.shade50,
                  child: Column(
                    children: [
                      SizedBox(
                        height: max(
                          mediaQuery(context).viewPadding.top,
                          kTextTabBarHeight,
                        ),
                      ),
                      _Searchbar(
                        searchKeyword.value ?? '',
                        onSearch: (String value) {
                          searchKeyword.value = value;
                          ref.invalidate(
                            queriedProductsProvider,
                          );
                        },
                      ),
                      const Spacer(),
                      const SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SortButton(),
                            VerticalDivider(
                              color: CustomColors.primary,
                              indent: 8,
                              endIndent: 8,
                            ),
                            FilterButton(),
                            VerticalDivider(
                              color: CustomColors.primary,
                              indent: 8,
                              endIndent: 8,
                            ),
                            PriceSortButton(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverList.list(
            children: [
              products.when(
                data: (products) {
                  if (products?.isNotEmpty == true) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 24,
                      ),
                      child: GridView.count(
                        shrinkWrap: true,
                        crossAxisCount: 2,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        childAspectRatio: 180 / 290,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 14,
                        children: products!.map((product) {
                          return ProductCard(product);
                        }).toList(),
                      ),
                    );
                  } else {
                    return Center(
                      child: Column(
                        children: [
                          const SizedBox(height: 64),
                          Image.asset(
                            'assets/images/goma_sad.png',
                            height: 160,
                          ),
                          const Text(
                            "Sorry, we couldn't find any product matching your search.\nTry searching with other keywords.",
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }
                },
                error: (error, stack) {
                  return const SizedBox.shrink();
                },
                loading: () {
                  return const Padding(
                    padding: EdgeInsets.only(top: 24),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _Searchbar extends HookConsumerWidget {
  const _Searchbar(
    this.initialQuery, {
    required this.onSearch,
  });
  final String initialQuery;
  final Function(String) onSearch;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = useTextEditingController(text: initialQuery);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Hero(
        tag: 'commerce-searchbar',
        child: SizedBox(
          height: 40,
          child: Material(
            elevation: 4,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            child: TextField(
              controller: _controller,
              onSubmitted: (value) {
                if (value.isEmpty) {
                  return;
                }
                onSearch(value);
              },
              autocorrect: false,
              decoration: InputDecoration(
                prefixIcon: BackButton(
                  onPressed: () {
                    const CommerceRoute().go(context);
                  },
                  style: const ButtonStyle(
                    iconSize: WidgetStatePropertyAll(20),
                    iconColor: WidgetStatePropertyAll(CustomColors.primaries),
                  ),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      visualDensity: VisualDensity.compact,
                      icon: const Icon(
                        Icons.search,
                        size: 20,
                        color: CustomColors.primaries,
                      ),
                      onPressed: () {
                        if (_controller.value.text.isEmpty) {
                          return;
                        }

                        onSearch(_controller.value.text);
                      },
                    ),
                  ],
                ),
                hintText: 'Search products',
                hintStyle: textTheme(context)
                    .bodyMedium!
                    .copyWith(color: CustomColors.placeholder),
                contentPadding: const EdgeInsets.fromLTRB(0, 3.5, 0, 0),
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
