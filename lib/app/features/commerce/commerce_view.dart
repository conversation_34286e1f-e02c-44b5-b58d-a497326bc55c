import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/checkout_line_provider.dart';
import 'package:gomama/app/features/commerce/cart/widget/commerce_refresh_Indicator.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:gomama/app/features/commerce/collections/widget/collection_filter_list.dart';
import 'package:gomama/app/features/commerce/collections/widget/collection_product_list.dart';
import 'package:gomama/app/features/commerce/widget/commerce_navigation_panel.dart';
import 'package:gomama/app/features/commerce/widget/commerce_swipeable_banner.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommerceView extends HookConsumerWidget {
  const CommerceView({super.key});

  static const routeName = 'commerce';
  static const routePath = '/commerce';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      // initialize cart here
      ..watch(cartControllerProvider)
      // initialize here so unselected lines wont reset when navigate out of cart page
      ..watch(unselectedLineProvider);
    final collectionsWithMetafieldAsync =
        ref.watch(collectionsProvider(includeEmpty: false));
    final selectedCollectionId = ref.watch(selectedCollectionProvider);

    return Scaffold(
      backgroundColor: CustomColors.primaries.shade50,
      body: Stack(
        children: [
          CommerceRefreshIndicator(
            offset: 50,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // hero banner
                const CommerceSwipeableBanner(bannerType: 'home'),

                // commerce navigation panel
                const CommerceNavigationPanel(),

                // TODO flash sales

                collectionsWithMetafieldAsync.when(
                  data: (collectionsWithMetafield) {
                    // Filter the collections if a collection is selected
                    final filteredCollections = selectedCollectionId != null
                        ? collectionsWithMetafield
                            .where((item) =>
                                item.collection.id == selectedCollectionId)
                            .toList()
                        : collectionsWithMetafield;

                    return Column(
                      children: [
                        // Collection filter list
                        CollectionFilterList(
                          collections: collectionsWithMetafield,
                        ),
                        ListView.builder(
                          padding: const EdgeInsets.only(top: 24, bottom: 32),
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return CollectionProductList(
                              filteredCollections[index],
                            );
                          },
                          itemCount: filteredCollections.length,
                        ),
                      ],
                    );
                  },
                  error: (error, stackTrace) {
                    return const SizedBox.shrink();
                  },
                  loading: () {
                    return const Padding(
                      padding: EdgeInsets.only(top: 24),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
