import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/address/model/shopify_address_input.dart';
import 'package:gomama/app/features/commerce/provider/shopify_token_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'shopify_address_providers.g.dart';

final addressIsBusyProvider = StateProvider<bool>((ref) => false);
final defaultAddressIdProvider = StateProvider<String?>((ref) => null);

@riverpod
Future<List<Address>> addresses(AddressesRef ref) async {
  final shopifyAuth = ShopifyAuth.instance;
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return [];
  }

  try {
    await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );
    final shopifyUser = await shopifyAuth.currentUser(
      forceRefresh: true, // true to fetch updated addresses
    );

    if (shopifyUser != null) {
      final addresses = shopifyUser.address?.addressList;
      // set default address id
      ref
          .read(defaultAddressIdProvider.notifier)
          .update((state) => shopifyUser.defaultAddress?.id);
      return addresses ?? [];
    } else {
      return [];
    }
  } catch (error) {
    Groveman.error('addresses', error: error);
    rethrow;
  }
}

@riverpod
Future<Address> createAddress(
  CreateAddressRef ref,
  ShopifyAddressInput input,
) async {
  final shopifyCustomer = ShopifyCustomer.instance;
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    throw Exception('Shopify profile not found');
  }

  try {
    final accessToken = await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );

    final address = await shopifyCustomer.customerAddressCreate(
      customerAccessToken: accessToken,
      address1: input.address1,
      address2: input.address2,
      company: input.company,
      city: input.city,
      country: input.country,
      firstName: input.firstName,
      lastName: input.lastName,
      phone: input.phone,
      province: input.province,
      zip: input.zip,
    );
    ref.invalidate(addressesProvider);
    return address;
  } catch (error) {
    Groveman.error('createAddress', error: error);
    rethrow;
  }
}

@riverpod
Future<void> updateAddress(
  UpdateAddressRef ref,
  String addressId,
  ShopifyAddressInput input,
) async {
  final shopifyCustomer = ShopifyCustomer.instance;
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return;
  }

  try {
    final accessToken = await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );

    if (accessToken == null) {
      throw Exception('User not authenticated');
    }

    await shopifyCustomer.customerAddressUpdate(
      id: addressId,
      customerAccessToken: accessToken,
      address1: input.address1,
      address2: input.address2,
      company: input.company,
      city: input.city,
      country: input.country,
      firstName: input.firstName,
      lastName: input.lastName,
      phone: input.phone,
      province: input.province,
      zip: input.zip,
    );
    ref.invalidate(addressesProvider);
  } catch (error) {
    Groveman.error('updateAddress', error: error);
    rethrow;
  }
}

@riverpod
Future<void> deleteAddress(
  DeleteAddressRef ref,
  String addressId,
) async {
  final shopifyCustomer = ShopifyCustomer.instance;
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return;
  }

  try {
    final accessToken = await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );

    await shopifyCustomer.customerAddressDelete(
      customerAccessToken: accessToken,
      addressId: addressId,
    );
    ref.invalidate(addressesProvider);
  } catch (error) {
    Groveman.error('deleteAddress', error: error);
    rethrow;
  }
}

@riverpod
Future<void> setDefaultAddress(
  SetDefaultAddressRef ref,
  String addressId,
) async {
  final shopifyCustomer = ShopifyCustomer.instance;
  final user = ref.watch(authControllerProvider).requireValue;
  final shopifyProfile = user.shopifyProfile;

  if (shopifyProfile == null) {
    return;
  }

  try {
    final accessToken = await getShopifyAccessToken(
      email: shopifyProfile.email,
      password: shopifyProfile.password!,
    );

    if (accessToken == null) {
      throw Exception('User not authenticated');
    }

    await shopifyCustomer.customerDefaultAddressUpdate(
      customerAccessToken: accessToken,
      addressId: addressId,
    );
    ref.invalidate(addressesProvider);
  } catch (error) {
    Groveman.error('setDefaultAddress', error: error);
    rethrow;
  }
}
