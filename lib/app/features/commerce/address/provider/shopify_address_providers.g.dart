// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopify_address_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addressesHash() => r'12249330aac869c82b68c193568323904b13c3b7';

/// See also [addresses].
@ProviderFor(addresses)
final addressesProvider = AutoDisposeFutureProvider<List<Address>>.internal(
  addresses,
  name: r'addressesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$addressesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AddressesRef = AutoDisposeFutureProviderRef<List<Address>>;
String _$createAddressHash() => r'ba56adb69480ee8a09140ba4bd4de23755e769aa';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [createAddress].
@ProviderFor(createAddress)
const createAddressProvider = CreateAddressFamily();

/// See also [createAddress].
class CreateAddressFamily extends Family<AsyncValue<Address>> {
  /// See also [createAddress].
  const CreateAddressFamily();

  /// See also [createAddress].
  CreateAddressProvider call(
    ShopifyAddressInput input,
  ) {
    return CreateAddressProvider(
      input,
    );
  }

  @override
  CreateAddressProvider getProviderOverride(
    covariant CreateAddressProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'createAddressProvider';
}

/// See also [createAddress].
class CreateAddressProvider extends AutoDisposeFutureProvider<Address> {
  /// See also [createAddress].
  CreateAddressProvider(
    ShopifyAddressInput input,
  ) : this._internal(
          (ref) => createAddress(
            ref as CreateAddressRef,
            input,
          ),
          from: createAddressProvider,
          name: r'createAddressProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$createAddressHash,
          dependencies: CreateAddressFamily._dependencies,
          allTransitiveDependencies:
              CreateAddressFamily._allTransitiveDependencies,
          input: input,
        );

  CreateAddressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final ShopifyAddressInput input;

  @override
  Override overrideWith(
    FutureOr<Address> Function(CreateAddressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CreateAddressProvider._internal(
        (ref) => create(ref as CreateAddressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Address> createElement() {
    return _CreateAddressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CreateAddressProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CreateAddressRef on AutoDisposeFutureProviderRef<Address> {
  /// The parameter `input` of this provider.
  ShopifyAddressInput get input;
}

class _CreateAddressProviderElement
    extends AutoDisposeFutureProviderElement<Address> with CreateAddressRef {
  _CreateAddressProviderElement(super.provider);

  @override
  ShopifyAddressInput get input => (origin as CreateAddressProvider).input;
}

String _$updateAddressHash() => r'6cdacd6a84f25e77d4e9bb54ecd1e8a35ece7ca0';

/// See also [updateAddress].
@ProviderFor(updateAddress)
const updateAddressProvider = UpdateAddressFamily();

/// See also [updateAddress].
class UpdateAddressFamily extends Family<AsyncValue<void>> {
  /// See also [updateAddress].
  const UpdateAddressFamily();

  /// See also [updateAddress].
  UpdateAddressProvider call(
    String addressId,
    ShopifyAddressInput input,
  ) {
    return UpdateAddressProvider(
      addressId,
      input,
    );
  }

  @override
  UpdateAddressProvider getProviderOverride(
    covariant UpdateAddressProvider provider,
  ) {
    return call(
      provider.addressId,
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'updateAddressProvider';
}

/// See also [updateAddress].
class UpdateAddressProvider extends AutoDisposeFutureProvider<void> {
  /// See also [updateAddress].
  UpdateAddressProvider(
    String addressId,
    ShopifyAddressInput input,
  ) : this._internal(
          (ref) => updateAddress(
            ref as UpdateAddressRef,
            addressId,
            input,
          ),
          from: updateAddressProvider,
          name: r'updateAddressProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$updateAddressHash,
          dependencies: UpdateAddressFamily._dependencies,
          allTransitiveDependencies:
              UpdateAddressFamily._allTransitiveDependencies,
          addressId: addressId,
          input: input,
        );

  UpdateAddressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.addressId,
    required this.input,
  }) : super.internal();

  final String addressId;
  final ShopifyAddressInput input;

  @override
  Override overrideWith(
    FutureOr<void> Function(UpdateAddressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateAddressProvider._internal(
        (ref) => create(ref as UpdateAddressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        addressId: addressId,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _UpdateAddressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateAddressProvider &&
        other.addressId == addressId &&
        other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, addressId.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateAddressRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `addressId` of this provider.
  String get addressId;

  /// The parameter `input` of this provider.
  ShopifyAddressInput get input;
}

class _UpdateAddressProviderElement
    extends AutoDisposeFutureProviderElement<void> with UpdateAddressRef {
  _UpdateAddressProviderElement(super.provider);

  @override
  String get addressId => (origin as UpdateAddressProvider).addressId;
  @override
  ShopifyAddressInput get input => (origin as UpdateAddressProvider).input;
}

String _$deleteAddressHash() => r'8ff7b60ebb1c68c6ac0680474bf1ef5f5fa30667';

/// See also [deleteAddress].
@ProviderFor(deleteAddress)
const deleteAddressProvider = DeleteAddressFamily();

/// See also [deleteAddress].
class DeleteAddressFamily extends Family<AsyncValue<void>> {
  /// See also [deleteAddress].
  const DeleteAddressFamily();

  /// See also [deleteAddress].
  DeleteAddressProvider call(
    String addressId,
  ) {
    return DeleteAddressProvider(
      addressId,
    );
  }

  @override
  DeleteAddressProvider getProviderOverride(
    covariant DeleteAddressProvider provider,
  ) {
    return call(
      provider.addressId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'deleteAddressProvider';
}

/// See also [deleteAddress].
class DeleteAddressProvider extends AutoDisposeFutureProvider<void> {
  /// See also [deleteAddress].
  DeleteAddressProvider(
    String addressId,
  ) : this._internal(
          (ref) => deleteAddress(
            ref as DeleteAddressRef,
            addressId,
          ),
          from: deleteAddressProvider,
          name: r'deleteAddressProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$deleteAddressHash,
          dependencies: DeleteAddressFamily._dependencies,
          allTransitiveDependencies:
              DeleteAddressFamily._allTransitiveDependencies,
          addressId: addressId,
        );

  DeleteAddressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.addressId,
  }) : super.internal();

  final String addressId;

  @override
  Override overrideWith(
    FutureOr<void> Function(DeleteAddressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeleteAddressProvider._internal(
        (ref) => create(ref as DeleteAddressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        addressId: addressId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _DeleteAddressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DeleteAddressProvider && other.addressId == addressId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, addressId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin DeleteAddressRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `addressId` of this provider.
  String get addressId;
}

class _DeleteAddressProviderElement
    extends AutoDisposeFutureProviderElement<void> with DeleteAddressRef {
  _DeleteAddressProviderElement(super.provider);

  @override
  String get addressId => (origin as DeleteAddressProvider).addressId;
}

String _$setDefaultAddressHash() => r'b569581bf5ef957156d089359baad701bd07415f';

/// See also [setDefaultAddress].
@ProviderFor(setDefaultAddress)
const setDefaultAddressProvider = SetDefaultAddressFamily();

/// See also [setDefaultAddress].
class SetDefaultAddressFamily extends Family<AsyncValue<void>> {
  /// See also [setDefaultAddress].
  const SetDefaultAddressFamily();

  /// See also [setDefaultAddress].
  SetDefaultAddressProvider call(
    String addressId,
  ) {
    return SetDefaultAddressProvider(
      addressId,
    );
  }

  @override
  SetDefaultAddressProvider getProviderOverride(
    covariant SetDefaultAddressProvider provider,
  ) {
    return call(
      provider.addressId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'setDefaultAddressProvider';
}

/// See also [setDefaultAddress].
class SetDefaultAddressProvider extends AutoDisposeFutureProvider<void> {
  /// See also [setDefaultAddress].
  SetDefaultAddressProvider(
    String addressId,
  ) : this._internal(
          (ref) => setDefaultAddress(
            ref as SetDefaultAddressRef,
            addressId,
          ),
          from: setDefaultAddressProvider,
          name: r'setDefaultAddressProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$setDefaultAddressHash,
          dependencies: SetDefaultAddressFamily._dependencies,
          allTransitiveDependencies:
              SetDefaultAddressFamily._allTransitiveDependencies,
          addressId: addressId,
        );

  SetDefaultAddressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.addressId,
  }) : super.internal();

  final String addressId;

  @override
  Override overrideWith(
    FutureOr<void> Function(SetDefaultAddressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SetDefaultAddressProvider._internal(
        (ref) => create(ref as SetDefaultAddressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        addressId: addressId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _SetDefaultAddressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SetDefaultAddressProvider && other.addressId == addressId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, addressId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SetDefaultAddressRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `addressId` of this provider.
  String get addressId;
}

class _SetDefaultAddressProviderElement
    extends AutoDisposeFutureProviderElement<void> with SetDefaultAddressRef {
  _SetDefaultAddressProviderElement(super.provider);

  @override
  String get addressId => (origin as SetDefaultAddressProvider).addressId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
