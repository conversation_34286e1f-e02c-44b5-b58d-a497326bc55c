import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/address/provider/shopify_address_providers.dart';
import 'package:gomama/app/features/commerce/address/widget/add_address_sheet.dart';
import 'package:gomama/app/features/commerce/address/widget/edit_address_sheet.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class AddressView extends ConsumerWidget {
  const AddressView({super.key});

  static const routeName = 'address';
  static const routePath = 'address';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressesAsync = ref.watch(addressesProvider);
    final isBusy = ref.watch(addressIsBusyProvider);

    return Stack(
      children: [
        Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            title: const Text('My Addresses'),
            centerTitle: true,
          ),
          body: addressesAsync.when(
            data: (addresses) => addresses.isEmpty
                ? const Center(
                    child: Text('No addresses found'),
                  )
                : ListView.separated(
                    padding:
                        const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                    itemCount: addresses.length,
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 8),
                    itemBuilder: (context, index) {
                      final address = addresses[index];
                      return AddressCard(address: address);
                    },
                  ),
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: ElevatedButton.icon(
                onPressed: () => ref.invalidate(addressesProvider),
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            heroTag: 'address_add_button',
            onPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                showDragHandle: true,
                useSafeArea: true,
                backgroundColor: CustomColors.secondaryLight,
                builder: (context) => const AddAddressSheet(),
              );
            },
            child: const Icon(Icons.add),
          ),
        ),
        if (isBusy)
          const Positioned.fill(
            child: ColoredBox(
              color: Colors.black26,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
      ],
    );
  }
}

class AddressCard extends ConsumerWidget {
  const AddressCard({
    super.key,
    required this.address,
  });

  final Address address;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final defaultAddressId = ref.watch(defaultAddressIdProvider);
    final isDefault = address.id == defaultAddressId;

    return Card(
      child: Column(
        children: [
          ListTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isDefault)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: CustomColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Default',
                      style: TextStyle(
                        color: CustomColors.primary,
                        fontSize: 12,
                      ),
                    ),
                  ),
                Text(
                  '${address.firstName ?? ''} ${address.lastName ?? ''}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(address.address1!),
                if (address.address2?.isEmpty == false) Text(address.address2!),
                Text(address.city!),
                Text(
                  address.province?.isEmpty == false
                      ? '${address.province}, ${address.country}, ${address.zip}'
                      : '${address.country}, ${address.zip}',
                ),
                if (address.phone?.isEmpty == false) Text(address.phone!),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) async {
                switch (value) {
                  case 'default':
                    try {
                      await ref
                          .read(setDefaultAddressProvider(address.id!).future);
                    } catch (error) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to set default: $error'),
                          ),
                        );
                      }
                    }
                    break;
                  case 'edit':
                    await showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      showDragHandle: true,
                      useSafeArea: true,
                      backgroundColor: CustomColors.secondaryLight,
                      builder: (context) => EditAddressSheet(address: address),
                    );
                    break;
                  case 'delete':
                    try {
                      ref.read(addressIsBusyProvider.notifier).state = true;
                      await ref.read(deleteAddressProvider(address.id!).future);

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Address deleted successfully'),
                          ),
                        );
                      }
                    } catch (error) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to delete: $error'),
                          ),
                        );
                      }
                    } finally {
                      if (context.mounted) {
                        ref.read(addressIsBusyProvider.notifier).state = false;
                      }
                    }
                    break;
                }
              },
              itemBuilder: (context) => [
                if (!isDefault)
                  const PopupMenuItem(
                    value: 'default',
                    child: Text('Set as Default'),
                  ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
