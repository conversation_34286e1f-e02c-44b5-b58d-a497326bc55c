import 'package:freezed_annotation/freezed_annotation.dart';

part 'shopify_address_input.freezed.dart';
part 'shopify_address_input.g.dart';

@freezed
class ShopifyAddressInput with _$ShopifyAddressInput {
  const factory ShopifyAddressInput({
    required String address1,
    String? address2,
    String? company,
    required String city,
    required String country,
    required String firstName,
    required String lastName,
    String? phone,
    required String province,
    required String zip,
  }) = _ShopifyAddressInput;

  factory ShopifyAddressInput.fromJson(Map<String, dynamic> json) =>
      _$ShopifyAddressInputFromJson(json);
}
