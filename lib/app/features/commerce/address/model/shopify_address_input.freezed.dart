// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shopify_address_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ShopifyAddressInput _$ShopifyAddressInputFromJson(Map<String, dynamic> json) {
  return _ShopifyAddressInput.fromJson(json);
}

/// @nodoc
mixin _$ShopifyAddressInput {
  String get address1 => throw _privateConstructorUsedError;
  String? get address2 => throw _privateConstructorUsedError;
  String? get company => throw _privateConstructorUsedError;
  String get city => throw _privateConstructorUsedError;
  String get country => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String get province => throw _privateConstructorUsedError;
  String get zip => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShopifyAddressInputCopyWith<ShopifyAddressInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopifyAddressInputCopyWith<$Res> {
  factory $ShopifyAddressInputCopyWith(
          ShopifyAddressInput value, $Res Function(ShopifyAddressInput) then) =
      _$ShopifyAddressInputCopyWithImpl<$Res, ShopifyAddressInput>;
  @useResult
  $Res call(
      {String address1,
      String? address2,
      String? company,
      String city,
      String country,
      String firstName,
      String lastName,
      String? phone,
      String province,
      String zip});
}

/// @nodoc
class _$ShopifyAddressInputCopyWithImpl<$Res, $Val extends ShopifyAddressInput>
    implements $ShopifyAddressInputCopyWith<$Res> {
  _$ShopifyAddressInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address1 = null,
    Object? address2 = freezed,
    Object? company = freezed,
    Object? city = null,
    Object? country = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? phone = freezed,
    Object? province = null,
    Object? zip = null,
  }) {
    return _then(_value.copyWith(
      address1: null == address1
          ? _value.address1
          : address1 // ignore: cast_nullable_to_non_nullable
              as String,
      address2: freezed == address2
          ? _value.address2
          : address2 // ignore: cast_nullable_to_non_nullable
              as String?,
      company: freezed == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      province: null == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String,
      zip: null == zip
          ? _value.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShopifyAddressInputImplCopyWith<$Res>
    implements $ShopifyAddressInputCopyWith<$Res> {
  factory _$$ShopifyAddressInputImplCopyWith(_$ShopifyAddressInputImpl value,
          $Res Function(_$ShopifyAddressInputImpl) then) =
      __$$ShopifyAddressInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String address1,
      String? address2,
      String? company,
      String city,
      String country,
      String firstName,
      String lastName,
      String? phone,
      String province,
      String zip});
}

/// @nodoc
class __$$ShopifyAddressInputImplCopyWithImpl<$Res>
    extends _$ShopifyAddressInputCopyWithImpl<$Res, _$ShopifyAddressInputImpl>
    implements _$$ShopifyAddressInputImplCopyWith<$Res> {
  __$$ShopifyAddressInputImplCopyWithImpl(_$ShopifyAddressInputImpl _value,
      $Res Function(_$ShopifyAddressInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address1 = null,
    Object? address2 = freezed,
    Object? company = freezed,
    Object? city = null,
    Object? country = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? phone = freezed,
    Object? province = null,
    Object? zip = null,
  }) {
    return _then(_$ShopifyAddressInputImpl(
      address1: null == address1
          ? _value.address1
          : address1 // ignore: cast_nullable_to_non_nullable
              as String,
      address2: freezed == address2
          ? _value.address2
          : address2 // ignore: cast_nullable_to_non_nullable
              as String?,
      company: freezed == company
          ? _value.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      province: null == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String,
      zip: null == zip
          ? _value.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShopifyAddressInputImpl implements _ShopifyAddressInput {
  const _$ShopifyAddressInputImpl(
      {required this.address1,
      this.address2,
      this.company,
      required this.city,
      required this.country,
      required this.firstName,
      required this.lastName,
      this.phone,
      required this.province,
      required this.zip});

  factory _$ShopifyAddressInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShopifyAddressInputImplFromJson(json);

  @override
  final String address1;
  @override
  final String? address2;
  @override
  final String? company;
  @override
  final String city;
  @override
  final String country;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String? phone;
  @override
  final String province;
  @override
  final String zip;

  @override
  String toString() {
    return 'ShopifyAddressInput(address1: $address1, address2: $address2, company: $company, city: $city, country: $country, firstName: $firstName, lastName: $lastName, phone: $phone, province: $province, zip: $zip)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShopifyAddressInputImpl &&
            (identical(other.address1, address1) ||
                other.address1 == address1) &&
            (identical(other.address2, address2) ||
                other.address2 == address2) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.zip, zip) || other.zip == zip));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, address1, address2, company,
      city, country, firstName, lastName, phone, province, zip);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShopifyAddressInputImplCopyWith<_$ShopifyAddressInputImpl> get copyWith =>
      __$$ShopifyAddressInputImplCopyWithImpl<_$ShopifyAddressInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShopifyAddressInputImplToJson(
      this,
    );
  }
}

abstract class _ShopifyAddressInput implements ShopifyAddressInput {
  const factory _ShopifyAddressInput(
      {required final String address1,
      final String? address2,
      final String? company,
      required final String city,
      required final String country,
      required final String firstName,
      required final String lastName,
      final String? phone,
      required final String province,
      required final String zip}) = _$ShopifyAddressInputImpl;

  factory _ShopifyAddressInput.fromJson(Map<String, dynamic> json) =
      _$ShopifyAddressInputImpl.fromJson;

  @override
  String get address1;
  @override
  String? get address2;
  @override
  String? get company;
  @override
  String get city;
  @override
  String get country;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String? get phone;
  @override
  String get province;
  @override
  String get zip;
  @override
  @JsonKey(ignore: true)
  _$$ShopifyAddressInputImplCopyWith<_$ShopifyAddressInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
