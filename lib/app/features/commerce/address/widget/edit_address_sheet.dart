import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/commerce/address/model/shopify_address_input.dart';
import 'package:gomama/app/features/commerce/address/provider/shopify_address_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class EditAddressSheet extends HookConsumerWidget {
  const EditAddressSheet({
    super.key,
    required this.address,
  });

  final Address address;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormBuilderState>());
    final isBusy = useState(false);
    final errorText = useState<String?>(null);

    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.of(context).viewInsets.bottom,
      ),
      child: FormBuilder(
        key: formKey.value,
        initialValue: {
          'first_name': address.firstName ?? '',
          'last_name': address.lastName ?? '',
          'address1': address.address1 ?? '',
          'address2': address.address2 ?? '',
          'city': address.city ?? '',
          'province': address.province ?? '',
          'zip': address.zip ?? '',
          'country': address.country ?? '',
          'phone': address.phone ?? '',
        },
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  const SizedBox(
                    width: double.infinity,
                    height: kToolbarHeight,
                  ),
                  Text(
                    'Edit Address',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Positioned(
                    top: 4,
                    right: 8,
                    child: CloseButton(),
                  ),
                ],
              ),
              FormBuilderTextField(
                name: 'first_name',
                decoration: const InputDecoration(
                  labelText: 'First Name*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter first name',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your first name',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'last_name',
                decoration: const InputDecoration(
                  labelText: 'Last Name*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter last name',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your last name',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'address1',
                decoration: const InputDecoration(
                  labelText: 'Address Line 1*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter street address',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your address',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'address2',
                decoration: const InputDecoration(
                  labelText: 'Address Line 2',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Apartment, suite, unit, etc. (optional)',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'city',
                decoration: const InputDecoration(
                  labelText: 'City*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter city',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your city',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'province',
                decoration: const InputDecoration(
                  labelText: 'State/Province',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter state/province',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'zip',
                decoration: const InputDecoration(
                  labelText: 'Postal Code*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter postal code',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your postal code',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'country',
                decoration: const InputDecoration(
                  labelText: 'Country*',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter country',
                ),
                validator: FormBuilderValidators.required(
                  errorText: 'Please enter your country',
                ),
              ),
              const SizedBox(height: 16),
              FormBuilderTextField(
                name: 'phone',
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Enter phone number (optional)',
                ),
              ),
              if (errorText.value != null) ...[
                const SizedBox(height: 16),
                Text(
                  errorText.value!,
                  style: const TextStyle(color: Colors.red),
                ),
              ],
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  style: TextButton.styleFrom(
                    backgroundColor: CustomColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: isBusy.value
                      ? null
                      : () async {
                          errorText.value = null;

                          final success =
                              formKey.value.currentState?.saveAndValidate();
                          if (success != true) return;

                          final values = formKey.value.currentState!.value;
                          final input = ShopifyAddressInput.fromJson(values);

                          isBusy.value = true;

                          try {
                            await ref.read(
                              updateAddressProvider(address.id!, input).future,
                            );
                            isBusy.value = false;
                            if (context.mounted) {
                              context.pop();
                            }
                          } catch (error) {
                            errorText.value = error.toString();
                            isBusy.value = false;
                          }
                        },
                  child: Text(
                    isBusy.value ? 'Saving...' : 'Save Changes',
                    style: const TextStyle(color: CustomColors.secondaryLight),
                  ),
                ),
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
}
