import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';

part 'commerce_banner.freezed.dart';
part 'commerce_banner.g.dart';

@freezed
class CommerceBanner with _$CommerceBanner {
  @CustomDateTimeConverter()
  factory CommerceBanner({
    required int id,
    String? bannerName,
    String? imageUrl,
    String? actionLink,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CommerceBanner;

  factory CommerceBanner.fromJson(Json json) => _$CommerceBannerFromJson(json);
}

@freezed
class CommerceBannerResponse with _$CommerceBannerResponse {
  factory CommerceBannerResponse({
    required List<CommerceBanner> data,
    required Pagination meta,
  }) = _CommerceBannerResponse;

  factory CommerceBannerResponse.fromJson(Json json) =>
      _$CommerceBannerResponseFromJson(json);
}
