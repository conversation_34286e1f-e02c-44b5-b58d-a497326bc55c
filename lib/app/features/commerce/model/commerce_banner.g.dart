// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commerce_banner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CommerceBannerImpl _$$CommerceBannerImplFromJson(Map<String, dynamic> json) =>
    _$CommerceBannerImpl(
      id: (json['id'] as num).toInt(),
      bannerName: json['banner_name'] as String?,
      imageUrl: json['image_url'] as String?,
      actionLink: json['action_link'] as String?,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$CommerceBannerImplToJson(
        _$CommerceBannerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'banner_name': instance.bannerName,
      'image_url': instance.imageUrl,
      'action_link': instance.actionLink,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$CommerceBannerResponseImpl _$$CommerceBannerResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CommerceBannerResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => CommerceBanner.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CommerceBannerResponseImplToJson(
        _$CommerceBannerResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
