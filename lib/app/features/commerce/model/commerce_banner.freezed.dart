// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commerce_banner.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CommerceBanner _$CommerceBannerFromJson(Map<String, dynamic> json) {
  return _CommerceBanner.fromJson(json);
}

/// @nodoc
mixin _$CommerceBanner {
  int get id => throw _privateConstructorUsedError;
  String? get bannerName => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get actionLink => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommerceBannerCopyWith<CommerceBanner> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommerceBannerCopyWith<$Res> {
  factory $CommerceBannerCopyWith(
          CommerceBanner value, $Res Function(CommerceBanner) then) =
      _$CommerceBannerCopyWithImpl<$Res, CommerceBanner>;
  @useResult
  $Res call(
      {int id,
      String? bannerName,
      String? imageUrl,
      String? actionLink,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$CommerceBannerCopyWithImpl<$Res, $Val extends CommerceBanner>
    implements $CommerceBannerCopyWith<$Res> {
  _$CommerceBannerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? bannerName = freezed,
    Object? imageUrl = freezed,
    Object? actionLink = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      bannerName: freezed == bannerName
          ? _value.bannerName
          : bannerName // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionLink: freezed == actionLink
          ? _value.actionLink
          : actionLink // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommerceBannerImplCopyWith<$Res>
    implements $CommerceBannerCopyWith<$Res> {
  factory _$$CommerceBannerImplCopyWith(_$CommerceBannerImpl value,
          $Res Function(_$CommerceBannerImpl) then) =
      __$$CommerceBannerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? bannerName,
      String? imageUrl,
      String? actionLink,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$CommerceBannerImplCopyWithImpl<$Res>
    extends _$CommerceBannerCopyWithImpl<$Res, _$CommerceBannerImpl>
    implements _$$CommerceBannerImplCopyWith<$Res> {
  __$$CommerceBannerImplCopyWithImpl(
      _$CommerceBannerImpl _value, $Res Function(_$CommerceBannerImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? bannerName = freezed,
    Object? imageUrl = freezed,
    Object? actionLink = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$CommerceBannerImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      bannerName: freezed == bannerName
          ? _value.bannerName
          : bannerName // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionLink: freezed == actionLink
          ? _value.actionLink
          : actionLink // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$CommerceBannerImpl implements _CommerceBanner {
  _$CommerceBannerImpl(
      {required this.id,
      this.bannerName,
      this.imageUrl,
      this.actionLink,
      this.createdAt,
      this.updatedAt});

  factory _$CommerceBannerImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommerceBannerImplFromJson(json);

  @override
  final int id;
  @override
  final String? bannerName;
  @override
  final String? imageUrl;
  @override
  final String? actionLink;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CommerceBanner(id: $id, bannerName: $bannerName, imageUrl: $imageUrl, actionLink: $actionLink, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommerceBannerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.bannerName, bannerName) ||
                other.bannerName == bannerName) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.actionLink, actionLink) ||
                other.actionLink == actionLink) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, bannerName, imageUrl, actionLink, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CommerceBannerImplCopyWith<_$CommerceBannerImpl> get copyWith =>
      __$$CommerceBannerImplCopyWithImpl<_$CommerceBannerImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommerceBannerImplToJson(
      this,
    );
  }
}

abstract class _CommerceBanner implements CommerceBanner {
  factory _CommerceBanner(
      {required final int id,
      final String? bannerName,
      final String? imageUrl,
      final String? actionLink,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$CommerceBannerImpl;

  factory _CommerceBanner.fromJson(Map<String, dynamic> json) =
      _$CommerceBannerImpl.fromJson;

  @override
  int get id;
  @override
  String? get bannerName;
  @override
  String? get imageUrl;
  @override
  String? get actionLink;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$CommerceBannerImplCopyWith<_$CommerceBannerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CommerceBannerResponse _$CommerceBannerResponseFromJson(
    Map<String, dynamic> json) {
  return _CommerceBannerResponse.fromJson(json);
}

/// @nodoc
mixin _$CommerceBannerResponse {
  List<CommerceBanner> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommerceBannerResponseCopyWith<CommerceBannerResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommerceBannerResponseCopyWith<$Res> {
  factory $CommerceBannerResponseCopyWith(CommerceBannerResponse value,
          $Res Function(CommerceBannerResponse) then) =
      _$CommerceBannerResponseCopyWithImpl<$Res, CommerceBannerResponse>;
  @useResult
  $Res call({List<CommerceBanner> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$CommerceBannerResponseCopyWithImpl<$Res,
        $Val extends CommerceBannerResponse>
    implements $CommerceBannerResponseCopyWith<$Res> {
  _$CommerceBannerResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CommerceBanner>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CommerceBannerResponseImplCopyWith<$Res>
    implements $CommerceBannerResponseCopyWith<$Res> {
  factory _$$CommerceBannerResponseImplCopyWith(
          _$CommerceBannerResponseImpl value,
          $Res Function(_$CommerceBannerResponseImpl) then) =
      __$$CommerceBannerResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CommerceBanner> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$CommerceBannerResponseImplCopyWithImpl<$Res>
    extends _$CommerceBannerResponseCopyWithImpl<$Res,
        _$CommerceBannerResponseImpl>
    implements _$$CommerceBannerResponseImplCopyWith<$Res> {
  __$$CommerceBannerResponseImplCopyWithImpl(
      _$CommerceBannerResponseImpl _value,
      $Res Function(_$CommerceBannerResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$CommerceBannerResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CommerceBanner>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommerceBannerResponseImpl implements _CommerceBannerResponse {
  _$CommerceBannerResponseImpl(
      {required final List<CommerceBanner> data, required this.meta})
      : _data = data;

  factory _$CommerceBannerResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommerceBannerResponseImplFromJson(json);

  final List<CommerceBanner> _data;
  @override
  List<CommerceBanner> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'CommerceBannerResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommerceBannerResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CommerceBannerResponseImplCopyWith<_$CommerceBannerResponseImpl>
      get copyWith => __$$CommerceBannerResponseImplCopyWithImpl<
          _$CommerceBannerResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommerceBannerResponseImplToJson(
      this,
    );
  }
}

abstract class _CommerceBannerResponse implements CommerceBannerResponse {
  factory _CommerceBannerResponse(
      {required final List<CommerceBanner> data,
      required final Pagination meta}) = _$CommerceBannerResponseImpl;

  factory _CommerceBannerResponse.fromJson(Map<String, dynamic> json) =
      _$CommerceBannerResponseImpl.fromJson;

  @override
  List<CommerceBanner> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$CommerceBannerResponseImplCopyWith<_$CommerceBannerResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
