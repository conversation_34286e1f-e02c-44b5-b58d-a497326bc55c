import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/checkout_line_provider.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_items_group.dart';
import 'package:gomama/app/features/commerce/cart/widget/checkout_button.dart';
import 'package:gomama/app/features/commerce/cart/widget/commerce_refresh_Indicator.dart';
import 'package:gomama/app/features/commerce/cart/widget/discount_list_tile.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/coin/widget/redeem_coin_tile.dart';
import 'package:gomama/app/widgets/brand_list_tile.dart';
import 'package:gomama/app/widgets/custom_list_tile.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/models/src/cart/cart.dart';
import 'package:shopify_flutter/models/src/cart/lines/line/line.dart';

class CartView extends StatefulHookConsumerWidget {
  const CartView({super.key});

  static const routeName = 'cart';
  static const routePath = 'cart';

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CartViewState();
}

class _CartViewState extends ConsumerState<CartView> {
  @override
  void initState() {
    super.initState();
  }

  // Calculate the total price for selected items only
  String _calculateTotalPrice({
    required Cart cart,
    required bool? isRedeemingCoin,
    required double? coinDiscount,
  }) {
    // Get selected lines and calculate their total
    final selectedLines = ref.watch(selectedLinesProvider(cart.lines));
    final hasSelected = ref.watch(hasSelectedItemsProvider);

    // If cart cost is null or no items are selected, show $0.00
    if (cart.cost == null ||
        (cart.cost!.totalAmount.amount <= 0) ||
        !hasSelected) {
      return r'$0.00';
    }

    // If no lines are selected, return $0.00
    if (selectedLines.isEmpty) {
      return r'$0.00';
    }

    // Calculate total for selected items
    final selectedTotal = selectedLines.fold<double>(
      0,
      (sum, line) =>
          sum +
          (line.cost?.amountPerQuantity.amount ?? 0) * (line.quantity ?? 1),
    );

    // Apply coin discount if applicable
    if (isRedeemingCoin == true && coinDiscount != null && coinDiscount > 0.0) {
      return '\$${(selectedTotal - coinDiscount).toStringAsFixed(2)}';
    }

    return '\$${selectedTotal.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    final isRedeemingCoin = ref.watch(isRedeemCoinProvider);
    final coinDiscount = ref.watch(coinsDiscountProvider);
    final isCheckingout = ref.watch(cartCheckingOutProvider);
    final cart = ref.watch(cartControllerProvider);

    return cart.when(
      data: (cart) {
        Groveman.info('checkoutUrl', error: cart.checkoutUrl);

        // group lines by vendor
        final groupedLines = cart.lines.fold<Map<String, List<Line>>>(
          {},
          (map, line) {
            final key = line.merchandise?.product?.vendor ?? '';
            map[key] = [...(map[key] ?? []), line];
            return map;
          },
        );

        return Stack(
          children: [
            Scaffold(
              backgroundColor: CustomColors.primaries.shade50,
              appBar: AppBar(
                title: const Text('Cart'),
                centerTitle: true,
                // actions: const [Icon(Icons.more_vert)],
              ),
              // cart footer
              bottomNavigationBar: SafeArea(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    0,
                    16,
                    0,
                    mediaQuery(context).padding.bottom > 0
                        ? 0
                        : 16, // with notch ? 0 : 16
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // TODO : add discount
                      // DiscountListTile(
                      //   leadingIcon: Icons.discount_outlined,
                      //   discountTitle: 'Platform Discount',
                      //   // amount: '-',
                      //   tileColor: CustomColors.primaries.shade100,
                      // ),
                      // DiscountListTile(
                      //   leadingIcon: Icons.local_shipping_outlined,
                      //   discountTitle: 'Shipping Discount',
                      //   // amount: '-',
                      //   tileColor: CustomColors.primaries.shade100,
                      // ),
                      // Divider(color: CustomColors.primaries.shade100),
                      const RedeemCoinTile(),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Total:',
                                  style: Theme.of(context).textTheme.labelSmall,
                                ),
                                Text(
                                  _calculateTotalPrice(
                                    cart: cart,
                                    isRedeemingCoin: isRedeemingCoin,
                                    coinDiscount: coinDiscount,
                                  ),
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                          ),
                          CheckoutButton(cart.checkoutUrl),
                          const SizedBox(width: 16),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              body: CommerceRefreshIndicator(
                child: CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    SliverPadding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 8,
                      ),
                      sliver: SliverList.separated(
                        itemBuilder: (context, index) {
                          // map by vendor/collection
                          final vendor = groupedLines.keys.elementAt(index);
                          final lines = groupedLines[vendor] ?? [];

                          return CartItemsGroup(
                            vendor: vendor,
                            cartLines: lines,
                          );
                        },
                        separatorBuilder: (context, index) {
                          return const SizedBox(height: 12);
                        },
                        itemCount: groupedLines.length,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // block action on check out
            if (isCheckingout)
              Positioned.fill(
                child: ColoredBox(
                  color: Colors.grey.shade300.withOpacity(0.6),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ),
          ],
        );
      },
      error: (error, stackTrace) {
        Groveman.error('CartView', error: error, stackTrace: stackTrace);

        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            title: const Text('Cart'),
            centerTitle: true,
            // actions: const [Icon(Icons.more_vert)],
          ),
          body: Center(
            child: OutlinedButton(
              child: const Text('Try Again'),
              onPressed: () {
                // reset cart if error
                ref.read(cartControllerProvider.notifier).resetCart();
                ref.invalidate(cartControllerProvider);
              },
            ),
          ),
        );
      },
      loading: () {
        return Scaffold(
          backgroundColor: CustomColors.primaries.shade50,
          appBar: AppBar(
            title: const Text('Cart'),
            centerTitle: true,
            // actions: const [Icon(Icons.more_vert)],
          ),
          body: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}
