// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_line_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedLinesHash() => r'106b5fc8927d4ac41054fa9b2382450bf0b0d219';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider to get all selected lines
///
/// Copied from [selectedLines].
@ProviderFor(selectedLines)
const selectedLinesProvider = SelectedLinesFamily();

/// Provider to get all selected lines
///
/// Copied from [selectedLines].
class SelectedLinesFamily extends Family<List<Line>> {
  /// Provider to get all selected lines
  ///
  /// Copied from [selectedLines].
  const SelectedLinesFamily();

  /// Provider to get all selected lines
  ///
  /// Copied from [selectedLines].
  SelectedLinesProvider call(
    List<Line> allLines,
  ) {
    return SelectedLinesProvider(
      allLines,
    );
  }

  @override
  SelectedLinesProvider getProviderOverride(
    covariant SelectedLinesProvider provider,
  ) {
    return call(
      provider.allLines,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'selectedLinesProvider';
}

/// Provider to get all selected lines
///
/// Copied from [selectedLines].
class SelectedLinesProvider extends AutoDisposeProvider<List<Line>> {
  /// Provider to get all selected lines
  ///
  /// Copied from [selectedLines].
  SelectedLinesProvider(
    List<Line> allLines,
  ) : this._internal(
          (ref) => selectedLines(
            ref as SelectedLinesRef,
            allLines,
          ),
          from: selectedLinesProvider,
          name: r'selectedLinesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$selectedLinesHash,
          dependencies: SelectedLinesFamily._dependencies,
          allTransitiveDependencies:
              SelectedLinesFamily._allTransitiveDependencies,
          allLines: allLines,
        );

  SelectedLinesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.allLines,
  }) : super.internal();

  final List<Line> allLines;

  @override
  Override overrideWith(
    List<Line> Function(SelectedLinesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SelectedLinesProvider._internal(
        (ref) => create(ref as SelectedLinesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        allLines: allLines,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<List<Line>> createElement() {
    return _SelectedLinesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SelectedLinesProvider && other.allLines == allLines;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, allLines.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SelectedLinesRef on AutoDisposeProviderRef<List<Line>> {
  /// The parameter `allLines` of this provider.
  List<Line> get allLines;
}

class _SelectedLinesProviderElement
    extends AutoDisposeProviderElement<List<Line>> with SelectedLinesRef {
  _SelectedLinesProviderElement(super.provider);

  @override
  List<Line> get allLines => (origin as SelectedLinesProvider).allLines;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
