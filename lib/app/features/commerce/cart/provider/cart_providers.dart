import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/provider/shopify_token_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/mixins/src/shopify_error.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

part 'cart_providers.g.dart';

final cartIsBusyProvider = StateProvider<bool>((ref) => false);
final cartCheckingOutProvider = StateProvider<bool>((ref) => false);
final isRedeemCoinProvider = StateProvider<bool>((ref) => false);

@riverpod
class CartController extends _$CartController {
  final shopifyCart = ShopifyCart.instance;

  @override
  Future<Cart> build() async {
    final savedCartId =
        await ref.read(securedAppStorageProvider).readValue('cartId');

    final user = ref.watch(authControllerProvider).requireValue;
    final shopifyProfile = user.shopifyProfile;

    try {
      if (shopifyProfile == null) {
        throw Exception('Shopify profile not found');
      }

      final accessToken = await getShopifyAccessToken(
        email: shopifyProfile.email,
        password: shopifyProfile.password!,
      );

      // because buyer identity could get removed through SHOPIFY CHECKOUT SHEET
      // listen to buyerIdentity changes
      // ensure buyer identity same as backend profile
      ref.listenSelf((previous, next) async {
        if (next.hasValue &&
            next.value?.buyerIdentity?.email != shopifyProfile.email) {
          Groveman.debug('Cart identity synced');
          await syncBuyerIdentity(shopifyProfile.email, accessToken);
        }
      });

      // create cart if null
      if (savedCartId == null) {
        return await _createNewCart(
          email: shopifyProfile.email,
          accessToken: accessToken,
        );
      }
      // otherwise fetch cart
      else {
        Groveman.info('savedCartId', error: savedCartId);

        try {
          final cart = await shopifyCart.getCartById(savedCartId);
          return cart;
        } catch (error) {
          // assume cart expired/invalid if getCartById fails
          // therefore create a new cart instance
          Groveman.warning('cartController getCartById failed', error: error);
          return await _createNewCart(
            email: shopifyProfile.email,
            accessToken: accessToken,
          );
        }
      }
    } catch (error) {
      Groveman.warning('cartController build', error: error);
      rethrow;
    }
  }

  Future<Cart> _createNewCart({
    required String email,
    String? accessToken,
  }) async {
    final cart = await shopifyCart.createCart(
      CartInput(
        buyerIdentity: CartBuyerIdentityInput(
          email: email,
          customerAccessToken: accessToken,
        ),
      ),
    );
    Groveman.info('_createNewCart', error: cart.id);
    await ref.read(securedAppStorageProvider).writeValue('cartId', cart.id);
    return cart;
  }

  Future<void> resetCart() async {
    final user = ref.watch(authControllerProvider).requireValue;
    final shopifyProfile = user.shopifyProfile;

    if (shopifyProfile == null) {
      throw Exception('Shopify profile not found');
    }

    if (user.emailAddress != null) {
      try {
        final accessToken = await getShopifyAccessToken(
          email: shopifyProfile.email,
          password: shopifyProfile.password!,
        );

        final cart = await _createNewCart(
          email: shopifyProfile.email,
          accessToken: accessToken,
        );
        state = AsyncValue.data(cart);
      } catch (error) {
        Groveman.warning('resetCart', error: error);
      } finally {
        ref.invalidate(maxRedeemableCoinsProvider);
      }
    }
  }

  // add to cart with selected variant
  Future<void> addToCart(String variantId, int quantity) async {
    final savedCartId =
        await ref.read(securedAppStorageProvider).readValue('cartId');
    final cartLineInput = CartLineInput(
      quantity: quantity,
      merchandiseId: variantId,
    );

    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      if (savedCartId == null) {
        await resetCart();
      }

      final updatedCart = await shopifyCart.addLineItemsToCart(
        cartId: state.value!.id,
        cartLineInputs: [cartLineInput],
      );
      state = AsyncValue.data(updatedCart);

      Groveman.debug('addToCart updatedCart', error: updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('addToCart shopifyException', error: error);
    } catch (error) {
      Groveman.warning('addToCart', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref.invalidate(maxRedeemableCoinsProvider);
    }
  }

  // add to cart without select variant
  Future<void> instantAddToCart(Product product, int quantity) async {
    final savedCartId =
        await ref.read(securedAppStorageProvider).readValue('cartId');
    final cartLineInput = CartLineInput(
      quantity: quantity,
      merchandiseId: product.productVariants.first.id,
    );

    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      if (savedCartId == null) {
        await resetCart();
      }

      final updatedCart = await shopifyCart.addLineItemsToCart(
        cartId: state.value!.id,
        cartLineInputs: [cartLineInput],
      );
      state = AsyncValue.data(updatedCart);

      Groveman.debug('instantAddToCart updatedCart', error: updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('instantAddToCart shopifyException', error: error);
    } catch (error) {
      Groveman.warning('instantAddToCart', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref.invalidate(maxRedeemableCoinsProvider);
    }
  }

  // change quantity
  Future<void> updateCart(
    String cartlineId,
    String variantId,
    int quantity,
  ) async {
    final cartLineInput = CartLineInput(
      id: cartlineId,
      quantity: quantity,
      merchandiseId: variantId,
    );
    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      final updatedCart = await shopifyCart.updateLineItemsInCart(
        cartId: state.value!.id,
        cartLineInputs: [cartLineInput],
      );
      state = AsyncValue.data(updatedCart);

      Groveman.debug('updateCart updatedCart', error: updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('updateCart shopifyException', error: error);
    } catch (error) {
      Groveman.warning('updateCart', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref.invalidate(maxRedeemableCoinsProvider);
    }
  }

  Future<void> removeFromCart(String lineId) async {
    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);
      final updatedCart = await shopifyCart.removeLineItemsFromCart(
        cartId: state.value!.id,
        lineIds: [lineId],
      );
      state = AsyncValue.data(updatedCart);

      Groveman.debug('removeFromCart updatedCart', error: updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('removeFromCart shopifyException', error: error);
    } catch (error) {
      Groveman.warning('removeFromCart', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref.invalidate(maxRedeemableCoinsProvider);
    }
  }

  Future<void> applyDiscountCode(String? discountCode) async {
    try {
      final updatedCart = await shopifyCart.updateCartDiscountCodes(
        cartId: state.value!.id,
        discountCodes: discountCode != null ? [discountCode] : [],
      );
      state = AsyncValue.data(updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('applyDiscountCode shopifyException', error: error);
    } catch (error, stackTrace) {
      Groveman.warning(
        'applyDiscountCode',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  Future<void> syncBuyerIdentity(
    String email,
    String? accessToken,
  ) async {
    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);
      final updatedCart = await shopifyCart.updateBuyerIdentityInCart(
        cartId: state.value!.id,
        buyerIdentity: CartBuyerIdentityInput(
          email: email,
          customerAccessToken: accessToken,
        ),
      );
      state = AsyncValue.data(updatedCart);

      Groveman.debug('syncBuyerIdentity updatedCart', error: updatedCart);
    } on ShopifyException catch (error) {
      Groveman.warning('syncBuyerIdentity shopifyException', error: error);
    } catch (error) {
      Groveman.warning('syncBuyerIdentity', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref
        ..invalidate(maxRedeemableCoinsProvider)
        ..invalidateSelf();
    }
  }

  /// Prepares the current cart for partial checkout by removing unselected items
  /// Returns a backup of the removed items to restore later
  Future<List<Line>> preparePartialCheckout(List<Line> selectedLines) async {
    if (selectedLines.isEmpty) {
      Groveman.warning('preparePartialCheckout', error: 'No lines selected');
      return [];
    }

    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      // Get all line IDs that are NOT selected (to be removed temporarily)
      final allLines = state.value!.lines;
      final selectedLineIds = selectedLines.map((line) => line.id!).toSet();
      final unselectedLines =
          allLines.where((line) => !selectedLineIds.contains(line.id)).toList();

      final unselectedLineIds = unselectedLines
          .where((line) => line.id != null)
          .map((line) => line.id!)
          .toList();

      if (unselectedLineIds.isEmpty) {
        // All items are selected, no need to modify cart
        return [];
      }

      // Remove unselected items from cart temporarily
      final updatedCart = await shopifyCart.removeLineItemsFromCart(
        cartId: state.value!.id,
        lineIds: unselectedLineIds,
      );

      state = AsyncValue.data(updatedCart);
      Groveman.debug(
        'preparePartialCheckout',
        error: 'Unselected items removed temporarily',
      );

      // Return backup of unselected lines to restore later
      return unselectedLines;
    } on ShopifyException catch (error) {
      Groveman.warning('preparePartialCheckout shopifyException', error: error);
      return [];
    } catch (error) {
      Groveman.warning('preparePartialCheckout', error: error);
      return [];
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
    }
  }

  /// Restores unselected items to cart after checkout
  /// If isCompleted is true, creates a new cart with unselected items
  /// If isCompleted is false (cancelled), adds unselected items back to existing cart
  Future<void> restoreUnselectedItems(
    List<Line> unselectedLines, {
    bool isCompleted = false,
  }) async {
    if (unselectedLines.isEmpty) return;

    try {
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      // Prepare cart line inputs from unselected lines
      final cartLineInputs = unselectedLines
          .where((line) => line.merchandise != null)
          .map(
            (line) => CartLineInput(
              quantity: line.quantity ?? 1,
              merchandiseId: line.merchandise!.id,
            ),
          )
          .toList();

      if (cartLineInputs.isEmpty) return;

      if (isCompleted) {
        // Create a new cart for completed checkout
        final user = ref.watch(authControllerProvider).requireValue;
        final shopifyProfile = user.shopifyProfile;

        if (shopifyProfile == null) {
          throw Exception('Shopify profile not found');
        }

        final accessToken = await getShopifyAccessToken(
          email: shopifyProfile.email,
          password: shopifyProfile.password!,
        );

        final newCart = await _createNewCart(
          email: shopifyProfile.email,
          accessToken: accessToken,
        );
        Groveman.debug(
          'restoreUnselectedItems',
          error: 'Created new cart for unselected items',
        );

        // Add unselected items to the new cart
        final updatedCart = await shopifyCart.addLineItemsToCart(
          cartId: newCart.id,
          cartLineInputs: cartLineInputs,
        );

        state = AsyncValue.data(updatedCart);
        Groveman.debug(
          'restoreUnselectedItems',
          error: 'Unselected items restored to new cart',
        );
      } else {
        // Cancelled checkout - add items back to existing cart
        final updatedCart = await shopifyCart.addLineItemsToCart(
          cartId: state.value!.id,
          cartLineInputs: cartLineInputs,
        );

        state = AsyncValue.data(updatedCart);
        Groveman.debug(
          'restoreUnselectedItems',
          error: 'Unselected items restored to existing cart',
        );
      }
    } on ShopifyException catch (error) {
      Groveman.warning('restoreUnselectedItems shopifyException', error: error);
    } catch (error) {
      Groveman.warning('restoreUnselectedItems', error: error);
    } finally {
      ref.read(cartIsBusyProvider.notifier).update((state) => false);
      ref.invalidate(maxRedeemableCoinsProvider);
    }
  }
}
