import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shopify_flutter/models/src/cart/lines/line/line.dart';

part 'checkout_line_provider.g.dart';

/// Provider to track which cart lines are selected for partial checkout
final selectedCartLinesProvider = StateProvider<Map<String, bool>>((ref) => {});

/// Provider to track if all items from a vendor are selected
final selectedVendorsProvider = StateProvider<Map<String, bool>>((ref) => {});

/// Provider to track if all items from a vendor are selected
final unselectedLineProvider = StateProvider<List<Line>>((ref) => []);

/// Provider to check if any items are selected for checkout
final hasSelectedItemsProvider = Provider<bool>((ref) {
  final selectedLines = ref.watch(selectedCartLinesProvider);
  return selectedLines.values.any((isSelected) => isSelected);
});

/// Provider to get all selected lines
@riverpod
List<Line> selectedLines(SelectedLinesRef ref, List<Line> allLines) {
  final selectedLineIds = ref.watch(selectedCartLinesProvider);
  return allLines.where((line) => selectedLineIds[line.id] == true).toList();
}

/// Toggle selection for a single cart line
void toggleCartLineSelection(WidgetRef ref, String lineId, String vendor) {
  final selectedLines = ref.read(selectedCartLinesProvider);
  final newValue = !(selectedLines[lineId] ?? false);

  ref.read(selectedCartLinesProvider.notifier).update(
        (state) => {
          ...state,
          lineId: newValue,
        },
      );

  // Update vendor selection if needed
  updateVendorSelectionState(ref, vendor);
}

/// Toggle selection for all lines from a vendor
void toggleVendorSelection(
  WidgetRef ref,
  String vendor,
  List<Line> vendorLines,
) {
  final selectedVendors = ref.read(selectedVendorsProvider);
  final newValue = !(selectedVendors[vendor] ?? false);

  // Update vendor selection state
  ref.read(selectedVendorsProvider.notifier).update(
        (state) => {
          ...state,
          vendor: newValue,
        },
      );

  // Update all lines from this vendor
  final selectedLines = ref.read(selectedCartLinesProvider);
  final updatedLines = {...selectedLines};

  for (final line in vendorLines) {
    if (line.id != null) {
      updatedLines[line.id!] = newValue;
    }
  }

  ref.read(selectedCartLinesProvider.notifier).state = updatedLines;
}

/// Update vendor selection state based on its lines
void updateVendorSelectionState(WidgetRef ref, String vendor) {
  final vendorLines = ref
      .read(selectedCartLinesProvider)
      .entries
      .where((entry) => entry.key.contains(vendor))
      .toList();

  final allSelected =
      vendorLines.isNotEmpty && vendorLines.every((line) => line.value);

  ref.read(selectedVendorsProvider.notifier).update(
        (state) => {
          ...state,
          vendor: allSelected,
        },
      );
}
