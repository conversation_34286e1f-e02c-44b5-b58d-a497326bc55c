// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cartControllerHash() => r'e732dc74e9cb2b5f97d78771259d5fc1f30cf415';

/// See also [CartController].
@ProviderFor(CartController)
final cartControllerProvider =
    AutoDisposeAsyncNotifierProvider<CartController, Cart>.internal(
  CartController.new,
  name: r'cartControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CartController = AutoDisposeAsyncNotifier<Cart>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
