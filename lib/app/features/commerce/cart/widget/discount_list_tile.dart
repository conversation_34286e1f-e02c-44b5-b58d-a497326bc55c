import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DiscountListTile extends ConsumerWidget {
  const DiscountListTile({
    super.key,
    this.onTap,
    this.discountTitle,
    this.amount,
    this.leadingIcon,
    this.tileColor,
    this.color,
  });
  final void Function()? onTap;
  final String? discountTitle;
  final String? amount;
  final IconData? leadingIcon;
  final Color? tileColor;
  final Color? color;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListTileTheme(
      data: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        tileColor: tileColor ?? Colors.white,
        iconColor: color ?? iconTheme(context).color,
        textColor: color ?? colorTheme(context).primary,
      ),
      child: ListTile(
        dense: true,
        leading: Icon(leadingIcon),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(discountTitle ?? ''),
            Text(amount != null ? '-\$$amount' : ''),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }
}

// class BrandListTileContainer extends ConsumerWidget {
//   const BrandListTileContainer({this.child, super.key});
//   final Widget? child;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Card(
//       clipBehavior: Clip.antiAlias,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(20),
//       ),
//       child: child,
//     );
//   }
// }

// class BrandExpansionTile extends ConsumerWidget {
//   const BrandExpansionTile({
//     super.key,
//     this.titleString,
//     this.leadingIcon,
//     this.color,
//     this.children,
//   });
//   final String? titleString;
//   final IconData? leadingIcon;
//   final Color? color;
//   final List<Widget>? children;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return ExpansionTileTheme(
//       data: ExpansionTileThemeData(
//         backgroundColor: Colors.white,
//         iconColor: color ?? iconTheme(context).color,
//         collapsedIconColor: color ?? iconTheme(context).color,
//         textColor: color ?? colorTheme(context).primary,
//         collapsedTextColor: color ?? colorTheme(context).primary,
//         shape: const Border(
//           top: BorderSide(color: Colors.transparent),
//           bottom: BorderSide(color: Colors.transparent),
//         ),
//       ),
//       child: ExpansionTile(
//         leading: Icon(leadingIcon),
//         title: Text(titleString ?? ''),
//         children: [
//           const Divider(height: 0, thickness: 0, color: Colors.black26),
//           ...ListTile.divideTiles(
//             color: Colors.black26,
//             tiles: children ?? [],
//           ).toList(),
//         ],
//         // onTap: onTap,
//       ),
//     );
//   }
// }
