import 'package:flutter/material.dart';
import 'package:gomama/app/features/commerce/address/provider/shopify_address_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:gomama/app/features/commerce/collections/provider/collection_providers.dart';
import 'package:gomama/app/features/commerce/price_match/provider/price_match_provider.dart';
import 'package:gomama/app/features/commerce/products/provider/favourite_product_providers.dart';
import 'package:gomama/app/features/commerce/products/provider/product_providers.dart';
import 'package:gomama/app/features/commerce/provider/commerce_banner_provider.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommerceRefreshIndicator extends ConsumerWidget {
  const CommerceRefreshIndicator({super.key, required this.child, this.offset});

  final Widget child;
  final double? offset;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RefreshIndicator(
      edgeOffset: offset ?? 0,
      onRefresh: () async {
        ref
          ..invalidate(commerceBannersProvider('home'))
          ..invalidate(commerceBannersProvider('order'))
          ..invalidate(collectionsProvider)
          ..invalidate(cartControllerProvider)
          ..invalidate(collectionProductsProvider)
          ..invalidate(commerceBannersProvider)
          ..invalidate(priceMatchRequestsProvider)
          ..invalidate(addressesProvider)
          ..invalidate(coinBalanceProvider)
          ..invalidate(maxRedeemableCoinsProvider)
          ..invalidate(favouriteProductsProvider)
          ..invalidate(favouriteProductIdsProvider());
      },
      child: child,
    );
  }
}
