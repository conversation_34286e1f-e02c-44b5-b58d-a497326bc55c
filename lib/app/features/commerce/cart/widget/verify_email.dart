import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';

void showEmailVerificationDialog({
  required BuildContext context,
  void Function()? onBack,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('Verify Your Email'),
        content: const Text(
          'Please verify your Email Address in the edit profile page to continue.',
        ),
        actions: [
          OutlinedButton(
            style: OutlinedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            onPressed: onBack ??
                () {
                  context.pop();
                },
            child: const Text('Back'),
          ),
          FilledButton(
            style: FilledButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            onPressed: () {
              const EditProfileRoute().go(context);
            },
            child: const Text('Confirm'),
          ),
        ],
      );
    },
  );
}
