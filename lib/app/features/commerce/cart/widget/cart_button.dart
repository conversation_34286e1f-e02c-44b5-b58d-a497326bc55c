import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/widget/verify_email.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CartButton extends ConsumerWidget {
  const CartButton({super.key, this.compact = false});

  final bool compact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cart = ref.watch(cartControllerProvider);

    return IconButton(
      visualDensity: VisualDensity.compact,
      icon: Stack(
        clipBehavior: Clip.none,
        children: [
          Icon(
            Icons.shopping_cart,
            size: compact ? 20 : 28,
            color: CustomColors.primaries,
          ),
          cart.when(
            data: (cart) => cart.totalQuantity == 0
                ? const SizedBox.shrink()
                : Positioned(
                    right: compact ? -8 : -4,
                    top: compact ? -10 : -8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: CustomColors.secondary,
                      ),
                      child: Text(
                        cart.totalQuantity.toString(),
                        style: textTheme(context).labelMedium!.copyWith(
                              fontWeight: FontWeight.bold,
                              color: CustomColors.primary,
                            ),
                      ),
                    ),
                  ),
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),
        ],
      ),
      onPressed: () async {
        try {
          // check user shopify profile
          final user = ref.watch(authControllerProvider).requireValue;
          final shopifyProfile = user.shopifyProfile;

          // verify email
          if (shopifyProfile == null) {
            showEmailVerificationDialog(context: context);
          } else {
            await const CartRoute().push(context);
          }
        } catch (error, stack) {
          Groveman.error(
            'cart button',
            error: error,
            stackTrace: stack,
          );
        }
      },
    );
  }
}
