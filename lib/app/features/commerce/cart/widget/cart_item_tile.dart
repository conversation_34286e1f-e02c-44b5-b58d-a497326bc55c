import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/checkout_line_provider.dart';
import 'package:gomama/app/features/commerce/products/widget/variant_picker.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/models/src/cart/lines/line/line.dart';

class CartItemTile extends HookConsumerWidget {
  const CartItemTile(this.cartLine, {super.key});

  final Line cartLine;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartIsBusy = ref.watch(cartIsBusyProvider);
    final productVariant = cartLine.merchandise;

    final selectedLines = ref.watch(selectedCartLinesProvider);
    final isSelected = selectedLines[cartLine.id] ?? false;

    final _updatingLine = useState(false);

    return SizedBox(
      height: 120,
      child: Row(
        children: [
          // Partial checkout selection checkbox
          Checkbox(
            visualDensity: VisualDensity.compact,
            value: isSelected,
            shape: const CircleBorder(),
            side: const BorderSide(color: CustomColors.placeholder),
            onChanged: (isChecked) {
              final vendor = productVariant?.product?.vendor ?? '';
              toggleCartLineSelection(ref, cartLine.id!, vendor);
            },
          ),
          Expanded(
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (productVariant != null &&
                        productVariant.product != null) {
                      ProductRoute(productVariant.product!.handle!)
                          .push(context);
                    }
                  },
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: CachedNetworkImage(
                      imageUrl: productVariant?.image?.originalSrc ?? '',
                      imageBuilder: (context, imageProvider) {
                        return DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: DecorationImage(
                              image: imageProvider,
                              fit: BoxFit.cover,
                            ),
                          ),
                        );
                      },
                      errorWidget: (context, url, error) =>
                          const Icon(CustomIcon.error),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            productVariant?.product?.title ?? '',
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      color: CustomColors.primary,
                                    ),
                          ),
                          const SizedBox(height: 4),
                          // show if more than 1 variant
                          if (productVariant != null &&
                              productVariant.product != null &&
                              productVariant.product!.productVariants.length >
                                  1) ...[
                            GestureDetector(
                              onTap: () {
                                showVariantPicker(
                                  context: context,
                                  product: productVariant.product,
                                  cartLine: cartLine,
                                );
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(32),
                                  color: Colors.grey.shade200,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      productVariant.title,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelMedium!
                                          .copyWith(color: Colors.grey),
                                    ),
                                    const SizedBox(width: 4),
                                    const Icon(
                                      CustomIcon.keyboardArrowDown,
                                      color: Colors.grey,
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                          ],
                          Text(
                            productVariant?.price.formattedPrice ?? '',
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      color: CustomColors.primary,
                                    ),
                          ),
                        ],
                      ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(32),
                          ),
                          child: SizedBox(
                            width: 56,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                GestureDetector(
                                  onTap: (cartIsBusy == true ||
                                          _updatingLine.value == true ||
                                          cartLine.quantity == null ||
                                          productVariant == null)
                                      ? null
                                      : () async {
                                          try {
                                            _updatingLine.value = true;

                                            // remove item if 1
                                            if ((cartLine.quantity ?? 1) <= 1) {
                                              await ref
                                                  .read(
                                                    cartControllerProvider
                                                        .notifier,
                                                  )
                                                  .removeFromCart(
                                                    cartLine.id ?? '',
                                                  );

                                              if (context.mounted) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'Item removed from cart',
                                                    ),
                                                  ),
                                                );
                                              }
                                            } else {
                                              await ref
                                                  .read(
                                                    cartControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateCart(
                                                    cartLine.id ?? '',
                                                    productVariant.id,
                                                    (cartLine.quantity ?? 1) -
                                                        1,
                                                  );
                                              if (context.mounted) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'Cart updated',
                                                    ),
                                                  ),
                                                );
                                              }
                                            }
                                          } catch (e) {
                                            Groveman.error(
                                              'reduce cart quatity button',
                                              error: e,
                                            );
                                          } finally {
                                            _updatingLine.value = false;
                                            ref.invalidate(
                                              cartControllerProvider,
                                            );
                                          }
                                        },
                                  child: const Icon(
                                    Icons.remove,
                                    size: 14,
                                    color: CustomColors.primary,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                if (_updatingLine.value == true)
                                  const SizedBox(
                                    height: 12,
                                    width: 12,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                else
                                  Text(
                                    '${cartLine.quantity}',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelMedium!
                                        .copyWith(
                                          color: CustomColors.primary,
                                        ),
                                  ),
                                const SizedBox(width: 4),
                                GestureDetector(
                                  onTap: (cartIsBusy == true ||
                                          _updatingLine.value == true ||
                                          cartLine.quantity == null ||
                                          productVariant == null)
                                      ? null
                                      : () async {
                                          try {
                                            _updatingLine.value = true;

                                            if (cartLine.quantity! <
                                                (cartLine.merchandise!
                                                    .quantityAvailable)) {
                                              await ref
                                                  .read(
                                                    cartControllerProvider
                                                        .notifier,
                                                  )
                                                  .updateCart(
                                                    cartLine.id!,
                                                    productVariant.id,
                                                    (cartLine.quantity ?? 1) +
                                                        1,
                                                  );

                                              if (context.mounted) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'Cart updated',
                                                    ),
                                                  ),
                                                );
                                              }
                                            } else if (cartLine.quantity! >=
                                                (cartLine.merchandise!
                                                    .quantityAvailable)) {
                                              if (context.mounted) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  const SnackBar(
                                                    content: Text(
                                                      'Max quantity reached',
                                                    ),
                                                  ),
                                                );
                                              }
                                            }
                                          } catch (e) {
                                            Groveman.error(
                                              'increase cart quatity button',
                                              error: e,
                                            );
                                          } finally {
                                            _updatingLine.value = false;
                                            ref.invalidate(
                                              cartControllerProvider,
                                            );
                                          }
                                        },
                                  child: const Icon(
                                    Icons.add,
                                    size: 14,
                                    color: CustomColors.primary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
