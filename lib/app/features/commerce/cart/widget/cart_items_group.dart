import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/provider/checkout_line_provider.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_item_tile.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_flutter/shopify_flutter.dart';

class CartItemsGroup extends ConsumerWidget {
  const CartItemsGroup({
    super.key,
    required this.vendor,
    required this.cartLines,
  });

  final String vendor;
  final List<Line> cartLines;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedVendors = ref.watch(selectedVendorsProvider);
    final isSelected = selectedVendors[vendor] ?? false;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // vendor
          GestureDetector(
            onTap: () {
              // TODO: go vendor page?
            },
            child: Row(
              children: [
                // Vendor selection checkbox for partial checkout
                Checkbox(
                  visualDensity: VisualDensity.compact,
                  value: isSelected,
                  shape: const CircleBorder(),
                  side: const BorderSide(color: CustomColors.placeholder),
                  onChanged: (isChecked) {
                    toggleVendorSelection(ref, vendor, cartLines);
                  },
                ),
                const Icon(Icons.store),
                const SizedBox(width: 6),
                Text(
                  vendor,
                  style: textTheme(context).bodyMedium!.copyWith(
                        color: CustomColors.primary,
                      ),
                ),
                // const SizedBox(width: 16),
                // const Icon(Icons.chevron_right),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // items
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return CartItemTile(cartLines[index]);
            },
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemCount: cartLines.length,
          ),
        ],
      ),
    );
  }
}
