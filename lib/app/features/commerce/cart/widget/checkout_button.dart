import 'package:flutter/material.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/commerce/cart/provider/cart_providers.dart';
import 'package:gomama/app/features/commerce/cart/provider/checkout_line_provider.dart';
import 'package:gomama/app/features/commerce/coin/provider/coins_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shopify_checkout_sheet_kit/shopify_checkout_sheet_kit.dart';
import 'package:shopify_flutter/models/src/cart/lines/line/line.dart';

class CheckoutButton extends ConsumerStatefulWidget {
  const CheckoutButton(this.checkoutUrl, {super.key});

  final String? checkoutUrl;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CheckoutButtonState();
}

class _CheckoutButtonState extends ConsumerState<CheckoutButton> {
  bool isCompleted = false; // to indicate checkout complete
  final _shopifyCheckoutSheetKitPlugin = ShopifyCheckoutSheetKit();

  @override
  void initState() {
    super.initState();
    _configureCheckout();
  }

  Future<void> _configureCheckout() async {
    await _shopifyCheckoutSheetKitPlugin.configureCheckout(
      colorScheme: ShopifyCheckoutSheetKitColorScheme.automatic,
      tintColor: '#FF0000',
      backgroundColor: '#FFFFFF',
      title: 'Checkout',
      preloadingEnabled: true,
    );
  }

  // reset flag & invalidate cart
  void resetState() {
    ref.read(isRedeemCoinProvider.notifier).update((state) => false);
    ref.read(cartCheckingOutProvider.notifier).update((state) => false);
    ref.read(cartIsBusyProvider.notifier).update((state) => false);
    ref.invalidate(cartControllerProvider);
  }

  // NOTE: not sure if iOS triggers this on complete checkout
  // NOTE: ANDROID does not trigger this when checkout completed & close sheet
  Future<void> completeCheckout() async {
    // If this was a partial checkout, restore unselected items to a new cart
    final unselectedLines = ref.read(unselectedLineProvider);
    if (unselectedLines.isNotEmpty) {
      // Restore unselected items to a new cart (isCompleted = true)
      await ref
          .read(cartControllerProvider.notifier)
          .restoreUnselectedItems(unselectedLines, isCompleted: true);

      // Clear unselected lines provider
      ref.read(selectedCartLinesProvider.notifier).state = {};
      ref.read(selectedVendorsProvider.notifier).state = {};
      ref.read(unselectedLineProvider.notifier).state = [];
    } else {
      // reset cart for regular checkout
      await ref.read(cartControllerProvider.notifier).resetCart();
    }

    resetState();

    // completed flag
    setState(() {
      isCompleted = true;
    });

    if (!mounted) {
      return;
    }

    // navigate to order placed page after order completes
    await const OrderPlacedRoute().push(context);
  }

  // NOTE: only ANDROID triggers this after CANCEL or COMPLETE checkout & close the checkout sheet
  Future<void> cancelCheckout() async {
    // NOTE: do this checking to prevent running cancel logic after complete checkout
    if (isCompleted) {
      // go to order placed page
      // await const OrderPlacedRoute().push(context);
      return;
    }

    // If this was a partial checkout and was cancelled, restore the original cart
    final unselectedLines = ref.read(unselectedLineProvider);
    if (unselectedLines.isNotEmpty) {
      // Restore unselected items to the existing cart (isCompleted = false)
      await ref
          .read(cartControllerProvider.notifier)
          .restoreUnselectedItems(unselectedLines);

      // Clear unselected lines provider
      ref.read(unselectedLineProvider.notifier).state = [];
    }

    resetState();

    // cancel coin redemption (if any)
    // clear discount code from cart
    await ref.read(cancelRedeemProvider.future);
    await ref.read(cartControllerProvider.notifier).applyDiscountCode(null);
    ref
      ..invalidate(cartControllerProvider)
      ..invalidate(coinBalanceProvider);
  }

  Future<void> _presentCheckout() async {
    try {
      ref.read(cartCheckingOutProvider.notifier).update((state) => true);
      ref.read(cartIsBusyProvider.notifier).update((state) => true);

      final hasSelected = ref.read(hasSelectedItemsProvider);
      // Handle partial checkout if there are selected items
      if (hasSelected == true) {
        // Get the cart
        final cart = await ref.read(cartControllerProvider.future);
        final selectedLines = ref.read(selectedLinesProvider(cart.lines));

        // Temporarily remove unselected items from cart
        final unselectedLines = await ref
            .read(cartControllerProvider.notifier)
            .preparePartialCheckout(selectedLines);

        // Store in the provider for restoration after checkout
        ref.read(unselectedLineProvider.notifier).state = unselectedLines;
      }

      // check redeem coin flag
      final isRedeemCoin = ref.read(isRedeemCoinProvider);

      if (widget.checkoutUrl == null || widget.checkoutUrl == '') {
        resetState();
        return;
      }

      // apply discount code if redeem coin is true
      if (isRedeemCoin == true) {
        // fetch max redeemable coin amount
        final maxRedeemCoins =
            await ref.read(maxRedeemableCoinsProvider.future);
        Groveman.debug('maxRedeemCoins', error: maxRedeemCoins);

        // redeem using the amount
        final discountCode = await ref.read(
          redeemCoinsProvider(maxRedeemCoins.actualMaxRedeemable).future,
        );
        Groveman.debug('discountCode', error: discountCode);

        // apply discount code
        await ref
            .read(cartControllerProvider.notifier)
            .applyDiscountCode(discountCode);
      }

      // show checkout
      await _shopifyCheckoutSheetKitPlugin.presentCheckout(
        // checkoutUrl:
        //     'https://gaincue.myshopify.com/checkouts/cn/Z2NwLWFzaWEtc291dGhlYXN0MTowMUpYVkE4NVdaUERKNlAwUFdWTUtUNUcxTg',
        checkoutUrl: widget.checkoutUrl ?? '',
        onCompleted: (orderId) {
          completeCheckout();
          Groveman.debug('presentCheckout onCompleted', error: orderId);
        },
        onCanceled: () {
          cancelCheckout();
          Groveman.debug('presentCheckout onCancelled');
        },
        onFailed: (errorCode, errorMessage) {
          Groveman.error('presentCheckout onFailed', error: errorMessage);
        },
      );
    } on CheckoutException catch (e) {
      Groveman.error('presentCheckout catch', error: e.message);
    }
  }

  @override
  Widget build(BuildContext context) {
    final cart = ref.watch(cartControllerProvider).valueOrNull;
    final cartIsBusy = ref.watch(cartIsBusyProvider);
    final hasSelected = ref.watch(hasSelectedItemsProvider);

    return FilledButton(
      style: FilledButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      onPressed:
          (cart?.totalQuantity ?? 0) <= 0 || cartIsBusy == true || !hasSelected
              ? null
              : _presentCheckout,
      child: const Text('Checkout'),
    );
  }
}
