import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommerceNavigationPanel extends ConsumerWidget {
  const CommerceNavigationPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: 80 + 32,
      child: GridView.count(
        crossAxisCount: 5,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        childAspectRatio: 9 / 16,
        children: [
          _PanelButton(
            label: 'My Orders',
            icon: Icons.history,
            onPressed: () => const MyOrdersRoute().push(context),
          ),
          _PanelButton(
            label: 'Price\nMatch',
            icon: Icons.attach_money,
            onPressed: () => const PriceMatchRoute().push(context),
          ),
          _PanelButton(
            label: 'My\nAddresses',
            icon: Icons.place,
            onPressed: () => const AddressRoute().push(context),
          ),
          // _PanelButton(
          //   label: 'Go!Mama Feedback\nCommunity',
          //   icon: Icons.people,
          //   onPressed: () {},
          // ),
          _PanelButton(
            label: 'Daily Coin\nRewards',
            icon: Icons.monetization_on,
            onPressed: () {
              const DailyCoinsRoute().push(context);
            },
          ),
          _PanelButton(
            label: 'Go!Mama\nCertified',
            icon: Icons.verified,
            // assume this collection ID is specially reserved
            onPressed: () =>
                const CollectionRoute('gid://shopify/Collection/478138171639')
                    .push(context),
          ),
        ],
      ),
    );
  }
}

class _PanelButton extends ConsumerWidget {
  const _PanelButton({
    required this.label,
    required this.icon,
    required this.onPressed,
  });

  final String label;
  final IconData icon;
  final void Function() onPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        IconButton.filled(
          style: IconButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          onPressed: onPressed,
          icon: Icon(
            icon,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelSmall!.copyWith(
                fontSize: 8,
                color: CustomColors.primary,
                fontWeight: FontWeight.bold,
                height: 1.2,
              ),
        ),
      ],
    );
  }
}
