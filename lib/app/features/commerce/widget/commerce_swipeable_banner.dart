import 'dart:async';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/commerce/cart/widget/cart_button.dart';
import 'package:gomama/app/features/commerce/provider/commerce_banner_provider.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:url_launcher/url_launcher_string.dart';

class CommerceSwipeableBanner extends HookConsumerWidget {
  const CommerceSwipeableBanner({
    super.key,
    required this.bannerType,
    this.showSearch = true,
    this.aspectRatio,
    this.borderRadius,
    this.elevation,
    this.clipBehavior,
    this.label,
  });

  final String bannerType;
  final bool? showSearch;
  final double? aspectRatio;
  final BorderRadiusGeometry? borderRadius;
  final double? elevation;
  final Clip? clipBehavior;
  final String? label;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _bannerController = usePageController();
    final currentPage = useState<int>(0);
    final banners = ref.watch(commerceBannersProvider(bannerType));

    // auto banner scroll
    useEffect(
      () {
        if (banners.valueOrNull == null) return null;
        if (banners.value?.isEmpty == true) return null;

        final timer =
            Timer.periodic(const Duration(seconds: 12), (Timer timer) {
          currentPage.value++;
          _bannerController.animateToPage(
            currentPage.value % banners.value!.length,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        });

        return timer.cancel;
      },
      [banners, _bannerController],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (label?.isNotEmpty == true && banners.value?.isNotEmpty == true) ...[
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              'Earn More Coins',
              style: textTheme(context)
                  .titleMedium!
                  .copyWith(color: CustomColors.primary),
              textAlign: TextAlign.start,
            ),
          ),
          const SizedBox(height: 12),
        ],
        Material(
          borderRadius: borderRadius ?? BorderRadius.zero,
          elevation: elevation ?? 0,
          clipBehavior: clipBehavior ?? Clip.none,
          child: Stack(
            children: [
              banners.when(
                data: (banners) {
                  final galleryFrames = banners
                      ?.map(
                        (banner) => PhotoViewGalleryPageOptions(
                          tightMode: true,
                          imageProvider:
                              CachedNetworkImageProvider(banner.imageUrl ?? ''),
                          minScale: PhotoViewComputedScale.covered,
                          disableGestures:
                              true, // prevent photo swipe due to covered overflow
                        ),
                      )
                      .toList();

                  if (banners == null || banners.isEmpty == true) {
                    // dont show coin banner if not provided
                    if (bannerType == 'coin') {
                      return const SizedBox.shrink();
                    }

                    return AspectRatio(
                      aspectRatio: aspectRatio ?? 780 / 640,
                      child: ColoredBox(
                        color: CustomColors.placeholder.withAlpha(150),
                      ),
                    );
                  }

                  return Stack(
                    children: [
                      AspectRatio(
                        aspectRatio: aspectRatio ?? 780 / 640,
                        child: PhotoViewGallery(
                          pageController: _bannerController,
                          pageOptions: galleryFrames,
                          loadingBuilder: (context, progress) => Stack(
                            children: [
                              Positioned.fill(
                                child: ColoredBox(
                                  color:
                                      CustomColors.placeholder.withAlpha(150),
                                ),
                              ),
                              const Center(child: CircularProgressIndicator()),
                            ],
                          ),
                          backgroundDecoration: const BoxDecoration(
                            color: Colors.transparent,
                          ),
                          onPageChanged: (page) {
                            currentPage.value = page;
                          },
                        ),
                      ),
                      // pagination dots
                      if (banners.length > 1)
                        Positioned(
                          bottom: 12,
                          left: 0,
                          right: 0,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(banners.length, (index) {
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 3.5),
                                child: Icon(
                                  currentPage.value == index
                                      ? Icons.radio_button_checked
                                      : Icons.radio_button_off,
                                  size: 9,
                                ),
                              );
                            }),
                          ),
                        ),
                      // action handler
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: () async {
                            final actionLink =
                                banners[currentPage.value].actionLink;
                            if (actionLink != null) {
                              try {
                                await launchUrlString(
                                  actionLink,
                                  mode: LaunchMode.externalApplication,
                                );
                              } catch (e) {
                                Groveman.error('commerceBanner', error: e);
                              }
                            }
                          },
                        ),
                      ),
                    ],
                  );
                },
                error: (error, stack) {
                  return AspectRatio(
                    aspectRatio: aspectRatio ?? 780 / 640,
                    child: ColoredBox(
                      color: CustomColors.placeholder.withAlpha(150),
                    ),
                  );
                },
                loading: () {
                  return Stack(
                    children: [
                      AspectRatio(
                        aspectRatio: aspectRatio ?? 780 / 640,
                        child: ColoredBox(
                          color: CustomColors.placeholder.withAlpha(150),
                        ),
                      ),
                      const Positioned.fill(
                        child: Center(child: CircularProgressIndicator()),
                      ),
                    ],
                  );
                },
              ),
              if (showSearch == true)
                Positioned(
                  top: max(
                      mediaQuery(context).viewPadding.top, kTextTabBarHeight),
                  left: 32,
                  right: 32,
                  child: Hero(
                    tag: 'commerce-searchbar',
                    child: SizedBox(
                      height: 40,
                      child: Material(
                        elevation: 4,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(12)),
                        child: TextField(
                          readOnly: true,
                          onTap: () {
                            const CommerceSearchRoute().push(context);
                          },
                          decoration: InputDecoration(
                            prefixIcon: const Icon(
                              Icons.search,
                              size: 20,
                              color: CustomColors.primaries,
                            ),
                            suffixIcon: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [CartButton(compact: true)],
                            ),
                            hintText: 'Search products',
                            hintStyle: textTheme(context)
                                .bodyMedium!
                                .copyWith(color: CustomColors.placeholder),
                            contentPadding:
                                const EdgeInsets.fromLTRB(0, 3.5, 0, 0),
                            border: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
