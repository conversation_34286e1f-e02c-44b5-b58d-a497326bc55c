import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/widgets/custom_list_tile.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AboutView extends HookConsumerWidget {
  const AboutView({super.key});

  static const routeName = 'about';
  static const routePath = '/about';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [CustomColors.secondary, CustomColors.primaries.shade100],
          begin: Alignment.topLeft,
          end: Alignment.topRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomColors.secondary,
                  CustomColors.primaries.shade100,
                ],
                begin: Alignment.topLeft,
                end: Alignment.topRight,
              ),
            ),
          ),
          iconTheme: const IconThemeData(
            color: CustomColors.primaries,
          ),
          foregroundColor: CustomColors.primaries,
          title: const Text(
            'About',
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        body: ClipPath(
          clipper: RoundedClipper(),
          child: Container(
            color: Colors.white,
            height: double.infinity,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 32),
                  CustomListTile(
                    leadingIcon: CustomIcon.info,
                    titleString: 'About Us',
                    onTap: () {},
                  ),
                  const SizedBox(height: 12),
                  CustomListTile(
                    leadingIcon: CustomIcon.info,
                    titleString: 'Privacy Policy',
                    onTap: () {},
                  ),
                  const SizedBox(height: 12),
                  CustomListTile(
                    leadingIcon: CustomIcon.info,
                    titleString: 'Terms of Service',
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
