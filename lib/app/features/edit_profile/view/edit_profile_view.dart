import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/extensions.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/edit_profile/view/edit_email_sheet.dart';
import 'package:gomama/app/features/edit_profile/view/edit_mobile_number_sheet.dart';
import 'package:gomama/app/features/edit_profile/view/edit_username_sheet.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class EditProfileView extends HookConsumerWidget {
  const EditProfileView({super.key});

  static const routeName = 'edit-profile';
  static const routePath = 'edit';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const BrandScaffold(
      title: Text('Edit Profile'),
      physics: NeverScrollableScrollPhysics(),
      resizeToAvoidBottomInset: false,
      child: _Body(),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authControllerProvider).requireValue;

    return BrandBottomSheet(
      minHeight: mediaQuery(context).size.height / 2,
      maxHeight: max(
            mediaQuery(context).viewPadding.top,
            kToolbarHeight / 2,
          ) +
          100,
      slivers: [
        const SliverPadding(padding: EdgeInsets.only(top: 12)),
        SliverPadding(
          padding: const EdgeInsets.fromLTRB(20, 8, 20, 0),
          sliver: SliverToBoxAdapter(
            child: Column(
              children: [
                _VerifyCard(isVerified: user.isVerified),
                const SizedBox(height: 32),
                const _EditProfileForm(),
              ],
            ),
          ),
        ),
        const SliverPadding(padding: EdgeInsets.only(top: 32)),
      ],
    );
  }
}

class _VerifyCard extends ConsumerWidget {
  const _VerifyCard({required this.isVerified});

  final bool isVerified;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CustomPaint(
      painter: _BorderPainter(),
      child: DecoratedBox(
        decoration: BoxDecoration(
          image: const DecorationImage(
            image: AssetImage('assets/images/goma_mascot.png'),
            alignment: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [
              Colors.white,
              CustomColors.secondaryLight,
              CustomColors.primaryExtraLight,
            ],
            stops: [0.0, 0.5283, 1.0],
            begin: Alignment(-0.777, 0),
            end: Alignment(1.1141, 0),
            transform: GradientRotation(102.97 - 90),
          ),
          boxShadow: const [
            BoxShadow(
              color: Color(0x1A000000),
              blurRadius: 20,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            onTap: () {
              const VerificationRoute().push(context);
            },
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
              child: Align(
                alignment: Alignment.topLeft,
                child: FractionallySizedBox(
                  widthFactor: 0.75,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            'assets/images/gomama_type_logo.png',
                            width: 25,
                          ),
                          const SizedBox(width: 12),
                          Column(
                            children: [
                              if (isVerified)
                                Text(
                                  'Re-verify your',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        color: CustomColors.primary,
                                        fontWeight: FontWeight.bold,
                                        height: 1.2,
                                      ),
                                ),
                              Text(
                                'Go!Mama Pass',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      color: CustomColors.primary,
                                      fontWeight: FontWeight.bold,
                                      height: 1.2,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(width: 2),
                          const Icon(CustomIcon.keyboardArrowRight),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isVerified
                            ? 'Re-verify if you have a newborn, a new foster child, or need to update your details.'
                            : 'Verify for access with Singpass or selfie verification.',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _EditProfileForm extends HookConsumerWidget {
  const _EditProfileForm();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authControllerProvider).requireValue;
    final usernameTextController =
        useTextEditingController(text: user.username);
    final phoneTextController =
        useTextEditingController(text: user.fullMobileNumber);
    final emailTextController =
        useTextEditingController(text: user.emailAddress);

    ref
      ..listen(
          authControllerProvider.selectAsync((state) => state.emailAddress),
          (previous, next) async {
        emailTextController.text = await next ?? await previous ?? '';
      })
      ..listen(
          authControllerProvider.selectAsync((state) => state.fullMobileNumber),
          (previous, next) async {
        phoneTextController.text = await next ?? await previous ?? '';
      })
      ..listen(authControllerProvider.selectAsync((state) => state.username),
          (previous, next) async {
        usernameTextController.text = await next ?? await previous ?? '';
      });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        TextFormField(
          readOnly: true,
          controller: phoneTextController,
          style: const TextStyle(color: Colors.grey),
          decoration: InputDecoration(
            suffix: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  (user.isMobileNumberVerified ?? false)
                      ? 'Verified'
                      : 'Unverified',
                  style: TextStyle(
                    color: (user.isMobileNumberVerified ?? false)
                        ? Colors.green
                        : Colors.red,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(CustomIcon.keyboardArrowRight),
              ],
            ),
            border: const OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            labelText: 'Mobile Number',
            floatingLabelBehavior: FloatingLabelBehavior.always,
          ),
          onTap: () {
            if (!user.isMobileNumberVerified!) {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.white,
                builder: (context) {
                  return const EditMobileNumberSheet();
                },
              );
            }
          },
        ),
        const SizedBox(height: 24),
        TextFormField(
          readOnly: true,
          controller: emailTextController,
          style: const TextStyle(color: Colors.grey),
          decoration: InputDecoration(
            suffix: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  (user.isEmailAddressVerified ?? false)
                      ? 'Verified'
                      : 'Unverified',
                  style: TextStyle(
                    color: (user.isEmailAddressVerified ?? false)
                        ? Colors.green
                        : Colors.red,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(CustomIcon.keyboardArrowRight),
              ],
            ),
            border: const OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            labelText: 'Email Address',
            floatingLabelBehavior: FloatingLabelBehavior.always,
          ),
          onTap: () {
            if (!user.isEmailAddressVerified!) {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.white,
                builder: (context) {
                  return const EditEmailAddressSheet();
                },
              );
            }
          },
        ),
        const SizedBox(height: 24),
        Stack(
          children: [
            TextFormField(
              readOnly: true,
              controller: usernameTextController,
              style: const TextStyle(color: Colors.grey),
              decoration: const InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                ),
                labelText: 'Username',
                floatingLabelBehavior: FloatingLabelBehavior.always,
              ),
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.white,
                  builder: (context) {
                    return EditUsernameSheet(usernameTextController.text);
                  },
                );
              },
            ),
            const Positioned(
              right: 10,
              top: 0,
              bottom: 0,
              child: Icon(CustomIcon.keyboardArrowRight),
            ),
          ],
        ),
        const SizedBox(height: 20),
        TextFormField(
          readOnly: true,
          initialValue: user.fullName,
          decoration: const InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: 'Full Name',
            hintText: 'GO!MAMA',
          ),
        ),
        const SizedBox(height: 20),
        TextFormField(
          readOnly: true,
          initialValue: user.gender,
          decoration: const InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: 'Gender',
          ),
        ),
        const SizedBox(height: 20),
        TextFormField(
          readOnly: true,
          initialValue: user.birthday != null
              ? DateFormat('dd/MM/yyyy').format(user.birthday!)
              : '',
          decoration: const InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: 'Date of birth',
            hintText: 'DD/MM/YYYY',
          ),
        ),
        // FormBuilderDateTimePicker(
        //   name: 'Date of birth',
        //   inputType: InputType.date,
        //   initialDate: user.birthday,
        //   decoration: const InputDecoration(
        //     labelText: 'Date of Birth',
        //     suffixIcon: Icon(Icons.calendar_today_outlined),
        //     hintText: 'DD/MM/YYYY',
        //     floatingLabelBehavior: FloatingLabelBehavior.always,
        //   ),
        //   validator: FormBuilderValidators.compose([
        //     FormBuilderValidators.required(
        //       errorText: 'Please pick your date of birth',
        //     ),
        //   ]),
        //   textInputAction: TextInputAction.next,
        //   valueTransformer: (value) {
        //     if (value == null) {
        //       return '';
        //     }
        //     return value.toIso8601String();
        //   },
        // ),
        const SizedBox(height: 24),
        if (user.childrenBirthdays?.isNotEmpty ?? false)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Child's date of birth",
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                height: 130,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  itemBuilder: (context, index) {
                    return buildChildColumn(
                      'assets/images/baby.png',
                      '${index.toOrdinal().capitalize()} Child',
                      DateFormat('dd/MM/yyyy')
                          .format(user.childrenBirthdays![index]),
                    );
                  },
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 16),
                  itemCount: user.childrenBirthdays?.length ?? 0,
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        const Text(
          'Tips: Username, Gender, and Date of Birth will be updated automatically after verification via Gomama/Singpass.',
          style: TextStyle(
            fontSize: 12,
            color: CustomColors.primary,
          ),
        ),
        const SizedBox(height: 16),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            BrandButton.singpass(
              onPressed: () {
                const DeleteAccountRoute().push(context);
              },
              child: const Text('Delete account'),
            ),
          ],
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget buildChildColumn(
    String imagePath,
    String childName,
    String birthDate,
  ) {
    return Column(
      children: [
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 60,
          ),
          child: Image.asset(
            imagePath,
            fit: BoxFit.cover,
          ),
        ),
        const SizedBox(height: 6),
        Text(childName),
        Text(
          birthDate,
          style: const TextStyle(
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

class _BorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final rrect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      const Radius.circular(16),
    );

    // First gradient
    paint.shader = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        CustomColors.secondary.withOpacity(0.5),
        const Color.fromRGBO(251, 218, 186, 0),
      ],
      stops: const [0.0101, 0.9117],
    ).createShader(rrect.outerRect);
    canvas.drawRRect(rrect, paint);

    // Second gradient
    paint.shader = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color.fromRGBO(251, 218, 186, 0),
        CustomColors.primaryLight.withOpacity(0.5),
      ],
      stops: const [0.0006, 0.9577],
    ).createShader(rrect.outerRect);

    canvas.drawRRect(rrect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
