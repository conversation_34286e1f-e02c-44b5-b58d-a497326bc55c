import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/auth/model/otp.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/auth/widget/auth_otp_form.dart';
import 'package:gomama/app/features/edit_profile/provider/profile_providers.dart';
import 'package:gomama/app/features/edit_profile/view/edit_profile_otp_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EditEmailAddressSheet extends HookConsumerWidget {
  const EditEmailAddressSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());
    final errorText = useState<String?>(null);
    final isBusy = useState(false);

    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.of(context).viewInsets.bottom,
      ),
      child: FormBuilder(
        key: _formKey.value,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                const SizedBox(width: double.infinity, height: kToolbarHeight),
                Text(
                  'Edit Email Address',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Positioned(
                  top: 4,
                  right: 8,
                  child: CloseButton(),
                ),
              ],
            ),
            FormBuilderTextField(
              name: 'email_address',
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'Email Address*',
                floatingLabelBehavior: FloatingLabelBehavior.always,
                hintText: 'Email Address',
                errorText: errorText.value,
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  errorText: 'Please fill in your email address',
                ),
                FormBuilderValidators.email(
                  errorText: 'Please insert the correct email address',
                ),
              ]),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: CustomColors.primary,
                  foregroundColor: Colors.white,
                ),
                onPressed: () async {
                  errorText.value = null;

                  // Validate and save the form values
                  final success =
                      _formKey.value.currentState?.saveAndValidate();

                  if (success != true) {
                    return;
                  }

                  final email = _formKey
                      .value.currentState!.value['email_address']
                      .toString();

                  isBusy.value = true;

                  try {
                    await ref
                        .read(requestEmailOrPhoneOtpProvider(email).future);

                    if (context.mounted) {
                      Navigator.pop(context);

                      await showCupertinoDialog(
                        context: context,
                        builder: (context) {
                          return EditProfileOtpForm(newEmail: email);
                        },
                      );
                    }
                  } catch (error) {
                    if (error is AppNetworkResponseException) {
                      errorText.value = error.message;
                    }

                    isBusy.value = false;

                    Groveman.warning('EditEmailAddressSheet', error: error);
                  }
                },
                child: Text(
                  isBusy.value ? 'Sending an email...' : 'Verify Email Address',
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
