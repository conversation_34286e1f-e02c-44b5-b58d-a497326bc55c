import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/custom_validators.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:gomama/app/features/auth/widget/countdown_timer.dart';
import 'package:gomama/app/features/auth/widget/otp_field.dart';
import 'package:gomama/app/features/edit_profile/provider/profile_providers.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EditProfileOtpForm extends HookConsumerWidget {
  const EditProfileOtpForm({
    this.countryDialCode,
    this.newMobileNumber,
    this.newEmail,
    super.key,
  });
  final String? countryDialCode;
  final String? newMobileNumber;
  final String? newEmail;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bool isMobileNumber;
    final fullMobileNumber =
        countryDialCode.toString() + newMobileNumber.toString();
    if (newMobileNumber != null && newMobileNumber != null) {
      isMobileNumber = isValidMobileNumber(fullMobileNumber);
    } else {
      isMobileNumber = false;
    }
    final errorText = useState<String?>(null);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: ClipPath(
        clipper: FilterWaveClipper(),
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: CustomColors.secondaryExtraLight,
          ),
          child: SafeArea(
            bottom: false,
            child: Stack(
              children: [
                ListView(
                  children: [
                    const SizedBox(height: 80),
                    Image.asset(
                      'assets/images/otp.png',
                      height: 120,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Verify OTP',
                      style: textTheme(context).titleLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'We sent a verification code to',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: CustomColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      isMobileNumber ? fullMobileNumber.replaceAllMapped(
                              RegExp(r'(\d{4})'), (match) => '${match[0]} ')
                          : newEmail != null ? newEmail! : '',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: CustomColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (isMobileNumber)
                      const Text(
                        'If you did not receive any message after sometime, please ensure your number is correct with correct country code, and try again.',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: CustomColors.primary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    const SizedBox(height: 36),
                    OtpField(
                      errorText: errorText.value,
                      onCompleted: (otp) async {
                        errorText.value = null;
                        try {
                          await ref.read(
                            verifyEmailOrPhoneOtpProvider(
                              newEmail,
                              newMobileNumber,
                              countryDialCode.toString(),
                              otp,
                            ).future,
                          );

                          // show success snackbar
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Verified successfully'),
                              ),
                            );

                            Navigator.pop(context);
                          }
                        } catch (e, s) {
                          Groveman.error(
                            'OtpField onComplete',
                            error: e,
                            stackTrace: s,
                          );

                          errorText.value = 'Invalid OTP';
                        }
                      },
                    ),
                    const SizedBox(height: 20),
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: CountdownTimerWidget(
                          onResend: () async {
                            await ref.read(
                              requestEmailOrPhoneOtpProvider(fullMobileNumber)
                                  .future,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const Positioned(
                  top: 4,
                  right: 8,
                  child: CloseButton(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
