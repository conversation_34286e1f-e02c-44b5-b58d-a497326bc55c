import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_phone_field/form_builder_phone_field.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/features/edit_profile/provider/profile_providers.dart';
import 'package:gomama/app/features/edit_profile/view/edit_profile_otp_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EditMobileNumberSheet extends HookConsumerWidget {
  const EditMobileNumberSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());
    final errorText = useState<String?>(null);
    final isBusy = useState(false);
    final countryCodeState = useState('');

    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.of(context).viewInsets.bottom,
      ),
      child: FormBuilder(
        key: _formKey.value,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                const SizedBox(width: double.infinity, height: kToolbarHeight),
                Text(
                  'Edit Mobile Number',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Positioned(
                  top: 4,
                  right: 8,
                  child: CloseButton(),
                ),
              ],
            ),
            FormBuilderPhoneField(
              name: 'mobile_number',
              defaultSelectedCountryIsoCode: 'SG',
              decoration: InputDecoration(
                labelText: 'Mobile Number',
                floatingLabelBehavior: FloatingLabelBehavior.always,
                hintText: 'Mobile Number',
                errorText: errorText.value,
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  errorText: 'Please fill in your mobile number',
                ),
              ]),
              textInputAction: TextInputAction.next,
              countryPicker: (flag, countryCode) {
                countryCodeState.value = countryCode;
                return Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Icon(CustomIcon.keyboardArrowDown),
                    const SizedBox(width: 5),
                    flag,
                    const SizedBox(width: 5),
                    Text(countryCode),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: CustomColors.primary,
                  foregroundColor: Colors.white,
                ),
                onPressed: () async {
                  // Validate and save the form values
                  errorText.value = null;

                  // Validate and save the form values
                  final success =
                      _formKey.value.currentState?.saveAndValidate();

                  if (success != true) {
                    return;
                  }

                  final fullMobileNumber = countryCodeState.value +
                      _formKey.value.currentState!.value['mobile_number']
                          .toString();

                  isBusy.value = true;

                  try {
                    await ref.read(
                        requestEmailOrPhoneOtpProvider(fullMobileNumber)
                            .future);

                    if (context.mounted) {
                      Navigator.pop(context);

                      await showCupertinoDialog(
                        context: context,
                        builder: (context) {
                          return EditProfileOtpForm(
                            countryDialCode: countryCodeState.value,
                            newMobileNumber: _formKey
                                .value.currentState!.value['mobile_number']
                                .toString(),
                          );
                        },
                      );
                    }
                  } catch (error) {
                    if (error is AppNetworkResponseException) {
                      if (error.message ==
                          'Please provide either phone or email') {
                        errorText.value = 'Please provide a valid phone';
                      } else {
                        errorText.value = error.message;
                      }
                    }

                    isBusy.value = false;

                    Groveman.warning('EditMobileNumberSheet', error: error);
                  }
                },
                child: Text(
                  isBusy.value ? 'Sending a sms...' : 'Verify Mobile Number',
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
