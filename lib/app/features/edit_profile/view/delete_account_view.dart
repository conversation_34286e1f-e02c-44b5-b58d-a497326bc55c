import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DeleteAccountView extends HookConsumerWidget {
  const DeleteAccountView({super.key});

  static const routeName = 'delete account';
  static const routePath = 'delete';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final animationController = useAnimationController(
      duration: const Duration(seconds: 1),
    );

    final animation = CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOutBack,
    );

    final isVisible = useState(false);

    void handleNextButton() {
      isVisible.value = !isVisible.value;
      if (isVisible.value) {
        animationController.forward();
      } else {
        animationController.reverse();
      }
    }

    final bottomSheetKey = useRef(GlobalKey<__DraggableBottomSheetState>());

    return Scaffold(
      body: DecoratedBox(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/backgrounds/stars_background_edit_profile.png',
            ),
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CustomColors.backgroundGradientLight,
              CustomColors.backgroundGradient,
              CustomColors.backgroundGradientDark,
            ],
          ),
        ),
        child: Stack(
          children: [
            CustomScrollView(
              slivers: [
                SliverPersistentHeader(
                  delegate: BrandAppBar(
                    maxHeight: kToolbarHeight +
                        32 +
                        mediaQuery(context).viewPadding.top,
                    minHeight:
                        kToolbarHeight + mediaQuery(context).viewPadding.top,
                    title: const Text('Delete Account'),
                  ),
                  pinned: true,
                ),
              ],
            ),
            _DraggableBottomSheet(
              key: bottomSheetKey.value,
              onNextButtonPressed: handleNextButton,
              isVisible: !isVisible.value,
            ),
            AnimatedBuilder(
              animation: animation,
              builder: (context, child) {
                final screenHeight = MediaQuery.of(context).size.height;
                final slidePosition =
                    -100 - screenHeight * (1 - animation.value);
                return Stack(
                  children: [
                    Positioned(
                      top: slidePosition,
                      left: 0,
                      right: 0,
                      child: Stack(
                        children: [
                          Image.asset(
                            'assets/backgrounds/gmm-header.png',
                            fit: BoxFit.cover,
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(50, 225, 50, 0),
                            child: Column(
                              children: [
                                Image.asset(
                                  'assets/images/goma_sad.png',
                                  height: 167,
                                ),
                                Text(
                                  'Are you sure?',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(
                                          color: Colors.black,
                                          fontFamily: 'AveriaSansLibre'),
                                ),
                                const SizedBox(height: 10),
                                const Text(
                                  'Once you delete your account, there is no getting it back.',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                SizedBox(
                                  width: double.infinity,
                                  child: FilledButton(
                                    onPressed: handleNextButton,
                                    child: const Text('Keep my account'),
                                  ),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    final reason = bottomSheetKey
                                        .value.currentState
                                        ?.getDeleteReason();
                                    final success = await ref.read(
                                        deleteUserProvider(reason!).future);

                                    if (context.mounted) {
                                      if (success) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Successfully deleted your account.'),
                                          ),
                                        );
                                        await ref
                                            .read(
                                              authControllerProvider.notifier,
                                            )
                                            .logout();
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content:
                                                Text('Something went wrong'),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.zero,
                                  ),
                                  child: Text(
                                    'Delete my account',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.copyWith(
                                          color: CustomColors.red,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _DraggableBottomSheet extends ConsumerStatefulWidget {
  const _DraggableBottomSheet({
    super.key,
    required this.onNextButtonPressed,
    required this.isVisible,
  });
  final VoidCallback onNextButtonPressed;
  final bool isVisible;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      __DraggableBottomSheetState();
}

class __DraggableBottomSheetState extends ConsumerState<_DraggableBottomSheet> {
  final _sheet = GlobalKey();
  final _controller = DraggableScrollableController();

  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();

  DraggableScrollableSheet get sheet =>
      _sheet.currentWidget! as DraggableScrollableSheet;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  String? getDeleteReason() {
    final reason = _formKey.currentState?.fields['reason']?.value as String?;
    final otherReason =
        _formKey.currentState?.fields['other_reason']?.value as String?;

    return reason == 'Others' ? otherReason : reason;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxHeight = (MediaQuery.of(context).size.height -
                (max(
                      mediaQuery(context).viewPadding.top,
                      kToolbarHeight / 2,
                    ) +
                    108)) /
            MediaQuery.of(context).size.height;

        return DraggableScrollableSheet(
          key: _sheet,
          initialChildSize: maxHeight,
          minChildSize: maxHeight,
          maxChildSize: maxHeight,
          snap: true,
          controller: _controller,
          builder: (BuildContext context, ScrollController scrollController) {
            return DecoratedBox(
              decoration: const BoxDecoration(
                color: CustomColors.secondaryExtraLight,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(44),
                  topRight: Radius.circular(44),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x40c59a70),
                    blurRadius: 4,
                    offset: Offset(0, -2), // changes position of shadow
                  ),
                ],
              ),
              child: AnimatedOpacity(
                opacity: widget.isVisible ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 500),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(44),
                    topRight: Radius.circular(44),
                  ),
                  child: CustomScrollView(
                    controller: scrollController,
                    slivers: [
                      const SliverPadding(padding: EdgeInsets.only(top: 12)),
                      SliverPadding(
                        padding: const EdgeInsets.fromLTRB(20, 8, 20, 0),
                        sliver: SliverToBoxAdapter(
                          child: Column(
                            children: [
                              Image.asset(
                                'assets/images/goma_sad.png',
                                height: 167,
                              ),
                              Text(
                                "We're sad to see you go!",
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge
                                    ?.copyWith(
                                      color: CustomColors.text,
                                      fontFamily: 'AveriaSansLibre',
                                    ),
                              ),
                              const SizedBox(),
                              const Text(
                                "Before you go, let us know why you're leaving so that we can improve the app experience.",
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: FormBuilder(
                                  key: _formKey,
                                  child: Column(
                                    children: [
                                      FormBuilderRadioGroup<String>(
                                        name: 'reason',
                                        controlAffinity:
                                            ControlAffinity.trailing,
                                        orientation:
                                            OptionsOrientation.vertical,
                                        separator: const Divider(
                                          color: CustomColors.secondary,
                                          height: 15,
                                        ),
                                        validator:
                                            FormBuilderValidators.required(
                                          errorText:
                                              "Please let us know why you're leaving.",
                                        ),
                                        decoration: const InputDecoration(
                                          border: InputBorder.none,
                                        ),
                                        options: [
                                          'No longer using the app',
                                          'Privacy concerns',
                                          'Difficulty navigating the app',
                                          'Others',
                                        ]
                                            .map(
                                              (option) =>
                                                  FormBuilderFieldOption(
                                                value: option,
                                                child: SizedBox(
                                                  width: double.infinity,
                                                  child: Text(option),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        onChanged: (value) {
                                          setState(() {});
                                        },
                                      ),
                                      if (_formKey.currentState
                                              ?.fields['reason']?.value ==
                                          'Others')
                                        FormBuilderTextField(
                                          name: 'other_reason',
                                          maxLines: 5,
                                          cursorColor: CustomColors.primary,
                                          decoration: InputDecoration(
                                            hintText:
                                                "We're here to improve your experience. Share your thoughts!",
                                            hintStyle: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(
                                                  color:
                                                      CustomColors.placeholder,
                                                ),
                                            border: OutlineInputBorder(
                                              borderSide: BorderSide.none,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              gapPadding: 0,
                                            ),
                                            fillColor:
                                                CustomColors.secondaryLight,
                                            filled: true,
                                          ),
                                        ),
                                      const SizedBox(height: 20),
                                      SizedBox(
                                        width: double.infinity,
                                        child: FilledButton(
                                          onPressed: () {
                                            if (_formKey.currentState
                                                    ?.saveAndValidate() ??
                                                false) {
                                              // print(_formKey.currentState?.value);
                                              widget.onNextButtonPressed();
                                            } else {
                                              // print("Validation failed");
                                            }
                                          },
                                          child: const Text('Next'),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SliverPadding(padding: EdgeInsets.only(top: 24)),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
