import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class _CustomField {
  _CustomField({
    required this.key,
    required this.name,
  });

  Key key;
  String name;
}

class DynamicChildBirthdayFields extends ConsumerStatefulWidget {
  const DynamicChildBirthdayFields({super.key});

  @override
  ConsumerState<DynamicChildBirthdayFields> createState() =>
      _DynamicChildBirthdayFieldsState();
}

class _DynamicChildBirthdayFieldsState
    extends ConsumerState<DynamicChildBirthdayFields> {
  int fieldKey = 0;
  List<_CustomField> datetimeFields = [];

  @override
  void initState() {
    super.initState();

    final newTextFieldName = 'children_birthdays[${fieldKey++}]';
    final firstKey = ValueKey(fieldKey);

    datetimeFields = [
      _CustomField(key: firstKey, name: newTextFieldName),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ...datetimeFields.map((field) {
          return CustomDatetimeField(
            key: field.key,
            name: field.name,
            onDelete: datetimeFields.length > 1
                ? () {
                    setState(() {
                      datetimeFields.removeWhere((e) => e.key == field.key);
                    });
                  }
                : null,
          );
        }),
        const SizedBox(height: 8),
        BrandButton.cta(
          padding: EdgeInsets.zero,
          child: const Text('Add Child'),
          onPressed: () {
            final newTextFieldName = 'children_birthdays[${fieldKey++}]';
            final newTextFieldKey = ValueKey(fieldKey);

            setState(() {
              datetimeFields.add(
                _CustomField(key: newTextFieldKey, name: newTextFieldName),
              );
            });
          },
        ),
      ],
    );
  }
}

class CustomDatetimeField extends ConsumerWidget {
  const CustomDatetimeField({
    super.key,
    this.onDelete,
    required this.name,
  });
  final String name;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dateNow = DateTime.now();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: FormBuilderDateTimePicker(
              name: name,
              initialValue: dateNow,
              firstDate:
                  DateTime(dateNow.year - 16, dateNow.month, dateNow.day),
              lastDate: dateNow,
              inputType: InputType.date,
              decoration: const InputDecoration(
                labelText: "Child's Date of Birth",
                suffixIcon: Icon(CustomIcon.calendarFull),
                floatingLabelBehavior: FloatingLabelBehavior.always,
              ),
              valueTransformer: (value) => value?.toIso8601String(),
            ),
          ),
          IconButton(
            icon: const Icon(CustomIcon.minusCircle),
            onPressed: onDelete,
          ),
        ],
      ),
    );
  }
}
