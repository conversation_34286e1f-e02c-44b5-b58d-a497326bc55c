import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// THIS IS LIKELY AN OLD GOMAMA VERIFICATION FORM FILE. CAN BE DELETED

class FillVerificationDetailsView extends HookConsumerWidget {
  const FillVerificationDetailsView({super.key});

  static const routeName = 'fill verification details';
  static const routePath = '/fill-verification-details';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());

    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [CustomColors.secondary, CustomColors.primaries.shade100],
          begin: Alignment.topLeft,
          end: Alignment.topRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomColors.secondary,
                  CustomColors.primaries.shade100,
                ],
                begin: Alignment.topLeft,
                end: Alignment.topRight,
              ),
            ),
          ),
          iconTheme: const IconThemeData(
            color: CustomColors.primaries,
          ),
          foregroundColor: CustomColors.primaries,
          title: const Text('Go!Mama Approved Selfie!'),
        ),
        body: ClipPath(
          clipper: RoundedClipper(),
          child: Container(
            color: Colors.white,
            height: double.infinity,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(32, 0, 32, 0),
              child: SingleChildScrollView(
                child: FormBuilder(
                  key: _formKey.value,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Your Details',
                        style: TextStyle(
                          fontSize: 20,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      // TextFormField(
                      //   controller: _genderController,
                      //   readOnly: true,
                      //   style: const TextStyle(color: Colors.grey, fontSize: 16),
                      //   decoration: const InputDecoration(
                      //     labelText: 'Gender',
                      //   ),
                      //   onTap: () async {
                      //     final selectedGender =
                      //         await showModalBottomSheet<String>(
                      //       context: context,
                      //       builder: (BuildContext builder) {
                      //         var selectedValue = 'Female';

                      //         return Column(
                      //           mainAxisSize: MainAxisSize.min,
                      //           children: [
                      //             ColoredBox(
                      //               color: Colors.white,
                      //               child: Row(
                      //                 mainAxisAlignment:
                      //                     MainAxisAlignment.spaceBetween,
                      //                 children: [
                      //                   TextButton(
                      //                     onPressed: () {
                      //                       Navigator.of(context).pop(); // Cancel
                      //                     },
                      //                     child: const Text('Cancel'),
                      //                   ),
                      //                   TextButton(
                      //                     onPressed: () {
                      //                       Navigator.of(context)
                      //                           .pop(selectedValue); // Select
                      //                     },
                      //                     child: const Text('Select'),
                      //                   ),
                      //                 ],
                      //               ),
                      //             ),
                      //             Container(
                      //               height: MediaQuery.of(context)
                      //                       .copyWith()
                      //                       .size
                      //                       .height /
                      //                   3,
                      //               color: Colors.white,
                      //               child: CupertinoPicker(
                      //                 itemExtent:
                      //                     32, // Set the height of each item
                      //                 onSelectedItemChanged: (int index) {
                      //                   selectedValue = [
                      //                     'Female',
                      //                     'Male',
                      //                   ][index]; // Set the selected value
                      //                 },
                      //                 children: const [
                      //                   Text('Female'),
                      //                   Text('Male'),
                      //                 ],
                      //               ),
                      //             ),
                      //           ],
                      //         );
                      //       },
                      //     );
                      //     if (selectedGender != null) {
                      //       _genderController.text = selectedGender;
                      //     }
                      //   },
                      // ),
                      FormBuilderTextField(
                        name: 'gender',
                        readOnly: true,
                        initialValue: 'Female',
                        decoration: const InputDecoration(
                          labelText: 'Gender',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderTextField(
                        name: 'full_name',
                        decoration: const InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          labelText: 'Full Name',
                          hintText: 'GO!MAMA',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please fill in your full name',
                          ),
                        ]),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderTextField(
                        name: 'nric',
                        decoration: const InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          labelText: 'NRIC/FIN/Passport (Last 4 digits)',
                          hintText: '1234',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please fill in the NRIC/FIN/Passport',
                          ),
                          FormBuilderValidators.minLength(
                            4,
                            errorText: 'Please fill in the NRIC/FIN/Passport',
                          ),
                        ]),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderDateTimePicker(
                        name: 'birthday',
                        inputType: InputType.date,
                        decoration: const InputDecoration(
                          labelText:
                              'Your Date of Birth (Must be 16+ years old)',
                          suffixIcon: Icon(CustomIcon.calendarFull),
                          hintText: 'DD/MM/YYYY',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                        // onTap: () async {
                        //   await AdaptiveDatePicker().showDate(
                        //     context,
                        //     onDateTimeChanged: (value) {
                        //       _dobController.text =
                        //           '${value.day}/${value.month}/${value.year}';
                        //     },
                        //   );
                        // },
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please pick your date of birth',
                          ),
                        ]),
                        valueTransformer: (value) {
                          Groveman.debug('value1: $value');
                          if (value == null) {
                            return '';
                          }

                          return value.toIso8601String();
                        },
                      ),
                      const SizedBox(height: 8),
                      FormBuilderDateTimePicker(
                        name: 'child_birthday',
                        inputType: InputType.date,
                        decoration: const InputDecoration(
                          labelText: "Youngest Child's Date of Birth",
                          suffixIcon: Icon(CustomIcon.calendarFull),
                          hintText: 'DD/MM/YYYY',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                        // onTap: () async {
                        //   await AdaptiveDatePicker().showDate(
                        //     context,
                        //     onDateTimeChanged: (value) {
                        //       _youngestChildDobController.text =
                        //           '${value.day}/${value.month}/${value.year}';
                        //     },
                        //   );
                        // },
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please pick your date of birth',
                          ),
                        ]),
                        valueTransformer: (value) {
                          Groveman.debug('value2: $value');
                          if (value == null) {
                            return '';
                          }

                          return value.toIso8601String();
                        },
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        'Contact Details',
                        style: TextStyle(
                          fontSize: 20,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderTextField(
                        name: 'mobile_number',
                        keyboardType: TextInputType.phone,
                        decoration: const InputDecoration(
                          labelText: 'Mobile Number (Singaporean)',
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          hintText: 'Mobile Number',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please fill in your mobile number',
                          ),
                        ]),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderTextField(
                        name: 'email_address',
                        keyboardType: TextInputType.emailAddress,
                        decoration: const InputDecoration(
                          labelText: 'Email Address',
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          hintText: 'Email Address',
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                            errorText: 'Please fill in your email address',
                          ),
                          FormBuilderValidators.email(
                            errorText:
                                'Please insert the correct email address',
                          ),
                        ]),
                      ),
                      const SizedBox(height: 20),
                      ListTileTheme(
                        data: const ListTileThemeData(
                          titleAlignment: ListTileTitleAlignment.top,
                          contentPadding: EdgeInsets.zero,
                          minVerticalPadding: 0,
                        ),
                        child: FormBuilderCheckbox(
                          name: 'checkbox',
                          title: const Text(
                            'I agree that all the information provided is true and accurate. I accept full responsibility for the accuracy of this information. (By checking this box, you acknowledge that the information provided is accurate and you consent to its use. False information may have legal consequences. Please refer to our Terms of Service and Privacy Policy for more details.)',
                          ),
                          validator: FormBuilderValidators.equal(
                            true,
                            errorText: 'You must agree to continue',
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              style: TextButton.styleFrom(
                                side: const BorderSide(
                                  color: CustomColors.primary,
                                ),
                                foregroundColor: CustomColors.primary,
                              ),
                              onPressed: () {
                                // Clear profile verification form values
                                context.pop();
                              },
                              child: const Text('Cancel'),
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: TextButton(
                              style: TextButton.styleFrom(
                                backgroundColor: CustomColors.primary,
                                foregroundColor: Colors.white,
                              ),
                              onPressed: () {
                                // Validate and save the form values
                                final success = _formKey.value.currentState
                                    ?.saveAndValidate();

                                if (success != true) {
                                  return;
                                }

                                // final values =
                                //     _formKey.value.currentState?.validate();

                                // send to backend

                                // On another side, can access all field values without saving form with instantValues
                                // _formKey.value.currentState?.validate();

                                // context.pop();
                              },
                              child: const Text('Submit'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
