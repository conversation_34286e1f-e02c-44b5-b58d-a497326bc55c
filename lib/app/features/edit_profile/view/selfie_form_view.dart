import 'dart:io';

import 'package:country_pickers/country.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_phone_field/form_builder_phone_field.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/edit_profile/model/selfie_verification.dart';
import 'package:gomama/app/features/edit_profile/view/dynamic_child_birthday_fields.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class SelfieFormView extends HookConsumerWidget {
  const SelfieFormView({
    required this.photoPath,
    super.key,
  });

  final String photoPath;

  static const routeName = 'selfie form';
  static const routePath = '/selfie-form';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: ClipPath(
        clipper: FilterWaveClipper(),
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: CustomColors.secondaryExtraLight,
          ),
          child: SafeArea(
            bottom: false,
            child: Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    const SizedBox(
                      width: double.infinity,
                      height: kToolbarHeight,
                    ),
                    Text(
                      'Go!Mama Approved Selfie!',
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium!
                          .copyWith(color: CustomColors.primary),
                    ),
                    Positioned(
                      top: 4,
                      right: 8,
                      child: CloseButton(
                        onPressed: () {
                          _controller.toSelfie();
                          context.pop();
                        },
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: SingleChildScrollView(
                      child: _SelfieForm(photoPath),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SelfieForm extends HookConsumerWidget {
  const _SelfieForm(this.photoPath);
  final String photoPath;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);
    final user = ref.watch(authControllerProvider).requireValue;
    final dateNow = DateTime.now();
    final countryCodeState = useState('');

    return FormBuilder(
      clearValueOnUnregister: true,
      key: _formKey.value,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your Details',
                style: textTheme(context).titleLarge,
              ),
              const SizedBox(height: 8),
              FormBuilderTextField(
                name: 'gender',
                readOnly: true,
                initialValue: 'Female',
                decoration: const InputDecoration(
                  labelText: 'Gender',
                ),
              ),
              const SizedBox(height: 8),
              FormBuilderTextField(
                name: 'first_name',
                decoration: const InputDecoration(
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: 'First Name',
                  hintText: 'GO!',
                ),
                autocorrect: false,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please fill in your first name',
                  ),
                ]),
                textInputAction: TextInputAction.next,
              ),
              FormBuilderTextField(
                name: 'last_name',
                decoration: const InputDecoration(
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: 'Last Name',
                  hintText: 'MAMA',
                ),
                autocorrect: false,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please fill in your last name',
                  ),
                ]),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 8),
              FormBuilderTextField(
                name: 'fin_or_passport',
                decoration: const InputDecoration(
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: 'FIN/Passport (Last 4 digits)',
                  hintText: '1234',
                ),
                maxLength: 4,
                autocorrect: false,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please fill in the NRIC/FIN/Passport',
                  ),
                  FormBuilderValidators.minLength(
                    4,
                    errorText: 'Please fill in the NRIC/FIN/Passport',
                  ),
                ]),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 8),
              FormBuilderDateTimePicker(
                name: 'birthday',
                initialDate:
                    DateTime(dateNow.year - 16, dateNow.month, dateNow.day),
                lastDate:
                    DateTime(dateNow.year - 16, dateNow.month, dateNow.day),
                inputType: InputType.date,
                decoration: const InputDecoration(
                  labelText: 'Date of Birth (Must be 16+ years old)',
                  suffixIcon: Icon(CustomIcon.calendarFull),
                  hintText: 'DD/MM/YYYY',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                ),
                // onTap: () async {
                //   await AdaptiveDatePicker().showDate(
                //     context,
                //     onDateTimeChanged: (value) {
                //       _dobController.text =
                //           '${value.day}/${value.month}/${value.year}';
                //     },
                //   );
                // },
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please pick your date of birth',
                  ),
                ]),
                textInputAction: TextInputAction.next,
                valueTransformer: (value) {
                  if (value == null) {
                    return '';
                  }

                  return value.toIso8601String();
                },
              ),
              const SizedBox(height: 8),
              const DynamicChildBirthdayFields(),
              const SizedBox(height: 20),
              Text(
                'Contact Details',
                style: textTheme(context).titleLarge,
              ),
              const SizedBox(height: 8),
              FormBuilderPhoneField(
                name: 'mobile_number',
                defaultSelectedCountryIsoCode: 'SG',
                initialValue: user.mobileNumber,
                enabled: user.mobileNumber == null,
                decoration: const InputDecoration(
                  labelText: 'Mobile Number',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Mobile Number',
                ),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please fill in your mobile number',
                  ),
                ]),
                textInputAction: TextInputAction.next,
                countryPicker: (flag, countryCode) {
                  countryCodeState.value = countryCode;
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Icon(CustomIcon.keyboardArrowDown),
                      const SizedBox(width: 5),
                      flag,
                      const SizedBox(width: 5),
                      Text(countryCode),
                    ],
                  );
                },
              ),
              const SizedBox(height: 8),
              FormBuilderTextField(
                name: 'email_address',
                keyboardType: TextInputType.emailAddress,
                initialValue: user.emailAddress,
                readOnly: user.emailAddress != null,
                decoration: const InputDecoration(
                  labelText: 'Email Address',
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  hintText: 'Email Address',
                ),
                autocorrect: false,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(
                    errorText: 'Please fill in your email address',
                  ),
                  FormBuilderValidators.email(
                    errorText: 'Please insert the correct email address',
                  ),
                ]),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ListTileTheme(
            data: const ListTileThemeData(
              titleAlignment: ListTileTitleAlignment.top,
              minVerticalPadding: 0,
              horizontalTitleGap: 0,
            ),
            child: FormBuilderCheckbox(
              name: 'checkbox',
              title: const Text(
                'I agree that all the information provided is true and accurate. I accept full responsibility for the accuracy of this information. (By checking this box, you acknowledge that the information provided is accurate and you consent to its use. False information may have legal consequences. Please refer to our Terms of Service and Privacy Policy for more details.)',
              ),
              validator: FormBuilderValidators.equal(
                true,
                errorText: 'You must agree to continue',
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: BrandButton.outlined(
                  onPressed: () {
                    _controller.toSelfie();
                    context.pop();
                  },
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: BrandButton.cta(
                  padding: EdgeInsets.zero,
                  onPressed: () async {
                    // Validate and save the form values
                    final success =
                        _formKey.value.currentState?.saveAndValidate();

                    if (success != true) {
                      return;
                    }

                    // Process the children birthdays fields
                    final childrenBirthdays = <DateTime>[];
                    for (final entry
                        in _formKey.value.currentState!.value.entries) {
                      if (entry.key.startsWith('children_birthdays[') &&
                          entry.key.endsWith(']')) {
                        final date = DateTime.tryParse(entry.value as String);

                        if (date != null) {
                          childrenBirthdays.add(date);
                        }
                      }
                    }

                    final values = {
                      'email_address': _formKey
                          .value.currentState!.value['email_address']
                          .toString(),
                      'first_name': _formKey
                          .value.currentState!.value['first_name']
                          .toString(),
                      'last_name': _formKey
                          .value.currentState!.value['last_name']
                          .toString(),
                      'fin_or_passport': _formKey
                          .value.currentState!.value['fin_or_passport']
                          .toString(),
                      'birthday': _formKey.value.currentState!.value['birthday']
                          .toString(),
                      'mobile_number': _formKey
                          .value.currentState!.value['mobile_number']
                          .toString(),
                      'children_birthdays': childrenBirthdays,
                      'country_code': countryCodeState.value,
                      'gender': _formKey.value.currentState!.value['gender']
                          .toString(),
                    };

                    try {
                      await _controller.submitGomamaVerification(
                        values,
                        photoPath,
                      );

                      if (context.mounted) {
                        context.pop();
                      }
                    } catch (error, stackTrace) {
                      Groveman.error(
                        'gomamaVerifyProvider',
                        error: error,
                        stackTrace: stackTrace,
                      );

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              error is AppNetworkResponseException
                                  ? '${error.message}'
                                  : 'Something went wrong, please try again later',
                            ),
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    }
                  },
                  child: const Text('Submit'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 144),
        ],
      ),
    );
  }
}
