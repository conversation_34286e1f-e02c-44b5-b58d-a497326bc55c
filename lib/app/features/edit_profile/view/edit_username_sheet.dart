import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EditUsernameSheet extends HookConsumerWidget {
  const EditUsernameSheet(this.username, {super.key});
  final String username;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());
    final errorText = useState<String?>(null);
    final isBusy = useState(false);

    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.of(context).viewInsets.bottom,
      ),
      child: FormBuilder(
        key: _formKey.value,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                const SizedBox(width: double.infinity, height: kToolbarHeight),
                Text(
                  'Edit Username',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Positioned(
                  top: 4,
                  right: 8,
                  child: CloseButton(),
                ),
              ],
            ),
            FormBuilderTextField(
              name: 'username',
              maxLength: 12,
              initialValue: username,
              keyboardType: TextInputType.name,
              decoration: const InputDecoration(
                labelText: 'Username*',
                floatingLabelBehavior: FloatingLabelBehavior.always,
                hintText: 'Username',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  errorText: 'Please fill in your username',
                ),
              ]),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: CustomColors.primary,
                  foregroundColor: Colors.white,
                ),
                onPressed: () async {
                  final success =
                      _formKey.value.currentState?.saveAndValidate();

                  if (success != true) {
                    return;
                  }

                  final username =
                      _formKey.value.currentState?.value['username'].toString();

                  final input = UpdateUserInput(
                    username: username,
                  );

                  isBusy.value = true;

                  try {
                    await ref.read(updateUserProvider(input).future);

                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Updated successfully'),
                        ),
                      );

                      Navigator.pop(context);
                    }
                  } catch (error) {
                    if (error is AppNetworkResponseException) {
                      errorText.value = error.message;
                    }

                    isBusy.value = false;

                    Groveman.warning('EditEmailAddressSheet', error: error);
                  }
                },
                child: Text(isBusy.value ? 'Saving...' : 'Save'),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
