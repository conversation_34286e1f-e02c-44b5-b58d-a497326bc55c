// ignore_for_file: invalid_annotation_target

import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'selfie_verification.freezed.dart';
part 'selfie_verification.g.dart';

// SelfieVerification = Gomama verification
@freezed
class SelfieVerificationInput with _$SelfieVerificationInput {
  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  factory SelfieVerificationInput({
    @JsonKey(includeFromJson: false) File? userSelfieFile,
    @JsonKey(includeFromJson: false) File? userSelfieFileCopy,
    required String firstName,
    required String lastName,
    required String finOrPassport,
    required DateTime birthday,
    required List<DateTime> childrenBirthdays,
    required String mobileNumber,
    required String email,
    required String gender,
  }) = _SelfieVerificationInput;

  factory SelfieVerificationInput.fromJson(Json json) =>
      _$SelfieVerificationInputFromJson(json);
}
