// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'singpass_verification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SingpassVerificationInputImpl _$$SingpassVerificationInputImplFromJson(
        Map<String, dynamic> json) =>
    _$SingpassVerificationInputImpl(
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      birthday: DateTime.parse(json['birthday'] as String),
      childBirthday: const CustomDateTimeConverter()
          .fromJson(json['child_birthday'] as String?),
      childrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['children_birthdays'] as List?),
      mobileNumber: json['mobile_number'] as String,
      gender: json['gender'] as String,
      nric: json['nric'] as String,
    );

Map<String, dynamic> _$$SingpassVerificationInputImplToJson(
        _$SingpassVerificationInputImpl instance) =>
    <String, dynamic>{
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'birthday': instance.birthday.toIso8601String(),
      'child_birthday':
          const CustomDateTimeConverter().toJson(instance.childBirthday),
      'children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.childrenBirthdays),
      'mobile_number': instance.mobileNumber,
      'gender': instance.gender,
      'nric': instance.nric,
    };
