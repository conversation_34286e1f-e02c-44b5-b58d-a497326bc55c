// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_verification_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserVerificationRequestImpl _$$UserVerificationRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UserVerificationRequestImpl(
      id: json['id'] as String,
      type: json['type'] as String,
      userId: json['user_id'] as String,
      gomamaUserVerifyPhotoUrl: json['gomama_user_verify_photo_url'] as String?,
      gomamaFirstName: json['gomama_first_name'] as String?,
      gomamaLastName: json['gomama_last_name'] as String?,
      gomamaBirthday: const CustomDateTimeConverter()
          .fromJson(json['gomama_birthday'] as String?),
      gomamaChildrenBirthdays: const CustomDateTimeListConverter()
          .fromJson(json['gomama_children_birthdays'] as List?),
      gomamaMobileNumber: json['gomama_mobile_number'] as String?,
      gomamaEmail: json['gomama_email'] as String?,
      gomamaGender: json['gomama_gender'] as String?,
      gomamaFinOrPassport: json['gomama_fin_or_passport'] as String?,
      singpassMobileNumber: json['singpass_mobile_number'] as String?,
      singpassEmail: json['singpass_email'] as String?,
      singpassBirthday: json['singpass_birthday'] as String?,
      singpassNric: json['singpass_nric'] as String?,
      singpassFullName: json['singpass_full_name'] as String?,
      singpassGender: json['singpass_gender'] as String?,
      singpassChildrenBirthday: json['singpass_children_birthday'] as String?,
      reviewedAt: DateTime.parse(json['reviewed_at'] as String),
      status: json['status'] as String,
      reason: json['reason'] as String,
      reviewBy: json['review_by'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$UserVerificationRequestImplToJson(
        _$UserVerificationRequestImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'user_id': instance.userId,
      'gomama_user_verify_photo_url': instance.gomamaUserVerifyPhotoUrl,
      'gomama_first_name': instance.gomamaFirstName,
      'gomama_last_name': instance.gomamaLastName,
      'gomama_birthday':
          const CustomDateTimeConverter().toJson(instance.gomamaBirthday),
      'gomama_children_birthdays': const CustomDateTimeListConverter()
          .toJson(instance.gomamaChildrenBirthdays),
      'gomama_mobile_number': instance.gomamaMobileNumber,
      'gomama_email': instance.gomamaEmail,
      'gomama_gender': instance.gomamaGender,
      'gomama_fin_or_passport': instance.gomamaFinOrPassport,
      'singpass_mobile_number': instance.singpassMobileNumber,
      'singpass_email': instance.singpassEmail,
      'singpass_birthday': instance.singpassBirthday,
      'singpass_nric': instance.singpassNric,
      'singpass_full_name': instance.singpassFullName,
      'singpass_gender': instance.singpassGender,
      'singpass_children_birthday': instance.singpassChildrenBirthday,
      'reviewed_at': instance.reviewedAt.toIso8601String(),
      'status': instance.status,
      'reason': instance.reason,
      'review_by': instance.reviewBy,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };
