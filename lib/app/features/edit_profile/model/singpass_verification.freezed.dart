// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'singpass_verification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SingpassVerificationInput _$SingpassVerificationInputFromJson(
    Map<String, dynamic> json) {
  return _SingpassVerificationInput.fromJson(json);
}

/// @nodoc
mixin _$SingpassVerificationInput {
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  DateTime get birthday => throw _privateConstructorUsedError;
  DateTime? get childBirthday =>
      throw _privateConstructorUsedError; // If only one child.
  List<DateTime>? get childrenBirthdays => throw _privateConstructorUsedError;
  String get mobileNumber => throw _privateConstructorUsedError;
  String get gender => throw _privateConstructorUsedError;
  String get nric => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SingpassVerificationInputCopyWith<SingpassVerificationInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SingpassVerificationInputCopyWith<$Res> {
  factory $SingpassVerificationInputCopyWith(SingpassVerificationInput value,
          $Res Function(SingpassVerificationInput) then) =
      _$SingpassVerificationInputCopyWithImpl<$Res, SingpassVerificationInput>;
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      DateTime birthday,
      DateTime? childBirthday,
      List<DateTime>? childrenBirthdays,
      String mobileNumber,
      String gender,
      String nric});
}

/// @nodoc
class _$SingpassVerificationInputCopyWithImpl<$Res,
        $Val extends SingpassVerificationInput>
    implements $SingpassVerificationInputCopyWith<$Res> {
  _$SingpassVerificationInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? birthday = null,
    Object? childBirthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? mobileNumber = null,
    Object? gender = null,
    Object? nric = null,
  }) {
    return _then(_value.copyWith(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      birthday: null == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime,
      childBirthday: freezed == childBirthday
          ? _value.childBirthday
          : childBirthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value.childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      mobileNumber: null == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      nric: null == nric
          ? _value.nric
          : nric // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SingpassVerificationInputImplCopyWith<$Res>
    implements $SingpassVerificationInputCopyWith<$Res> {
  factory _$$SingpassVerificationInputImplCopyWith(
          _$SingpassVerificationInputImpl value,
          $Res Function(_$SingpassVerificationInputImpl) then) =
      __$$SingpassVerificationInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      DateTime birthday,
      DateTime? childBirthday,
      List<DateTime>? childrenBirthdays,
      String mobileNumber,
      String gender,
      String nric});
}

/// @nodoc
class __$$SingpassVerificationInputImplCopyWithImpl<$Res>
    extends _$SingpassVerificationInputCopyWithImpl<$Res,
        _$SingpassVerificationInputImpl>
    implements _$$SingpassVerificationInputImplCopyWith<$Res> {
  __$$SingpassVerificationInputImplCopyWithImpl(
      _$SingpassVerificationInputImpl _value,
      $Res Function(_$SingpassVerificationInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? birthday = null,
    Object? childBirthday = freezed,
    Object? childrenBirthdays = freezed,
    Object? mobileNumber = null,
    Object? gender = null,
    Object? nric = null,
  }) {
    return _then(_$SingpassVerificationInputImpl(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      birthday: null == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime,
      childBirthday: freezed == childBirthday
          ? _value.childBirthday
          : childBirthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      childrenBirthdays: freezed == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      mobileNumber: null == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      nric: null == nric
          ? _value.nric
          : nric // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$SingpassVerificationInputImpl implements _SingpassVerificationInput {
  _$SingpassVerificationInputImpl(
      {required this.firstName,
      required this.lastName,
      required this.birthday,
      this.childBirthday,
      final List<DateTime>? childrenBirthdays,
      required this.mobileNumber,
      required this.gender,
      required this.nric})
      : _childrenBirthdays = childrenBirthdays;

  factory _$SingpassVerificationInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$SingpassVerificationInputImplFromJson(json);

  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final DateTime birthday;
  @override
  final DateTime? childBirthday;
// If only one child.
  final List<DateTime>? _childrenBirthdays;
// If only one child.
  @override
  List<DateTime>? get childrenBirthdays {
    final value = _childrenBirthdays;
    if (value == null) return null;
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String mobileNumber;
  @override
  final String gender;
  @override
  final String nric;

  @override
  String toString() {
    return 'SingpassVerificationInput(firstName: $firstName, lastName: $lastName, birthday: $birthday, childBirthday: $childBirthday, childrenBirthdays: $childrenBirthdays, mobileNumber: $mobileNumber, gender: $gender, nric: $nric)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SingpassVerificationInputImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            (identical(other.childBirthday, childBirthday) ||
                other.childBirthday == childBirthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.nric, nric) || other.nric == nric));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      firstName,
      lastName,
      birthday,
      childBirthday,
      const DeepCollectionEquality().hash(_childrenBirthdays),
      mobileNumber,
      gender,
      nric);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SingpassVerificationInputImplCopyWith<_$SingpassVerificationInputImpl>
      get copyWith => __$$SingpassVerificationInputImplCopyWithImpl<
          _$SingpassVerificationInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SingpassVerificationInputImplToJson(
      this,
    );
  }
}

abstract class _SingpassVerificationInput implements SingpassVerificationInput {
  factory _SingpassVerificationInput(
      {required final String firstName,
      required final String lastName,
      required final DateTime birthday,
      final DateTime? childBirthday,
      final List<DateTime>? childrenBirthdays,
      required final String mobileNumber,
      required final String gender,
      required final String nric}) = _$SingpassVerificationInputImpl;

  factory _SingpassVerificationInput.fromJson(Map<String, dynamic> json) =
      _$SingpassVerificationInputImpl.fromJson;

  @override
  String get firstName;
  @override
  String get lastName;
  @override
  DateTime get birthday;
  @override
  DateTime? get childBirthday;
  @override // If only one child.
  List<DateTime>? get childrenBirthdays;
  @override
  String get mobileNumber;
  @override
  String get gender;
  @override
  String get nric;
  @override
  @JsonKey(ignore: true)
  _$$SingpassVerificationInputImplCopyWith<_$SingpassVerificationInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
