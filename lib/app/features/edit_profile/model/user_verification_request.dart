import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'user_verification_request.freezed.dart';
part 'user_verification_request.g.dart';

@freezed
class UserVerificationRequest with _$UserVerificationRequest {
  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  factory UserVerificationRequest({
    required String id,
    required String type,
    required String userId,
    String? gomamaUserVerifyPhotoUrl,
    String? gomamaFirstName,
    String? gomamaLastName,
    DateTime? gomamaBirthday,
    List<DateTime>? gomamaChildrenBirthdays,
    String? gomamaMobileNumber,
    String? gomamaEmail,
    String? gomamaGender,
    String? gomamaFinOrPassport,
    String? singpassMobileNumber,
    String? singpassEmail,
    String? singpassBirthday,
    String? singpassNric,
    String? singpassFullName,
    String? singpassGender,
    String? singpassChildrenBirthday,
    required DateTime reviewedAt,
    required String status,
    required String reason,
    required String reviewBy,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _UserVerificationRequest;

  factory UserVerificationRequest.fromJson(Json json) =>
      _$UserVerificationRequestFromJson(json);
}
