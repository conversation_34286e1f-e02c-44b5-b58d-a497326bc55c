// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'selfie_verification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SelfieVerificationInput _$SelfieVerificationInputFromJson(
    Map<String, dynamic> json) {
  return _SelfieVerificationInput.fromJson(json);
}

/// @nodoc
mixin _$SelfieVerificationInput {
  @JsonKey(includeFromJson: false)
  File? get userSelfieFile => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false)
  File? get userSelfieFileCopy => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get finOrPassport => throw _privateConstructorUsedError;
  DateTime get birthday => throw _privateConstructorUsedError;
  List<DateTime> get childrenBirthdays => throw _privateConstructorUsedError;
  String get mobileNumber => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get gender => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SelfieVerificationInputCopyWith<SelfieVerificationInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelfieVerificationInputCopyWith<$Res> {
  factory $SelfieVerificationInputCopyWith(SelfieVerificationInput value,
          $Res Function(SelfieVerificationInput) then) =
      _$SelfieVerificationInputCopyWithImpl<$Res, SelfieVerificationInput>;
  @useResult
  $Res call(
      {@JsonKey(includeFromJson: false) File? userSelfieFile,
      @JsonKey(includeFromJson: false) File? userSelfieFileCopy,
      String firstName,
      String lastName,
      String finOrPassport,
      DateTime birthday,
      List<DateTime> childrenBirthdays,
      String mobileNumber,
      String email,
      String gender});
}

/// @nodoc
class _$SelfieVerificationInputCopyWithImpl<$Res,
        $Val extends SelfieVerificationInput>
    implements $SelfieVerificationInputCopyWith<$Res> {
  _$SelfieVerificationInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userSelfieFile = freezed,
    Object? userSelfieFileCopy = freezed,
    Object? firstName = null,
    Object? lastName = null,
    Object? finOrPassport = null,
    Object? birthday = null,
    Object? childrenBirthdays = null,
    Object? mobileNumber = null,
    Object? email = null,
    Object? gender = null,
  }) {
    return _then(_value.copyWith(
      userSelfieFile: freezed == userSelfieFile
          ? _value.userSelfieFile
          : userSelfieFile // ignore: cast_nullable_to_non_nullable
              as File?,
      userSelfieFileCopy: freezed == userSelfieFileCopy
          ? _value.userSelfieFileCopy
          : userSelfieFileCopy // ignore: cast_nullable_to_non_nullable
              as File?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      finOrPassport: null == finOrPassport
          ? _value.finOrPassport
          : finOrPassport // ignore: cast_nullable_to_non_nullable
              as String,
      birthday: null == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime,
      childrenBirthdays: null == childrenBirthdays
          ? _value.childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      mobileNumber: null == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelfieVerificationInputImplCopyWith<$Res>
    implements $SelfieVerificationInputCopyWith<$Res> {
  factory _$$SelfieVerificationInputImplCopyWith(
          _$SelfieVerificationInputImpl value,
          $Res Function(_$SelfieVerificationInputImpl) then) =
      __$$SelfieVerificationInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(includeFromJson: false) File? userSelfieFile,
      @JsonKey(includeFromJson: false) File? userSelfieFileCopy,
      String firstName,
      String lastName,
      String finOrPassport,
      DateTime birthday,
      List<DateTime> childrenBirthdays,
      String mobileNumber,
      String email,
      String gender});
}

/// @nodoc
class __$$SelfieVerificationInputImplCopyWithImpl<$Res>
    extends _$SelfieVerificationInputCopyWithImpl<$Res,
        _$SelfieVerificationInputImpl>
    implements _$$SelfieVerificationInputImplCopyWith<$Res> {
  __$$SelfieVerificationInputImplCopyWithImpl(
      _$SelfieVerificationInputImpl _value,
      $Res Function(_$SelfieVerificationInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userSelfieFile = freezed,
    Object? userSelfieFileCopy = freezed,
    Object? firstName = null,
    Object? lastName = null,
    Object? finOrPassport = null,
    Object? birthday = null,
    Object? childrenBirthdays = null,
    Object? mobileNumber = null,
    Object? email = null,
    Object? gender = null,
  }) {
    return _then(_$SelfieVerificationInputImpl(
      userSelfieFile: freezed == userSelfieFile
          ? _value.userSelfieFile
          : userSelfieFile // ignore: cast_nullable_to_non_nullable
              as File?,
      userSelfieFileCopy: freezed == userSelfieFileCopy
          ? _value.userSelfieFileCopy
          : userSelfieFileCopy // ignore: cast_nullable_to_non_nullable
              as File?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      finOrPassport: null == finOrPassport
          ? _value.finOrPassport
          : finOrPassport // ignore: cast_nullable_to_non_nullable
              as String,
      birthday: null == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime,
      childrenBirthdays: null == childrenBirthdays
          ? _value._childrenBirthdays
          : childrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      mobileNumber: null == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$SelfieVerificationInputImpl implements _SelfieVerificationInput {
  _$SelfieVerificationInputImpl(
      {@JsonKey(includeFromJson: false) this.userSelfieFile,
      @JsonKey(includeFromJson: false) this.userSelfieFileCopy,
      required this.firstName,
      required this.lastName,
      required this.finOrPassport,
      required this.birthday,
      required final List<DateTime> childrenBirthdays,
      required this.mobileNumber,
      required this.email,
      required this.gender})
      : _childrenBirthdays = childrenBirthdays;

  factory _$SelfieVerificationInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$SelfieVerificationInputImplFromJson(json);

  @override
  @JsonKey(includeFromJson: false)
  final File? userSelfieFile;
  @override
  @JsonKey(includeFromJson: false)
  final File? userSelfieFileCopy;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String finOrPassport;
  @override
  final DateTime birthday;
  final List<DateTime> _childrenBirthdays;
  @override
  List<DateTime> get childrenBirthdays {
    if (_childrenBirthdays is EqualUnmodifiableListView)
      return _childrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_childrenBirthdays);
  }

  @override
  final String mobileNumber;
  @override
  final String email;
  @override
  final String gender;

  @override
  String toString() {
    return 'SelfieVerificationInput(userSelfieFile: $userSelfieFile, userSelfieFileCopy: $userSelfieFileCopy, firstName: $firstName, lastName: $lastName, finOrPassport: $finOrPassport, birthday: $birthday, childrenBirthdays: $childrenBirthdays, mobileNumber: $mobileNumber, email: $email, gender: $gender)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfieVerificationInputImpl &&
            (identical(other.userSelfieFile, userSelfieFile) ||
                other.userSelfieFile == userSelfieFile) &&
            (identical(other.userSelfieFileCopy, userSelfieFileCopy) ||
                other.userSelfieFileCopy == userSelfieFileCopy) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.finOrPassport, finOrPassport) ||
                other.finOrPassport == finOrPassport) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthdays, _childrenBirthdays) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.gender, gender) || other.gender == gender));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userSelfieFile,
      userSelfieFileCopy,
      firstName,
      lastName,
      finOrPassport,
      birthday,
      const DeepCollectionEquality().hash(_childrenBirthdays),
      mobileNumber,
      email,
      gender);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfieVerificationInputImplCopyWith<_$SelfieVerificationInputImpl>
      get copyWith => __$$SelfieVerificationInputImplCopyWithImpl<
          _$SelfieVerificationInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SelfieVerificationInputImplToJson(
      this,
    );
  }
}

abstract class _SelfieVerificationInput implements SelfieVerificationInput {
  factory _SelfieVerificationInput(
      {@JsonKey(includeFromJson: false) final File? userSelfieFile,
      @JsonKey(includeFromJson: false) final File? userSelfieFileCopy,
      required final String firstName,
      required final String lastName,
      required final String finOrPassport,
      required final DateTime birthday,
      required final List<DateTime> childrenBirthdays,
      required final String mobileNumber,
      required final String email,
      required final String gender}) = _$SelfieVerificationInputImpl;

  factory _SelfieVerificationInput.fromJson(Map<String, dynamic> json) =
      _$SelfieVerificationInputImpl.fromJson;

  @override
  @JsonKey(includeFromJson: false)
  File? get userSelfieFile;
  @override
  @JsonKey(includeFromJson: false)
  File? get userSelfieFileCopy;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get finOrPassport;
  @override
  DateTime get birthday;
  @override
  List<DateTime> get childrenBirthdays;
  @override
  String get mobileNumber;
  @override
  String get email;
  @override
  String get gender;
  @override
  @JsonKey(ignore: true)
  _$$SelfieVerificationInputImplCopyWith<_$SelfieVerificationInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
