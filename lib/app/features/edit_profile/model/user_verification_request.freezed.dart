// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_verification_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserVerificationRequest _$UserVerificationRequestFromJson(
    Map<String, dynamic> json) {
  return _UserVerificationRequest.fromJson(json);
}

/// @nodoc
mixin _$UserVerificationRequest {
  String get id => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String? get gomamaUserVerifyPhotoUrl => throw _privateConstructorUsedError;
  String? get gomamaFirstName => throw _privateConstructorUsedError;
  String? get gomamaLastName => throw _privateConstructorUsedError;
  DateTime? get gomamaBirthday => throw _privateConstructorUsedError;
  List<DateTime>? get gomamaChildrenBirthdays =>
      throw _privateConstructorUsedError;
  String? get gomamaMobileNumber => throw _privateConstructorUsedError;
  String? get gomamaEmail => throw _privateConstructorUsedError;
  String? get gomamaGender => throw _privateConstructorUsedError;
  String? get gomamaFinOrPassport => throw _privateConstructorUsedError;
  String? get singpassMobileNumber => throw _privateConstructorUsedError;
  String? get singpassEmail => throw _privateConstructorUsedError;
  String? get singpassBirthday => throw _privateConstructorUsedError;
  String? get singpassNric => throw _privateConstructorUsedError;
  String? get singpassFullName => throw _privateConstructorUsedError;
  String? get singpassGender => throw _privateConstructorUsedError;
  String? get singpassChildrenBirthday => throw _privateConstructorUsedError;
  DateTime get reviewedAt => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;
  String get reviewBy => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserVerificationRequestCopyWith<UserVerificationRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserVerificationRequestCopyWith<$Res> {
  factory $UserVerificationRequestCopyWith(UserVerificationRequest value,
          $Res Function(UserVerificationRequest) then) =
      _$UserVerificationRequestCopyWithImpl<$Res, UserVerificationRequest>;
  @useResult
  $Res call(
      {String id,
      String type,
      String userId,
      String? gomamaUserVerifyPhotoUrl,
      String? gomamaFirstName,
      String? gomamaLastName,
      DateTime? gomamaBirthday,
      List<DateTime>? gomamaChildrenBirthdays,
      String? gomamaMobileNumber,
      String? gomamaEmail,
      String? gomamaGender,
      String? gomamaFinOrPassport,
      String? singpassMobileNumber,
      String? singpassEmail,
      String? singpassBirthday,
      String? singpassNric,
      String? singpassFullName,
      String? singpassGender,
      String? singpassChildrenBirthday,
      DateTime reviewedAt,
      String status,
      String reason,
      String reviewBy,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$UserVerificationRequestCopyWithImpl<$Res,
        $Val extends UserVerificationRequest>
    implements $UserVerificationRequestCopyWith<$Res> {
  _$UserVerificationRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? userId = null,
    Object? gomamaUserVerifyPhotoUrl = freezed,
    Object? gomamaFirstName = freezed,
    Object? gomamaLastName = freezed,
    Object? gomamaBirthday = freezed,
    Object? gomamaChildrenBirthdays = freezed,
    Object? gomamaMobileNumber = freezed,
    Object? gomamaEmail = freezed,
    Object? gomamaGender = freezed,
    Object? gomamaFinOrPassport = freezed,
    Object? singpassMobileNumber = freezed,
    Object? singpassEmail = freezed,
    Object? singpassBirthday = freezed,
    Object? singpassNric = freezed,
    Object? singpassFullName = freezed,
    Object? singpassGender = freezed,
    Object? singpassChildrenBirthday = freezed,
    Object? reviewedAt = null,
    Object? status = null,
    Object? reason = null,
    Object? reviewBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gomamaUserVerifyPhotoUrl: freezed == gomamaUserVerifyPhotoUrl
          ? _value.gomamaUserVerifyPhotoUrl
          : gomamaUserVerifyPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaFirstName: freezed == gomamaFirstName
          ? _value.gomamaFirstName
          : gomamaFirstName // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaLastName: freezed == gomamaLastName
          ? _value.gomamaLastName
          : gomamaLastName // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaBirthday: freezed == gomamaBirthday
          ? _value.gomamaBirthday
          : gomamaBirthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gomamaChildrenBirthdays: freezed == gomamaChildrenBirthdays
          ? _value.gomamaChildrenBirthdays
          : gomamaChildrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      gomamaMobileNumber: freezed == gomamaMobileNumber
          ? _value.gomamaMobileNumber
          : gomamaMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaEmail: freezed == gomamaEmail
          ? _value.gomamaEmail
          : gomamaEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaGender: freezed == gomamaGender
          ? _value.gomamaGender
          : gomamaGender // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaFinOrPassport: freezed == gomamaFinOrPassport
          ? _value.gomamaFinOrPassport
          : gomamaFinOrPassport // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassMobileNumber: freezed == singpassMobileNumber
          ? _value.singpassMobileNumber
          : singpassMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassEmail: freezed == singpassEmail
          ? _value.singpassEmail
          : singpassEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassBirthday: freezed == singpassBirthday
          ? _value.singpassBirthday
          : singpassBirthday // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassNric: freezed == singpassNric
          ? _value.singpassNric
          : singpassNric // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassFullName: freezed == singpassFullName
          ? _value.singpassFullName
          : singpassFullName // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassGender: freezed == singpassGender
          ? _value.singpassGender
          : singpassGender // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassChildrenBirthday: freezed == singpassChildrenBirthday
          ? _value.singpassChildrenBirthday
          : singpassChildrenBirthday // ignore: cast_nullable_to_non_nullable
              as String?,
      reviewedAt: null == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
      reviewBy: null == reviewBy
          ? _value.reviewBy
          : reviewBy // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserVerificationRequestImplCopyWith<$Res>
    implements $UserVerificationRequestCopyWith<$Res> {
  factory _$$UserVerificationRequestImplCopyWith(
          _$UserVerificationRequestImpl value,
          $Res Function(_$UserVerificationRequestImpl) then) =
      __$$UserVerificationRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String type,
      String userId,
      String? gomamaUserVerifyPhotoUrl,
      String? gomamaFirstName,
      String? gomamaLastName,
      DateTime? gomamaBirthday,
      List<DateTime>? gomamaChildrenBirthdays,
      String? gomamaMobileNumber,
      String? gomamaEmail,
      String? gomamaGender,
      String? gomamaFinOrPassport,
      String? singpassMobileNumber,
      String? singpassEmail,
      String? singpassBirthday,
      String? singpassNric,
      String? singpassFullName,
      String? singpassGender,
      String? singpassChildrenBirthday,
      DateTime reviewedAt,
      String status,
      String reason,
      String reviewBy,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$UserVerificationRequestImplCopyWithImpl<$Res>
    extends _$UserVerificationRequestCopyWithImpl<$Res,
        _$UserVerificationRequestImpl>
    implements _$$UserVerificationRequestImplCopyWith<$Res> {
  __$$UserVerificationRequestImplCopyWithImpl(
      _$UserVerificationRequestImpl _value,
      $Res Function(_$UserVerificationRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? userId = null,
    Object? gomamaUserVerifyPhotoUrl = freezed,
    Object? gomamaFirstName = freezed,
    Object? gomamaLastName = freezed,
    Object? gomamaBirthday = freezed,
    Object? gomamaChildrenBirthdays = freezed,
    Object? gomamaMobileNumber = freezed,
    Object? gomamaEmail = freezed,
    Object? gomamaGender = freezed,
    Object? gomamaFinOrPassport = freezed,
    Object? singpassMobileNumber = freezed,
    Object? singpassEmail = freezed,
    Object? singpassBirthday = freezed,
    Object? singpassNric = freezed,
    Object? singpassFullName = freezed,
    Object? singpassGender = freezed,
    Object? singpassChildrenBirthday = freezed,
    Object? reviewedAt = null,
    Object? status = null,
    Object? reason = null,
    Object? reviewBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$UserVerificationRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      gomamaUserVerifyPhotoUrl: freezed == gomamaUserVerifyPhotoUrl
          ? _value.gomamaUserVerifyPhotoUrl
          : gomamaUserVerifyPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaFirstName: freezed == gomamaFirstName
          ? _value.gomamaFirstName
          : gomamaFirstName // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaLastName: freezed == gomamaLastName
          ? _value.gomamaLastName
          : gomamaLastName // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaBirthday: freezed == gomamaBirthday
          ? _value.gomamaBirthday
          : gomamaBirthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gomamaChildrenBirthdays: freezed == gomamaChildrenBirthdays
          ? _value._gomamaChildrenBirthdays
          : gomamaChildrenBirthdays // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      gomamaMobileNumber: freezed == gomamaMobileNumber
          ? _value.gomamaMobileNumber
          : gomamaMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaEmail: freezed == gomamaEmail
          ? _value.gomamaEmail
          : gomamaEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaGender: freezed == gomamaGender
          ? _value.gomamaGender
          : gomamaGender // ignore: cast_nullable_to_non_nullable
              as String?,
      gomamaFinOrPassport: freezed == gomamaFinOrPassport
          ? _value.gomamaFinOrPassport
          : gomamaFinOrPassport // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassMobileNumber: freezed == singpassMobileNumber
          ? _value.singpassMobileNumber
          : singpassMobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassEmail: freezed == singpassEmail
          ? _value.singpassEmail
          : singpassEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassBirthday: freezed == singpassBirthday
          ? _value.singpassBirthday
          : singpassBirthday // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassNric: freezed == singpassNric
          ? _value.singpassNric
          : singpassNric // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassFullName: freezed == singpassFullName
          ? _value.singpassFullName
          : singpassFullName // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassGender: freezed == singpassGender
          ? _value.singpassGender
          : singpassGender // ignore: cast_nullable_to_non_nullable
              as String?,
      singpassChildrenBirthday: freezed == singpassChildrenBirthday
          ? _value.singpassChildrenBirthday
          : singpassChildrenBirthday // ignore: cast_nullable_to_non_nullable
              as String?,
      reviewedAt: null == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
      reviewBy: null == reviewBy
          ? _value.reviewBy
          : reviewBy // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
@CustomDateTimeListConverter()
class _$UserVerificationRequestImpl implements _UserVerificationRequest {
  _$UserVerificationRequestImpl(
      {required this.id,
      required this.type,
      required this.userId,
      this.gomamaUserVerifyPhotoUrl,
      this.gomamaFirstName,
      this.gomamaLastName,
      this.gomamaBirthday,
      final List<DateTime>? gomamaChildrenBirthdays,
      this.gomamaMobileNumber,
      this.gomamaEmail,
      this.gomamaGender,
      this.gomamaFinOrPassport,
      this.singpassMobileNumber,
      this.singpassEmail,
      this.singpassBirthday,
      this.singpassNric,
      this.singpassFullName,
      this.singpassGender,
      this.singpassChildrenBirthday,
      required this.reviewedAt,
      required this.status,
      required this.reason,
      required this.reviewBy,
      required this.createdAt,
      required this.updatedAt})
      : _gomamaChildrenBirthdays = gomamaChildrenBirthdays;

  factory _$UserVerificationRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserVerificationRequestImplFromJson(json);

  @override
  final String id;
  @override
  final String type;
  @override
  final String userId;
  @override
  final String? gomamaUserVerifyPhotoUrl;
  @override
  final String? gomamaFirstName;
  @override
  final String? gomamaLastName;
  @override
  final DateTime? gomamaBirthday;
  final List<DateTime>? _gomamaChildrenBirthdays;
  @override
  List<DateTime>? get gomamaChildrenBirthdays {
    final value = _gomamaChildrenBirthdays;
    if (value == null) return null;
    if (_gomamaChildrenBirthdays is EqualUnmodifiableListView)
      return _gomamaChildrenBirthdays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? gomamaMobileNumber;
  @override
  final String? gomamaEmail;
  @override
  final String? gomamaGender;
  @override
  final String? gomamaFinOrPassport;
  @override
  final String? singpassMobileNumber;
  @override
  final String? singpassEmail;
  @override
  final String? singpassBirthday;
  @override
  final String? singpassNric;
  @override
  final String? singpassFullName;
  @override
  final String? singpassGender;
  @override
  final String? singpassChildrenBirthday;
  @override
  final DateTime reviewedAt;
  @override
  final String status;
  @override
  final String reason;
  @override
  final String reviewBy;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'UserVerificationRequest(id: $id, type: $type, userId: $userId, gomamaUserVerifyPhotoUrl: $gomamaUserVerifyPhotoUrl, gomamaFirstName: $gomamaFirstName, gomamaLastName: $gomamaLastName, gomamaBirthday: $gomamaBirthday, gomamaChildrenBirthdays: $gomamaChildrenBirthdays, gomamaMobileNumber: $gomamaMobileNumber, gomamaEmail: $gomamaEmail, gomamaGender: $gomamaGender, gomamaFinOrPassport: $gomamaFinOrPassport, singpassMobileNumber: $singpassMobileNumber, singpassEmail: $singpassEmail, singpassBirthday: $singpassBirthday, singpassNric: $singpassNric, singpassFullName: $singpassFullName, singpassGender: $singpassGender, singpassChildrenBirthday: $singpassChildrenBirthday, reviewedAt: $reviewedAt, status: $status, reason: $reason, reviewBy: $reviewBy, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserVerificationRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(
                    other.gomamaUserVerifyPhotoUrl, gomamaUserVerifyPhotoUrl) ||
                other.gomamaUserVerifyPhotoUrl == gomamaUserVerifyPhotoUrl) &&
            (identical(other.gomamaFirstName, gomamaFirstName) ||
                other.gomamaFirstName == gomamaFirstName) &&
            (identical(other.gomamaLastName, gomamaLastName) ||
                other.gomamaLastName == gomamaLastName) &&
            (identical(other.gomamaBirthday, gomamaBirthday) ||
                other.gomamaBirthday == gomamaBirthday) &&
            const DeepCollectionEquality().equals(
                other._gomamaChildrenBirthdays, _gomamaChildrenBirthdays) &&
            (identical(other.gomamaMobileNumber, gomamaMobileNumber) ||
                other.gomamaMobileNumber == gomamaMobileNumber) &&
            (identical(other.gomamaEmail, gomamaEmail) ||
                other.gomamaEmail == gomamaEmail) &&
            (identical(other.gomamaGender, gomamaGender) ||
                other.gomamaGender == gomamaGender) &&
            (identical(other.gomamaFinOrPassport, gomamaFinOrPassport) ||
                other.gomamaFinOrPassport == gomamaFinOrPassport) &&
            (identical(other.singpassMobileNumber, singpassMobileNumber) ||
                other.singpassMobileNumber == singpassMobileNumber) &&
            (identical(other.singpassEmail, singpassEmail) ||
                other.singpassEmail == singpassEmail) &&
            (identical(other.singpassBirthday, singpassBirthday) ||
                other.singpassBirthday == singpassBirthday) &&
            (identical(other.singpassNric, singpassNric) ||
                other.singpassNric == singpassNric) &&
            (identical(other.singpassFullName, singpassFullName) ||
                other.singpassFullName == singpassFullName) &&
            (identical(other.singpassGender, singpassGender) ||
                other.singpassGender == singpassGender) &&
            (identical(
                    other.singpassChildrenBirthday, singpassChildrenBirthday) ||
                other.singpassChildrenBirthday == singpassChildrenBirthday) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.reviewBy, reviewBy) ||
                other.reviewBy == reviewBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        type,
        userId,
        gomamaUserVerifyPhotoUrl,
        gomamaFirstName,
        gomamaLastName,
        gomamaBirthday,
        const DeepCollectionEquality().hash(_gomamaChildrenBirthdays),
        gomamaMobileNumber,
        gomamaEmail,
        gomamaGender,
        gomamaFinOrPassport,
        singpassMobileNumber,
        singpassEmail,
        singpassBirthday,
        singpassNric,
        singpassFullName,
        singpassGender,
        singpassChildrenBirthday,
        reviewedAt,
        status,
        reason,
        reviewBy,
        createdAt,
        updatedAt
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserVerificationRequestImplCopyWith<_$UserVerificationRequestImpl>
      get copyWith => __$$UserVerificationRequestImplCopyWithImpl<
          _$UserVerificationRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserVerificationRequestImplToJson(
      this,
    );
  }
}

abstract class _UserVerificationRequest implements UserVerificationRequest {
  factory _UserVerificationRequest(
      {required final String id,
      required final String type,
      required final String userId,
      final String? gomamaUserVerifyPhotoUrl,
      final String? gomamaFirstName,
      final String? gomamaLastName,
      final DateTime? gomamaBirthday,
      final List<DateTime>? gomamaChildrenBirthdays,
      final String? gomamaMobileNumber,
      final String? gomamaEmail,
      final String? gomamaGender,
      final String? gomamaFinOrPassport,
      final String? singpassMobileNumber,
      final String? singpassEmail,
      final String? singpassBirthday,
      final String? singpassNric,
      final String? singpassFullName,
      final String? singpassGender,
      final String? singpassChildrenBirthday,
      required final DateTime reviewedAt,
      required final String status,
      required final String reason,
      required final String reviewBy,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$UserVerificationRequestImpl;

  factory _UserVerificationRequest.fromJson(Map<String, dynamic> json) =
      _$UserVerificationRequestImpl.fromJson;

  @override
  String get id;
  @override
  String get type;
  @override
  String get userId;
  @override
  String? get gomamaUserVerifyPhotoUrl;
  @override
  String? get gomamaFirstName;
  @override
  String? get gomamaLastName;
  @override
  DateTime? get gomamaBirthday;
  @override
  List<DateTime>? get gomamaChildrenBirthdays;
  @override
  String? get gomamaMobileNumber;
  @override
  String? get gomamaEmail;
  @override
  String? get gomamaGender;
  @override
  String? get gomamaFinOrPassport;
  @override
  String? get singpassMobileNumber;
  @override
  String? get singpassEmail;
  @override
  String? get singpassBirthday;
  @override
  String? get singpassNric;
  @override
  String? get singpassFullName;
  @override
  String? get singpassGender;
  @override
  String? get singpassChildrenBirthday;
  @override
  DateTime get reviewedAt;
  @override
  String get status;
  @override
  String get reason;
  @override
  String get reviewBy;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$UserVerificationRequestImplCopyWith<_$UserVerificationRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
