import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'singpass_verification.freezed.dart';
part 'singpass_verification.g.dart';

@freezed
class SingpassVerificationInput with _$SingpassVerificationInput {
  @CustomDateTimeConverter()
  @CustomDateTimeListConverter()
  factory SingpassVerificationInput({
    required String firstName,
    required String lastName,
    required DateTime birthday,
    DateTime? childBirthday, // If only one child.
    List<DateTime>? childrenBirthdays,
    required String mobileNumber,
    required String gender,
    required String nric,
  }) = _SingpassVerificationInput;

  factory SingpassVerificationInput.fromJson(Json json) =>
      _$SingpassVerificationInputFromJson(json);
}
