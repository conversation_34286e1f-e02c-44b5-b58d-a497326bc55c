// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$requestEmailOrPhoneOtpHash() =>
    r'93123e06893b919155eb3bfc8090b21e23a7c130';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [requestEmailOrPhoneOtp].
@ProviderFor(requestEmailOrPhoneOtp)
const requestEmailOrPhoneOtpProvider = RequestEmailOrPhoneOtpFamily();

/// See also [requestEmailOrPhoneOtp].
class RequestEmailOrPhoneOtpFamily
    extends Family<AsyncValue<PostResponse<void>>> {
  /// See also [requestEmailOrPhoneOtp].
  const RequestEmailOrPhoneOtpFamily();

  /// See also [requestEmailOrPhoneOtp].
  RequestEmailOrPhoneOtpProvider call(
    String emailOrMobile,
  ) {
    return RequestEmailOrPhoneOtpProvider(
      emailOrMobile,
    );
  }

  @override
  RequestEmailOrPhoneOtpProvider getProviderOverride(
    covariant RequestEmailOrPhoneOtpProvider provider,
  ) {
    return call(
      provider.emailOrMobile,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'requestEmailOrPhoneOtpProvider';
}

/// See also [requestEmailOrPhoneOtp].
class RequestEmailOrPhoneOtpProvider
    extends AutoDisposeFutureProvider<PostResponse<void>> {
  /// See also [requestEmailOrPhoneOtp].
  RequestEmailOrPhoneOtpProvider(
    String emailOrMobile,
  ) : this._internal(
          (ref) => requestEmailOrPhoneOtp(
            ref as RequestEmailOrPhoneOtpRef,
            emailOrMobile,
          ),
          from: requestEmailOrPhoneOtpProvider,
          name: r'requestEmailOrPhoneOtpProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$requestEmailOrPhoneOtpHash,
          dependencies: RequestEmailOrPhoneOtpFamily._dependencies,
          allTransitiveDependencies:
              RequestEmailOrPhoneOtpFamily._allTransitiveDependencies,
          emailOrMobile: emailOrMobile,
        );

  RequestEmailOrPhoneOtpProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.emailOrMobile,
  }) : super.internal();

  final String emailOrMobile;

  @override
  Override overrideWith(
    FutureOr<PostResponse<void>> Function(RequestEmailOrPhoneOtpRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RequestEmailOrPhoneOtpProvider._internal(
        (ref) => create(ref as RequestEmailOrPhoneOtpRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        emailOrMobile: emailOrMobile,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<PostResponse<void>> createElement() {
    return _RequestEmailOrPhoneOtpProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RequestEmailOrPhoneOtpProvider &&
        other.emailOrMobile == emailOrMobile;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, emailOrMobile.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin RequestEmailOrPhoneOtpRef
    on AutoDisposeFutureProviderRef<PostResponse<void>> {
  /// The parameter `emailOrMobile` of this provider.
  String get emailOrMobile;
}

class _RequestEmailOrPhoneOtpProviderElement
    extends AutoDisposeFutureProviderElement<PostResponse<void>>
    with RequestEmailOrPhoneOtpRef {
  _RequestEmailOrPhoneOtpProviderElement(super.provider);

  @override
  String get emailOrMobile =>
      (origin as RequestEmailOrPhoneOtpProvider).emailOrMobile;
}

String _$verifyEmailOrPhoneOtpHash() =>
    r'750d389a724996c11b1c7a59fc56f703fa41a659';

/// See also [verifyEmailOrPhoneOtp].
@ProviderFor(verifyEmailOrPhoneOtp)
const verifyEmailOrPhoneOtpProvider = VerifyEmailOrPhoneOtpFamily();

/// See also [verifyEmailOrPhoneOtp].
class VerifyEmailOrPhoneOtpFamily
    extends Family<AsyncValue<PostResponse<User>>> {
  /// See also [verifyEmailOrPhoneOtp].
  const VerifyEmailOrPhoneOtpFamily();

  /// See also [verifyEmailOrPhoneOtp].
  VerifyEmailOrPhoneOtpProvider call(
    String? newEmail,
    String? newMobileNumber,
    String? countryDialCode,
    String otp,
  ) {
    return VerifyEmailOrPhoneOtpProvider(
      newEmail,
      newMobileNumber,
      countryDialCode,
      otp,
    );
  }

  @override
  VerifyEmailOrPhoneOtpProvider getProviderOverride(
    covariant VerifyEmailOrPhoneOtpProvider provider,
  ) {
    return call(
      provider.newEmail,
      provider.newMobileNumber,
      provider.countryDialCode,
      provider.otp,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'verifyEmailOrPhoneOtpProvider';
}

/// See also [verifyEmailOrPhoneOtp].
class VerifyEmailOrPhoneOtpProvider
    extends AutoDisposeFutureProvider<PostResponse<User>> {
  /// See also [verifyEmailOrPhoneOtp].
  VerifyEmailOrPhoneOtpProvider(
    String? newEmail,
    String? newMobileNumber,
    String? countryDialCode,
    String otp,
  ) : this._internal(
          (ref) => verifyEmailOrPhoneOtp(
            ref as VerifyEmailOrPhoneOtpRef,
            newEmail,
            newMobileNumber,
            countryDialCode,
            otp,
          ),
          from: verifyEmailOrPhoneOtpProvider,
          name: r'verifyEmailOrPhoneOtpProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$verifyEmailOrPhoneOtpHash,
          dependencies: VerifyEmailOrPhoneOtpFamily._dependencies,
          allTransitiveDependencies:
              VerifyEmailOrPhoneOtpFamily._allTransitiveDependencies,
          newEmail: newEmail,
          newMobileNumber: newMobileNumber,
          countryDialCode: countryDialCode,
          otp: otp,
        );

  VerifyEmailOrPhoneOtpProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.newEmail,
    required this.newMobileNumber,
    required this.countryDialCode,
    required this.otp,
  }) : super.internal();

  final String? newEmail;
  final String? newMobileNumber;
  final String? countryDialCode;
  final String otp;

  @override
  Override overrideWith(
    FutureOr<PostResponse<User>> Function(VerifyEmailOrPhoneOtpRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: VerifyEmailOrPhoneOtpProvider._internal(
        (ref) => create(ref as VerifyEmailOrPhoneOtpRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        newEmail: newEmail,
        newMobileNumber: newMobileNumber,
        countryDialCode: countryDialCode,
        otp: otp,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<PostResponse<User>> createElement() {
    return _VerifyEmailOrPhoneOtpProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is VerifyEmailOrPhoneOtpProvider &&
        other.newEmail == newEmail &&
        other.newMobileNumber == newMobileNumber &&
        other.countryDialCode == countryDialCode &&
        other.otp == otp;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, newEmail.hashCode);
    hash = _SystemHash.combine(hash, newMobileNumber.hashCode);
    hash = _SystemHash.combine(hash, countryDialCode.hashCode);
    hash = _SystemHash.combine(hash, otp.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin VerifyEmailOrPhoneOtpRef
    on AutoDisposeFutureProviderRef<PostResponse<User>> {
  /// The parameter `newEmail` of this provider.
  String? get newEmail;

  /// The parameter `newMobileNumber` of this provider.
  String? get newMobileNumber;

  /// The parameter `countryDialCode` of this provider.
  String? get countryDialCode;

  /// The parameter `otp` of this provider.
  String get otp;
}

class _VerifyEmailOrPhoneOtpProviderElement
    extends AutoDisposeFutureProviderElement<PostResponse<User>>
    with VerifyEmailOrPhoneOtpRef {
  _VerifyEmailOrPhoneOtpProviderElement(super.provider);

  @override
  String? get newEmail => (origin as VerifyEmailOrPhoneOtpProvider).newEmail;
  @override
  String? get newMobileNumber =>
      (origin as VerifyEmailOrPhoneOtpProvider).newMobileNumber;
  @override
  String? get countryDialCode =>
      (origin as VerifyEmailOrPhoneOtpProvider).countryDialCode;
  @override
  String get otp => (origin as VerifyEmailOrPhoneOtpProvider).otp;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
