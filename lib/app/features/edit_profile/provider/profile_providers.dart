import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/auth_flow.dart';
import 'package:gomama/app/features/auth/model/otp.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/user_providers.dart';
import 'package:gomama/app/features/auth/repository/otp_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'profile_providers.g.dart';

@riverpod
Future<PostResponse<void>> requestEmailOrPhoneOtp(
  RequestEmailOrPhoneOtpRef ref,
  String emailOrMobile,
) {
  return ref.watch(otpRepositoryProvider).requestUpdateEmailOrPhoneOtp(
        RequestEmailOrPhoneUpdateOtpInput(
          mobileOrEmail: emailOrMobile,
        ),
      );
}

@riverpod
Future<PostResponse<User>> verifyEmailOrPhoneOtp(
  VerifyEmailOrPhoneOtpRef ref,
  String? newEmail,
  String? newMobileNumber,
  String? countryDialCode,
  String otp,
) async {
  final verifyUpdateEmailOrPhoneOtpInput = newEmail != null
      ? VerifyUpdateEmailOrPhoneOtpInput(
          newEmail: newEmail,
          otp: otp,
        )
      : newMobileNumber != null && countryDialCode != null
          ? VerifyUpdateEmailOrPhoneOtpInput(
              newMobileNumber: newMobileNumber,
              countryDialCode: countryDialCode,
              otp: otp,
            )
          : VerifyUpdateEmailOrPhoneOtpInput();

  final response = await ref
      .watch(otpRepositoryProvider)
      .validateUpdateEmailOrPhoneOtp(verifyUpdateEmailOrPhoneOtpInput);

  if (response.success) {
    ref.read(authControllerProvider.notifier).setUser(response.data);
  }

  return response;
}
