import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gomama/app/features/auth/repository/user_repository.dart';
import 'package:gomama/app/features/singpass/model/singpass.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'singpass_providers.g.dart';

@riverpod
Future<void> redirectToSingpassMobile(
  RedirectToSingpassMobileRef ref, {
  String uri = '',
}) async {
  const _channel = MethodChannel('singpassMobile');

  try {
    Groveman.info('redirectToSingpassMobile');

    final _result = await _channel
        .invokeMethod('redirectToSingpassMobile', <String, dynamic>{
      'uri': uri,
    });

    return _result;
  } catch (e) {
    return Future.value();
  }
}

@riverpod
Future<SingpassProcessResponse> processSingpassMyinfo(
  ProcessSingpassMyinfoRef ref,
  String singpassCode,
  String singpassState,
) async {
  return ref.watch(userRepositoryProvider).singpassProcess(
        SingpassProcessInput(
          authorizationCode: singpassCode,
          authorizationState: singpassState,
        ),
      );
}

final singpassResponseProvider = StateProvider<SingpassProcessResponse?>((ref) => null);
