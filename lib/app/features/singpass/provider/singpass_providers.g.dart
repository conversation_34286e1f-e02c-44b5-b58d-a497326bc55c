// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'singpass_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$redirectToSingpassMobileHash() =>
    r'6c762e43df0559a9e34b556db23910538e52c7c0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [redirectToSingpassMobile].
@ProviderFor(redirectToSingpassMobile)
const redirectToSingpassMobileProvider = RedirectToSingpassMobileFamily();

/// See also [redirectToSingpassMobile].
class RedirectToSingpassMobileFamily extends Family<AsyncValue<void>> {
  /// See also [redirectToSingpassMobile].
  const RedirectToSingpassMobileFamily();

  /// See also [redirectToSingpassMobile].
  RedirectToSingpassMobileProvider call({
    String uri = '',
  }) {
    return RedirectToSingpassMobileProvider(
      uri: uri,
    );
  }

  @override
  RedirectToSingpassMobileProvider getProviderOverride(
    covariant RedirectToSingpassMobileProvider provider,
  ) {
    return call(
      uri: provider.uri,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'redirectToSingpassMobileProvider';
}

/// See also [redirectToSingpassMobile].
class RedirectToSingpassMobileProvider extends AutoDisposeFutureProvider<void> {
  /// See also [redirectToSingpassMobile].
  RedirectToSingpassMobileProvider({
    String uri = '',
  }) : this._internal(
          (ref) => redirectToSingpassMobile(
            ref as RedirectToSingpassMobileRef,
            uri: uri,
          ),
          from: redirectToSingpassMobileProvider,
          name: r'redirectToSingpassMobileProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$redirectToSingpassMobileHash,
          dependencies: RedirectToSingpassMobileFamily._dependencies,
          allTransitiveDependencies:
              RedirectToSingpassMobileFamily._allTransitiveDependencies,
          uri: uri,
        );

  RedirectToSingpassMobileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.uri,
  }) : super.internal();

  final String uri;

  @override
  Override overrideWith(
    FutureOr<void> Function(RedirectToSingpassMobileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RedirectToSingpassMobileProvider._internal(
        (ref) => create(ref as RedirectToSingpassMobileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        uri: uri,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _RedirectToSingpassMobileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RedirectToSingpassMobileProvider && other.uri == uri;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, uri.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin RedirectToSingpassMobileRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `uri` of this provider.
  String get uri;
}

class _RedirectToSingpassMobileProviderElement
    extends AutoDisposeFutureProviderElement<void>
    with RedirectToSingpassMobileRef {
  _RedirectToSingpassMobileProviderElement(super.provider);

  @override
  String get uri => (origin as RedirectToSingpassMobileProvider).uri;
}

String _$processSingpassMyinfoHash() =>
    r'79e246988ad5ab27a33870264239f0754e56d107';

/// See also [processSingpassMyinfo].
@ProviderFor(processSingpassMyinfo)
const processSingpassMyinfoProvider = ProcessSingpassMyinfoFamily();

/// See also [processSingpassMyinfo].
class ProcessSingpassMyinfoFamily
    extends Family<AsyncValue<SingpassProcessResponse>> {
  /// See also [processSingpassMyinfo].
  const ProcessSingpassMyinfoFamily();

  /// See also [processSingpassMyinfo].
  ProcessSingpassMyinfoProvider call(
    String singpassCode,
    String singpassState,
  ) {
    return ProcessSingpassMyinfoProvider(
      singpassCode,
      singpassState,
    );
  }

  @override
  ProcessSingpassMyinfoProvider getProviderOverride(
    covariant ProcessSingpassMyinfoProvider provider,
  ) {
    return call(
      provider.singpassCode,
      provider.singpassState,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'processSingpassMyinfoProvider';
}

/// See also [processSingpassMyinfo].
class ProcessSingpassMyinfoProvider
    extends AutoDisposeFutureProvider<SingpassProcessResponse> {
  /// See also [processSingpassMyinfo].
  ProcessSingpassMyinfoProvider(
    String singpassCode,
    String singpassState,
  ) : this._internal(
          (ref) => processSingpassMyinfo(
            ref as ProcessSingpassMyinfoRef,
            singpassCode,
            singpassState,
          ),
          from: processSingpassMyinfoProvider,
          name: r'processSingpassMyinfoProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$processSingpassMyinfoHash,
          dependencies: ProcessSingpassMyinfoFamily._dependencies,
          allTransitiveDependencies:
              ProcessSingpassMyinfoFamily._allTransitiveDependencies,
          singpassCode: singpassCode,
          singpassState: singpassState,
        );

  ProcessSingpassMyinfoProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.singpassCode,
    required this.singpassState,
  }) : super.internal();

  final String singpassCode;
  final String singpassState;

  @override
  Override overrideWith(
    FutureOr<SingpassProcessResponse> Function(
            ProcessSingpassMyinfoRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProcessSingpassMyinfoProvider._internal(
        (ref) => create(ref as ProcessSingpassMyinfoRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        singpassCode: singpassCode,
        singpassState: singpassState,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SingpassProcessResponse> createElement() {
    return _ProcessSingpassMyinfoProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProcessSingpassMyinfoProvider &&
        other.singpassCode == singpassCode &&
        other.singpassState == singpassState;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, singpassCode.hashCode);
    hash = _SystemHash.combine(hash, singpassState.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ProcessSingpassMyinfoRef
    on AutoDisposeFutureProviderRef<SingpassProcessResponse> {
  /// The parameter `singpassCode` of this provider.
  String get singpassCode;

  /// The parameter `singpassState` of this provider.
  String get singpassState;
}

class _ProcessSingpassMyinfoProviderElement
    extends AutoDisposeFutureProviderElement<SingpassProcessResponse>
    with ProcessSingpassMyinfoRef {
  _ProcessSingpassMyinfoProviderElement(super.provider);

  @override
  String get singpassCode =>
      (origin as ProcessSingpassMyinfoProvider).singpassCode;
  @override
  String get singpassState =>
      (origin as ProcessSingpassMyinfoProvider).singpassState;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
