import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_phone_field/form_builder_phone_field.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/edit_profile/model/singpass_verification.dart';
import 'package:gomama/app/features/singpass/model/singpass.dart';
import 'package:gomama/app/features/singpass/provider/singpass_providers.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class SingpassFormView extends HookConsumerWidget {
  const SingpassFormView(this.singpassResponse, {super.key});

  final SingpassProcessResponse singpassResponse;

  static const routeName = 'singpass form';
  static const routePath = '/singpass-form';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());

    return Theme(
      data: Theme.of(context).copyWith(
        dividerTheme: const DividerThemeData(
          color: Colors.transparent,
        ),
      ),
      child: Scaffold(
        backgroundColor: CustomColors.backgroundForm,
        appBar: AppBar(
          foregroundColor: CustomColors.primaries,
          automaticallyImplyLeading: false,
          title: const Text('Singpass Approved!'),
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: SingleChildScrollView(
            child: _SingpassForm(
              singpassResponse,
              formKey: _formKey.value,
            ),
          ),
        ),
        persistentFooterButtons: [
          _SingpassFormFooter(formKey: _formKey.value),
        ],
      ),
    );
  }
}

class _SingpassForm extends ConsumerWidget {
  const _SingpassForm(
    this.singpassResponse, {
    required this.formKey,
  });
  final SingpassProcessResponse singpassResponse;
  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FormBuilder(
      clearValueOnUnregister: true,
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          const Text(
            'Your Details',
            style: TextStyle(
              fontSize: 20,
              fontStyle: FontStyle.italic,
            ),
          ),
          FormBuilderTextField(
            name: 'gender',
            readOnly: true,
            initialValue: singpassResponse.gender,
            decoration: const InputDecoration(
              label: Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: Text('Gender'),
              ),
            ),
          ),
          const SizedBox(height: 8),
          FormBuilderTextField(
            name: 'full_name',
            readOnly: true,
            initialValue: singpassResponse.name,
            decoration: const InputDecoration(
              floatingLabelBehavior: FloatingLabelBehavior.always,
              labelText: 'Full Name',
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: 'Please fill in your full name',
              ),
            ]),
            textInputAction: TextInputAction.next,
          ),
          const SizedBox(height: 8),
          FormBuilderTextField(
            name: 'nric',
            readOnly: true,
            initialValue: singpassResponse.nric?.replaceAll('*', ''),
            decoration: const InputDecoration(
              floatingLabelBehavior: FloatingLabelBehavior.always,
              labelText: 'NRIC/FIN/Passport (Last 4 digits)',
              hintText: '1234',
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: 'Please fill in the NRIC/FIN/Passport',
              ),
              FormBuilderValidators.minLength(
                4,
                errorText: 'Please fill in the NRIC/FIN/Passport',
              ),
            ]),
            textInputAction: TextInputAction.next,
          ),
          const SizedBox(height: 8),
          FormBuilderDateTimePicker(
            name: 'birthday',
            enabled: false,
            initialValue: singpassResponse.birthday,
            inputType: InputType.date,
            decoration: const InputDecoration(
              labelText: 'Date of Birth (Must be 16+ years old)',
              suffixIcon: Icon(CustomIcon.calendarFull),
              hintText: 'DD/MM/YYYY',
              floatingLabelBehavior: FloatingLabelBehavior.always,
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: 'Please pick your date of birth',
              ),
            ]),
            textInputAction: TextInputAction.next,
            valueTransformer: (value) {
              if (value == null) {
                return '';
              }

              return value.toIso8601String();
            },
          ),
          const SizedBox(height: 8),
          if (singpassResponse.childrenBirthRecords?.isNotEmpty ?? false)
            ...(singpassResponse.childrenBirthRecords ?? [])
                .mapIndexed(
                  (index, singpassChildrenBirthRecord) =>
                      FormBuilderDateTimePicker(
                    name: 'children_birthdays_$index',
                    enabled: false,
                    inputType: InputType.date,
                    initialValue: DateFormat('yyyy-MM-dd')
                        .tryParse(singpassChildrenBirthRecord),
                    decoration: InputDecoration(
                      labelText: "Child ${index + 1}'s Date of Birth",
                      suffixIcon: const Icon(
                        CustomIcon.calendarFull,
                      ),
                      hintText: 'DD/MM/YYYY',
                      floatingLabelBehavior: FloatingLabelBehavior.always,
                    ),
                    textInputAction: TextInputAction.next,
                    valueTransformer: (value) {
                      if (value == null) {
                        return '';
                      }

                      return value.toIso8601String();
                    },
                  ),
                )
                .toList()
                .cast<Widget>()
          else
            const Column(
              children: [
                SizedBox(height: 20),
                Center(
                  child: Text(
                    'No children details found',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          // FormBuilderDateTimePicker(
          //   name: 'child_birthday',
          //   enabled: false,
          //   inputType: InputType.date,
          //   decoration: const InputDecoration(
          //     labelText: "Youngest child's Date of Birth",
          //     suffixIcon: Icon(
          //       CustomIcon.calendarFull,
          //     ),
          //     hintText: 'DD/MM/YYYY',
          //     floatingLabelBehavior: FloatingLabelBehavior.always,
          //   ),
          //   textInputAction: TextInputAction.next,
          //   valueTransformer: (value) {
          //     if (value == null) {
          //       return '';
          //     }

          //     return value.toIso8601String();
          //   },
          // ),
          const SizedBox(height: 20),
          const Text(
            'Contact Details',
            style: TextStyle(
              fontSize: 20,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 8),
          FormBuilderPhoneField(
            name: 'mobile_number',
            defaultSelectedCountryIsoCode: 'SG',
            initialValue: singpassResponse.mobileNumber,
            enabled: false,
            decoration: const InputDecoration(
              labelText: 'Mobile Number',
              floatingLabelBehavior: FloatingLabelBehavior.always,
              hintText: 'Mobile Number',
            ),
            textInputAction: TextInputAction.next,
          ),
          // const SizedBox(height: 8),
          // FormBuilderTextField(
          //   name: 'email',
          //   keyboardType: TextInputType.emailAddress,
          //   decoration: const InputDecoration(
          //     labelText: 'Email Address',
          //     floatingLabelBehavior: FloatingLabelBehavior.always,
          //     hintText: 'Email Address',
          //   ),
          //   validator: FormBuilderValidators.compose([
          //     FormBuilderValidators.required(
          //       errorText: 'Please fill in your email address',
          //     ),
          //     FormBuilderValidators.email(
          //       errorText: 'Please insert the correct email address',
          //     ),
          //   ]),
          // ),
          const SizedBox(height: 20),
          ListTileTheme(
            data: const ListTileThemeData(
              titleAlignment: ListTileTitleAlignment.top,
              minVerticalPadding: 0,
              horizontalTitleGap: 0,
            ),
            child: FormBuilderCheckbox(
              name: 'checkbox',
              title: Text(
                'I agree that all the information provided is true and accurate. I accept full responsibility for the accuracy of this information. (By checking this box, you acknowledge that the information provided is accurate and you consent to its use. False information may have legal consequences. Please refer to our Terms of Service and Privacy Policy for more details.)',
                style: textTheme(context).labelSmall,
              ),
              validator: FormBuilderValidators.equal(
                true,
                errorText: 'You must agree to continue',
              ),
            ),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _SingpassFormFooter extends HookConsumerWidget {
  const _SingpassFormFooter({
    required this.formKey,
  });
  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final errorState = useState<String?>(null);
    final _controller = ref.watch(verificationFlowControllerProvider.notifier);

    ref.listen(verificationFlowControllerProvider, (prev, next) {
      if (prev?.step != next.step && next.step == 23) {
        errorState.value =
            "We apologize for the inconvenience. Singpass is unable to retrieve you or your child's information. Please verify your Singpass details or contact Singpass for assistance in updating them.\nFor an alternative login option, please verify your identity with a selfie.";
      }
    });

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (errorState.value != null)
          Container(
            color: Colors.red[100],
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        errorState.value!,
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      BrandButton(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        onPressed: () {
                          ref.read(
                            sendWhatsAppMessageProvider(
                              'Hi GO!MAMA, I need some help on ',
                              Environment.gomamaWhatsappPhone,
                            ),
                          );
                        },
                        child: const Text('Support'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        Row(
          children: [
            const SizedBox(width: 16),
            Expanded(
              child: BrandButton.outlined(
                onPressed: () {
                  // Clear profile verification form values
                  if (context.canPop()) {
                    context.pop();
                  } else {
                    // we are likely redirected via deeplink
                    context.pushReplacement(const ProfileRoute().location);
                  }
                },
                child: const Text('Cancel'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: BrandButton.cta(
                onPressed: () async {
                  // Validate and save the form values
                  final success = formKey.currentState?.saveAndValidate();

                  if (success != true) {
                    return;
                  }

                  final values = formKey.currentState!.value;

                  try {
                    await _controller.submitSingpassVerification(
                      values,
                    );

                    // if (context.mounted && context.canPop()) {
                    //   context.pop();
                    // } else {
                    //   // we are likely redirected via deeplink
                    //   if (context.mounted) {
                    //     context.pushReplacement(const ProfileRoute().location);
                    //   }
                    // }
                  } catch (error, stackTrace) {
                    Groveman.error(
                      'singpassVerifyProvider',
                      error: error,
                      stackTrace: stackTrace,
                    );

                    errorState.value = error is AppNetworkResponseException
                        ? '${error.message}\nIf the issue persists, contact us on WhatsApp below.'
                        : 'Something went wrong, please try again later. If the issue persists, contact us on WhatsApp below.';
                  }
                },
                child: const Text('Submit'),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
      ],
    );
  }
}
