import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:groveman/groveman.dart';
import 'package:url_launcher/url_launcher.dart';

// final browserOption = InAppBrowserClassOptions(
//   ios: IOSInAppBrowserOptions(
//     // toolbarBottomBackgroundColor: Colors.grey.shade800,
//     // toolbarBottomTintColor: Colors.grey.shade800,
//     toolbarBottomTranslucent: false,
//     toolbarTopTranslucent: false,
//   ),
//   android: AndroidInAppBrowserOptions(
//     shouldCloseOnBackButtonPressed: true,
//   ),
//   crossPlatform: InAppBrowserOptions(
//     toolbarTopBackgroundColor: Colors.grey.shade800,
//   ),
//   inAppWebViewGroupOptions: InAppWebViewGroupOptions(
//     crossPlatform: InAppWebViewOptions(
//       useShouldOverrideUrlLoading: true,
//     ),
//   ),
// );

final singpassBrowserSettings = InAppBrowserClassSettings(
  browserSettings: InAppBrowserSettings(
    hideUrlBar: true,
    hideCloseButton: true,
    hideTitleBar: true,
    hideToolbarTop: true,
    hideToolbarBottom: true,
    toolbarTopBackgroundColor: Colors.white,
    toolbarBottomBackgroundColor: Colors.white,
    presentationStyle: ModalPresentationStyle.POPOVER,
    // toolbarTopTranslucent: false,
    // toolbarBottomTranslucent: false,
  ),
  webViewSettings: InAppWebViewSettings(useShouldOverrideUrlLoading: true),
);

final singpassChromeSafariBrowserSettings = ChromeSafariBrowserSettings(
  secondaryToolbarColor: Colors.white,
  toolbarBackgroundColor: Colors.white,
  presentationStyle: ModalPresentationStyle.POPOVER,
);

// part 'singpass_browser.g.dart';

// @riverpod
// SingpassBrowser singpassBrowser(SingpassBrowserRef ref) =>
//     SingpassBrowser(this.ref);

// class SingpassBrowser extends InAppBrowser {
//   SingpassBrowser(
//     this.successCallback, {
//     this.failCallback,
//     this.exitCallback,
//   });
//   final Function successCallback;
//   final Function? failCallback;
//   final Function? exitCallback;

//   @override
//   Future<NavigationActionPolicy?>? shouldOverrideUrlLoading(
//     NavigationAction navigationAction,
//   ) async {
//     final uri = navigationAction.request.url;
//     if (uri == null) {
//       return NavigationActionPolicy.CANCEL;
//     }

//     final url = uri.toString();

//     /// NOTE: redirect to singpass native app
//     if (url.startsWith(Environment.singpassIntentUrl)) {
//       if (Platform.isAndroid) {
//         // intent:// doesn't work for Android
//         try {
//           final query = url.split('qrlogin')[1];

//           await launchUrl(
//             Uri.parse(
//               'https://app.singpass.gov.sg/qrlogin$query',
//             ),
//           );
//         } catch (e) {
//           Groveman.warning('launch uri android', error: e);
//         }
//       } else if (Platform.isIOS) {
//         if (await launchUrl(uri)) {
//           await launchUrl(uri);
//         }
//       }

//       return NavigationActionPolicy.CANCEL;
//     }

//     if (url.startsWith(Environment.singpassRedirectUrlIos) ||
//         url.startsWith(Environment.singpassRedirectUrlAndroid)) {
//       final _code = uri.queryParameters['code'];
//       final _state = uri.queryParameters['state'];

//       if (_code != null && _state != null && _code != 'undefined') {
//         // segue to SingpassFormView
//         successCallback(_code, _state);
//       } else {
//         failCallback?.call();
//       }

//       await close();
//     }

//     return NavigationActionPolicy.ALLOW;
//   }

//   // @override
//   // Future<void> onLoadStop(WebUri? url) async {
//   //   super.onLoadStop(url);

//   //   if (url == null) return;

//   //   var gomamaIntentUri = Environment.singpassRedirectUrlIos;
//   //   if (Platform.isAndroid) {
//   //     gomamaIntentUri = Environment.singpassRedirectUrlAndroid;
//   //   }

//   //   if (url.toString().startsWith(gomamaIntentUri)) {
//   //     final _code = url.queryParameters['code'];
//   //     final _state = url.queryParameters['state'];

//   //     if (_code != null && _state != null && _code != 'undefined') {
//   //       // segue to SingpassFormView
//   //       successCallback(_code, _state);
//   //     } else {
//   //       failCallback?.call();
//   //     }

//   //     await close();
//   //   }
//   // }

//   @override
//   void onExit() {
//     super.onExit();
//     exitCallback?.call();
//   }
// }

class SingpassBrowser extends ChromeSafariBrowser {
  SingpassBrowser({
    this.failCallback,
    this.exitCallback,
  });
  final Function? failCallback;
  final Function? exitCallback;

  @override
  void onPostMessage(String message) {
    Groveman.info('onPostMessage: $message');
  }

  @override
  void onOpened() {
    Groveman.info('ChromeSafari browser opened');
  }

  @override
  void onCompletedInitialLoad(bool? didLoadSuccessfully) {
    Groveman.info('ChromeSafari browser initial load completed');
  }

  @override
  void onClosed() {
    Groveman.info('ChromeSafari browser closed');
    exitCallback?.call();
  }
}
