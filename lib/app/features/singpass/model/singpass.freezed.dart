// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'singpass.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SingpassProcessResponse _$SingpassProcessResponseFromJson(
    Map<String, dynamic> json) {
  return _SingpassProcessResponse.fromJson(json);
}

/// @nodoc
mixin _$SingpassProcessResponse {
  String? get nric => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  DateTime? get birthday => throw _privateConstructorUsedError;
  String? get mobileNumber => throw _privateConstructorUsedError;
  List<String>? get childrenBirthRecords => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SingpassProcessResponseCopyWith<SingpassProcessResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SingpassProcessResponseCopyWith<$Res> {
  factory $SingpassProcessResponseCopyWith(SingpassProcessResponse value,
          $Res Function(SingpassProcessResponse) then) =
      _$SingpassProcessResponseCopyWithImpl<$Res, SingpassProcessResponse>;
  @useResult
  $Res call(
      {String? nric,
      String? name,
      String? gender,
      DateTime? birthday,
      String? mobileNumber,
      List<String>? childrenBirthRecords});
}

/// @nodoc
class _$SingpassProcessResponseCopyWithImpl<$Res,
        $Val extends SingpassProcessResponse>
    implements $SingpassProcessResponseCopyWith<$Res> {
  _$SingpassProcessResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nric = freezed,
    Object? name = freezed,
    Object? gender = freezed,
    Object? birthday = freezed,
    Object? mobileNumber = freezed,
    Object? childrenBirthRecords = freezed,
  }) {
    return _then(_value.copyWith(
      nric: freezed == nric
          ? _value.nric
          : nric // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      childrenBirthRecords: freezed == childrenBirthRecords
          ? _value.childrenBirthRecords
          : childrenBirthRecords // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SingpassProcessResponseImplCopyWith<$Res>
    implements $SingpassProcessResponseCopyWith<$Res> {
  factory _$$SingpassProcessResponseImplCopyWith(
          _$SingpassProcessResponseImpl value,
          $Res Function(_$SingpassProcessResponseImpl) then) =
      __$$SingpassProcessResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nric,
      String? name,
      String? gender,
      DateTime? birthday,
      String? mobileNumber,
      List<String>? childrenBirthRecords});
}

/// @nodoc
class __$$SingpassProcessResponseImplCopyWithImpl<$Res>
    extends _$SingpassProcessResponseCopyWithImpl<$Res,
        _$SingpassProcessResponseImpl>
    implements _$$SingpassProcessResponseImplCopyWith<$Res> {
  __$$SingpassProcessResponseImplCopyWithImpl(
      _$SingpassProcessResponseImpl _value,
      $Res Function(_$SingpassProcessResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nric = freezed,
    Object? name = freezed,
    Object? gender = freezed,
    Object? birthday = freezed,
    Object? mobileNumber = freezed,
    Object? childrenBirthRecords = freezed,
  }) {
    return _then(_$SingpassProcessResponseImpl(
      nric: freezed == nric
          ? _value.nric
          : nric // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      childrenBirthRecords: freezed == childrenBirthRecords
          ? _value._childrenBirthRecords
          : childrenBirthRecords // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$SingpassProcessResponseImpl implements _SingpassProcessResponse {
  _$SingpassProcessResponseImpl(
      {this.nric,
      this.name,
      this.gender,
      this.birthday,
      this.mobileNumber,
      final List<String>? childrenBirthRecords})
      : _childrenBirthRecords = childrenBirthRecords;

  factory _$SingpassProcessResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SingpassProcessResponseImplFromJson(json);

  @override
  final String? nric;
  @override
  final String? name;
  @override
  final String? gender;
  @override
  final DateTime? birthday;
  @override
  final String? mobileNumber;
  final List<String>? _childrenBirthRecords;
  @override
  List<String>? get childrenBirthRecords {
    final value = _childrenBirthRecords;
    if (value == null) return null;
    if (_childrenBirthRecords is EqualUnmodifiableListView)
      return _childrenBirthRecords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SingpassProcessResponse(nric: $nric, name: $name, gender: $gender, birthday: $birthday, mobileNumber: $mobileNumber, childrenBirthRecords: $childrenBirthRecords)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SingpassProcessResponseImpl &&
            (identical(other.nric, nric) || other.nric == nric) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            const DeepCollectionEquality()
                .equals(other._childrenBirthRecords, _childrenBirthRecords));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nric, name, gender, birthday,
      mobileNumber, const DeepCollectionEquality().hash(_childrenBirthRecords));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SingpassProcessResponseImplCopyWith<_$SingpassProcessResponseImpl>
      get copyWith => __$$SingpassProcessResponseImplCopyWithImpl<
          _$SingpassProcessResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SingpassProcessResponseImplToJson(
      this,
    );
  }
}

abstract class _SingpassProcessResponse implements SingpassProcessResponse {
  factory _SingpassProcessResponse(
          {final String? nric,
          final String? name,
          final String? gender,
          final DateTime? birthday,
          final String? mobileNumber,
          final List<String>? childrenBirthRecords}) =
      _$SingpassProcessResponseImpl;

  factory _SingpassProcessResponse.fromJson(Map<String, dynamic> json) =
      _$SingpassProcessResponseImpl.fromJson;

  @override
  String? get nric;
  @override
  String? get name;
  @override
  String? get gender;
  @override
  DateTime? get birthday;
  @override
  String? get mobileNumber;
  @override
  List<String>? get childrenBirthRecords;
  @override
  @JsonKey(ignore: true)
  _$$SingpassProcessResponseImplCopyWith<_$SingpassProcessResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SingpassProcessInput _$SingpassProcessInputFromJson(Map<String, dynamic> json) {
  return _SingpassProcessInput.fromJson(json);
}

/// @nodoc
mixin _$SingpassProcessInput {
  String get authorizationCode => throw _privateConstructorUsedError;
  String get authorizationState => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SingpassProcessInputCopyWith<SingpassProcessInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SingpassProcessInputCopyWith<$Res> {
  factory $SingpassProcessInputCopyWith(SingpassProcessInput value,
          $Res Function(SingpassProcessInput) then) =
      _$SingpassProcessInputCopyWithImpl<$Res, SingpassProcessInput>;
  @useResult
  $Res call({String authorizationCode, String authorizationState});
}

/// @nodoc
class _$SingpassProcessInputCopyWithImpl<$Res,
        $Val extends SingpassProcessInput>
    implements $SingpassProcessInputCopyWith<$Res> {
  _$SingpassProcessInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authorizationCode = null,
    Object? authorizationState = null,
  }) {
    return _then(_value.copyWith(
      authorizationCode: null == authorizationCode
          ? _value.authorizationCode
          : authorizationCode // ignore: cast_nullable_to_non_nullable
              as String,
      authorizationState: null == authorizationState
          ? _value.authorizationState
          : authorizationState // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SingpassProcessInputImplCopyWith<$Res>
    implements $SingpassProcessInputCopyWith<$Res> {
  factory _$$SingpassProcessInputImplCopyWith(_$SingpassProcessInputImpl value,
          $Res Function(_$SingpassProcessInputImpl) then) =
      __$$SingpassProcessInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String authorizationCode, String authorizationState});
}

/// @nodoc
class __$$SingpassProcessInputImplCopyWithImpl<$Res>
    extends _$SingpassProcessInputCopyWithImpl<$Res, _$SingpassProcessInputImpl>
    implements _$$SingpassProcessInputImplCopyWith<$Res> {
  __$$SingpassProcessInputImplCopyWithImpl(_$SingpassProcessInputImpl _value,
      $Res Function(_$SingpassProcessInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authorizationCode = null,
    Object? authorizationState = null,
  }) {
    return _then(_$SingpassProcessInputImpl(
      authorizationCode: null == authorizationCode
          ? _value.authorizationCode
          : authorizationCode // ignore: cast_nullable_to_non_nullable
              as String,
      authorizationState: null == authorizationState
          ? _value.authorizationState
          : authorizationState // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SingpassProcessInputImpl implements _SingpassProcessInput {
  _$SingpassProcessInputImpl(
      {required this.authorizationCode, required this.authorizationState});

  factory _$SingpassProcessInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$SingpassProcessInputImplFromJson(json);

  @override
  final String authorizationCode;
  @override
  final String authorizationState;

  @override
  String toString() {
    return 'SingpassProcessInput(authorizationCode: $authorizationCode, authorizationState: $authorizationState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SingpassProcessInputImpl &&
            (identical(other.authorizationCode, authorizationCode) ||
                other.authorizationCode == authorizationCode) &&
            (identical(other.authorizationState, authorizationState) ||
                other.authorizationState == authorizationState));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, authorizationCode, authorizationState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SingpassProcessInputImplCopyWith<_$SingpassProcessInputImpl>
      get copyWith =>
          __$$SingpassProcessInputImplCopyWithImpl<_$SingpassProcessInputImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SingpassProcessInputImplToJson(
      this,
    );
  }
}

abstract class _SingpassProcessInput implements SingpassProcessInput {
  factory _SingpassProcessInput(
      {required final String authorizationCode,
      required final String authorizationState}) = _$SingpassProcessInputImpl;

  factory _SingpassProcessInput.fromJson(Map<String, dynamic> json) =
      _$SingpassProcessInputImpl.fromJson;

  @override
  String get authorizationCode;
  @override
  String get authorizationState;
  @override
  @JsonKey(ignore: true)
  _$$SingpassProcessInputImplCopyWith<_$SingpassProcessInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
