// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'singpass.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SingpassProcessResponseImpl _$$SingpassProcessResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SingpassProcessResponseImpl(
      nric: json['nric'] as String?,
      name: json['name'] as String?,
      gender: json['gender'] as String?,
      birthday:
          const CustomDateTimeConverter().fromJson(json['birthday'] as String?),
      mobileNumber: json['mobile_number'] as String?,
      childrenBirthRecords: (json['children_birth_records'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$SingpassProcessResponseImplToJson(
        _$SingpassProcessResponseImpl instance) =>
    <String, dynamic>{
      'nric': instance.nric,
      'name': instance.name,
      'gender': instance.gender,
      'birthday': const CustomDateTimeConverter().toJson(instance.birthday),
      'mobile_number': instance.mobileNumber,
      'children_birth_records': instance.childrenBirthRecords,
    };

_$SingpassProcessInputImpl _$$SingpassProcessInputImplFromJson(
        Map<String, dynamic> json) =>
    _$SingpassProcessInputImpl(
      authorizationCode: json['authorization_code'] as String,
      authorizationState: json['authorization_state'] as String,
    );

Map<String, dynamic> _$$SingpassProcessInputImplToJson(
        _$SingpassProcessInputImpl instance) =>
    <String, dynamic>{
      'authorization_code': instance.authorizationCode,
      'authorization_state': instance.authorizationState,
    };
