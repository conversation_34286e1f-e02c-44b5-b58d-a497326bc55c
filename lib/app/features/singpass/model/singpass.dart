import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'singpass.freezed.dart';
part 'singpass.g.dart';

@freezed
class SingpassProcessResponse with _$SingpassProcessResponse {
  @CustomDateTimeConverter()
  factory SingpassProcessResponse({
    String? nric,
    String? name,
    String? gender,
    DateTime? birthday,
    String? mobileNumber,
    List<String>? childrenBirthRecords,
  }) = _SingpassProcessResponse;

  factory SingpassProcessResponse.fromJson(Json json) =>
      _$SingpassProcessResponseFromJson(json);
}

@freezed
class SingpassProcessInput with _$SingpassProcessInput {
  factory SingpassProcessInput({
    required String authorizationCode,
    required String authorizationState,
  }) = _SingpassProcessInput;

  factory SingpassProcessInput.fromJson(Json json) =>
      _$SingpassProcessInputFromJson(json);
}
