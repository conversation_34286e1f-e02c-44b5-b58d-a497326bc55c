// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_listing_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activeListingHash() => r'e902a6c6ccd1b07aa0847c8f79638d551c788086';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ActiveListing
    extends BuildlessAutoDisposeNotifier<ListingStatus?> {
  late final String listingId;

  ListingStatus? build(
    String listingId,
  );
}

/// NOTE: backend notifies this listing's status in realtime via SSE
///
/// Copied from [ActiveListing].
@ProviderFor(ActiveListing)
const activeListingProvider = ActiveListingFamily();

/// NOTE: backend notifies this listing's status in realtime via SSE
///
/// Copied from [ActiveListing].
class ActiveListingFamily extends Family<ListingStatus?> {
  /// NOTE: backend notifies this listing's status in realtime via SSE
  ///
  /// Copied from [ActiveListing].
  const ActiveListingFamily();

  /// NOTE: backend notifies this listing's status in realtime via SSE
  ///
  /// Copied from [ActiveListing].
  ActiveListingProvider call(
    String listingId,
  ) {
    return ActiveListingProvider(
      listingId,
    );
  }

  @override
  ActiveListingProvider getProviderOverride(
    covariant ActiveListingProvider provider,
  ) {
    return call(
      provider.listingId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'activeListingProvider';
}

/// NOTE: backend notifies this listing's status in realtime via SSE
///
/// Copied from [ActiveListing].
class ActiveListingProvider
    extends AutoDisposeNotifierProviderImpl<ActiveListing, ListingStatus?> {
  /// NOTE: backend notifies this listing's status in realtime via SSE
  ///
  /// Copied from [ActiveListing].
  ActiveListingProvider(
    String listingId,
  ) : this._internal(
          () => ActiveListing()..listingId = listingId,
          from: activeListingProvider,
          name: r'activeListingProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$activeListingHash,
          dependencies: ActiveListingFamily._dependencies,
          allTransitiveDependencies:
              ActiveListingFamily._allTransitiveDependencies,
          listingId: listingId,
        );

  ActiveListingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.listingId,
  }) : super.internal();

  final String listingId;

  @override
  ListingStatus? runNotifierBuild(
    covariant ActiveListing notifier,
  ) {
    return notifier.build(
      listingId,
    );
  }

  @override
  Override overrideWith(ActiveListing Function() create) {
    return ProviderOverride(
      origin: this,
      override: ActiveListingProvider._internal(
        () => create()..listingId = listingId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        listingId: listingId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<ActiveListing, ListingStatus?>
      createElement() {
    return _ActiveListingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ActiveListingProvider && other.listingId == listingId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, listingId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ActiveListingRef on AutoDisposeNotifierProviderRef<ListingStatus?> {
  /// The parameter `listingId` of this provider.
  String get listingId;
}

class _ActiveListingProviderElement
    extends AutoDisposeNotifierProviderElement<ActiveListing, ListingStatus?>
    with ActiveListingRef {
  _ActiveListingProviderElement(super.provider);

  @override
  String get listingId => (origin as ActiveListingProvider).listingId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
