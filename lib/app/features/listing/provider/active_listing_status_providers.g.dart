// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_listing_status_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activeListingsStatusHash() =>
    r'51586b8d78efd67d06f55a265d9b90b3f1c3edff';

/// See also [ActiveListingsStatus].
@ProviderFor(ActiveListingsStatus)
final activeListingsStatusProvider =
    NotifierProvider<ActiveListingsStatus, ActiveListingStatuses>.internal(
  ActiveListingsStatus.new,
  name: r'activeListingsStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activeListingsStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ActiveListingsStatus = Notifier<ActiveListingStatuses>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
