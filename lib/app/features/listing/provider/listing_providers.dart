import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/update_favourite_listings_input.dart';
import 'package:gomama/app/features/listing/model/upload_listing_images_input.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_providers.g.dart';

@riverpod
class SingleListing extends _$SingleListing {
  @override
  FutureOr<Listing> build(String id) async {
    final position = await ref.watch(currentPositionProvider.future);

    return ref
        .watch(listingRepositoryProvider)
        .fetchListing(id, position: position);
  }

  void invalidate(String listingId) {
    ref.read(listingRepositoryProvider).clearCache(listingId);
    ref.invalidateSelf();
  }
}

@riverpod
Future<ListingsResponse> allListingsPages(
  AllListingsPagesRef ref,
  AllListingsPagination meta,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  await Future<void>.delayed(const Duration(milliseconds: 250));
  if (cancelToken.isCancelled) throw Exception();

  final amenities = ref.watch(amenityFiltersProvider);
  Groveman.info('amenities selected', error: amenities);

  return ref.watch(listingRepositoryProvider).fetchListings(
        input: meta.input,
        offset: meta.page,
        cancelToken: cancelToken,
        amenities: amenities,
      );
}

@riverpod
AsyncValue<int> allListingsCount(
  AllListingsCountRef ref,
  AllListingsInput? input,
) {
  final meta = AllListingsPagination(page: 0, input: input);

  return ref
      .watch(allListingsPagesProvider(meta))
      .whenData((value) => value.meta.total);
}

@riverpod
AsyncValue<Listing> listingAtIndex(
  ListingAtIndexRef ref,
  AllListingsOffset query,
) {
  final offsetInPage = query.offset % kPageLimit;

  final meta = AllListingsPagination(
    page: query.offset ~/ kPageLimit,
    input: query.input,
  );

  return ref.watch(allListingsPagesProvider(meta)).whenData(
        (value) => value.data[offsetInPage],
      );
}

@riverpod
Future<bool> uploadListingImage(
  UploadListingImageRef ref,
  String listingId,
  UploadListingImageInput input,
) async {
  final response = await ref
      .watch(listingRepositoryProvider)
      .uploadListingImage(listingId, input);
  return response.success;
}
