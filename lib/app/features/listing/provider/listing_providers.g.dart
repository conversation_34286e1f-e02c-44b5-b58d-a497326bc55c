// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allListingsPagesHash() => r'5df2fbf949fd3c5fef05ed8e39d8f423c0d06c28';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [allListingsPages].
@ProviderFor(allListingsPages)
const allListingsPagesProvider = AllListingsPagesFamily();

/// See also [allListingsPages].
class AllListingsPagesFamily extends Family<AsyncValue<ListingsResponse>> {
  /// See also [allListingsPages].
  const AllListingsPagesFamily();

  /// See also [allListingsPages].
  AllListingsPagesProvider call(
    AllListingsPagination meta,
  ) {
    return AllListingsPagesProvider(
      meta,
    );
  }

  @override
  AllListingsPagesProvider getProviderOverride(
    covariant AllListingsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'allListingsPagesProvider';
}

/// See also [allListingsPages].
class AllListingsPagesProvider
    extends AutoDisposeFutureProvider<ListingsResponse> {
  /// See also [allListingsPages].
  AllListingsPagesProvider(
    AllListingsPagination meta,
  ) : this._internal(
          (ref) => allListingsPages(
            ref as AllListingsPagesRef,
            meta,
          ),
          from: allListingsPagesProvider,
          name: r'allListingsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$allListingsPagesHash,
          dependencies: AllListingsPagesFamily._dependencies,
          allTransitiveDependencies:
              AllListingsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  AllListingsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final AllListingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingsResponse> Function(AllListingsPagesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AllListingsPagesProvider._internal(
        (ref) => create(ref as AllListingsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingsResponse> createElement() {
    return _AllListingsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AllListingsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AllListingsPagesRef on AutoDisposeFutureProviderRef<ListingsResponse> {
  /// The parameter `meta` of this provider.
  AllListingsPagination get meta;
}

class _AllListingsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingsResponse>
    with AllListingsPagesRef {
  _AllListingsPagesProviderElement(super.provider);

  @override
  AllListingsPagination get meta => (origin as AllListingsPagesProvider).meta;
}

String _$allListingsCountHash() => r'155c942263eeea6de003ecaac3993a0d1ebcfce0';

/// See also [allListingsCount].
@ProviderFor(allListingsCount)
const allListingsCountProvider = AllListingsCountFamily();

/// See also [allListingsCount].
class AllListingsCountFamily extends Family<AsyncValue<int>> {
  /// See also [allListingsCount].
  const AllListingsCountFamily();

  /// See also [allListingsCount].
  AllListingsCountProvider call(
    AllListingsInput? input,
  ) {
    return AllListingsCountProvider(
      input,
    );
  }

  @override
  AllListingsCountProvider getProviderOverride(
    covariant AllListingsCountProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'allListingsCountProvider';
}

/// See also [allListingsCount].
class AllListingsCountProvider extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [allListingsCount].
  AllListingsCountProvider(
    AllListingsInput? input,
  ) : this._internal(
          (ref) => allListingsCount(
            ref as AllListingsCountRef,
            input,
          ),
          from: allListingsCountProvider,
          name: r'allListingsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$allListingsCountHash,
          dependencies: AllListingsCountFamily._dependencies,
          allTransitiveDependencies:
              AllListingsCountFamily._allTransitiveDependencies,
          input: input,
        );

  AllListingsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final AllListingsInput? input;

  @override
  Override overrideWith(
    AsyncValue<int> Function(AllListingsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AllListingsCountProvider._internal(
        (ref) => create(ref as AllListingsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _AllListingsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AllListingsCountProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AllListingsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `input` of this provider.
  AllListingsInput? get input;
}

class _AllListingsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with AllListingsCountRef {
  _AllListingsCountProviderElement(super.provider);

  @override
  AllListingsInput? get input => (origin as AllListingsCountProvider).input;
}

String _$listingAtIndexHash() => r'f43e319cb04d83c52a4fbfad8448cb6fb8b07d0e';

/// See also [listingAtIndex].
@ProviderFor(listingAtIndex)
const listingAtIndexProvider = ListingAtIndexFamily();

/// See also [listingAtIndex].
class ListingAtIndexFamily extends Family<AsyncValue<Listing>> {
  /// See also [listingAtIndex].
  const ListingAtIndexFamily();

  /// See also [listingAtIndex].
  ListingAtIndexProvider call(
    AllListingsOffset query,
  ) {
    return ListingAtIndexProvider(
      query,
    );
  }

  @override
  ListingAtIndexProvider getProviderOverride(
    covariant ListingAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingAtIndexProvider';
}

/// See also [listingAtIndex].
class ListingAtIndexProvider extends AutoDisposeProvider<AsyncValue<Listing>> {
  /// See also [listingAtIndex].
  ListingAtIndexProvider(
    AllListingsOffset query,
  ) : this._internal(
          (ref) => listingAtIndex(
            ref as ListingAtIndexRef,
            query,
          ),
          from: listingAtIndexProvider,
          name: r'listingAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingAtIndexHash,
          dependencies: ListingAtIndexFamily._dependencies,
          allTransitiveDependencies:
              ListingAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  ListingAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final AllListingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<Listing> Function(ListingAtIndexRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingAtIndexProvider._internal(
        (ref) => create(ref as ListingAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<Listing>> createElement() {
    return _ListingAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingAtIndexRef on AutoDisposeProviderRef<AsyncValue<Listing>> {
  /// The parameter `query` of this provider.
  AllListingsOffset get query;
}

class _ListingAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<Listing>>
    with ListingAtIndexRef {
  _ListingAtIndexProviderElement(super.provider);

  @override
  AllListingsOffset get query => (origin as ListingAtIndexProvider).query;
}

String _$uploadListingImageHash() =>
    r'c1e767f809d07b7e8661c1e329a8fa3fff73cfeb';

/// See also [uploadListingImage].
@ProviderFor(uploadListingImage)
const uploadListingImageProvider = UploadListingImageFamily();

/// See also [uploadListingImage].
class UploadListingImageFamily extends Family<AsyncValue<bool>> {
  /// See also [uploadListingImage].
  const UploadListingImageFamily();

  /// See also [uploadListingImage].
  UploadListingImageProvider call(
    String listingId,
    UploadListingImageInput input,
  ) {
    return UploadListingImageProvider(
      listingId,
      input,
    );
  }

  @override
  UploadListingImageProvider getProviderOverride(
    covariant UploadListingImageProvider provider,
  ) {
    return call(
      provider.listingId,
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'uploadListingImageProvider';
}

/// See also [uploadListingImage].
class UploadListingImageProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [uploadListingImage].
  UploadListingImageProvider(
    String listingId,
    UploadListingImageInput input,
  ) : this._internal(
          (ref) => uploadListingImage(
            ref as UploadListingImageRef,
            listingId,
            input,
          ),
          from: uploadListingImageProvider,
          name: r'uploadListingImageProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$uploadListingImageHash,
          dependencies: UploadListingImageFamily._dependencies,
          allTransitiveDependencies:
              UploadListingImageFamily._allTransitiveDependencies,
          listingId: listingId,
          input: input,
        );

  UploadListingImageProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.listingId,
    required this.input,
  }) : super.internal();

  final String listingId;
  final UploadListingImageInput input;

  @override
  Override overrideWith(
    FutureOr<bool> Function(UploadListingImageRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UploadListingImageProvider._internal(
        (ref) => create(ref as UploadListingImageRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        listingId: listingId,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _UploadListingImageProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UploadListingImageProvider &&
        other.listingId == listingId &&
        other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, listingId.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UploadListingImageRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `listingId` of this provider.
  String get listingId;

  /// The parameter `input` of this provider.
  UploadListingImageInput get input;
}

class _UploadListingImageProviderElement
    extends AutoDisposeFutureProviderElement<bool> with UploadListingImageRef {
  _UploadListingImageProviderElement(super.provider);

  @override
  String get listingId => (origin as UploadListingImageProvider).listingId;
  @override
  UploadListingImageInput get input =>
      (origin as UploadListingImageProvider).input;
}

String _$singleListingHash() => r'c41892756a0c204a4e03fa7b18d4999aae0ddcde';

abstract class _$SingleListing
    extends BuildlessAutoDisposeAsyncNotifier<Listing> {
  late final String id;

  FutureOr<Listing> build(
    String id,
  );
}

/// See also [SingleListing].
@ProviderFor(SingleListing)
const singleListingProvider = SingleListingFamily();

/// See also [SingleListing].
class SingleListingFamily extends Family<AsyncValue<Listing>> {
  /// See also [SingleListing].
  const SingleListingFamily();

  /// See also [SingleListing].
  SingleListingProvider call(
    String id,
  ) {
    return SingleListingProvider(
      id,
    );
  }

  @override
  SingleListingProvider getProviderOverride(
    covariant SingleListingProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'singleListingProvider';
}

/// See also [SingleListing].
class SingleListingProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SingleListing, Listing> {
  /// See also [SingleListing].
  SingleListingProvider(
    String id,
  ) : this._internal(
          () => SingleListing()..id = id,
          from: singleListingProvider,
          name: r'singleListingProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$singleListingHash,
          dependencies: SingleListingFamily._dependencies,
          allTransitiveDependencies:
              SingleListingFamily._allTransitiveDependencies,
          id: id,
        );

  SingleListingProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  FutureOr<Listing> runNotifierBuild(
    covariant SingleListing notifier,
  ) {
    return notifier.build(
      id,
    );
  }

  @override
  Override overrideWith(SingleListing Function() create) {
    return ProviderOverride(
      origin: this,
      override: SingleListingProvider._internal(
        () => create()..id = id,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SingleListing, Listing>
      createElement() {
    return _SingleListingProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SingleListingProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleListingRef on AutoDisposeAsyncNotifierProviderRef<Listing> {
  /// The parameter `id` of this provider.
  String get id;
}

class _SingleListingProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SingleListing, Listing>
    with SingleListingRef {
  _SingleListingProviderElement(super.provider);

  @override
  String get id => (origin as SingleListingProvider).id;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
