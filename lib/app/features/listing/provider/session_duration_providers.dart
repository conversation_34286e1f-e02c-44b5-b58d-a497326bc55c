import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_duration_providers.g.dart';

@riverpod
class SessionDuration extends _$SessionDuration {
  @override
  int build() {
    return 30; // Default value
  }

  void increment() {
    if (state < 45) {
      state += 15;
    }
  }

  void decrement() {
    if (state > 30) {
      state -= 15;
    }
  }

  int currentValue(){
    return state;
  }
}
