// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_flags_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$submitListingFlagHash() => r'9ce3d2ee84e9c734be796ea97cc83341dd23a6f7';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [submitListingFlag].
@ProviderFor(submitListingFlag)
const submitListingFlagProvider = SubmitListingFlagFamily();

/// See also [submitListingFlag].
class SubmitListingFlagFamily extends Family<AsyncValue<bool>> {
  /// See also [submitListingFlag].
  const SubmitListingFlagFamily();

  /// See also [submitListingFlag].
  SubmitListingFlagProvider call(
    CreateListingFlagInput input,
  ) {
    return SubmitListingFlagProvider(
      input,
    );
  }

  @override
  SubmitListingFlagProvider getProviderOverride(
    covariant SubmitListingFlagProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'submitListingFlagProvider';
}

/// See also [submitListingFlag].
class SubmitListingFlagProvider extends AutoDisposeFutureProvider<bool> {
  /// See also [submitListingFlag].
  SubmitListingFlagProvider(
    CreateListingFlagInput input,
  ) : this._internal(
          (ref) => submitListingFlag(
            ref as SubmitListingFlagRef,
            input,
          ),
          from: submitListingFlagProvider,
          name: r'submitListingFlagProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$submitListingFlagHash,
          dependencies: SubmitListingFlagFamily._dependencies,
          allTransitiveDependencies:
              SubmitListingFlagFamily._allTransitiveDependencies,
          input: input,
        );

  SubmitListingFlagProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final CreateListingFlagInput input;

  @override
  Override overrideWith(
    FutureOr<bool> Function(SubmitListingFlagRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubmitListingFlagProvider._internal(
        (ref) => create(ref as SubmitListingFlagRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _SubmitListingFlagProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubmitListingFlagProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SubmitListingFlagRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `input` of this provider.
  CreateListingFlagInput get input;
}

class _SubmitListingFlagProviderElement
    extends AutoDisposeFutureProviderElement<bool> with SubmitListingFlagRef {
  _SubmitListingFlagProviderElement(super.provider);

  @override
  CreateListingFlagInput get input =>
      (origin as SubmitListingFlagProvider).input;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
