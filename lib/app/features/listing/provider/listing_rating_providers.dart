import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:gomama/app/features/listing/repository/listing_rating_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_rating_providers.g.dart';

@riverpod
Future<ListingRatingSummary> listingRatingSummary(
  ListingRatingSummaryRef ref,
  String listingId,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  await Future<void>.delayed(const Duration(milliseconds: 250));
  if (cancelToken.isCancelled) throw Exception();

  return ref.watch(listingRatingRepositoryProvider).fetchListingRatingSummary(
        listingId: listingId,
      );
}

@riverpod
Future<ListingRatingsResponse> listingRatingsPages(
  ListingRatingsPagesRef ref,
  ListingRatingsPagination meta,
) async {
  final cancelToken = CancelToken();
  ref.onDispose(cancelToken.cancel);

  await Future<void>.delayed(const Duration(milliseconds: 250));
  if (cancelToken.isCancelled) throw Exception();

  String? sort;
  if (meta.query.sort != null) {
    switch (meta.query.sort!.toLowerCase()) {
      case 'most recent':
        sort = 'created_at:desc';
        break;
      case 'lowest rated':
        sort = 'experience_rating:asc';
        break;
      case 'highest rated':
        sort = 'experience_rating:desc';
        break;
    }
  }

  return ref.watch(listingRatingRepositoryProvider).fetchListingRatings(
        listingId: meta.query.listingId,
        sort: sort,
        offset: meta.page,
        cancelToken: cancelToken,
      );
}

@riverpod
AsyncValue<int> listingRatingsCount(
  ListingRatingsCountRef ref,
  ListingRatingsQuery query,
) {
  final meta = ListingRatingsPagination(page: 0, query: query);

  return ref
      .watch(listingRatingsPagesProvider(meta))
      .whenData((value) => value.meta.total);
}

@riverpod
AsyncValue<ListingRating> listingRatingAtIndex(
  ListingRatingAtIndexRef ref,
  ListingRatingsOffset query,
) {
  final offsetInPage = query.offset % kPageLimit;

  final meta = ListingRatingsPagination(
    page: query.offset ~/ kPageLimit,
    query: query.query,
  );

  return ref.watch(listingRatingsPagesProvider(meta)).whenData(
        (value) => value.data[offsetInPage],
      );
}
