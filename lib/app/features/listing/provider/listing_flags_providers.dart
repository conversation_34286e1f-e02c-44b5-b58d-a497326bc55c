import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/listing_flags.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_flags_providers.g.dart';

@riverpod
FutureOr<bool> submitListingFlag(
  SubmitListingFlagRef ref,
  CreateListingFlagInput input,
) async {
  // TODO(kkcy): test this
  final response = await ref.read(listingRepositoryProvider).flagListing(input);

  return response.success;
}
