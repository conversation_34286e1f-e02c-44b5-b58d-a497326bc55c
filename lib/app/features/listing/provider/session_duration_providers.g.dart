// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_duration_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionDurationHash() => r'1b701f4171cab3193131800213c33c31d552fe5b';

/// See also [SessionDuration].
@ProviderFor(SessionDuration)
final sessionDurationProvider =
    AutoDisposeNotifierProvider<SessionDuration, int>.internal(
  SessionDuration.new,
  name: r'sessionDurationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionDurationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SessionDuration = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
