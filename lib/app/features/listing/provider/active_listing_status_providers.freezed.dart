// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_listing_status_providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ActiveListingStatus {
  String get id => throw _privateConstructorUsedError;
  ListingStatus get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ActiveListingStatusCopyWith<ActiveListingStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActiveListingStatusCopyWith<$Res> {
  factory $ActiveListingStatusCopyWith(
          ActiveListingStatus value, $Res Function(ActiveListingStatus) then) =
      _$ActiveListingStatusCopyWithImpl<$Res, ActiveListingStatus>;
  @useResult
  $Res call({String id, ListingStatus status});
}

/// @nodoc
class _$ActiveListingStatusCopyWithImpl<$Res, $Val extends ActiveListingStatus>
    implements $ActiveListingStatusCopyWith<$Res> {
  _$ActiveListingStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActiveListingStatusImplCopyWith<$Res>
    implements $ActiveListingStatusCopyWith<$Res> {
  factory _$$ActiveListingStatusImplCopyWith(_$ActiveListingStatusImpl value,
          $Res Function(_$ActiveListingStatusImpl) then) =
      __$$ActiveListingStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, ListingStatus status});
}

/// @nodoc
class __$$ActiveListingStatusImplCopyWithImpl<$Res>
    extends _$ActiveListingStatusCopyWithImpl<$Res, _$ActiveListingStatusImpl>
    implements _$$ActiveListingStatusImplCopyWith<$Res> {
  __$$ActiveListingStatusImplCopyWithImpl(_$ActiveListingStatusImpl _value,
      $Res Function(_$ActiveListingStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? status = null,
  }) {
    return _then(_$ActiveListingStatusImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus,
    ));
  }
}

/// @nodoc

class _$ActiveListingStatusImpl implements _ActiveListingStatus {
  const _$ActiveListingStatusImpl({required this.id, required this.status});

  @override
  final String id;
  @override
  final ListingStatus status;

  @override
  String toString() {
    return 'ActiveListingStatus(id: $id, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActiveListingStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActiveListingStatusImplCopyWith<_$ActiveListingStatusImpl> get copyWith =>
      __$$ActiveListingStatusImplCopyWithImpl<_$ActiveListingStatusImpl>(
          this, _$identity);
}

abstract class _ActiveListingStatus implements ActiveListingStatus {
  const factory _ActiveListingStatus(
      {required final String id,
      required final ListingStatus status}) = _$ActiveListingStatusImpl;

  @override
  String get id;
  @override
  ListingStatus get status;
  @override
  @JsonKey(ignore: true)
  _$$ActiveListingStatusImplCopyWith<_$ActiveListingStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ActiveListingStatuses {
  List<ActiveListingStatus> get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ActiveListingStatusesCopyWith<ActiveListingStatuses> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActiveListingStatusesCopyWith<$Res> {
  factory $ActiveListingStatusesCopyWith(ActiveListingStatuses value,
          $Res Function(ActiveListingStatuses) then) =
      _$ActiveListingStatusesCopyWithImpl<$Res, ActiveListingStatuses>;
  @useResult
  $Res call({List<ActiveListingStatus> items});
}

/// @nodoc
class _$ActiveListingStatusesCopyWithImpl<$Res,
        $Val extends ActiveListingStatuses>
    implements $ActiveListingStatusesCopyWith<$Res> {
  _$ActiveListingStatusesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ActiveListingStatus>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActiveListingStatusesImplCopyWith<$Res>
    implements $ActiveListingStatusesCopyWith<$Res> {
  factory _$$ActiveListingStatusesImplCopyWith(
          _$ActiveListingStatusesImpl value,
          $Res Function(_$ActiveListingStatusesImpl) then) =
      __$$ActiveListingStatusesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ActiveListingStatus> items});
}

/// @nodoc
class __$$ActiveListingStatusesImplCopyWithImpl<$Res>
    extends _$ActiveListingStatusesCopyWithImpl<$Res,
        _$ActiveListingStatusesImpl>
    implements _$$ActiveListingStatusesImplCopyWith<$Res> {
  __$$ActiveListingStatusesImplCopyWithImpl(_$ActiveListingStatusesImpl _value,
      $Res Function(_$ActiveListingStatusesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
  }) {
    return _then(_$ActiveListingStatusesImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ActiveListingStatus>,
    ));
  }
}

/// @nodoc

class _$ActiveListingStatusesImpl extends _ActiveListingStatuses {
  const _$ActiveListingStatusesImpl(
      {final List<ActiveListingStatus> items = const []})
      : _items = items,
        super._();

  final List<ActiveListingStatus> _items;
  @override
  @JsonKey()
  List<ActiveListingStatus> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'ActiveListingStatuses(items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActiveListingStatusesImpl &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActiveListingStatusesImplCopyWith<_$ActiveListingStatusesImpl>
      get copyWith => __$$ActiveListingStatusesImplCopyWithImpl<
          _$ActiveListingStatusesImpl>(this, _$identity);
}

abstract class _ActiveListingStatuses extends ActiveListingStatuses {
  const factory _ActiveListingStatuses(
      {final List<ActiveListingStatus> items}) = _$ActiveListingStatusesImpl;
  const _ActiveListingStatuses._() : super._();

  @override
  List<ActiveListingStatus> get items;
  @override
  @JsonKey(ignore: true)
  _$$ActiveListingStatusesImplCopyWith<_$ActiveListingStatusesImpl>
      get copyWith => throw _privateConstructorUsedError;
}
