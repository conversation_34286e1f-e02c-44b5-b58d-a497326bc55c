import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'active_listing_status_providers.freezed.dart';
part 'active_listing_status_providers.g.dart';

@freezed
class ActiveListingStatus with _$ActiveListingStatus {
  const factory ActiveListingStatus({
    required String id,
    required ListingStatus status,
  }) = _ActiveListingStatus;
}

@freezed
class ActiveListingStatuses with _$ActiveListingStatuses {
  const factory ActiveListingStatuses({
    @Default([]) List<ActiveListingStatus> items,
  }) = _ActiveListingStatuses;

  const ActiveListingStatuses._();

  ActiveListingStatuses upsertStatus(String id, ListingStatus status) {
    return copyWith(
      items: [
        for (final item in items)
          if (item.id != id) item,
        ActiveListingStatus(id: id, status: status),
      ],
    );
  }

  ActiveListingStatus? findById(String id) {
    return items.cast<ActiveListingStatus?>().firstWhere(
          (item) => item?.id == id,
          orElse: () => null,
        );
  }
}

@Riverpod(keepAlive: true)
class ActiveListingsStatus extends _$ActiveListingsStatus {
  @override
  ActiveListingStatuses build() {
    return const ActiveListingStatuses();
  }

  void upsertStatus(String id, ListingStatus status) {
    state = state.upsertStatus(id, status);
  }
}
