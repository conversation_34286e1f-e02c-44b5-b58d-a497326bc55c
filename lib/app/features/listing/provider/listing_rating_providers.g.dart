// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_rating_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingRatingSummaryHash() =>
    r'615731a5bbd6f07a6a82074c6ee439abbeea7e2f';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [listingRatingSummary].
@ProviderFor(listingRatingSummary)
const listingRatingSummaryProvider = ListingRatingSummaryFamily();

/// See also [listingRatingSummary].
class ListingRatingSummaryFamily
    extends Family<AsyncValue<ListingRatingSummary>> {
  /// See also [listingRatingSummary].
  const ListingRatingSummaryFamily();

  /// See also [listingRatingSummary].
  ListingRatingSummaryProvider call(
    String listingId,
  ) {
    return ListingRatingSummaryProvider(
      listingId,
    );
  }

  @override
  ListingRatingSummaryProvider getProviderOverride(
    covariant ListingRatingSummaryProvider provider,
  ) {
    return call(
      provider.listingId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingRatingSummaryProvider';
}

/// See also [listingRatingSummary].
class ListingRatingSummaryProvider
    extends AutoDisposeFutureProvider<ListingRatingSummary> {
  /// See also [listingRatingSummary].
  ListingRatingSummaryProvider(
    String listingId,
  ) : this._internal(
          (ref) => listingRatingSummary(
            ref as ListingRatingSummaryRef,
            listingId,
          ),
          from: listingRatingSummaryProvider,
          name: r'listingRatingSummaryProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingRatingSummaryHash,
          dependencies: ListingRatingSummaryFamily._dependencies,
          allTransitiveDependencies:
              ListingRatingSummaryFamily._allTransitiveDependencies,
          listingId: listingId,
        );

  ListingRatingSummaryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.listingId,
  }) : super.internal();

  final String listingId;

  @override
  Override overrideWith(
    FutureOr<ListingRatingSummary> Function(ListingRatingSummaryRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingRatingSummaryProvider._internal(
        (ref) => create(ref as ListingRatingSummaryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        listingId: listingId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingRatingSummary> createElement() {
    return _ListingRatingSummaryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingRatingSummaryProvider &&
        other.listingId == listingId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, listingId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingRatingSummaryRef
    on AutoDisposeFutureProviderRef<ListingRatingSummary> {
  /// The parameter `listingId` of this provider.
  String get listingId;
}

class _ListingRatingSummaryProviderElement
    extends AutoDisposeFutureProviderElement<ListingRatingSummary>
    with ListingRatingSummaryRef {
  _ListingRatingSummaryProviderElement(super.provider);

  @override
  String get listingId => (origin as ListingRatingSummaryProvider).listingId;
}

String _$listingRatingsPagesHash() =>
    r'44b826fb2444aea41cc7c0f128af9d7b22b407d3';

/// See also [listingRatingsPages].
@ProviderFor(listingRatingsPages)
const listingRatingsPagesProvider = ListingRatingsPagesFamily();

/// See also [listingRatingsPages].
class ListingRatingsPagesFamily
    extends Family<AsyncValue<ListingRatingsResponse>> {
  /// See also [listingRatingsPages].
  const ListingRatingsPagesFamily();

  /// See also [listingRatingsPages].
  ListingRatingsPagesProvider call(
    ListingRatingsPagination meta,
  ) {
    return ListingRatingsPagesProvider(
      meta,
    );
  }

  @override
  ListingRatingsPagesProvider getProviderOverride(
    covariant ListingRatingsPagesProvider provider,
  ) {
    return call(
      provider.meta,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingRatingsPagesProvider';
}

/// See also [listingRatingsPages].
class ListingRatingsPagesProvider
    extends AutoDisposeFutureProvider<ListingRatingsResponse> {
  /// See also [listingRatingsPages].
  ListingRatingsPagesProvider(
    ListingRatingsPagination meta,
  ) : this._internal(
          (ref) => listingRatingsPages(
            ref as ListingRatingsPagesRef,
            meta,
          ),
          from: listingRatingsPagesProvider,
          name: r'listingRatingsPagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingRatingsPagesHash,
          dependencies: ListingRatingsPagesFamily._dependencies,
          allTransitiveDependencies:
              ListingRatingsPagesFamily._allTransitiveDependencies,
          meta: meta,
        );

  ListingRatingsPagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.meta,
  }) : super.internal();

  final ListingRatingsPagination meta;

  @override
  Override overrideWith(
    FutureOr<ListingRatingsResponse> Function(ListingRatingsPagesRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingRatingsPagesProvider._internal(
        (ref) => create(ref as ListingRatingsPagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        meta: meta,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ListingRatingsResponse> createElement() {
    return _ListingRatingsPagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingRatingsPagesProvider && other.meta == meta;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, meta.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingRatingsPagesRef
    on AutoDisposeFutureProviderRef<ListingRatingsResponse> {
  /// The parameter `meta` of this provider.
  ListingRatingsPagination get meta;
}

class _ListingRatingsPagesProviderElement
    extends AutoDisposeFutureProviderElement<ListingRatingsResponse>
    with ListingRatingsPagesRef {
  _ListingRatingsPagesProviderElement(super.provider);

  @override
  ListingRatingsPagination get meta =>
      (origin as ListingRatingsPagesProvider).meta;
}

String _$listingRatingsCountHash() =>
    r'00023393cebfd6dc124a7e12d3c619b7f5c1b00f';

/// See also [listingRatingsCount].
@ProviderFor(listingRatingsCount)
const listingRatingsCountProvider = ListingRatingsCountFamily();

/// See also [listingRatingsCount].
class ListingRatingsCountFamily extends Family<AsyncValue<int>> {
  /// See also [listingRatingsCount].
  const ListingRatingsCountFamily();

  /// See also [listingRatingsCount].
  ListingRatingsCountProvider call(
    ListingRatingsQuery query,
  ) {
    return ListingRatingsCountProvider(
      query,
    );
  }

  @override
  ListingRatingsCountProvider getProviderOverride(
    covariant ListingRatingsCountProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingRatingsCountProvider';
}

/// See also [listingRatingsCount].
class ListingRatingsCountProvider extends AutoDisposeProvider<AsyncValue<int>> {
  /// See also [listingRatingsCount].
  ListingRatingsCountProvider(
    ListingRatingsQuery query,
  ) : this._internal(
          (ref) => listingRatingsCount(
            ref as ListingRatingsCountRef,
            query,
          ),
          from: listingRatingsCountProvider,
          name: r'listingRatingsCountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingRatingsCountHash,
          dependencies: ListingRatingsCountFamily._dependencies,
          allTransitiveDependencies:
              ListingRatingsCountFamily._allTransitiveDependencies,
          query: query,
        );

  ListingRatingsCountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final ListingRatingsQuery query;

  @override
  Override overrideWith(
    AsyncValue<int> Function(ListingRatingsCountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingRatingsCountProvider._internal(
        (ref) => create(ref as ListingRatingsCountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<int>> createElement() {
    return _ListingRatingsCountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingRatingsCountProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingRatingsCountRef on AutoDisposeProviderRef<AsyncValue<int>> {
  /// The parameter `query` of this provider.
  ListingRatingsQuery get query;
}

class _ListingRatingsCountProviderElement
    extends AutoDisposeProviderElement<AsyncValue<int>>
    with ListingRatingsCountRef {
  _ListingRatingsCountProviderElement(super.provider);

  @override
  ListingRatingsQuery get query =>
      (origin as ListingRatingsCountProvider).query;
}

String _$listingRatingAtIndexHash() =>
    r'be2d9bf58a3abbb83488b5e38014dc4453d3c756';

/// See also [listingRatingAtIndex].
@ProviderFor(listingRatingAtIndex)
const listingRatingAtIndexProvider = ListingRatingAtIndexFamily();

/// See also [listingRatingAtIndex].
class ListingRatingAtIndexFamily extends Family<AsyncValue<ListingRating>> {
  /// See also [listingRatingAtIndex].
  const ListingRatingAtIndexFamily();

  /// See also [listingRatingAtIndex].
  ListingRatingAtIndexProvider call(
    ListingRatingsOffset query,
  ) {
    return ListingRatingAtIndexProvider(
      query,
    );
  }

  @override
  ListingRatingAtIndexProvider getProviderOverride(
    covariant ListingRatingAtIndexProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'listingRatingAtIndexProvider';
}

/// See also [listingRatingAtIndex].
class ListingRatingAtIndexProvider
    extends AutoDisposeProvider<AsyncValue<ListingRating>> {
  /// See also [listingRatingAtIndex].
  ListingRatingAtIndexProvider(
    ListingRatingsOffset query,
  ) : this._internal(
          (ref) => listingRatingAtIndex(
            ref as ListingRatingAtIndexRef,
            query,
          ),
          from: listingRatingAtIndexProvider,
          name: r'listingRatingAtIndexProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$listingRatingAtIndexHash,
          dependencies: ListingRatingAtIndexFamily._dependencies,
          allTransitiveDependencies:
              ListingRatingAtIndexFamily._allTransitiveDependencies,
          query: query,
        );

  ListingRatingAtIndexProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final ListingRatingsOffset query;

  @override
  Override overrideWith(
    AsyncValue<ListingRating> Function(ListingRatingAtIndexRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ListingRatingAtIndexProvider._internal(
        (ref) => create(ref as ListingRatingAtIndexRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<AsyncValue<ListingRating>> createElement() {
    return _ListingRatingAtIndexProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ListingRatingAtIndexProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ListingRatingAtIndexRef
    on AutoDisposeProviderRef<AsyncValue<ListingRating>> {
  /// The parameter `query` of this provider.
  ListingRatingsOffset get query;
}

class _ListingRatingAtIndexProviderElement
    extends AutoDisposeProviderElement<AsyncValue<ListingRating>>
    with ListingRatingAtIndexRef {
  _ListingRatingAtIndexProviderElement(super.provider);

  @override
  ListingRatingsOffset get query =>
      (origin as ListingRatingAtIndexProvider).query;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
