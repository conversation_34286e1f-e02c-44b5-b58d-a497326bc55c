import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/explore/model/nearby_listing_input.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listing_flags.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/update_favourite_listings_input.dart';
import 'package:gomama/app/features/listing/model/upload_listing_images_input.dart';
import 'package:gomama/app/features/suggest/model/listing_suggestion.dart';
import 'package:groveman/groveman.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_repository.g.dart';

@Riverpod(keepAlive: true)
ListingRepository listingRepository(ListingRepositoryRef ref) =>
    ListingRepository(ref);

class ListingRepository {
  ListingRepository(this.ref);

  final ListingRepositoryRef ref;
  final _listingCache = <String, Listing>{};
  final _nearbyListingCache = <String, ListingsResponse>{};
  final _searchListingCache = <String, ListingsResponse>{};
  final _favouriteListingCache = <String, ListingsResponse>{};

  void clearCache(String listingId){
    _listingCache.remove(listingId);
  }

  Future<String> fetchListingsGeojson({
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/geojson',
            cancelToken: cancelToken,
          );

      return jsonEncode(response.data);
    } catch (error) {
      Groveman.warning('fetchListingsGeojson', error: error);
      rethrow;
    }
  }

  Future<ListingsResponse> fetchListings({
    required int offset,
    int? limit = 10,
    // for filters
    AllListingsInput? input,
    String? nameStartsWith,
    CancelToken? cancelToken,
    List<String>? amenities,
  }) async {
    final cleanNameFilter = nameStartsWith?.trim();

    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings',
            queryParameters: {
              'page': offset + 1,
              'limit': limit,
              if (input != null) ...input.toJson(),
              if (cleanNameFilter != null && cleanNameFilter.isNotEmpty)
                'name': cleanNameFilter,
              if (amenities?.isNotEmpty ?? false) 'amenities': amenities,
            },
            cancelToken: cancelToken,
          );

      final result = ListingsResponse.fromJson(response.data!);

      // for (final listing in result.data) {
      //   _listingCache[listing.id] = listing;
      // }

      return result;
    } catch (error) {
      Groveman.warning('fetchListings', error: error);
      rethrow;
    }
  }

  Future<ListingsResponse> nearbyListings(
    NearbyListingsInput? input, {
    required int offset,
    int? limit = 10,
    CancelToken? cancelToken,
    List<String>? amenities,
    List<String>? types,
    String? sorting,
  }) async {
    try {
      final params = {
        'page': offset + 1,
        'limit': limit,
        if (input != null) ...input.toJson(),
        if (amenities?.isNotEmpty ?? false) 'amenities': amenities,
        if (types?.isNotEmpty ?? false) 'types': types,
        if (sorting?.isNotEmpty ?? false) 'sort': sorting,
      };
      final cacheKey = params.toString();

      Groveman.info('nearbyListings', error: params);

      if (_nearbyListingCache.containsKey(cacheKey)) {
        return _nearbyListingCache[cacheKey]!;
      }

      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/search-position',
            queryParameters: params,
          );

      final result = ListingsResponse.fromJson(response.data!);

      _nearbyListingCache[cacheKey] = result;

      // for (final listing in result.data) {
      //   _listingCache[listing.id] = listing;
      // }

      return result;
    } catch (error) {
      Groveman.warning('nearbyListings', error: error);
      rethrow;
    }
  }

  Future<ListingsResponse> searchListings(
    AllListingsInput? input, {
    required int offset,
    int? limit = 10,
    CancelToken? cancelToken,
    List<String>? amenities,
    String? sorting,
  }) async {
    try {
      final params = {
        'page': offset + 1,
        'limit': limit,
        if (input != null) ...input.toJson(),
        if (amenities?.isNotEmpty ?? false) 'amenities': amenities,
        if (sorting?.isNotEmpty ?? false) 'sort': sorting,
      };
      final cacheKey = params.toString();

      Groveman.info('searchListings', error: params);

      if (_searchListingCache.containsKey(cacheKey)) {
        return _searchListingCache[cacheKey]!;
      }

      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/search-keywords',
            queryParameters: params,
          );

      final result = ListingsResponse.fromJson(response.data!);

      _searchListingCache[cacheKey] = result;

      return result;
    } catch (error) {
      Groveman.warning('searchListings', error: error);
      rethrow;
    }
  }

  Future<ListingsResponse> fetchFavouriteListings(
    AllListingsInput? input, {
    required int offset,
    int? limit = 10,
    CancelToken? cancelToken,
    List<String>? amenities,
    String? sorting,
  }) async {
    try {
      final params = {
        'page': offset + 1,
        'limit': limit,
        if (input != null) ...input.toJson(),
        if (amenities?.isNotEmpty ?? false) 'amenities': amenities,
        if (sorting?.isNotEmpty ?? false) 'sort': sorting,
      };
      final cacheKey = params.toString();

      Groveman.info('fetchFavouriteListings', error: params);

      if (_favouriteListingCache.containsKey(cacheKey)) {
        return _favouriteListingCache[cacheKey]!;
      }

      final response = await ref.read(repositoryProvider).get<Json>(
            '/me/favorite-listings',
            queryParameters: params,
          );

      final result = ListingsResponse.fromJson(response.data!);

      _favouriteListingCache[cacheKey] = result;

      return result;
    } catch (error) {
      Groveman.warning('fetchFavouriteListings', error: error);
      rethrow;
    }
  }

  Future<PostResponse<void>> updateFavouriteListings(
    UpdateFavouriteListingsInput input,
  ) async {
    try {
      final response = await ref
          .watch(repositoryProvider)
          .put<Json>('/me/favorite-listings', data: input.toJson());

      // clear favorites cache
      _favouriteListingCache.clear();

      return PostResponse.fromJson(
        response.data!,
        (json) {},
      );
    } catch (error) {
      Groveman.warning('updateFavouriteListings', error: error);
      rethrow;
    }
  }

  Future<Listing> fetchListing(
    String id, {
    Position? position,
    CancelToken? cancelToken,
  }) async {
    // Don't fetch the Listing if it was already obtained previously,
    // in the detail page.
    if (_listingCache.containsKey(id)) {
      return _listingCache[id]!;
    }

    final queryString = position != null
        ? "?${Uri(
            queryParameters: {
              'lat': position.latitude.toString(),
              'lon': position.longitude.toString(),
            },
          ).query}"
        : '';

    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/$id$queryString',
            cancelToken: cancelToken,
          );

      final result = Listing.fromJson(response.data!['data'] as Json);

      _listingCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('fetchListing', error: error);
      rethrow;
    }
  }

  Future<PostResponse<Listing>> suggestListing(
    ListingSuggestionInput input,
  ) async {
    try {
      final formData = FormData.fromMap({
        ...input.toJson(),
      });

      if (input.mainImageFile != null) {
        formData.files.addAll(
          {
            'main_image_file': await MultipartFile.fromFile(
              input.mainImageFile!.path,
            ),
          }.entries.toList(),
        );
      }

      if (input.subImageFiles?.isNotEmpty ?? false) {
        final images = await Future.wait(
          input.subImageFiles!.map(
            (e) async => MapEntry(
              'sub_image_files[]',
              await MultipartFile.fromFile(
                e.path,
              ),
            ),
          ),
        );

        formData.files.addAll(images);
      }

      Groveman.info('suggestListing', error: formData.fields);

      final response = await ref
          .read(repositoryProvider)
          .multipartPost<Json>('/listings', data: formData);

      return PostResponse.fromJson(
        response.data!,
        (json) => Listing.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('suggestListing', error: error);
      rethrow;
    }
  }

  Future<ListingSuggestionsResponse> fetchSuggestions(
    AllListingsInput? input, {
    required int offset,
    int? limit = 10,
    // for filters
    String? nameStartsWith,
    CancelToken? cancelToken,
    List<String>? amenities,
    String? sorting,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/search-own-suggested',
            queryParameters: {
              'page': offset + 1,
              'limit': limit,
              if (input != null) ...input.toJson(),
              if (sorting?.isNotEmpty ?? false) 'sort': sorting,
            },
            cancelToken: cancelToken,
          );

      final result = ListingSuggestionsResponse.fromJson(response.data!);

      // for (final listing in result.data) {
      //   _listingCache[listing.id] = listing;
      // }

      return result;
    } catch (error) {
      Groveman.warning('fetchSuggestions', error: error);
      rethrow;
    }
  }

  Future<PostResponse<ListingFlag>> flagListing(
    CreateListingFlagInput input,
  ) async {
    Groveman.info('flagListing', error: input);

    try {
      final formData = FormData();

      // Add non-file fields
      formData.fields.add(MapEntry('category', input.category));
      formData.fields.add(MapEntry('reason', input.reason ?? ''));
      formData.fields.add(MapEntry('listing_id', input.listingId));

      // Add files
      if (input.referenceImageFiles?.isNotEmpty ?? false) {
        for (var i = 0; i < input.referenceImageFiles!.length; i++) {
          final file = input.referenceImageFiles![i];
          final filename = path.basename(file.path);
          formData.files.add(
            MapEntry(
              'reference_image_files[]',
              await MultipartFile.fromFile(
                file.path,
                filename: filename,
                contentType: MediaType(
                  'image',
                  path.extension(filename).toLowerCase().replaceAll('.', ''),
                ),
              ),
            ),
          );
        }
      }

      final response = await ref.read(repositoryProvider).multipartPost<Json>(
            '/listing-flags/${input.listingId}',
            data: formData,
          );

      return PostResponse.fromJson(
        response.data!,
        (json) => ListingFlag.fromJson(json! as Json),
      );
    } catch (error) {
      Groveman.warning('flagListing', error: error);
      rethrow;
    }
  }

  Future<PostResponse<void>> uploadListingImage(
    String listingId,
    UploadListingImageInput input,
  ) async {
    final formData = FormData.fromMap({
      ...input.toJson(),
    });

    if (input.subImageFile != null) {
      formData.files.addAll(
        {
          'sub_image_file': await MultipartFile.fromFile(
            input.subImageFile!.path,
          ),
        }.entries.toList(),
      );
    }

    try {
      final response = await ref.watch(repositoryProvider).multipartPut<Json>(
            '/listings/$listingId/images',
            data: formData,
          );

      return PostResponse.fromJson(
        response.data!,
        (json) {},
      );
    } catch (error) {
      Groveman.warning('uploadListingImage', error: error);
      rethrow;
    }
  }
}
