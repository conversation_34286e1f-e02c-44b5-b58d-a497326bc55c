// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_rating_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingRatingRepositoryHash() =>
    r'b7f118f24ccdba61141c187dcd3e5cc1fff21dd7';

/// See also [listingRatingRepository].
@ProviderFor(listingRatingRepository)
final listingRatingRepositoryProvider =
    Provider<ListingRatingRepository>.internal(
  listingRatingRepository,
  name: r'listingRatingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingRatingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListingRatingRepositoryRef = ProviderRef<ListingRatingRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
