// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'amenity_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$amenityRepositoryHash() => r'8049fee9749c48661bb09579007ac6b679137ea0';

/// See also [amenityRepository].
@ProviderFor(amenityRepository)
final amenityRepositoryProvider = Provider<AmenityRepository>.internal(
  amenityRepository,
  name: r'amenityRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$amenityRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AmenityRepositoryRef = ProviderRef<AmenityRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
