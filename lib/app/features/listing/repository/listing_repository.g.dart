// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listingRepositoryHash() => r'dda3cf71d0c4b08b079ebc9479bb7ce01873fd96';

/// See also [listingRepository].
@ProviderFor(listingRepository)
final listingRepositoryProvider = Provider<ListingRepository>.internal(
  listingRepository,
  name: r'listingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ListingRepositoryRef = ProviderRef<ListingRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
