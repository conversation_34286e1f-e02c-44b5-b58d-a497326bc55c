import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'amenity_repository.g.dart';

@Riverpod(keepAlive: true)
AmenityRepository amenityRepository(AmenityRepositoryRef ref) =>
    AmenityRepository(ref);

class AmenityRepository {
  AmenityRepository(this.ref);
  final AmenityRepositoryRef ref;
  final _amenityCache = <String, Amenity>{};

  Future<AmenitiesResponse> fetchAmenities() async {
    try {
      final response =
          await ref.read(repositoryProvider).get<Json>('/amenities');

      final result = AmenitiesResponse.fromJson(response.data!);

      for (final amenity in result.data) {
        _amenityCache[amenity.id] = amenity;
      }

      return AmenitiesResponse.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchAmenities', error: error);
      rethrow;
    }
  }
}
