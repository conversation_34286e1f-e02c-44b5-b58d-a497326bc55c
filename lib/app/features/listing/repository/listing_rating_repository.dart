import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_rating_repository.g.dart';

@Riverpod(keepAlive: true)
ListingRatingRepository listingRatingRepository(
  ListingRatingRepositoryRef ref,
) =>
    ListingRatingRepository(ref);

class ListingRatingRepository {
  ListingRatingRepository(this.ref);

  final ListingRatingRepositoryRef ref;
  final _listingRatingCache = <String, ListingRating>{};

  Future<ListingRatingSummary> fetchListingRatingSummary({
    required String listingId,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/$listingId/listing-rating-summary',
            cancelToken: cancelToken,
          );

      final result =
          ListingRatingSummary.fromJson(response.data?['data'] as Json);

      // for (final listingRating in result.data) {
      //   _listingRatingCache[listingRating.id] = listingRating;
      // }

      return result;
    } catch (error) {
      Groveman.warning('fetchListingRatings', error: error);
      rethrow;
    }
  }

  Future<ListingRatingsResponse> fetchListingRatings({
    required int offset,
    // for filters
    int? limit = 10,
    String? sort,
    required String listingId,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listings/$listingId/listing-ratings',
            queryParameters: {
              'page': offset + 1,
              'limit': limit,
              if (sort != null) 'sort': sort,
            },
            cancelToken: cancelToken,
          );

      final result = ListingRatingsResponse.fromJson(response.data!);

      // for (final listingRating in result.data) {
      //   _listingRatingCache[listingRating.id] = listingRating;
      // }

      return result;
    } catch (error) {
      Groveman.warning('fetchListingRatings', error: error);
      rethrow;
    }
  }

  Future<ListingRating> fetchListingRating(
    String id, {
    CancelToken? cancelToken,
  }) async {
    // TODO(kkcy): do we need to make sure fetchMany returns full detail
    // Don't fetch the ListingRating if it was already obtained previously, either
    // in the home page or in the detail page.
    if (_listingRatingCache.containsKey(id)) {
      return _listingRatingCache[id]!;
    }

    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/listingRatings/$id',
            cancelToken: cancelToken,
          );

      final result = ListingRating.fromJson(response.data!);

      _listingRatingCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('fetchListingRating', error: error);
      rethrow;
    }
  }

  Future<bool> submitRating(CreateListingRatingInput data) async {
    try {
      final response = await ref.watch(repositoryProvider).post<Json>(
            '/listing-ratings',
            data: data.toJson(),
          );

      return response.data!['success'] == true;
    } catch (error) {
      Groveman.warning('submitRating', error: error);
      rethrow;
    }
  }

  Future<bool> updateRating(UpdateListingRatingInput data) async {
    try {
      final response = await ref.watch(repositoryProvider).put<Json>(
            '/listing-ratings/${data.listingRatingId}',
            data: data.toJson(),
          );

      return response.data!['success'] == true;
    } catch (error) {
      Groveman.warning('updateRating', error: error);
      rethrow;
    }
  }
}
