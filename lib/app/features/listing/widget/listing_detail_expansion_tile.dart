import 'package:flutter/material.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/maps/widget/lite_map.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ListingDetail extends ConsumerWidget {
  const ListingDetail(this.listing, {super.key});
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Text('Listing Detail', style: textTheme(context).titleMedium),
          const SizedBox(height: 16),
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            child: AspectRatio(
              aspectRatio: 300 / 180,
              child: <PERSON><PERSON>Map(listing: listing),
            ),
          ),
          const SizedBox(height: 8),
          _RowText('Listing ID', listing.id),
          _RowText('Listing Name', listing.name ?? ''),
          _RowText('Listing Description', listing.description ?? ''),
          _RowText('Postal Code', listing.postalCode ?? ''),
          _RowText('Full Address', listing.fullAddress ?? ''),
          _RowText('Contact Number', listing.fullContactNumber ?? ''),
          // _RowText('Nearest Toilet', listing.id),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _RowText extends ConsumerWidget {
  const _RowText(this.title, this.value);
  final String title;
  final String value;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: Text(title)),
            Expanded(child: Text(value)),
          ],
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
