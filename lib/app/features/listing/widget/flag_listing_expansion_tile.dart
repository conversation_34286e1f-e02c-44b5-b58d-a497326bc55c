import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FlagListing extends ConsumerWidget {
  const FlagListing(this.listing, {super.key});
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          CustomIcon.flag,
          color: Colors.red,
        ),
        TextButton(
          onPressed: () {
            FlagListingRoute(listing).push(context);
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: const Text(
            'Flag this listing',
            style: TextStyle(
              decoration: TextDecoration.underline,
              decorationColor: Colors.red,
            ),
          ),
        ),
      ],
    );
  }
}
