import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/extensions.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/listing_media_provider.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/upload_listing_images_input.dart';
import 'package:gomama/app/features/listing/provider/active_listing_providers.dart';
import 'package:gomama/app/features/listing/provider/active_listing_status_providers.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/suggest/widget/add_listing_photo_dailog.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ListingGallery extends HookConsumerWidget {
  const ListingGallery(
    this.listing, {
    super.key,
    this.radius = const BorderRadius.all(Radius.circular(16)),
    this.isReview = false,
    this.isUnderGalleryView = false,
  });
  final Listing listing;
  final BorderRadius radius;
  final bool isReview;
  final bool isUnderGalleryView;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final imageIndex = useState(0);
    // final activeSession = ref.watch(activeSessionProvider);

    if (isUnderGalleryView) {
      var _isUploading = false;

      ref.listen(listingMediaStateControllerProvider, (previous, next) async {
        if (_isUploading || next.photos.isEmpty && (previous != next)) return;
        _isUploading = true;
        try {
          for (final photo in next.photos) {
            final input = UploadListingImageInput(
              subImageFile: photo,
            );
            final isSuccess = await ref
                .read(uploadListingImageProvider(listing.id, input).future);

            if (isSuccess) {
              ref
                  .read(singleListingProvider(listing.id).notifier)
                  .invalidate(listing.id);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Thanks for sharing the image! Your help makes it easier for other Mamas to have better reference.',
                    ),
                  ),
                );
              }
            }
          }
        } catch (e) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Something went wrong'),
              ),
            );
          }
        } finally {
          _isUploading = false;
          // Clear the photos after upload
          ref.read(listingMediaStateControllerProvider.notifier).setPhotos([]);
        }
      });
    }

    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: radius,
            child: PageView.builder(
              itemCount: isUnderGalleryView
                  ? (listing.listingFiles?.length ?? 0) + 1
                  : listing.listingFiles?.length ?? 0,
              onPageChanged: (index) {
                imageIndex.value = index;
              },
              itemBuilder: (context, pagePosition) {
                if (isUnderGalleryView) {
                  if (pagePosition < (listing.listingFiles?.length ?? 0)) {
                    // Display the images
                    return InkWell(
                      child: CachedNetworkImage(
                        imageUrl:
                            listing.listingFiles![pagePosition].imageUrl ?? '',
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(
                          child: SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 1,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) =>
                            const Icon(CustomIcon.error),
                      ),
                      onTap: () {
                        final imageUrls = listing.listingFiles!
                            .map((listingFile) => listingFile.imageUrl ?? '')
                            .toList();
                        ListingGalleryPhotoViewRoute(
                          imageUrls,
                        ).push(context);
                      },
                    );
                  } else {
                    return InkWell(
                      child: Center(
                        child: Text(
                          'Upload an image for this listing',
                          style: textTheme(context)
                              .titleSmall!
                              .copyWith(color: CustomColors.primary),
                        ),
                      ),
                      onTap: () {
                        showDialog<void>(
                          context: context,
                          builder: (BuildContext context) {
                            return const AddListingPhotoDialog();
                          },
                        );
                      },
                    );
                  }
                } else {
                  return CachedNetworkImage(
                    imageUrl:
                        listing.listingFiles![pagePosition].imageUrl ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 1,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) =>
                        const Icon(CustomIcon.error),
                  );
                }
              },
            ),
          ),
          Positioned.fill(
            child: IgnorePointer(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: radius,
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black38,
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (!isReview)
            Positioned(
              bottom: 16,
              left: 16,
              child: _ListingStatusPill(listing, isUnderGalleryView),
            ),
          Positioned(
            bottom: 16,
            right: 16,
            child: IgnorePointer(
              child: Theme(
                data: ThemeData(
                  canvasColor: Colors.transparent,
                ),
                child: Chip(
                  label: Text(
                    '${imageIndex.value + 1} / ${isUnderGalleryView ? (listing.listingFiles?.length ?? 1) + 1 : listing.listingFiles?.length}',
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                  side: BorderSide.none,
                  visualDensity: VisualDensity.compact,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                  backgroundColor: const Color(0xa6352F36),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ListingStatusPill extends ConsumerWidget {
  const _ListingStatusPill(
    this.listing,
    this.isUnderGalleryView,
  );
  final Listing listing;
  final bool isUnderGalleryView;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeListingsStatus = ref.watch(activeListingsStatusProvider);
    final realtimeListingStatus =
        isUnderGalleryView && listing.listingType == ListingType.gomama
            ? ref.watch(activeListingProvider(listing.firestoreId!))
            : null;
    final listingStatus = realtimeListingStatus ??
        activeListingsStatus.findById(listing.firestoreId ?? '')?.status ??
        listing.status;

    return IgnorePointer(
      child: listing.listingType == ListingType.gomama
          ? Row(
              children: [
                Chip(
                  label: Text(
                    listingStatus == ListingStatus.idle
                        ? 'Available'
                        : listingStatus?.name.capitalize() ?? '',
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                  side: BorderSide.none,
                  visualDensity: VisualDensity.compact,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  padding: EdgeInsets.zero,
                  backgroundColor: listingStatus == ListingStatus.idle
                      ? Colors.green
                      : Colors.red,
                ),
              ],
            )
          : null,
    );
  }
}
