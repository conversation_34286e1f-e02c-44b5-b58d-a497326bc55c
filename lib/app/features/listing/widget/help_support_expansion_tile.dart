import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HelpSupport extends ConsumerWidget {
  const HelpSupport(this.time, {super.key});
  final String time;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Text('Help and Support', style: textTheme(context).titleMedium),
          const SizedBox(height: 16),
          Column(
            children: [
              const Text('Support available:'),
              Text(time),
              const SizedBox(height: 16),
              BrandButton.whatsapp(
                onPressed: () {
                  ref.read(
                    sendWhatsAppMessageProvider(
                      'Hi GO!MAMA, I need some help on ',
                      Environment.gomamaWhatsappPhone,
                    ),
                  );
                },
                child: const Text('WhatsApp Us'),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
