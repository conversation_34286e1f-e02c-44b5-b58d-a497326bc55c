import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SortSheet extends ConsumerWidget {
  const SortSheet(
    this.sorts, {
    required this.onSelected,
    super.key,
  });
  final List<String> sorts;
  final void Function(String value) onSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            const SizedBox(width: double.infinity, height: kToolbarHeight),
            Text(
              'Sort',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Positioned(
              top: 4,
              right: 8,
              child: CloseButton(),
            ),
          ],
        ),
        ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            return ListTile(
              title: Text(sorts[index]),
              dense: true,
              onTap: () {
                onSelected(sorts[index]);
              },
            );
          },
          itemCount: sorts.length,
        ),
        const SizedBox(height: 32),
      ],
    );
  }
}
