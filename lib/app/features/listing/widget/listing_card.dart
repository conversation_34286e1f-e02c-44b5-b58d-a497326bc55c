import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/widget/listing_gallery.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ListingCard extends ConsumerWidget {
  const ListingCard(this.listing, {super.key});
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          ListingRoute(listing.id, $extra: listing).push(context);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListingGallery(listing),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                listing.name!,
                                style: textTheme(context)
                                    .titleSmall!
                                    .copyWith(color: CustomColors.primary),
                              ),
                            ),
                            if (listing.distance != null)
                              Text(
                                '${listing.distance!.toStringAsFixed(0)}m',
                                style: textTheme(context)
                                    .labelMedium!
                                    .copyWith(color: CustomColors.primary),
                              ),
                          ],
                        ),
                        if (listing.companyName != null) ...[
                          const SizedBox(height: 4),
                          Text('by ${listing.companyName!}'),
                        ],
                        if (listing.averageExperienceRatings != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                listing.averageExperienceRatings!
                                    .toStringAsFixed(1),
                              ),
                              const SizedBox(width: 4),
                              const Icon(CustomIcon.star, size: 16),
                            ],
                          ),
                        ],
                        if (listing.fullAddress != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(width: 2),
                              const Padding(
                                // to accomodate if full address is 2 lines
                                padding: EdgeInsets.only(top: 3),
                                child: Icon(
                                  CustomIcon.location,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(listing.fullAddress ?? ''),
                              ),
                            ],
                          ),
                        ],
                        if (listing.openingHours != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(width: 2),
                              const Padding(
                                padding: EdgeInsets.only(top: 3),
                                child: Icon(
                                  CustomIcon.time,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(listing.openingHours ?? ''),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }
}
