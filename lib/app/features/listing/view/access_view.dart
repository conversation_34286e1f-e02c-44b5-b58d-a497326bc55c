import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/listing/widget/sort_sheet.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/widgets/rounded_clipper.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'access_view.g.dart';

@riverpod
int _accessListingIndex(_AccessListingIndexRef ref) {
  throw UnimplementedError();
}

class AccessView extends HookConsumerWidget {
  const AccessView({super.key});

  static const routeName = 'access';
  static const routePath = '/access';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(amenityFiltersProvider);
    final position = ref.watch(currentPositionProvider).requireValue!;

    final listingCount = ref.watch(
      allListingsCountProvider(
        AllListingsInput(lon: position.longitude, lat: position.latitude),
      ),
    );

    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [CustomColors.secondary, CustomColors.primaries.shade100],
          begin: Alignment.topLeft,
          end: Alignment.topRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomColors.secondary,
                  CustomColors.primaries.shade100,
                ],
                begin: Alignment.topLeft,
                end: Alignment.topRight,
              ),
            ),
          ),
          iconTheme: const IconThemeData(
            color: CustomColors.primaries,
          ),
          foregroundColor: CustomColors.primaries,
          title: const Text(
            'Access',
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        body: ClipPath(
          clipper: RoundedClipper(),
          child: Container(
            color: Colors.white,
            height: double.infinity,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.white,
                          builder: (context) {
                            return SortSheet(
                              const [],
                              onSelected: (String value) {},
                            );
                          },
                        );
                      },
                      icon: const Icon(CustomIcon.sort),
                      label: const Text('Sort'),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.white,
                          builder: (context) {
                            return const FilterSheet();
                          },
                        );
                      },
                      icon: const Icon(CustomIcon.filter),
                      label: const Text('Filter'),
                    ),
                  ],
                ),
                Expanded(
                  child: listingCount.when(
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (err, stack) => Center(child: Text('Error $err')),
                    data: (listingCount) {
                      return ListView.separated(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          bottom: 16,
                        ),
                        itemBuilder: (context, index) {
                          return ProviderScope(
                            overrides: [
                              // ignore: scoped_providers_should_specify_dependencies
                              _accessListingIndexProvider
                                  .overrideWithValue(index),
                            ],
                            child: const _ListingCard(),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return const SizedBox(height: 16);
                        },
                        itemCount: listingCount,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ListingCard extends ConsumerWidget {
  const _ListingCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final position = ref.watch(currentPositionProvider).requireValue!;
    final index = ref.watch(_accessListingIndexProvider);
    final offset = AllListingsOffset(
      offset: index,
      input: AllListingsInput(lon: position.longitude, lat: position.latitude),
    );
    final listing = ref.watch(
      listingAtIndexProvider(offset),
    );

    return listing.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listing) {
        return Card(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          clipBehavior: Clip.antiAlias,
          child: InkWell(
            onTap: () {
              ListingRoute(
                listing.id,
                $extra: listing,
              ).push(context);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AspectRatio(
                  aspectRatio: 320 / 200,
                  child: CachedNetworkImage(
                    imageUrl: listing.previewImage ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 1,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) =>
                        const Icon(CustomIcon.error),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (listing.name != null)
                        Text(
                          listing.name!,
                          style: const TextStyle(color: CustomColors.primary),
                        ),
                      if (listing.fullAddress != null)
                        Text(listing.fullAddress!),
                      if (listing.openingHours != null)
                        Text(listing.openingHours!),
                      if (listing.averageExperienceRatings != null)
                        Text(
                          listing.averageExperienceRatings!.toStringAsFixed(2),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
