import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FlagListingDetailsView extends HookConsumerWidget {
  const FlagListingDetailsView({super.key});

  static const routeName = 'flag listing details';
  static const routePath = 'details';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSubmitted = useState(false);

    void _submitDetails(String details) {
      // Handle submit
      isSubmitted.value = true;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Flag this listing'),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 35, vertical: 20),
        child: Column(
          children: [
            if (isSubmitted.value)
              const _ReceivedMessage()
            else
              _ProvideDetailsForm(onSubmit: _submitDetails),
          ],
        ),
      ),
    );
  }
}

class _ProvideDetailsForm extends ConsumerWidget {
  const _ProvideDetailsForm({required this.onSubmit});

  final ValueSetter<String> onSubmit;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _textController = TextEditingController();

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Provide details about the inaccuracies',
            style: TextStyle(
              fontSize: 25,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _textController,
            maxLines: 10,
            minLines: 7,
            cursorColor: CustomColors.primary,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              hintText:
                  'Please add details to help us address the issue. Thank you!',
              hintStyle: TextStyle(color: Colors.grey.shade500),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(16),
                gapPadding: 0,
              ),
              fillColor: Colors.grey.shade200,
              filled: true,
            ),
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              style: TextButton.styleFrom(
                backgroundColor: CustomColors.primary,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                onSubmit(_textController.text);
              },
              child: const Text('Next'),
            ),
          ),
        ],
      ),
    );
  }
}

class _ReceivedMessage extends ConsumerWidget {
  const _ReceivedMessage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'We have received your feedback',
            style: TextStyle(
              fontSize: 25,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Thank you for helping us maintain the integrity of our listings. We will review the information provided.',
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              style: TextButton.styleFrom(
                backgroundColor: CustomColors.primary,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                Navigator.of(context)
                  ..pop()
                  ..pop();
              },
              child: const Text('Ok'),
            ),
          ),
        ],
      ),
    );
  }
}
