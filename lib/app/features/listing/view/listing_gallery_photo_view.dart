import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/info_content_management_providers.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ListingGalleryPhotoView extends HookConsumerWidget {
  const ListingGalleryPhotoView({
    required this.initialImageUrls,
    super.key,
  });

  final List<String> initialImageUrls;

  static const routeName = 'listing-gallery-photo-view';
  static const routePath = '/listing-gallery-photo-view';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final galleryFrames = initialImageUrls
        .map(
          (initialImageUrl) => PhotoViewGalleryPageOptions(
            imageProvider: CachedNetworkImageProvider(initialImageUrl),
          ),
        )
        .toList();

    return Stack(
      children: [
        PhotoViewGallery(
          pageOptions: galleryFrames,
          loadingBuilder: (context, progress) => const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: LoadingView(),
            ),
          ),
        ),
        Positioned(
          right: 4,
          child: SafeArea(
            child: CloseButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.white),
                iconColor: WidgetStateProperty.all(CustomColors.primary),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
