import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_rating_providers.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:gomama/app/widgets/custom_dropdown.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'listing_reviews_view.g.dart';

@riverpod
int _listingRatingIndex(_ListingRatingIndexRef ref) {
  throw UnimplementedError();
}

class ListingReviewsView extends HookConsumerWidget {
  const ListingReviewsView({
    required this.listingId,
    required this.initialListing,
    this.sorts = const ['Most recent', 'Highest rated', 'Lowest rated'],
    super.key,
  });

  static const routeName = 'listingReviews';
  static const routePath = 'reviews';

  final String listingId;
  final Listing initialListing;
  final List<String> sorts;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortSelected = useState<String?>('Most recent');
    final listingRatingSummaryAsync =
        ref.watch(listingRatingSummaryProvider(initialListing.id));
    final input = ListingRatingsQuery(
      listingId: initialListing.id,
      sort: sortSelected.value,
    );
    final listingRatingsCountAsync = ref.watch(
      listingRatingsCountProvider(input),
    );

    return BrandScaffold(
      title: const Text('Reviews'),
      physics: const NeverScrollableScrollPhysics(),
      child: listingRatingSummaryAsync.when(
        error: (error, stackTrace) {
          Groveman.error(
            'ListingReviewsView - Listing',
            error: error,
            stackTrace: stackTrace,
          );
          return const SizedBox.shrink();
        },
        loading: () {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
        data: (listingRatingSummary) {
          final filteredSorts = sorts
              .where((String value) => value != sortSelected.value)
              .toList();

          return BrandBottomSheet(
            minHeight: mediaQuery(context).size.height / 2,
            maxHeight: max(
                  mediaQuery(context).viewPadding.top,
                  kToolbarHeight / 2,
                ) +
                100,
            slivers: [
              const SliverPadding(padding: EdgeInsets.only(top: 12)),
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(20, 8, 20, 0),
                sliver: SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _RatingSummary(listingRatingSummary),
                      const SizedBox(height: 16),
                      const Divider(
                        color: CustomColors.divider,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${listingRatingSummary.totalExperienceRatings} reviews',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          CustomDropdown<String>(
                            onChange: (int index) {
                              sortSelected.value = filteredSorts[index];
                            },
                            dropdownButtonStyle: const DropdownButtonStyle(
                              backgroundColor: CustomColors.secondaryExtraLight,
                              primaryColor: CustomColors.primary,
                              padding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                            ),
                            dropdownStyle: DropdownStyle(
                              elevation: 1,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              shape: RoundedRectangleBorder(
                                side: const BorderSide(
                                  color: CustomColors.primary,
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              color: CustomColors.secondaryExtraLight,
                              textColor: CustomColors.primary,
                              // offset: Offset.zero,
                            ),
                            items: filteredSorts,
                            child: Text(
                              sortSelected.value ?? '',
                              style: const TextStyle(
                                color: CustomColors.primary,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      listingRatingsCountAsync.when(
                        error: (error, stackTrace) {
                          Groveman.error(
                            'ListingReviewsView - Listing Ratings',
                            error: error,
                            stackTrace: stackTrace,
                          );

                          return const SizedBox.shrink();
                        },
                        loading: () {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        },
                        data: (listingRatingCount) {
                          return ListView.separated(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            itemBuilder: (context, index) {
                              return ProviderScope(
                                overrides: [
                                  // ignore: scoped_providers_should_specify_dependencies
                                  _listingRatingIndexProvider
                                      .overrideWithValue(index),
                                ],
                                child: _ReviewCard(
                                  initialListing.id,
                                  sortSelected.value,
                                ),
                              );
                            },
                            separatorBuilder: (context, index) {
                              return const SizedBox(height: 24);
                            },
                            itemCount: listingRatingCount,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SliverPadding(padding: EdgeInsets.only(top: 24)),
            ],
          );
        },
      ),
    );
  }
}

class _RatingSummary extends ConsumerWidget {
  const _RatingSummary(this.listingRatingSummary);
  final ListingRatingSummary listingRatingSummary;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IntrinsicHeight(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  const Icon(
                    CustomIcon.star,
                    color: CustomColors.primary,
                    size: 40,
                  ),
                  Text(
                    listingRatingSummary.averageExperienceRatings
                            ?.toStringAsFixed(2) ??
                        '-',
                    style: const TextStyle(
                      color: CustomColors.primary,
                      fontSize: 40,
                    ),
                  ),
                ],
              ),
              Text(
                'Rated by ${listingRatingSummary.totalExperienceRatings} users',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '${listingRatingSummary.totalSessions} Sessions',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          const SizedBox(width: 10),
          const VerticalDivider(
            color: CustomColors.divider,
          ),
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _RatingRow(
                5,
                count: listingRatingSummary.fiveStarsCount ?? 0,
                total: listingRatingSummary.totalExperienceRatings ?? 0,
              ),
              _RatingRow(
                4,
                count: listingRatingSummary.fourStarsCount ?? 0,
                total: listingRatingSummary.totalExperienceRatings ?? 0,
              ),
              _RatingRow(
                3,
                count: listingRatingSummary.threeStarsCount ?? 0,
                total: listingRatingSummary.totalExperienceRatings ?? 0,
              ),
              _RatingRow(
                2,
                count: listingRatingSummary.twoStarsCount ?? 0,
                total: listingRatingSummary.totalExperienceRatings ?? 0,
              ),
              _RatingRow(
                1,
                count: listingRatingSummary.oneStarCount ?? 0,
                total: listingRatingSummary.totalExperienceRatings ?? 0,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _RatingRow extends ConsumerWidget {
  const _RatingRow(
    this.rating, {
    required this.count,
    required this.total,
  });
  final int rating;
  final int count;
  final int total;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Text(
          rating.toString(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: CustomColors.primary,
              ),
        ),
        const SizedBox(width: 8),
        Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.4,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(''),
            ),
            Container(
              width: MediaQuery.of(context).size.width * (count / total) * 0.4,
              height: 5,
              decoration: BoxDecoration(
                color: CustomColors.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(''),
            ),
          ],
        ),
      ],
    );
  }
}

class _ReviewCard extends ConsumerWidget {
  const _ReviewCard(this.listingId, this.sortSelected);
  final String listingId;
  final String? sortSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(_listingRatingIndexProvider);
    final offset = ListingRatingsOffset(
      offset: index,
      query: ListingRatingsQuery(
        listingId: listingId,
        sort: sortSelected,
      ),
    );
    final listingRatingAsync = ref.watch(
      listingRatingAtIndexProvider(offset),
    );

    return listingRatingAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listingRating) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '@${listingRating.username ?? 'Anonymous'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                if (listingRating.createdAt != null)
                  Text(
                    DateFormat.yMMMd().format(listingRating.createdAt!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: CustomColors.placeholder,
                        ),
                  ),
              ],
            ),
            const SizedBox(height: 5),
            Row(
              children: [
                for (int i = 0;
                    i < listingRating.experienceRating!.floor();
                    i++)
                  const Icon(
                    CustomIcon.star,
                    color: CustomColors.primary,
                    size: 15,
                  ),
                if (listingRating.experienceRating! % 1 != 0)
                  const Icon(
                    CustomIcon.starHalfAlt,
                    color: CustomColors.primary,
                    size: 15,
                  ),
                for (int i = 0;
                    i < 5 - listingRating.experienceRating!.ceil();
                    i++)
                  const Icon(
                    CustomIcon.starEmpty,
                    color: CustomColors.primary,
                    size: 15,
                  ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  listingRating.experienceRating?.toStringAsFixed(2) ?? '-',
                  style: const TextStyle(
                    color: CustomColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(listingRating.review ?? ''),
          ],
        );
      },
    );
  }
}
