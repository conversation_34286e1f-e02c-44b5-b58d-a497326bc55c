import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/explore/view/explore_view.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/session/provider/session_listing_rating_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:gomama/app/widgets/rating_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// NOTE: to submit listing review
class ReviewListingView extends HookConsumerWidget {
  const ReviewListingView(
    this.id, {
    this.listing,
    super.key,
  });
  final String id;
  final Listing? listing;

  static const routeName = 'review-listing';
  static const routePath = '/listings/:id/submit-reviews';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: _Body(listing!),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(this.listing);
  final Listing listing;

  double get maxExtent => 200 + kToolbarHeight;
  double get minExtent => kToolbarHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reviewSuccess = useState(false);

    return CustomScrollView(
      slivers: [
        SliverPersistentHeader(
          delegate: BrandAppBar(
            maxHeight:
                kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
            minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
            title: const Text('Rate your experience, Mama!'),
            child: Row(
              children: [
                SizedBox(
                  height: max(
                    mediaQuery(context).viewPadding.top,
                    kToolbarHeight * 10,
                  ),
                ),
              ],
            ),
          ),
          pinned: true,
        ),
        SliverList.list(
          children: reviewSuccess.value
              ? [
                  const _ReviewSubmitted(),
                ]
              : [
                  Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 0, 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 16),
                              Text(
                                listing.name ?? '',
                                style: textTheme(context).titleMedium!.copyWith(
                                      color: CustomColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                              const SizedBox(height: 6),
                              SizedBox(
                                width: mediaQuery(context).size.width * 0.6,
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Location: ',
                                          style: textTheme(context).bodySmall,
                                        ),
                                        Expanded(
                                          child: Text(
                                            listing.fullAddress ?? '',
                                            style: textTheme(context).bodySmall,
                                            overflow: TextOverflow.fade,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 6),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Listing ID: ',
                                          style: textTheme(context).bodySmall,
                                        ),
                                        Expanded(
                                          child: Text(
                                            listing.id,
                                            style: textTheme(context).bodySmall,
                                            overflow: TextOverflow.fade,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Image.asset(
                        'assets/images/goma_wink.png',
                        height: 160,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(),
                  ),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _ReviewForm(
                      listing.id,
                      onSuccess: () {
                        reviewSuccess.value = true;
                      },
                    ),
                  ),
                ],
        ),
      ],
    );
  }
}

class _ReviewForm extends HookConsumerWidget {
  const _ReviewForm(
    this.listingId, {
    required this.onSuccess,
  });
  final String listingId;
  final void Function() onSuccess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());

    return FormBuilder(
      key: _formKey.value,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your review',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mobile App',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Wayfinding, convenience'),
                  ],
                ),
              ),
              FormBuilderField(
                name: 'app_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Nursing Room',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Experience, cleanliness, etc'),
                  ],
                ),
              ),
              FormBuilderField(
                name: 'listing_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Experience',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Does this service improve your experience?'),
                  ],
                ),
              ),
              FormBuilderField(
                name: 'experience_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            'Any feedbacks or comments?',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          FormBuilderTextField(
            name: 'review',
            maxLines: 7,
            cursorColor: CustomColors.primary,
            decoration: InputDecoration(
              hintText:
                  'We’re here to improve your experience. Share your thoughts!',
              hintStyle: TextStyle(color: Colors.grey.shade500),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(16),
                gapPadding: 0,
              ),
              fillColor: CustomColors.secondaryLight,
              filled: true,
            ),
          ),
          const SizedBox(height: 16),
          BrandButton.cta(
            onPressed: () {
              // Validate and save the form values
              final success = _formKey.value.currentState?.saveAndValidate();

              if (success != true) {
                return;
              }

              final values =
                  Map<String, dynamic>.from(_formKey.value.currentState!.value)
                    ..addAll(
                      {'listing_id': listingId} as Json,
                    );

              ref.read(submitReviewProvider(values).future).then((value) {
                onSuccess();
                ref
                    .read(singleListingProvider(listingId).notifier)
                    .invalidate(listingId);
              }).onError((error, stackTrace) {
                Groveman.error(
                  'submitReviewProvider',
                  error: error,
                  stackTrace: stackTrace,
                );

                // TODO(kkcy): show error message as toast
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(error is AppNetworkResponseException
                          ? '${error.message}'
                          : 'Something went wrong, please try again later'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              });
            },
            child: const Text('Submit'),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _ReviewSubmitted extends ConsumerWidget {
  const _ReviewSubmitted();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          Text(
            'Thank you for your rating and feedback!',
            style: textTheme(context).headlineMedium,
          ),
          const SizedBox(height: 16),
          const Text(
            'Your feedback is valuable to us,\nhelping us to improve your experience.',
          ),
          const SizedBox(height: 32),
          Image.asset(
            'assets/images/goma_whiteboard.png',
            height: 240,
          ),
          const SizedBox(height: 32),
          BrandButton.cta(
            onPressed: () {
              if (context.canPop()) {
                context.pop();
              } else {
                const ExploreRoute().push(context);
              }
            },
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }
}
