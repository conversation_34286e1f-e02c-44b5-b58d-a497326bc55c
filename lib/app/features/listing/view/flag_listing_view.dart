import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/utils/media_provider.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/listing_flags.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_flags_providers.dart';
import 'package:gomama/app/features/suggest/widget/add_photo_dialog.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;

class FlagListingView extends HookConsumerWidget {
  const FlagListingView({
    required this.listing,
    super.key,
  });

  final Listing listing;

  static const routeName = 'flag-listing';
  static const routePath = '/flag';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormBuilderState>());
    final step = useState(0);
    final categorySelected = useState<ListingFlagCategory>(
      const ListingFlagCategory.pageMalfunction(),
    );
    final photos = ref.watch(
      mediaStateControllerProvider.select((value) => value.photos),
    );

    Future<void> onNext() async {
      if (categorySelected.value ==
              const ListingFlagCategory.pageMalfunction() ||
          categorySelected.value ==
              const ListingFlagCategory.duplicateListing()) {
        final createListingFlagInput = CreateListingFlagInput(
          listingId: listing.id,
          category: categorySelected.value.value,
        );

        try {
          await ref
              .read(submitListingFlagProvider(createListingFlagInput).future);
          step.value = 2;
        } catch (error) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error is AppNetworkResponseException
                    ? '${error.message}'
                    : 'Something went wrong, please try again later'),
                duration: const Duration(seconds: 3),
              ),
            );
            Navigator.pop(context);
          }
        }
      } else {
        step.value = 1;
      }
    }

    Future<void> onSubmit() async {
      // Validate and save the form values
      final success = formKey.value.currentState?.saveAndValidate();

      if (success != true) {
        return;
      }

      var createListingFlagInput = CreateListingFlagInput(
        listingId: listing.id,
        category: categorySelected.value.value,
        reason: formKey.value.currentState?.value['description'].toString(),
      );

      // Add the image files
      if (photos.isNotEmpty) {
        createListingFlagInput = createListingFlagInput.copyWith(
          referenceImageFiles: photos,
        );
      }

      try {
        await ref
            .read(submitListingFlagProvider(createListingFlagInput).future);
        step.value = 2;
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error is AppNetworkResponseException
                  ? '${error.message}'
                  : 'Something went wrong, please try again later'),
              duration: const Duration(seconds: 3),
            ),
          );
          Navigator.pop(context);
        }
      }
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            delegate: BrandAppBar(
              maxHeight:
                  kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
              minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
              title: _ListingNameBar(listing.name ?? ''),
              actions: [const SizedBox(width: 32)],
            ),
            pinned: true,
          ),
          _Body(
            formKey: formKey.value,
            categorySelected: categorySelected,
            step: step.value,
            onNext: onNext,
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(32, 0, 32, 8),
          child: Row(
            children: [
              if (step.value == 1) ...[
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => step.value = 0,
                    child: const Text('Back'),
                  ),
                ),
                const SizedBox(width: 16),
              ],
              Expanded(
                child: FilledButton(
                  onPressed: step.value == 0
                      ? onNext
                      : step.value == 1
                          ? onSubmit
                          : () => context.pop(),
                  child: Text(
                    step.value == 0
                        ? 'Next'
                        : step.value == 1
                            ? 'Submit'
                            : 'Okay',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ListingNameBar extends HookConsumerWidget {
  const _ListingNameBar(this.listingName);
  final String listingName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Material(
      elevation: 4,
      borderRadius: const BorderRadius.all(Radius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Row(
          children: [
            const Icon(
              CustomIcon.flag,
              color: CustomColors.red,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                listingName,
                style: textTheme(context)
                    .bodyMedium!
                    .copyWith(color: CustomColors.red),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body({
    required this.formKey,
    required this.step,
    required this.categorySelected,
    required this.onNext,
  });
  final GlobalKey<FormBuilderState> formKey;
  final int step;
  final ValueNotifier<ListingFlagCategory> categorySelected;
  final Future<void> Function() onNext;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      sliver: SliverToBoxAdapter(
        child: step == 0
            ? _SelectListingFlagCategory(categorySelected)
            : step == 1
                ? _ListingFlagDetails(formKey, categorySelected.value)
                : step == 2
                    ? _ReceivedFeedback(categorySelected.value)
                    : const SizedBox.shrink(),
      ),
    );
  }
}

// Step 0
class _SelectListingFlagCategory extends HookConsumerWidget {
  const _SelectListingFlagCategory(this.categorySelected);

  final ValueNotifier<ListingFlagCategory> categorySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Having issues?',
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: CustomColors.text,
                      fontFamily: 'AveriaSansLibre',
                    ),
              ),
              Text(
                'Let us know!',
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: CustomColors.text,
                      fontFamily: 'AveriaSansLibre',
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 30),
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: listingFlagCategories.length,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            return Column(
              children: [
                RadioListTile(
                  groupValue: categorySelected.value,
                  value: listingFlagCategories[index],
                  onChanged: (ListingFlagCategory? value) {
                    categorySelected.value = value!;
                  },
                  visualDensity: const VisualDensity(vertical: -4),
                  title: Text(listingFlagCategories[index].title),
                  controlAffinity: ListTileControlAffinity.trailing,
                ),
                if (index + 1 < listingFlagCategories.length) const Divider(),
              ],
            );
          },
        ),
        const SizedBox(height: 30),
      ],
    );
  }
}

// Step 1
class _ListingFlagDetails extends HookConsumerWidget {
  const _ListingFlagDetails(this.formKey, this.categorySelected);
  final ListingFlagCategory categorySelected;
  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FormBuilder(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Text(
                  categorySelected.instructions,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        color: CustomColors.text,
                        fontFamily: 'AveriaSansLibre',
                      ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: FormBuilderTextField(
              name: 'description',
              maxLines: 7,
              decoration: InputDecoration(
                hintText: categorySelected.description,
                hintStyle: const TextStyle(color: CustomColors.placeholder),
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(16),
                  gapPadding: 0,
                ),
                contentPadding: const EdgeInsets.all(12),
                fillColor: CustomColors.secondaryLight,
                filled: true,
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (categorySelected == const ListingFlagCategory.safetyHazard())
            const _PhotoInput(),
        ],
      ),
    );
  }
}

// Step 2
class _ReceivedFeedback extends HookConsumerWidget {
  const _ReceivedFeedback(this.categorySelected);
  final ListingFlagCategory categorySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Text(
                'We have received your feedback',
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: CustomColors.text,
                      fontFamily: 'AveriaSansLibre',
                    ),
              ),
              const SizedBox(height: 22),
              Text(
                categorySelected.feedback,
                style: Theme.of(context).textTheme.labelLarge,
              ),
            ],
          ),
        ),
        Image.asset(
          'assets/images/goma_whiteboard.png',
          height: 280,
        ),
        const SizedBox(height: 30),
      ],
    );
  }
}

class _PhotoInput extends ConsumerWidget {
  const _PhotoInput();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photos =
        ref.watch(mediaStateControllerProvider.select((value) => value.photos));
    final mediaStateController =
        ref.watch(mediaStateControllerProvider.notifier);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          SizedBox(
            height: 100,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, index) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                if (index < photos.length) {
                  return Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                        child: Center(
                          child: SizedBox(
                            width: 80,
                            height: 80,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                image: DecorationImage(
                                  image: FileImage(
                                    photos[index],
                                  ),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: DecoratedBox(
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: CustomColors.primary,
                          ),
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: InkWell(
                              onTap: () {
                                mediaStateController.removePhotoAt(index);
                              },
                              child: const Icon(
                                CustomIcon.close,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: SizedBox(
                        height: 80,
                        width: 80,
                        child: InkWell(
                          onTap: () {
                            showDialog<void>(
                              context: context,
                              builder: (BuildContext context) {
                                return const AddPhotoDialog();
                              },
                            );
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: CustomPaint(
                            painter: DottedBorderPainter(),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  CustomIcon.cameraAdd,
                                  size: 30,
                                ),
                                if (photos.isNotEmpty) ...[
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Text(
                                    '${photos.length}/3',
                                    style: textTheme(context)
                                        .bodySmall!
                                        .copyWith(color: CustomColors.primary),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }
              },
              itemCount: photos.length + 1,
            ),
          ),
          const SizedBox(height: 8),
          // FilledButton(
          //   onPressed: () {
          //     showDialog<void>(
          //       context: context,
          //       builder: (BuildContext context) {
          //         return const AddPhotoDialog();
          //       },
          //     );
          //   },
          //   child: const Row(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       Icon(Icons.addAPhoto),
          //       SizedBox(width: 8),
          //       Text('Add a photo'),
          //     ],
          //   ),
          // ),
          // CustomPaint(
          //   painter: DottedBorderPainter(),
          //   child: IconButton(
          //     icon: const Icon(
          //       Icons.addAPhoto,
          //       size: 30,
          //     ),
          //     padding: const EdgeInsets.all(25),
          //     onPressed: () {
          //       showDialog<void>(
          //         context: context,
          //         builder: (BuildContext context) {
          //           return const AddPhotoDialog();
          //         },
          //       );
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }
}

class DottedBorderPainter extends CustomPainter {
  DottedBorderPainter({this.radius = 8.0});

  final double radius;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = CustomColors.primary
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    const dotRadius = 0.8;
    const dotSpacing = 4.0;

    final path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, size.height),
          Radius.circular(radius),
        ),
      );

    final metrics = path.computeMetrics();
    for (final metric in metrics) {
      var distance = 0.0;
      while (distance < metric.length) {
        final offset = metric.getTangentForOffset(distance)!.position;
        canvas.drawCircle(offset, dotRadius, paint);
        distance += dotSpacing;
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
