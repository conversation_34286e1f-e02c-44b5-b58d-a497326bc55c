// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'access_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$accessListingIndexHash() =>
    r'a39bae196c102285237bedf43731b56ce4f2066f';

/// See also [_accessListingIndex].
@ProviderFor(_accessListingIndex)
final _accessListingIndexProvider = AutoDisposeProvider<int>.internal(
  _accessListingIndex,
  name: r'_accessListingIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$accessListingIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _AccessListingIndexRef = AutoDisposeProviderRef<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
