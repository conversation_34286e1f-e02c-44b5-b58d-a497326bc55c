import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ListingAmenitiesView extends HookConsumerWidget {
  const ListingAmenitiesView({
    required this.listingId,
    required this.initialListing,
    super.key,
  });

  static const routeName = 'listingAmenities';
  static const routePath = 'amenities';

  final String listingId;
  final Listing initialListing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final listingAsync = ref.watch(singleListingProvider(initialListing.id));

    return BrandScaffold(
      title: const Text('Amenities'),
      physics: const NeverScrollableScrollPhysics(),
      child: listingAsync.when(
        error: (error, stackTrace) {
          Groveman.error(
            'ListingAmenitiesView',
            error: error,
            stackTrace: stackTrace,
          );
          return const SizedBox.shrink();
        },
        loading: () {
          // while loading, we show what we have
          return _Body(initialListing);
        },
        data: (listing) {
          // on loaded, we show updated information
          return _Body(listing);
        },
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(this.listing);
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final amenitiesList = ref.watch(amenityListProvider);

    return amenitiesList.when(
      data: (response) {
        final amenities = response.data;

        return BrandBottomSheet(
          minHeight: mediaQuery(context).size.height / 2,
          maxHeight: max(
                mediaQuery(context).viewPadding.top,
                kToolbarHeight / 2,
              ) +
              100,
          slivers: [
            const SliverPadding(padding: EdgeInsets.only(top: 12)),
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(20, 8, 20, 0),
              sliver: SliverToBoxAdapter(
                child: Column(
                  children: [
                    GridView.count(
                      shrinkWrap: true,
                      childAspectRatio: 17 / 8,
                      crossAxisCount: 2,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      children: List.generate(
                        amenities.length,
                        (index) {
                          final isAvailable = listing.amenities?.any(
                                (amenity) => amenity.id == amenities[index].id,
                              ) ??
                              false;

                          return Card(
                            margin: const EdgeInsets.all(10),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            color: isAvailable
                                ? CustomColors.secondaryLight
                                : CustomColors.disableAmenities,
                            child: Row(
                              children: [
                                const SizedBox(width: 16),
                                Icon(
                                  amenityIconMap[amenities[index].fontIconName],
                                  color: isAvailable
                                      ? CustomColors.primary
                                      : CustomColors.disableTextAmenities,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    amenities[index].name,
                                    style: textTheme(context)
                                        .labelMedium!
                                        .copyWith(
                                          height: 1.2,
                                          color: isAvailable
                                              ? CustomColors.primary
                                              : CustomColors
                                                  .disableTextAmenities,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                              ],
                            ),
                          );

                          // return FilterChip(
                          //   avatar: CircleAvatar(
                          //     child: CachedNetworkImage(
                          //       imageUrl: amenities[index].imageUrl,
                          //       errorWidget: (context, url, error) =>
                          //           const Icon(Icons.error, size: 10),
                          //     ),
                          //   ),
                          //   label: Text(amenities[index].name),
                          //   shape: RoundedRectangleBorder(
                          //     borderRadius: BorderRadius.circular(16),
                          //     side: const BorderSide(
                          //       color: Colors.transparent,
                          //     ),
                          //   ),
                          //   visualDensity: VisualDensity.compact,
                          //   selected: filtering.value
                          //       .contains(amenities[index].id),
                          //   backgroundColor: Colors.grey.shade200,
                          //   selectedColor: CustomColors.primary,
                          //   labelStyle: Theme.of(context)
                          //       .textTheme
                          //       .labelLarge!
                          //       .copyWith(
                          //         color: filtering.value
                          //                 .contains(amenities[index].id)
                          //             ? Colors.white
                          //             : Colors.black,
                          //       ),
                          //   onSelected: (value) {
                          //     // ref
                          //     //     .read(amenityFiltersProvider.notifier)
                          //     //     .toggleAmenity(amenities[index].id);
                          //     if (filtering.value
                          //         .contains(amenities[index].id)) {
                          //       filtering.value
                          //           .remove(amenities[index].id);
                          //     } else {
                          //       filtering.value
                          //           .add(amenities[index].id);
                          //     }

                          //     filtering.value = [...filtering.value];
                          //   },
                          // );
                        },
                        growable: false,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SliverPadding(padding: EdgeInsets.only(top: 16)),
            const SliverPadding(padding: EdgeInsets.only(top: 8)),
          ],
        );
      },
      loading: () {
        return const CircularProgressIndicator();
      },
      error: (error, stackTrace) {
        Groveman.error('_AmenitiesStep', error: error, stackTrace: stackTrace);
        return const SizedBox.shrink();
      },
    );
  }

  Widget buildChildColumn(
    String imagePath,
    String childName,
    String birthDate,
  ) {
    return Expanded(
      child: Column(
        children: [
          ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 65,
            ),
            child: Image.asset(
              imagePath,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 5),
          Text(childName),
          Text(
            birthDate,
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
