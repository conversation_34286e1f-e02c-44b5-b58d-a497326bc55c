// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'amenities.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AmenityImpl _$$AmenityImplFromJson(Map<String, dynamic> json) =>
    _$AmenityImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String?,
      fontIconName: json['font_icon_name'] as String,
      isHidden: json['is_hidden'] as bool? ?? false,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$AmenityImplToJson(_$AmenityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'slug': instance.slug,
      'description': instance.description,
      'font_icon_name': instance.fontIconName,
      'is_hidden': instance.isHidden,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$AmenitiesResponseImpl _$$AmenitiesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AmenitiesResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => Amenity.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AmenitiesResponseImplToJson(
        _$AmenitiesResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
