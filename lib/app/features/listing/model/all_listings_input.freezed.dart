// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'all_listings_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AllListingsInput _$AllListingsInputFromJson(Map<String, dynamic> json) {
  return _AllListingsInput.fromJson(json);
}

/// @nodoc
mixin _$AllListingsInput {
  double get lat => throw _privateConstructorUsedError;
  double get lon => throw _privateConstructorUsedError;
  String? get keywords => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AllListingsInputCopyWith<AllListingsInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllListingsInputCopyWith<$Res> {
  factory $AllListingsInputCopyWith(
          AllListingsInput value, $Res Function(AllListingsInput) then) =
      _$AllListingsInputCopyWithImpl<$Res, AllListingsInput>;
  @useResult
  $Res call({double lat, double lon, String? keywords});
}

/// @nodoc
class _$AllListingsInputCopyWithImpl<$Res, $Val extends AllListingsInput>
    implements $AllListingsInputCopyWith<$Res> {
  _$AllListingsInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = null,
    Object? lon = null,
    Object? keywords = freezed,
  }) {
    return _then(_value.copyWith(
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
      keywords: freezed == keywords
          ? _value.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AllListingsInputImplCopyWith<$Res>
    implements $AllListingsInputCopyWith<$Res> {
  factory _$$AllListingsInputImplCopyWith(_$AllListingsInputImpl value,
          $Res Function(_$AllListingsInputImpl) then) =
      __$$AllListingsInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double lat, double lon, String? keywords});
}

/// @nodoc
class __$$AllListingsInputImplCopyWithImpl<$Res>
    extends _$AllListingsInputCopyWithImpl<$Res, _$AllListingsInputImpl>
    implements _$$AllListingsInputImplCopyWith<$Res> {
  __$$AllListingsInputImplCopyWithImpl(_$AllListingsInputImpl _value,
      $Res Function(_$AllListingsInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lat = null,
    Object? lon = null,
    Object? keywords = freezed,
  }) {
    return _then(_$AllListingsInputImpl(
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
      keywords: freezed == keywords
          ? _value.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AllListingsInputImpl implements _AllListingsInput {
  const _$AllListingsInputImpl(
      {required this.lat, required this.lon, this.keywords});

  factory _$AllListingsInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$AllListingsInputImplFromJson(json);

  @override
  final double lat;
  @override
  final double lon;
  @override
  final String? keywords;

  @override
  String toString() {
    return 'AllListingsInput(lat: $lat, lon: $lon, keywords: $keywords)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllListingsInputImpl &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lon, lon) || other.lon == lon) &&
            (identical(other.keywords, keywords) ||
                other.keywords == keywords));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lat, lon, keywords);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllListingsInputImplCopyWith<_$AllListingsInputImpl> get copyWith =>
      __$$AllListingsInputImplCopyWithImpl<_$AllListingsInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AllListingsInputImplToJson(
      this,
    );
  }
}

abstract class _AllListingsInput implements AllListingsInput {
  const factory _AllListingsInput(
      {required final double lat,
      required final double lon,
      final String? keywords}) = _$AllListingsInputImpl;

  factory _AllListingsInput.fromJson(Map<String, dynamic> json) =
      _$AllListingsInputImpl.fromJson;

  @override
  double get lat;
  @override
  double get lon;
  @override
  String? get keywords;
  @override
  @JsonKey(ignore: true)
  _$$AllListingsInputImplCopyWith<_$AllListingsInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
