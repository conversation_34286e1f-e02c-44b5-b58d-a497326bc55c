// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_listing_images_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UploadListingImageInput _$UploadListingImageInputFromJson(
    Map<String, dynamic> json) {
  return _UploadListingImageInput.fromJson(json);
}

/// @nodoc
mixin _$UploadListingImageInput {
  @JsonKey(includeFromJson: false)
  File? get subImageFile => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UploadListingImageInputCopyWith<UploadListingImageInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadListingImageInputCopyWith<$Res> {
  factory $UploadListingImageInputCopyWith(UploadListingImageInput value,
          $Res Function(UploadListingImageInput) then) =
      _$UploadListingImageInputCopyWithImpl<$Res, UploadListingImageInput>;
  @useResult
  $Res call({@JsonKey(includeFromJson: false) File? subImageFile});
}

/// @nodoc
class _$UploadListingImageInputCopyWithImpl<$Res,
        $Val extends UploadListingImageInput>
    implements $UploadListingImageInputCopyWith<$Res> {
  _$UploadListingImageInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subImageFile = freezed,
  }) {
    return _then(_value.copyWith(
      subImageFile: freezed == subImageFile
          ? _value.subImageFile
          : subImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadListingImageInputImplCopyWith<$Res>
    implements $UploadListingImageInputCopyWith<$Res> {
  factory _$$UploadListingImageInputImplCopyWith(
          _$UploadListingImageInputImpl value,
          $Res Function(_$UploadListingImageInputImpl) then) =
      __$$UploadListingImageInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(includeFromJson: false) File? subImageFile});
}

/// @nodoc
class __$$UploadListingImageInputImplCopyWithImpl<$Res>
    extends _$UploadListingImageInputCopyWithImpl<$Res,
        _$UploadListingImageInputImpl>
    implements _$$UploadListingImageInputImplCopyWith<$Res> {
  __$$UploadListingImageInputImplCopyWithImpl(
      _$UploadListingImageInputImpl _value,
      $Res Function(_$UploadListingImageInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subImageFile = freezed,
  }) {
    return _then(_$UploadListingImageInputImpl(
      subImageFile: freezed == subImageFile
          ? _value.subImageFile
          : subImageFile // ignore: cast_nullable_to_non_nullable
              as File?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UploadListingImageInputImpl implements _UploadListingImageInput {
  _$UploadListingImageInputImpl(
      {@JsonKey(includeFromJson: false) this.subImageFile});

  factory _$UploadListingImageInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$UploadListingImageInputImplFromJson(json);

  @override
  @JsonKey(includeFromJson: false)
  final File? subImageFile;

  @override
  String toString() {
    return 'UploadListingImageInput(subImageFile: $subImageFile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadListingImageInputImpl &&
            (identical(other.subImageFile, subImageFile) ||
                other.subImageFile == subImageFile));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subImageFile);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadListingImageInputImplCopyWith<_$UploadListingImageInputImpl>
      get copyWith => __$$UploadListingImageInputImplCopyWithImpl<
          _$UploadListingImageInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UploadListingImageInputImplToJson(
      this,
    );
  }
}

abstract class _UploadListingImageInput implements UploadListingImageInput {
  factory _UploadListingImageInput(
          {@JsonKey(includeFromJson: false) final File? subImageFile}) =
      _$UploadListingImageInputImpl;

  factory _UploadListingImageInput.fromJson(Map<String, dynamic> json) =
      _$UploadListingImageInputImpl.fromJson;

  @override
  @JsonKey(includeFromJson: false)
  File? get subImageFile;
  @override
  @JsonKey(ignore: true)
  _$$UploadListingImageInputImplCopyWith<_$UploadListingImageInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
