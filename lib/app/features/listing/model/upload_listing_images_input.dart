import 'dart:io';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'upload_listing_images_input.freezed.dart';
part 'upload_listing_images_input.g.dart';

@freezed
class UploadListingImageInput with _$UploadListingImageInput {
  factory UploadListingImageInput({
    @JsonKey(includeFromJson: false) File? subImageFile, // Assume all user submitted image can only be sub image, only admin can upload and decide if image is main
  }) = _UploadListingImageInput;

  factory UploadListingImageInput.fromJson(Json json) =>
      _$UploadListingImageInputFromJson(json);
}
