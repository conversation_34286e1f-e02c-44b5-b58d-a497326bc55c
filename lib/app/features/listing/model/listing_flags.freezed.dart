// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_flags.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ListingFlagCategory {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingFlagCategoryCopyWith<$Res> {
  factory $ListingFlagCategoryCopyWith(
          ListingFlagCategory value, $Res Function(ListingFlagCategory) then) =
      _$ListingFlagCategoryCopyWithImpl<$Res, ListingFlagCategory>;
}

/// @nodoc
class _$ListingFlagCategoryCopyWithImpl<$Res, $Val extends ListingFlagCategory>
    implements $ListingFlagCategoryCopyWith<$Res> {
  _$ListingFlagCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$PageMalfunctionImplCopyWith<$Res> {
  factory _$$PageMalfunctionImplCopyWith(_$PageMalfunctionImpl value,
          $Res Function(_$PageMalfunctionImpl) then) =
      __$$PageMalfunctionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PageMalfunctionImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$PageMalfunctionImpl>
    implements _$$PageMalfunctionImplCopyWith<$Res> {
  __$$PageMalfunctionImplCopyWithImpl(
      _$PageMalfunctionImpl _value, $Res Function(_$PageMalfunctionImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PageMalfunctionImpl extends PageMalfunction {
  const _$PageMalfunctionImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.pageMalfunction()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PageMalfunctionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return pageMalfunction();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return pageMalfunction?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (pageMalfunction != null) {
      return pageMalfunction();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return pageMalfunction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return pageMalfunction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (pageMalfunction != null) {
      return pageMalfunction(this);
    }
    return orElse();
  }
}

abstract class PageMalfunction extends ListingFlagCategory {
  const factory PageMalfunction() = _$PageMalfunctionImpl;
  const PageMalfunction._() : super._();
}

/// @nodoc
abstract class _$$DuplicateListingImplCopyWith<$Res> {
  factory _$$DuplicateListingImplCopyWith(_$DuplicateListingImpl value,
          $Res Function(_$DuplicateListingImpl) then) =
      __$$DuplicateListingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DuplicateListingImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$DuplicateListingImpl>
    implements _$$DuplicateListingImplCopyWith<$Res> {
  __$$DuplicateListingImplCopyWithImpl(_$DuplicateListingImpl _value,
      $Res Function(_$DuplicateListingImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$DuplicateListingImpl extends DuplicateListing {
  const _$DuplicateListingImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.duplicateListing()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DuplicateListingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return duplicateListing();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return duplicateListing?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (duplicateListing != null) {
      return duplicateListing();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return duplicateListing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return duplicateListing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (duplicateListing != null) {
      return duplicateListing(this);
    }
    return orElse();
  }
}

abstract class DuplicateListing extends ListingFlagCategory {
  const factory DuplicateListing() = _$DuplicateListingImpl;
  const DuplicateListing._() : super._();
}

/// @nodoc
abstract class _$$InaccurateInfoImplCopyWith<$Res> {
  factory _$$InaccurateInfoImplCopyWith(_$InaccurateInfoImpl value,
          $Res Function(_$InaccurateInfoImpl) then) =
      __$$InaccurateInfoImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InaccurateInfoImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$InaccurateInfoImpl>
    implements _$$InaccurateInfoImplCopyWith<$Res> {
  __$$InaccurateInfoImplCopyWithImpl(
      _$InaccurateInfoImpl _value, $Res Function(_$InaccurateInfoImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InaccurateInfoImpl extends InaccurateInfo {
  const _$InaccurateInfoImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.inaccurateInfo()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InaccurateInfoImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return inaccurateInfo();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return inaccurateInfo?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (inaccurateInfo != null) {
      return inaccurateInfo();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return inaccurateInfo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return inaccurateInfo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (inaccurateInfo != null) {
      return inaccurateInfo(this);
    }
    return orElse();
  }
}

abstract class InaccurateInfo extends ListingFlagCategory {
  const factory InaccurateInfo() = _$InaccurateInfoImpl;
  const InaccurateInfo._() : super._();
}

/// @nodoc
abstract class _$$PrivacyConcernImplCopyWith<$Res> {
  factory _$$PrivacyConcernImplCopyWith(_$PrivacyConcernImpl value,
          $Res Function(_$PrivacyConcernImpl) then) =
      __$$PrivacyConcernImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PrivacyConcernImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$PrivacyConcernImpl>
    implements _$$PrivacyConcernImplCopyWith<$Res> {
  __$$PrivacyConcernImplCopyWithImpl(
      _$PrivacyConcernImpl _value, $Res Function(_$PrivacyConcernImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PrivacyConcernImpl extends PrivacyConcern {
  const _$PrivacyConcernImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.privacyConcern()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PrivacyConcernImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return privacyConcern();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return privacyConcern?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (privacyConcern != null) {
      return privacyConcern();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return privacyConcern(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return privacyConcern?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (privacyConcern != null) {
      return privacyConcern(this);
    }
    return orElse();
  }
}

abstract class PrivacyConcern extends ListingFlagCategory {
  const factory PrivacyConcern() = _$PrivacyConcernImpl;
  const PrivacyConcern._() : super._();
}

/// @nodoc
abstract class _$$SafetyHazardImplCopyWith<$Res> {
  factory _$$SafetyHazardImplCopyWith(
          _$SafetyHazardImpl value, $Res Function(_$SafetyHazardImpl) then) =
      __$$SafetyHazardImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SafetyHazardImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$SafetyHazardImpl>
    implements _$$SafetyHazardImplCopyWith<$Res> {
  __$$SafetyHazardImplCopyWithImpl(
      _$SafetyHazardImpl _value, $Res Function(_$SafetyHazardImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SafetyHazardImpl extends SafetyHazard {
  const _$SafetyHazardImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.safetyHazard()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SafetyHazardImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return safetyHazard();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return safetyHazard?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (safetyHazard != null) {
      return safetyHazard();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return safetyHazard(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return safetyHazard?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (safetyHazard != null) {
      return safetyHazard(this);
    }
    return orElse();
  }
}

abstract class SafetyHazard extends ListingFlagCategory {
  const factory SafetyHazard() = _$SafetyHazardImpl;
  const SafetyHazard._() : super._();
}

/// @nodoc
abstract class _$$OtherImplCopyWith<$Res> {
  factory _$$OtherImplCopyWith(
          _$OtherImpl value, $Res Function(_$OtherImpl) then) =
      __$$OtherImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtherImplCopyWithImpl<$Res>
    extends _$ListingFlagCategoryCopyWithImpl<$Res, _$OtherImpl>
    implements _$$OtherImplCopyWith<$Res> {
  __$$OtherImplCopyWithImpl(
      _$OtherImpl _value, $Res Function(_$OtherImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$OtherImpl extends Other {
  const _$OtherImpl() : super._();

  @override
  String toString() {
    return 'ListingFlagCategory.other()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OtherImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() pageMalfunction,
    required TResult Function() duplicateListing,
    required TResult Function() inaccurateInfo,
    required TResult Function() privacyConcern,
    required TResult Function() safetyHazard,
    required TResult Function() other,
  }) {
    return other();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? pageMalfunction,
    TResult? Function()? duplicateListing,
    TResult? Function()? inaccurateInfo,
    TResult? Function()? privacyConcern,
    TResult? Function()? safetyHazard,
    TResult? Function()? other,
  }) {
    return other?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? pageMalfunction,
    TResult Function()? duplicateListing,
    TResult Function()? inaccurateInfo,
    TResult Function()? privacyConcern,
    TResult Function()? safetyHazard,
    TResult Function()? other,
    required TResult orElse(),
  }) {
    if (other != null) {
      return other();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PageMalfunction value) pageMalfunction,
    required TResult Function(DuplicateListing value) duplicateListing,
    required TResult Function(InaccurateInfo value) inaccurateInfo,
    required TResult Function(PrivacyConcern value) privacyConcern,
    required TResult Function(SafetyHazard value) safetyHazard,
    required TResult Function(Other value) other,
  }) {
    return other(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PageMalfunction value)? pageMalfunction,
    TResult? Function(DuplicateListing value)? duplicateListing,
    TResult? Function(InaccurateInfo value)? inaccurateInfo,
    TResult? Function(PrivacyConcern value)? privacyConcern,
    TResult? Function(SafetyHazard value)? safetyHazard,
    TResult? Function(Other value)? other,
  }) {
    return other?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PageMalfunction value)? pageMalfunction,
    TResult Function(DuplicateListing value)? duplicateListing,
    TResult Function(InaccurateInfo value)? inaccurateInfo,
    TResult Function(PrivacyConcern value)? privacyConcern,
    TResult Function(SafetyHazard value)? safetyHazard,
    TResult Function(Other value)? other,
    required TResult orElse(),
  }) {
    if (other != null) {
      return other(this);
    }
    return orElse();
  }
}

abstract class Other extends ListingFlagCategory {
  const factory Other() = _$OtherImpl;
  const Other._() : super._();
}

ListingFlag _$ListingFlagFromJson(Map<String, dynamic> json) {
  return _ListingFlag.fromJson(json);
}

/// @nodoc
mixin _$ListingFlag {
  String get id => throw _privateConstructorUsedError;
  @ListingFlagCategoryConverter()
  ListingFlagCategory? get category => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;
  List<String>? get referenceImages => throw _privateConstructorUsedError;
  DateTime? get reviewedAt => throw _privateConstructorUsedError;
  ListingFlagAction? get action => throw _privateConstructorUsedError;
  String? get actionReason => throw _privateConstructorUsedError;
  String? get actionBy => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingFlagCopyWith<ListingFlag> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingFlagCopyWith<$Res> {
  factory $ListingFlagCopyWith(
          ListingFlag value, $Res Function(ListingFlag) then) =
      _$ListingFlagCopyWithImpl<$Res, ListingFlag>;
  @useResult
  $Res call(
      {String id,
      @ListingFlagCategoryConverter() ListingFlagCategory? category,
      String? reason,
      List<String>? referenceImages,
      DateTime? reviewedAt,
      ListingFlagAction? action,
      String? actionReason,
      String? actionBy,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ListingFlagCategoryCopyWith<$Res>? get category;
}

/// @nodoc
class _$ListingFlagCopyWithImpl<$Res, $Val extends ListingFlag>
    implements $ListingFlagCopyWith<$Res> {
  _$ListingFlagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? category = freezed,
    Object? reason = freezed,
    Object? referenceImages = freezed,
    Object? reviewedAt = freezed,
    Object? action = freezed,
    Object? actionReason = freezed,
    Object? actionBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ListingFlagCategory?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceImages: freezed == referenceImages
          ? _value.referenceImages
          : referenceImages // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      reviewedAt: freezed == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as ListingFlagAction?,
      actionReason: freezed == actionReason
          ? _value.actionReason
          : actionReason // ignore: cast_nullable_to_non_nullable
              as String?,
      actionBy: freezed == actionBy
          ? _value.actionBy
          : actionBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingFlagCategoryCopyWith<$Res>? get category {
    if (_value.category == null) {
      return null;
    }

    return $ListingFlagCategoryCopyWith<$Res>(_value.category!, (value) {
      return _then(_value.copyWith(category: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingFlagImplCopyWith<$Res>
    implements $ListingFlagCopyWith<$Res> {
  factory _$$ListingFlagImplCopyWith(
          _$ListingFlagImpl value, $Res Function(_$ListingFlagImpl) then) =
      __$$ListingFlagImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @ListingFlagCategoryConverter() ListingFlagCategory? category,
      String? reason,
      List<String>? referenceImages,
      DateTime? reviewedAt,
      ListingFlagAction? action,
      String? actionReason,
      String? actionBy,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ListingFlagCategoryCopyWith<$Res>? get category;
}

/// @nodoc
class __$$ListingFlagImplCopyWithImpl<$Res>
    extends _$ListingFlagCopyWithImpl<$Res, _$ListingFlagImpl>
    implements _$$ListingFlagImplCopyWith<$Res> {
  __$$ListingFlagImplCopyWithImpl(
      _$ListingFlagImpl _value, $Res Function(_$ListingFlagImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? category = freezed,
    Object? reason = freezed,
    Object? referenceImages = freezed,
    Object? reviewedAt = freezed,
    Object? action = freezed,
    Object? actionReason = freezed,
    Object? actionBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ListingFlagImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ListingFlagCategory?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceImages: freezed == referenceImages
          ? _value._referenceImages
          : referenceImages // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      reviewedAt: freezed == reviewedAt
          ? _value.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as ListingFlagAction?,
      actionReason: freezed == actionReason
          ? _value.actionReason
          : actionReason // ignore: cast_nullable_to_non_nullable
              as String?,
      actionBy: freezed == actionBy
          ? _value.actionBy
          : actionBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ListingFlagImpl implements _ListingFlag {
  const _$ListingFlagImpl(
      {required this.id,
      @ListingFlagCategoryConverter() this.category,
      this.reason,
      final List<String>? referenceImages,
      this.reviewedAt,
      this.action,
      this.actionReason,
      this.actionBy,
      this.createdAt,
      this.updatedAt})
      : _referenceImages = referenceImages;

  factory _$ListingFlagImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingFlagImplFromJson(json);

  @override
  final String id;
  @override
  @ListingFlagCategoryConverter()
  final ListingFlagCategory? category;
  @override
  final String? reason;
  final List<String>? _referenceImages;
  @override
  List<String>? get referenceImages {
    final value = _referenceImages;
    if (value == null) return null;
    if (_referenceImages is EqualUnmodifiableListView) return _referenceImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? reviewedAt;
  @override
  final ListingFlagAction? action;
  @override
  final String? actionReason;
  @override
  final String? actionBy;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ListingFlag(id: $id, category: $category, reason: $reason, referenceImages: $referenceImages, reviewedAt: $reviewedAt, action: $action, actionReason: $actionReason, actionBy: $actionBy, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingFlagImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality()
                .equals(other._referenceImages, _referenceImages) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.actionReason, actionReason) ||
                other.actionReason == actionReason) &&
            (identical(other.actionBy, actionBy) ||
                other.actionBy == actionBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      category,
      reason,
      const DeepCollectionEquality().hash(_referenceImages),
      reviewedAt,
      action,
      actionReason,
      actionBy,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingFlagImplCopyWith<_$ListingFlagImpl> get copyWith =>
      __$$ListingFlagImplCopyWithImpl<_$ListingFlagImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingFlagImplToJson(
      this,
    );
  }
}

abstract class _ListingFlag implements ListingFlag {
  const factory _ListingFlag(
      {required final String id,
      @ListingFlagCategoryConverter() final ListingFlagCategory? category,
      final String? reason,
      final List<String>? referenceImages,
      final DateTime? reviewedAt,
      final ListingFlagAction? action,
      final String? actionReason,
      final String? actionBy,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ListingFlagImpl;

  factory _ListingFlag.fromJson(Map<String, dynamic> json) =
      _$ListingFlagImpl.fromJson;

  @override
  String get id;
  @override
  @ListingFlagCategoryConverter()
  ListingFlagCategory? get category;
  @override
  String? get reason;
  @override
  List<String>? get referenceImages;
  @override
  DateTime? get reviewedAt;
  @override
  ListingFlagAction? get action;
  @override
  String? get actionReason;
  @override
  String? get actionBy;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ListingFlagImplCopyWith<_$ListingFlagImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateListingFlagInput _$CreateListingFlagInputFromJson(
    Map<String, dynamic> json) {
  return _CreateListingFlagInput.fromJson(json);
}

/// @nodoc
mixin _$CreateListingFlagInput {
  String get listingId => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;
  @JsonKey(includeFromJson: false)
  List<File>? get referenceImageFiles => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateListingFlagInputCopyWith<CreateListingFlagInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateListingFlagInputCopyWith<$Res> {
  factory $CreateListingFlagInputCopyWith(CreateListingFlagInput value,
          $Res Function(CreateListingFlagInput) then) =
      _$CreateListingFlagInputCopyWithImpl<$Res, CreateListingFlagInput>;
  @useResult
  $Res call(
      {String listingId,
      String category,
      String? reason,
      @JsonKey(includeFromJson: false) List<File>? referenceImageFiles});
}

/// @nodoc
class _$CreateListingFlagInputCopyWithImpl<$Res,
        $Val extends CreateListingFlagInput>
    implements $CreateListingFlagInputCopyWith<$Res> {
  _$CreateListingFlagInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? category = null,
    Object? reason = freezed,
    Object? referenceImageFiles = freezed,
  }) {
    return _then(_value.copyWith(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceImageFiles: freezed == referenceImageFiles
          ? _value.referenceImageFiles
          : referenceImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateListingFlagInputImplCopyWith<$Res>
    implements $CreateListingFlagInputCopyWith<$Res> {
  factory _$$CreateListingFlagInputImplCopyWith(
          _$CreateListingFlagInputImpl value,
          $Res Function(_$CreateListingFlagInputImpl) then) =
      __$$CreateListingFlagInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String listingId,
      String category,
      String? reason,
      @JsonKey(includeFromJson: false) List<File>? referenceImageFiles});
}

/// @nodoc
class __$$CreateListingFlagInputImplCopyWithImpl<$Res>
    extends _$CreateListingFlagInputCopyWithImpl<$Res,
        _$CreateListingFlagInputImpl>
    implements _$$CreateListingFlagInputImplCopyWith<$Res> {
  __$$CreateListingFlagInputImplCopyWithImpl(
      _$CreateListingFlagInputImpl _value,
      $Res Function(_$CreateListingFlagInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? category = null,
    Object? reason = freezed,
    Object? referenceImageFiles = freezed,
  }) {
    return _then(_$CreateListingFlagInputImpl(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceImageFiles: freezed == referenceImageFiles
          ? _value._referenceImageFiles
          : referenceImageFiles // ignore: cast_nullable_to_non_nullable
              as List<File>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateListingFlagInputImpl implements _CreateListingFlagInput {
  const _$CreateListingFlagInputImpl(
      {required this.listingId,
      required this.category,
      this.reason,
      @JsonKey(includeFromJson: false) final List<File>? referenceImageFiles})
      : _referenceImageFiles = referenceImageFiles;

  factory _$CreateListingFlagInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateListingFlagInputImplFromJson(json);

  @override
  final String listingId;
  @override
  final String category;
  @override
  final String? reason;
  final List<File>? _referenceImageFiles;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get referenceImageFiles {
    final value = _referenceImageFiles;
    if (value == null) return null;
    if (_referenceImageFiles is EqualUnmodifiableListView)
      return _referenceImageFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CreateListingFlagInput(listingId: $listingId, category: $category, reason: $reason, referenceImageFiles: $referenceImageFiles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateListingFlagInputImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality()
                .equals(other._referenceImageFiles, _referenceImageFiles));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, listingId, category, reason,
      const DeepCollectionEquality().hash(_referenceImageFiles));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateListingFlagInputImplCopyWith<_$CreateListingFlagInputImpl>
      get copyWith => __$$CreateListingFlagInputImplCopyWithImpl<
          _$CreateListingFlagInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateListingFlagInputImplToJson(
      this,
    );
  }
}

abstract class _CreateListingFlagInput implements CreateListingFlagInput {
  const factory _CreateListingFlagInput(
      {required final String listingId,
      required final String category,
      final String? reason,
      @JsonKey(includeFromJson: false)
      final List<File>? referenceImageFiles}) = _$CreateListingFlagInputImpl;

  factory _CreateListingFlagInput.fromJson(Map<String, dynamic> json) =
      _$CreateListingFlagInputImpl.fromJson;

  @override
  String get listingId;
  @override
  String get category;
  @override
  String? get reason;
  @override
  @JsonKey(includeFromJson: false)
  List<File>? get referenceImageFiles;
  @override
  @JsonKey(ignore: true)
  _$$CreateListingFlagInputImplCopyWith<_$CreateListingFlagInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
