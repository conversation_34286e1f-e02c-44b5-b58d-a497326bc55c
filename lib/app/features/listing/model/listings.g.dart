// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingImpl _$$ListingImplFromJson(Map<String, dynamic> json) =>
    _$ListingImpl(
      id: json['id'] as String,
      firestoreId: json['firestore_id'] as String?,
      name: json['name'] as String?,
      companyName: json['company_name'] as String?,
      description: json['description'] as String?,
      fullAddress: json['full_address'] as String?,
      openingHours: json['opening_hours'] as String?,
      postalCode: json['postal_code'] as String?,
      fullContactNumber: json['full_contact_number'] as String?,
      position: json['position'] == null
          ? null
          : ListingPosition.fromJson(json['position'] as Map<String, dynamic>),
      averageExperienceRatings:
          (json['average_experience_ratings'] as num?)?.toDouble(),
      totalExperienceRatings:
          (json['total_experience_ratings'] as num?)?.toInt(),
      totalSessions: (json['total_sessions'] as num?)?.toInt(),
      maxNumberOfUsageExtensions:
          (json['max_number_of_usage_extensions'] as num?)?.toInt(),
      listingFiles: (json['listing_files'] as List<dynamic>?)
          ?.map((e) => ListingFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      listingRatings: (json['listing_ratings'] as List<dynamic>?)
          ?.map((e) => ListingRating.fromJson(e as Map<String, dynamic>))
          .toList(),
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => Amenity.fromJson(e as Map<String, dynamic>))
          .toList(),
      imageUrl: json['image_url'] as String?,
      status: $enumDecodeNullable(_$ListingStatusEnumMap, json['status']),
      listingType:
          $enumDecodeNullable(_$ListingTypeEnumMap, json['listing_type']),
      distance: (json['distance'] as num?)?.toDouble(),
      isHidden: json['is_hidden'] as bool? ?? false,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$ListingImplToJson(_$ListingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firestore_id': instance.firestoreId,
      'name': instance.name,
      'company_name': instance.companyName,
      'description': instance.description,
      'full_address': instance.fullAddress,
      'opening_hours': instance.openingHours,
      'postal_code': instance.postalCode,
      'full_contact_number': instance.fullContactNumber,
      'position': instance.position?.toJson(),
      'average_experience_ratings': instance.averageExperienceRatings,
      'total_experience_ratings': instance.totalExperienceRatings,
      'total_sessions': instance.totalSessions,
      'max_number_of_usage_extensions': instance.maxNumberOfUsageExtensions,
      'listing_files': instance.listingFiles?.map((e) => e.toJson()).toList(),
      'listing_ratings':
          instance.listingRatings?.map((e) => e.toJson()).toList(),
      'amenities': instance.amenities?.map((e) => e.toJson()).toList(),
      'image_url': instance.imageUrl,
      'status': _$ListingStatusEnumMap[instance.status],
      'listing_type': _$ListingTypeEnumMap[instance.listingType],
      'distance': instance.distance,
      'is_hidden': instance.isHidden,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

const _$ListingStatusEnumMap = {
  ListingStatus.idle: 'idle',
  ListingStatus.occupied: 'occupied',
  ListingStatus.disinfecting: 'disinfecting',
};

const _$ListingTypeEnumMap = {
  ListingType.gomama: 'gomama',
  ListingType.care: 'care',
};

_$ListingPositionImpl _$$ListingPositionImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingPositionImpl(
      coordinate: ListingCoordinate.fromJson(
          json['coordinate'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ListingPositionImplToJson(
        _$ListingPositionImpl instance) =>
    <String, dynamic>{
      'coordinate': instance.coordinate.toJson(),
    };

_$ListingCoordinateImpl _$$ListingCoordinateImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingCoordinateImpl(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );

Map<String, dynamic> _$$ListingCoordinateImplToJson(
        _$ListingCoordinateImpl instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
    };

_$ListingsResponseImpl _$$ListingsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingsResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => Listing.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ListingsResponseImplToJson(
        _$ListingsResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };
