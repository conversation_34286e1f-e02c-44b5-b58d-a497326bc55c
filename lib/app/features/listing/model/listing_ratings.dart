import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/session/model/sessions.dart';

part 'listing_ratings.freezed.dart';
part 'listing_ratings.g.dart';

@freezed
class ListingRating with _$ListingRating {
  @CustomDateTimeConverter()
  factory ListingRating({
    required String id,
    String? listingId,
    String? sessionId,
    String? username,
    double? appRating,
    double? experienceRating,
    double? listingRating,
    String? review,
    Session? session,
    @Default(false) bool isHidden,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ListingRating;

  factory ListingRating.fromJson(Json json) => _$ListingRatingFromJson(json);
}

@freezed
class ListingRatingSummary with _$ListingRatingSummary {
  factory ListingRatingSummary({
    int? totalSessions,
    double? averageExperienceRatings,
    int? totalExperienceRatings,
    int? fiveStarsCount,
    int? fourStarsCount,
    int? threeStarsCount,
    int? twoStarsCount,
    int? oneStarCount,
  }) = _ListingRatingSummary;

  factory ListingRatingSummary.fromJson(Json json) =>
      _$ListingRatingSummaryFromJson(json);
}

@freezed
class ListingRatingsResponse with _$ListingRatingsResponse {
  const factory ListingRatingsResponse({
    required List<ListingRating> data,
    required Pagination meta,
  }) = _ListingRatingsResponse;

  factory ListingRatingsResponse.fromJson(Json json) =>
      _$ListingRatingsResponseFromJson(json);
}

@freezed
class ListingRatingsPagination with _$ListingRatingsPagination {
  const factory ListingRatingsPagination({
    required int page,
    required ListingRatingsQuery query,
  }) = _ListingRatingsPagination;
}

@freezed
class ListingRatingsOffset with _$ListingRatingsOffset {
  const factory ListingRatingsOffset({
    required int offset,
    required ListingRatingsQuery query,
  }) = _ListingRatingsOffset;
}

@freezed
class ListingRatingsQuery with _$ListingRatingsQuery {
  const factory ListingRatingsQuery({
    required String listingId,
    String? sort,
  }) = _ListingRatingsQuery;
}

@freezed
class CreateListingRatingInput with _$CreateListingRatingInput {
  const factory CreateListingRatingInput({
    String? listingId,
    String? sessionId,
    required int experienceRating,
    required int listingRating,
    required int appRating,
    String? review,
  }) = _CreateListingRatingInput;

  factory CreateListingRatingInput.fromJson(Json json) =>
      _$CreateListingRatingInputFromJson(json);
}

@freezed
class UpdateListingRatingInput with _$UpdateListingRatingInput {
  const factory UpdateListingRatingInput({
    String? listingId,
    String? sessionId,
    required int experienceRating,
    required int listingRating,
    required int appRating,
    String? review,
    required String listingRatingId,
  }) = _UpdateListingRatingInput;

  factory UpdateListingRatingInput.fromJson(Json json) =>
      _$UpdateListingRatingInputFromJson(json);
}
