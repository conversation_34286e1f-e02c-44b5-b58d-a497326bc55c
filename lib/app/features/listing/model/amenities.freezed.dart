// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'amenities.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Amenity _$AmenityFromJson(Map<String, dynamic> json) {
  return _Amenity.fromJson(json);
}

/// @nodoc
mixin _$Amenity {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get slug => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String get fontIconName => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AmenityCopyWith<Amenity> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AmenityCopyWith<$Res> {
  factory $AmenityCopyWith(Amenity value, $Res Function(Amenity) then) =
      _$AmenityCopyWithImpl<$Res, Amenity>;
  @useResult
  $Res call(
      {String id,
      String name,
      String slug,
      String? description,
      String fontIconName,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$AmenityCopyWithImpl<$Res, $Val extends Amenity>
    implements $AmenityCopyWith<$Res> {
  _$AmenityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? slug = null,
    Object? description = freezed,
    Object? fontIconName = null,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      fontIconName: null == fontIconName
          ? _value.fontIconName
          : fontIconName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AmenityImplCopyWith<$Res> implements $AmenityCopyWith<$Res> {
  factory _$$AmenityImplCopyWith(
          _$AmenityImpl value, $Res Function(_$AmenityImpl) then) =
      __$$AmenityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String slug,
      String? description,
      String fontIconName,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$AmenityImplCopyWithImpl<$Res>
    extends _$AmenityCopyWithImpl<$Res, _$AmenityImpl>
    implements _$$AmenityImplCopyWith<$Res> {
  __$$AmenityImplCopyWithImpl(
      _$AmenityImpl _value, $Res Function(_$AmenityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? slug = null,
    Object? description = freezed,
    Object? fontIconName = null,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$AmenityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      fontIconName: null == fontIconName
          ? _value.fontIconName
          : fontIconName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$AmenityImpl implements _Amenity {
  _$AmenityImpl(
      {required this.id,
      required this.name,
      required this.slug,
      this.description,
      required this.fontIconName,
      this.isHidden = false,
      this.createdAt,
      this.updatedAt});

  factory _$AmenityImpl.fromJson(Map<String, dynamic> json) =>
      _$$AmenityImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String slug;
  @override
  final String? description;
  @override
  final String fontIconName;
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Amenity(id: $id, name: $name, slug: $slug, description: $description, fontIconName: $fontIconName, isHidden: $isHidden, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmenityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.slug, slug) || other.slug == slug) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.fontIconName, fontIconName) ||
                other.fontIconName == fontIconName) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, slug, description,
      fontIconName, isHidden, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AmenityImplCopyWith<_$AmenityImpl> get copyWith =>
      __$$AmenityImplCopyWithImpl<_$AmenityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AmenityImplToJson(
      this,
    );
  }
}

abstract class _Amenity implements Amenity {
  factory _Amenity(
      {required final String id,
      required final String name,
      required final String slug,
      final String? description,
      required final String fontIconName,
      final bool isHidden,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$AmenityImpl;

  factory _Amenity.fromJson(Map<String, dynamic> json) = _$AmenityImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get slug;
  @override
  String? get description;
  @override
  String get fontIconName;
  @override
  bool get isHidden;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$AmenityImplCopyWith<_$AmenityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AmenitiesResponse _$AmenitiesResponseFromJson(Map<String, dynamic> json) {
  return _AmenitiesResponse.fromJson(json);
}

/// @nodoc
mixin _$AmenitiesResponse {
  List<Amenity> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AmenitiesResponseCopyWith<AmenitiesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AmenitiesResponseCopyWith<$Res> {
  factory $AmenitiesResponseCopyWith(
          AmenitiesResponse value, $Res Function(AmenitiesResponse) then) =
      _$AmenitiesResponseCopyWithImpl<$Res, AmenitiesResponse>;
  @useResult
  $Res call({List<Amenity> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$AmenitiesResponseCopyWithImpl<$Res, $Val extends AmenitiesResponse>
    implements $AmenitiesResponseCopyWith<$Res> {
  _$AmenitiesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Amenity>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AmenitiesResponseImplCopyWith<$Res>
    implements $AmenitiesResponseCopyWith<$Res> {
  factory _$$AmenitiesResponseImplCopyWith(_$AmenitiesResponseImpl value,
          $Res Function(_$AmenitiesResponseImpl) then) =
      __$$AmenitiesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Amenity> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$AmenitiesResponseImplCopyWithImpl<$Res>
    extends _$AmenitiesResponseCopyWithImpl<$Res, _$AmenitiesResponseImpl>
    implements _$$AmenitiesResponseImplCopyWith<$Res> {
  __$$AmenitiesResponseImplCopyWithImpl(_$AmenitiesResponseImpl _value,
      $Res Function(_$AmenitiesResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$AmenitiesResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Amenity>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AmenitiesResponseImpl implements _AmenitiesResponse {
  _$AmenitiesResponseImpl(
      {required final List<Amenity> data, required this.meta})
      : _data = data;

  factory _$AmenitiesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AmenitiesResponseImplFromJson(json);

  final List<Amenity> _data;
  @override
  List<Amenity> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'AmenitiesResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmenitiesResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AmenitiesResponseImplCopyWith<_$AmenitiesResponseImpl> get copyWith =>
      __$$AmenitiesResponseImplCopyWithImpl<_$AmenitiesResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AmenitiesResponseImplToJson(
      this,
    );
  }
}

abstract class _AmenitiesResponse implements AmenitiesResponse {
  factory _AmenitiesResponse(
      {required final List<Amenity> data,
      required final Pagination meta}) = _$AmenitiesResponseImpl;

  factory _AmenitiesResponse.fromJson(Map<String, dynamic> json) =
      _$AmenitiesResponseImpl.fromJson;

  @override
  List<Amenity> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$AmenitiesResponseImplCopyWith<_$AmenitiesResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
