// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Listing _$ListingFromJson(Map<String, dynamic> json) {
  return _Listing.fromJson(json);
}

/// @nodoc
mixin _$Listing {
  String get id => throw _privateConstructorUsedError;
  String? get firestoreId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get fullAddress => throw _privateConstructorUsedError;
  String? get openingHours => throw _privateConstructorUsedError;
  String? get postalCode => throw _privateConstructorUsedError;
  String? get fullContactNumber => throw _privateConstructorUsedError;
  ListingPosition? get position =>
      throw _privateConstructorUsedError; // List<ListingRating>? listingRatings,
  double? get averageExperienceRatings => throw _privateConstructorUsedError;
  int? get totalExperienceRatings => throw _privateConstructorUsedError;
  int? get totalSessions => throw _privateConstructorUsedError;
  int? get maxNumberOfUsageExtensions => throw _privateConstructorUsedError;
  List<ListingFile>? get listingFiles => throw _privateConstructorUsedError;
  List<ListingRating>? get listingRatings => throw _privateConstructorUsedError;
  List<Amenity>? get amenities => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  ListingStatus? get status => throw _privateConstructorUsedError;
  ListingType? get listingType => throw _privateConstructorUsedError;
  double? get distance => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingCopyWith<Listing> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingCopyWith<$Res> {
  factory $ListingCopyWith(Listing value, $Res Function(Listing) then) =
      _$ListingCopyWithImpl<$Res, Listing>;
  @useResult
  $Res call(
      {String id,
      String? firestoreId,
      String? name,
      String? companyName,
      String? description,
      String? fullAddress,
      String? openingHours,
      String? postalCode,
      String? fullContactNumber,
      ListingPosition? position,
      double? averageExperienceRatings,
      int? totalExperienceRatings,
      int? totalSessions,
      int? maxNumberOfUsageExtensions,
      List<ListingFile>? listingFiles,
      List<ListingRating>? listingRatings,
      List<Amenity>? amenities,
      String? imageUrl,
      ListingStatus? status,
      ListingType? listingType,
      double? distance,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ListingPositionCopyWith<$Res>? get position;
}

/// @nodoc
class _$ListingCopyWithImpl<$Res, $Val extends Listing>
    implements $ListingCopyWith<$Res> {
  _$ListingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firestoreId = freezed,
    Object? name = freezed,
    Object? companyName = freezed,
    Object? description = freezed,
    Object? fullAddress = freezed,
    Object? openingHours = freezed,
    Object? postalCode = freezed,
    Object? fullContactNumber = freezed,
    Object? position = freezed,
    Object? averageExperienceRatings = freezed,
    Object? totalExperienceRatings = freezed,
    Object? totalSessions = freezed,
    Object? maxNumberOfUsageExtensions = freezed,
    Object? listingFiles = freezed,
    Object? listingRatings = freezed,
    Object? amenities = freezed,
    Object? imageUrl = freezed,
    Object? status = freezed,
    Object? listingType = freezed,
    Object? distance = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firestoreId: freezed == firestoreId
          ? _value.firestoreId
          : firestoreId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      fullAddress: freezed == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      openingHours: freezed == openingHours
          ? _value.openingHours
          : openingHours // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      fullContactNumber: freezed == fullContactNumber
          ? _value.fullContactNumber
          : fullContactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as ListingPosition?,
      averageExperienceRatings: freezed == averageExperienceRatings
          ? _value.averageExperienceRatings
          : averageExperienceRatings // ignore: cast_nullable_to_non_nullable
              as double?,
      totalExperienceRatings: freezed == totalExperienceRatings
          ? _value.totalExperienceRatings
          : totalExperienceRatings // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSessions: freezed == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int?,
      maxNumberOfUsageExtensions: freezed == maxNumberOfUsageExtensions
          ? _value.maxNumberOfUsageExtensions
          : maxNumberOfUsageExtensions // ignore: cast_nullable_to_non_nullable
              as int?,
      listingFiles: freezed == listingFiles
          ? _value.listingFiles
          : listingFiles // ignore: cast_nullable_to_non_nullable
              as List<ListingFile>?,
      listingRatings: freezed == listingRatings
          ? _value.listingRatings
          : listingRatings // ignore: cast_nullable_to_non_nullable
              as List<ListingRating>?,
      amenities: freezed == amenities
          ? _value.amenities
          : amenities // ignore: cast_nullable_to_non_nullable
              as List<Amenity>?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus?,
      listingType: freezed == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as ListingType?,
      distance: freezed == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as double?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingPositionCopyWith<$Res>? get position {
    if (_value.position == null) {
      return null;
    }

    return $ListingPositionCopyWith<$Res>(_value.position!, (value) {
      return _then(_value.copyWith(position: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingImplCopyWith<$Res> implements $ListingCopyWith<$Res> {
  factory _$$ListingImplCopyWith(
          _$ListingImpl value, $Res Function(_$ListingImpl) then) =
      __$$ListingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? firestoreId,
      String? name,
      String? companyName,
      String? description,
      String? fullAddress,
      String? openingHours,
      String? postalCode,
      String? fullContactNumber,
      ListingPosition? position,
      double? averageExperienceRatings,
      int? totalExperienceRatings,
      int? totalSessions,
      int? maxNumberOfUsageExtensions,
      List<ListingFile>? listingFiles,
      List<ListingRating>? listingRatings,
      List<Amenity>? amenities,
      String? imageUrl,
      ListingStatus? status,
      ListingType? listingType,
      double? distance,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ListingPositionCopyWith<$Res>? get position;
}

/// @nodoc
class __$$ListingImplCopyWithImpl<$Res>
    extends _$ListingCopyWithImpl<$Res, _$ListingImpl>
    implements _$$ListingImplCopyWith<$Res> {
  __$$ListingImplCopyWithImpl(
      _$ListingImpl _value, $Res Function(_$ListingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firestoreId = freezed,
    Object? name = freezed,
    Object? companyName = freezed,
    Object? description = freezed,
    Object? fullAddress = freezed,
    Object? openingHours = freezed,
    Object? postalCode = freezed,
    Object? fullContactNumber = freezed,
    Object? position = freezed,
    Object? averageExperienceRatings = freezed,
    Object? totalExperienceRatings = freezed,
    Object? totalSessions = freezed,
    Object? maxNumberOfUsageExtensions = freezed,
    Object? listingFiles = freezed,
    Object? listingRatings = freezed,
    Object? amenities = freezed,
    Object? imageUrl = freezed,
    Object? status = freezed,
    Object? listingType = freezed,
    Object? distance = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ListingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firestoreId: freezed == firestoreId
          ? _value.firestoreId
          : firestoreId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      fullAddress: freezed == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      openingHours: freezed == openingHours
          ? _value.openingHours
          : openingHours // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      fullContactNumber: freezed == fullContactNumber
          ? _value.fullContactNumber
          : fullContactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as ListingPosition?,
      averageExperienceRatings: freezed == averageExperienceRatings
          ? _value.averageExperienceRatings
          : averageExperienceRatings // ignore: cast_nullable_to_non_nullable
              as double?,
      totalExperienceRatings: freezed == totalExperienceRatings
          ? _value.totalExperienceRatings
          : totalExperienceRatings // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSessions: freezed == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int?,
      maxNumberOfUsageExtensions: freezed == maxNumberOfUsageExtensions
          ? _value.maxNumberOfUsageExtensions
          : maxNumberOfUsageExtensions // ignore: cast_nullable_to_non_nullable
              as int?,
      listingFiles: freezed == listingFiles
          ? _value._listingFiles
          : listingFiles // ignore: cast_nullable_to_non_nullable
              as List<ListingFile>?,
      listingRatings: freezed == listingRatings
          ? _value._listingRatings
          : listingRatings // ignore: cast_nullable_to_non_nullable
              as List<ListingRating>?,
      amenities: freezed == amenities
          ? _value._amenities
          : amenities // ignore: cast_nullable_to_non_nullable
              as List<Amenity>?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ListingStatus?,
      listingType: freezed == listingType
          ? _value.listingType
          : listingType // ignore: cast_nullable_to_non_nullable
              as ListingType?,
      distance: freezed == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as double?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ListingImpl extends _Listing {
  const _$ListingImpl(
      {required this.id,
      this.firestoreId,
      this.name,
      this.companyName,
      this.description,
      this.fullAddress,
      this.openingHours,
      this.postalCode,
      this.fullContactNumber,
      this.position,
      this.averageExperienceRatings,
      this.totalExperienceRatings,
      this.totalSessions,
      this.maxNumberOfUsageExtensions,
      final List<ListingFile>? listingFiles,
      final List<ListingRating>? listingRatings,
      final List<Amenity>? amenities,
      this.imageUrl,
      this.status,
      this.listingType,
      this.distance,
      this.isHidden = false,
      this.createdAt,
      this.updatedAt})
      : _listingFiles = listingFiles,
        _listingRatings = listingRatings,
        _amenities = amenities,
        super._();

  factory _$ListingImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingImplFromJson(json);

  @override
  final String id;
  @override
  final String? firestoreId;
  @override
  final String? name;
  @override
  final String? companyName;
  @override
  final String? description;
  @override
  final String? fullAddress;
  @override
  final String? openingHours;
  @override
  final String? postalCode;
  @override
  final String? fullContactNumber;
  @override
  final ListingPosition? position;
// List<ListingRating>? listingRatings,
  @override
  final double? averageExperienceRatings;
  @override
  final int? totalExperienceRatings;
  @override
  final int? totalSessions;
  @override
  final int? maxNumberOfUsageExtensions;
  final List<ListingFile>? _listingFiles;
  @override
  List<ListingFile>? get listingFiles {
    final value = _listingFiles;
    if (value == null) return null;
    if (_listingFiles is EqualUnmodifiableListView) return _listingFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ListingRating>? _listingRatings;
  @override
  List<ListingRating>? get listingRatings {
    final value = _listingRatings;
    if (value == null) return null;
    if (_listingRatings is EqualUnmodifiableListView) return _listingRatings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Amenity>? _amenities;
  @override
  List<Amenity>? get amenities {
    final value = _amenities;
    if (value == null) return null;
    if (_amenities is EqualUnmodifiableListView) return _amenities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? imageUrl;
  @override
  final ListingStatus? status;
  @override
  final ListingType? listingType;
  @override
  final double? distance;
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Listing(id: $id, firestoreId: $firestoreId, name: $name, companyName: $companyName, description: $description, fullAddress: $fullAddress, openingHours: $openingHours, postalCode: $postalCode, fullContactNumber: $fullContactNumber, position: $position, averageExperienceRatings: $averageExperienceRatings, totalExperienceRatings: $totalExperienceRatings, totalSessions: $totalSessions, maxNumberOfUsageExtensions: $maxNumberOfUsageExtensions, listingFiles: $listingFiles, listingRatings: $listingRatings, amenities: $amenities, imageUrl: $imageUrl, status: $status, listingType: $listingType, distance: $distance, isHidden: $isHidden, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firestoreId, firestoreId) ||
                other.firestoreId == firestoreId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.openingHours, openingHours) ||
                other.openingHours == openingHours) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.fullContactNumber, fullContactNumber) ||
                other.fullContactNumber == fullContactNumber) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(
                    other.averageExperienceRatings, averageExperienceRatings) ||
                other.averageExperienceRatings == averageExperienceRatings) &&
            (identical(other.totalExperienceRatings, totalExperienceRatings) ||
                other.totalExperienceRatings == totalExperienceRatings) &&
            (identical(other.totalSessions, totalSessions) ||
                other.totalSessions == totalSessions) &&
            (identical(other.maxNumberOfUsageExtensions,
                    maxNumberOfUsageExtensions) ||
                other.maxNumberOfUsageExtensions ==
                    maxNumberOfUsageExtensions) &&
            const DeepCollectionEquality()
                .equals(other._listingFiles, _listingFiles) &&
            const DeepCollectionEquality()
                .equals(other._listingRatings, _listingRatings) &&
            const DeepCollectionEquality()
                .equals(other._amenities, _amenities) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.listingType, listingType) ||
                other.listingType == listingType) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        firestoreId,
        name,
        companyName,
        description,
        fullAddress,
        openingHours,
        postalCode,
        fullContactNumber,
        position,
        averageExperienceRatings,
        totalExperienceRatings,
        totalSessions,
        maxNumberOfUsageExtensions,
        const DeepCollectionEquality().hash(_listingFiles),
        const DeepCollectionEquality().hash(_listingRatings),
        const DeepCollectionEquality().hash(_amenities),
        imageUrl,
        status,
        listingType,
        distance,
        isHidden,
        createdAt,
        updatedAt
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingImplCopyWith<_$ListingImpl> get copyWith =>
      __$$ListingImplCopyWithImpl<_$ListingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingImplToJson(
      this,
    );
  }
}

abstract class _Listing extends Listing {
  const factory _Listing(
      {required final String id,
      final String? firestoreId,
      final String? name,
      final String? companyName,
      final String? description,
      final String? fullAddress,
      final String? openingHours,
      final String? postalCode,
      final String? fullContactNumber,
      final ListingPosition? position,
      final double? averageExperienceRatings,
      final int? totalExperienceRatings,
      final int? totalSessions,
      final int? maxNumberOfUsageExtensions,
      final List<ListingFile>? listingFiles,
      final List<ListingRating>? listingRatings,
      final List<Amenity>? amenities,
      final String? imageUrl,
      final ListingStatus? status,
      final ListingType? listingType,
      final double? distance,
      final bool isHidden,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ListingImpl;
  const _Listing._() : super._();

  factory _Listing.fromJson(Map<String, dynamic> json) = _$ListingImpl.fromJson;

  @override
  String get id;
  @override
  String? get firestoreId;
  @override
  String? get name;
  @override
  String? get companyName;
  @override
  String? get description;
  @override
  String? get fullAddress;
  @override
  String? get openingHours;
  @override
  String? get postalCode;
  @override
  String? get fullContactNumber;
  @override
  ListingPosition? get position;
  @override // List<ListingRating>? listingRatings,
  double? get averageExperienceRatings;
  @override
  int? get totalExperienceRatings;
  @override
  int? get totalSessions;
  @override
  int? get maxNumberOfUsageExtensions;
  @override
  List<ListingFile>? get listingFiles;
  @override
  List<ListingRating>? get listingRatings;
  @override
  List<Amenity>? get amenities;
  @override
  String? get imageUrl;
  @override
  ListingStatus? get status;
  @override
  ListingType? get listingType;
  @override
  double? get distance;
  @override
  bool get isHidden;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ListingImplCopyWith<_$ListingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListingPosition _$ListingPositionFromJson(Map<String, dynamic> json) {
  return _ListingPosition.fromJson(json);
}

/// @nodoc
mixin _$ListingPosition {
  ListingCoordinate get coordinate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingPositionCopyWith<ListingPosition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingPositionCopyWith<$Res> {
  factory $ListingPositionCopyWith(
          ListingPosition value, $Res Function(ListingPosition) then) =
      _$ListingPositionCopyWithImpl<$Res, ListingPosition>;
  @useResult
  $Res call({ListingCoordinate coordinate});

  $ListingCoordinateCopyWith<$Res> get coordinate;
}

/// @nodoc
class _$ListingPositionCopyWithImpl<$Res, $Val extends ListingPosition>
    implements $ListingPositionCopyWith<$Res> {
  _$ListingPositionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coordinate = null,
  }) {
    return _then(_value.copyWith(
      coordinate: null == coordinate
          ? _value.coordinate
          : coordinate // ignore: cast_nullable_to_non_nullable
              as ListingCoordinate,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingCoordinateCopyWith<$Res> get coordinate {
    return $ListingCoordinateCopyWith<$Res>(_value.coordinate, (value) {
      return _then(_value.copyWith(coordinate: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingPositionImplCopyWith<$Res>
    implements $ListingPositionCopyWith<$Res> {
  factory _$$ListingPositionImplCopyWith(_$ListingPositionImpl value,
          $Res Function(_$ListingPositionImpl) then) =
      __$$ListingPositionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ListingCoordinate coordinate});

  @override
  $ListingCoordinateCopyWith<$Res> get coordinate;
}

/// @nodoc
class __$$ListingPositionImplCopyWithImpl<$Res>
    extends _$ListingPositionCopyWithImpl<$Res, _$ListingPositionImpl>
    implements _$$ListingPositionImplCopyWith<$Res> {
  __$$ListingPositionImplCopyWithImpl(
      _$ListingPositionImpl _value, $Res Function(_$ListingPositionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coordinate = null,
  }) {
    return _then(_$ListingPositionImpl(
      coordinate: null == coordinate
          ? _value.coordinate
          : coordinate // ignore: cast_nullable_to_non_nullable
              as ListingCoordinate,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingPositionImpl implements _ListingPosition {
  const _$ListingPositionImpl({required this.coordinate});

  factory _$ListingPositionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingPositionImplFromJson(json);

  @override
  final ListingCoordinate coordinate;

  @override
  String toString() {
    return 'ListingPosition(coordinate: $coordinate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingPositionImpl &&
            (identical(other.coordinate, coordinate) ||
                other.coordinate == coordinate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, coordinate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingPositionImplCopyWith<_$ListingPositionImpl> get copyWith =>
      __$$ListingPositionImplCopyWithImpl<_$ListingPositionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingPositionImplToJson(
      this,
    );
  }
}

abstract class _ListingPosition implements ListingPosition {
  const factory _ListingPosition(
      {required final ListingCoordinate coordinate}) = _$ListingPositionImpl;

  factory _ListingPosition.fromJson(Map<String, dynamic> json) =
      _$ListingPositionImpl.fromJson;

  @override
  ListingCoordinate get coordinate;
  @override
  @JsonKey(ignore: true)
  _$$ListingPositionImplCopyWith<_$ListingPositionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListingCoordinate _$ListingCoordinateFromJson(Map<String, dynamic> json) {
  return _ListingCoordinate.fromJson(json);
}

/// @nodoc
mixin _$ListingCoordinate {
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingCoordinateCopyWith<ListingCoordinate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingCoordinateCopyWith<$Res> {
  factory $ListingCoordinateCopyWith(
          ListingCoordinate value, $Res Function(ListingCoordinate) then) =
      _$ListingCoordinateCopyWithImpl<$Res, ListingCoordinate>;
  @useResult
  $Res call({double x, double y});
}

/// @nodoc
class _$ListingCoordinateCopyWithImpl<$Res, $Val extends ListingCoordinate>
    implements $ListingCoordinateCopyWith<$Res> {
  _$ListingCoordinateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_value.copyWith(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingCoordinateImplCopyWith<$Res>
    implements $ListingCoordinateCopyWith<$Res> {
  factory _$$ListingCoordinateImplCopyWith(_$ListingCoordinateImpl value,
          $Res Function(_$ListingCoordinateImpl) then) =
      __$$ListingCoordinateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double x, double y});
}

/// @nodoc
class __$$ListingCoordinateImplCopyWithImpl<$Res>
    extends _$ListingCoordinateCopyWithImpl<$Res, _$ListingCoordinateImpl>
    implements _$$ListingCoordinateImplCopyWith<$Res> {
  __$$ListingCoordinateImplCopyWithImpl(_$ListingCoordinateImpl _value,
      $Res Function(_$ListingCoordinateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_$ListingCoordinateImpl(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingCoordinateImpl implements _ListingCoordinate {
  const _$ListingCoordinateImpl({required this.x, required this.y});

  factory _$ListingCoordinateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingCoordinateImplFromJson(json);

  @override
  final double x;
  @override
  final double y;

  @override
  String toString() {
    return 'ListingCoordinate(x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingCoordinateImpl &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, x, y);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingCoordinateImplCopyWith<_$ListingCoordinateImpl> get copyWith =>
      __$$ListingCoordinateImplCopyWithImpl<_$ListingCoordinateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingCoordinateImplToJson(
      this,
    );
  }
}

abstract class _ListingCoordinate implements ListingCoordinate {
  const factory _ListingCoordinate(
      {required final double x,
      required final double y}) = _$ListingCoordinateImpl;

  factory _ListingCoordinate.fromJson(Map<String, dynamic> json) =
      _$ListingCoordinateImpl.fromJson;

  @override
  double get x;
  @override
  double get y;
  @override
  @JsonKey(ignore: true)
  _$$ListingCoordinateImplCopyWith<_$ListingCoordinateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListingsResponse _$ListingsResponseFromJson(Map<String, dynamic> json) {
  return _ListingsResponse.fromJson(json);
}

/// @nodoc
mixin _$ListingsResponse {
  List<Listing> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingsResponseCopyWith<ListingsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingsResponseCopyWith<$Res> {
  factory $ListingsResponseCopyWith(
          ListingsResponse value, $Res Function(ListingsResponse) then) =
      _$ListingsResponseCopyWithImpl<$Res, ListingsResponse>;
  @useResult
  $Res call({List<Listing> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$ListingsResponseCopyWithImpl<$Res, $Val extends ListingsResponse>
    implements $ListingsResponseCopyWith<$Res> {
  _$ListingsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Listing>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingsResponseImplCopyWith<$Res>
    implements $ListingsResponseCopyWith<$Res> {
  factory _$$ListingsResponseImplCopyWith(_$ListingsResponseImpl value,
          $Res Function(_$ListingsResponseImpl) then) =
      __$$ListingsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Listing> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$ListingsResponseImplCopyWithImpl<$Res>
    extends _$ListingsResponseCopyWithImpl<$Res, _$ListingsResponseImpl>
    implements _$$ListingsResponseImplCopyWith<$Res> {
  __$$ListingsResponseImplCopyWithImpl(_$ListingsResponseImpl _value,
      $Res Function(_$ListingsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$ListingsResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Listing>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingsResponseImpl implements _ListingsResponse {
  const _$ListingsResponseImpl(
      {required final List<Listing> data, required this.meta})
      : _data = data;

  factory _$ListingsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingsResponseImplFromJson(json);

  final List<Listing> _data;
  @override
  List<Listing> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'ListingsResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingsResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingsResponseImplCopyWith<_$ListingsResponseImpl> get copyWith =>
      __$$ListingsResponseImplCopyWithImpl<_$ListingsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingsResponseImplToJson(
      this,
    );
  }
}

abstract class _ListingsResponse implements ListingsResponse {
  const factory _ListingsResponse(
      {required final List<Listing> data,
      required final Pagination meta}) = _$ListingsResponseImpl;

  factory _ListingsResponse.fromJson(Map<String, dynamic> json) =
      _$ListingsResponseImpl.fromJson;

  @override
  List<Listing> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$ListingsResponseImplCopyWith<_$ListingsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AllListingsPagination {
  int get page => throw _privateConstructorUsedError;
  AllListingsInput? get input => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AllListingsPaginationCopyWith<AllListingsPagination> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllListingsPaginationCopyWith<$Res> {
  factory $AllListingsPaginationCopyWith(AllListingsPagination value,
          $Res Function(AllListingsPagination) then) =
      _$AllListingsPaginationCopyWithImpl<$Res, AllListingsPagination>;
  @useResult
  $Res call({int page, AllListingsInput? input});

  $AllListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class _$AllListingsPaginationCopyWithImpl<$Res,
        $Val extends AllListingsPagination>
    implements $AllListingsPaginationCopyWith<$Res> {
  _$AllListingsPaginationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? input = freezed,
  }) {
    return _then(_value.copyWith(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as AllListingsInput?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AllListingsInputCopyWith<$Res>? get input {
    if (_value.input == null) {
      return null;
    }

    return $AllListingsInputCopyWith<$Res>(_value.input!, (value) {
      return _then(_value.copyWith(input: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AllListingsPaginationImplCopyWith<$Res>
    implements $AllListingsPaginationCopyWith<$Res> {
  factory _$$AllListingsPaginationImplCopyWith(
          _$AllListingsPaginationImpl value,
          $Res Function(_$AllListingsPaginationImpl) then) =
      __$$AllListingsPaginationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int page, AllListingsInput? input});

  @override
  $AllListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class __$$AllListingsPaginationImplCopyWithImpl<$Res>
    extends _$AllListingsPaginationCopyWithImpl<$Res,
        _$AllListingsPaginationImpl>
    implements _$$AllListingsPaginationImplCopyWith<$Res> {
  __$$AllListingsPaginationImplCopyWithImpl(_$AllListingsPaginationImpl _value,
      $Res Function(_$AllListingsPaginationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? input = freezed,
  }) {
    return _then(_$AllListingsPaginationImpl(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as AllListingsInput?,
    ));
  }
}

/// @nodoc

class _$AllListingsPaginationImpl implements _AllListingsPagination {
  const _$AllListingsPaginationImpl({required this.page, this.input});

  @override
  final int page;
  @override
  final AllListingsInput? input;

  @override
  String toString() {
    return 'AllListingsPagination(page: $page, input: $input)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllListingsPaginationImpl &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.input, input) || other.input == input));
  }

  @override
  int get hashCode => Object.hash(runtimeType, page, input);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllListingsPaginationImplCopyWith<_$AllListingsPaginationImpl>
      get copyWith => __$$AllListingsPaginationImplCopyWithImpl<
          _$AllListingsPaginationImpl>(this, _$identity);
}

abstract class _AllListingsPagination implements AllListingsPagination {
  const factory _AllListingsPagination(
      {required final int page,
      final AllListingsInput? input}) = _$AllListingsPaginationImpl;

  @override
  int get page;
  @override
  AllListingsInput? get input;
  @override
  @JsonKey(ignore: true)
  _$$AllListingsPaginationImplCopyWith<_$AllListingsPaginationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AllListingsOffset {
  int get offset => throw _privateConstructorUsedError;
  AllListingsInput? get input => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AllListingsOffsetCopyWith<AllListingsOffset> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllListingsOffsetCopyWith<$Res> {
  factory $AllListingsOffsetCopyWith(
          AllListingsOffset value, $Res Function(AllListingsOffset) then) =
      _$AllListingsOffsetCopyWithImpl<$Res, AllListingsOffset>;
  @useResult
  $Res call({int offset, AllListingsInput? input});

  $AllListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class _$AllListingsOffsetCopyWithImpl<$Res, $Val extends AllListingsOffset>
    implements $AllListingsOffsetCopyWith<$Res> {
  _$AllListingsOffsetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? input = freezed,
  }) {
    return _then(_value.copyWith(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as AllListingsInput?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AllListingsInputCopyWith<$Res>? get input {
    if (_value.input == null) {
      return null;
    }

    return $AllListingsInputCopyWith<$Res>(_value.input!, (value) {
      return _then(_value.copyWith(input: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AllListingsOffsetImplCopyWith<$Res>
    implements $AllListingsOffsetCopyWith<$Res> {
  factory _$$AllListingsOffsetImplCopyWith(_$AllListingsOffsetImpl value,
          $Res Function(_$AllListingsOffsetImpl) then) =
      __$$AllListingsOffsetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int offset, AllListingsInput? input});

  @override
  $AllListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class __$$AllListingsOffsetImplCopyWithImpl<$Res>
    extends _$AllListingsOffsetCopyWithImpl<$Res, _$AllListingsOffsetImpl>
    implements _$$AllListingsOffsetImplCopyWith<$Res> {
  __$$AllListingsOffsetImplCopyWithImpl(_$AllListingsOffsetImpl _value,
      $Res Function(_$AllListingsOffsetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? input = freezed,
  }) {
    return _then(_$AllListingsOffsetImpl(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as AllListingsInput?,
    ));
  }
}

/// @nodoc

class _$AllListingsOffsetImpl implements _AllListingsOffset {
  const _$AllListingsOffsetImpl({required this.offset, this.input});

  @override
  final int offset;
  @override
  final AllListingsInput? input;

  @override
  String toString() {
    return 'AllListingsOffset(offset: $offset, input: $input)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllListingsOffsetImpl &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.input, input) || other.input == input));
  }

  @override
  int get hashCode => Object.hash(runtimeType, offset, input);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllListingsOffsetImplCopyWith<_$AllListingsOffsetImpl> get copyWith =>
      __$$AllListingsOffsetImplCopyWithImpl<_$AllListingsOffsetImpl>(
          this, _$identity);
}

abstract class _AllListingsOffset implements AllListingsOffset {
  const factory _AllListingsOffset(
      {required final int offset,
      final AllListingsInput? input}) = _$AllListingsOffsetImpl;

  @override
  int get offset;
  @override
  AllListingsInput? get input;
  @override
  @JsonKey(ignore: true)
  _$$AllListingsOffsetImplCopyWith<_$AllListingsOffsetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NearbyListingsPagination {
  int get page => throw _privateConstructorUsedError;
  NearbyListingsInput? get input => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NearbyListingsPaginationCopyWith<NearbyListingsPagination> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NearbyListingsPaginationCopyWith<$Res> {
  factory $NearbyListingsPaginationCopyWith(NearbyListingsPagination value,
          $Res Function(NearbyListingsPagination) then) =
      _$NearbyListingsPaginationCopyWithImpl<$Res, NearbyListingsPagination>;
  @useResult
  $Res call({int page, NearbyListingsInput? input});

  $NearbyListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class _$NearbyListingsPaginationCopyWithImpl<$Res,
        $Val extends NearbyListingsPagination>
    implements $NearbyListingsPaginationCopyWith<$Res> {
  _$NearbyListingsPaginationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? input = freezed,
  }) {
    return _then(_value.copyWith(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as NearbyListingsInput?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $NearbyListingsInputCopyWith<$Res>? get input {
    if (_value.input == null) {
      return null;
    }

    return $NearbyListingsInputCopyWith<$Res>(_value.input!, (value) {
      return _then(_value.copyWith(input: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NearbyListingsPaginationImplCopyWith<$Res>
    implements $NearbyListingsPaginationCopyWith<$Res> {
  factory _$$NearbyListingsPaginationImplCopyWith(
          _$NearbyListingsPaginationImpl value,
          $Res Function(_$NearbyListingsPaginationImpl) then) =
      __$$NearbyListingsPaginationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int page, NearbyListingsInput? input});

  @override
  $NearbyListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class __$$NearbyListingsPaginationImplCopyWithImpl<$Res>
    extends _$NearbyListingsPaginationCopyWithImpl<$Res,
        _$NearbyListingsPaginationImpl>
    implements _$$NearbyListingsPaginationImplCopyWith<$Res> {
  __$$NearbyListingsPaginationImplCopyWithImpl(
      _$NearbyListingsPaginationImpl _value,
      $Res Function(_$NearbyListingsPaginationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? input = freezed,
  }) {
    return _then(_$NearbyListingsPaginationImpl(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as NearbyListingsInput?,
    ));
  }
}

/// @nodoc

class _$NearbyListingsPaginationImpl implements _NearbyListingsPagination {
  const _$NearbyListingsPaginationImpl({required this.page, this.input});

  @override
  final int page;
  @override
  final NearbyListingsInput? input;

  @override
  String toString() {
    return 'NearbyListingsPagination(page: $page, input: $input)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NearbyListingsPaginationImpl &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.input, input) || other.input == input));
  }

  @override
  int get hashCode => Object.hash(runtimeType, page, input);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NearbyListingsPaginationImplCopyWith<_$NearbyListingsPaginationImpl>
      get copyWith => __$$NearbyListingsPaginationImplCopyWithImpl<
          _$NearbyListingsPaginationImpl>(this, _$identity);
}

abstract class _NearbyListingsPagination implements NearbyListingsPagination {
  const factory _NearbyListingsPagination(
      {required final int page,
      final NearbyListingsInput? input}) = _$NearbyListingsPaginationImpl;

  @override
  int get page;
  @override
  NearbyListingsInput? get input;
  @override
  @JsonKey(ignore: true)
  _$$NearbyListingsPaginationImplCopyWith<_$NearbyListingsPaginationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NearbyListingsOffset {
  int get offset => throw _privateConstructorUsedError;
  NearbyListingsInput? get input => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NearbyListingsOffsetCopyWith<NearbyListingsOffset> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NearbyListingsOffsetCopyWith<$Res> {
  factory $NearbyListingsOffsetCopyWith(NearbyListingsOffset value,
          $Res Function(NearbyListingsOffset) then) =
      _$NearbyListingsOffsetCopyWithImpl<$Res, NearbyListingsOffset>;
  @useResult
  $Res call({int offset, NearbyListingsInput? input});

  $NearbyListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class _$NearbyListingsOffsetCopyWithImpl<$Res,
        $Val extends NearbyListingsOffset>
    implements $NearbyListingsOffsetCopyWith<$Res> {
  _$NearbyListingsOffsetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? input = freezed,
  }) {
    return _then(_value.copyWith(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as NearbyListingsInput?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $NearbyListingsInputCopyWith<$Res>? get input {
    if (_value.input == null) {
      return null;
    }

    return $NearbyListingsInputCopyWith<$Res>(_value.input!, (value) {
      return _then(_value.copyWith(input: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NearbyListingsOffsetImplCopyWith<$Res>
    implements $NearbyListingsOffsetCopyWith<$Res> {
  factory _$$NearbyListingsOffsetImplCopyWith(_$NearbyListingsOffsetImpl value,
          $Res Function(_$NearbyListingsOffsetImpl) then) =
      __$$NearbyListingsOffsetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int offset, NearbyListingsInput? input});

  @override
  $NearbyListingsInputCopyWith<$Res>? get input;
}

/// @nodoc
class __$$NearbyListingsOffsetImplCopyWithImpl<$Res>
    extends _$NearbyListingsOffsetCopyWithImpl<$Res, _$NearbyListingsOffsetImpl>
    implements _$$NearbyListingsOffsetImplCopyWith<$Res> {
  __$$NearbyListingsOffsetImplCopyWithImpl(_$NearbyListingsOffsetImpl _value,
      $Res Function(_$NearbyListingsOffsetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? input = freezed,
  }) {
    return _then(_$NearbyListingsOffsetImpl(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      input: freezed == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as NearbyListingsInput?,
    ));
  }
}

/// @nodoc

class _$NearbyListingsOffsetImpl implements _NearbyListingsOffset {
  const _$NearbyListingsOffsetImpl({required this.offset, this.input});

  @override
  final int offset;
  @override
  final NearbyListingsInput? input;

  @override
  String toString() {
    return 'NearbyListingsOffset(offset: $offset, input: $input)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NearbyListingsOffsetImpl &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.input, input) || other.input == input));
  }

  @override
  int get hashCode => Object.hash(runtimeType, offset, input);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NearbyListingsOffsetImplCopyWith<_$NearbyListingsOffsetImpl>
      get copyWith =>
          __$$NearbyListingsOffsetImplCopyWithImpl<_$NearbyListingsOffsetImpl>(
              this, _$identity);
}

abstract class _NearbyListingsOffset implements NearbyListingsOffset {
  const factory _NearbyListingsOffset(
      {required final int offset,
      final NearbyListingsInput? input}) = _$NearbyListingsOffsetImpl;

  @override
  int get offset;
  @override
  NearbyListingsInput? get input;
  @override
  @JsonKey(ignore: true)
  _$$NearbyListingsOffsetImplCopyWith<_$NearbyListingsOffsetImpl>
      get copyWith => throw _privateConstructorUsedError;
}
