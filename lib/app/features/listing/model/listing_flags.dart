// ignore_for_file: invalid_annotation_target

import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/constants/listing_flag_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/explore/model/nearby_listing_input.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listing_files.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';

part 'listing_flags.freezed.dart';
part 'listing_flags.g.dart';

@freezed
class ListingFlagCategory with _$ListingFlagCategory {
  const factory ListingFlagCategory.pageMalfunction() = PageMalfunction;
  const factory ListingFlagCategory.duplicateListing() = DuplicateListing;
  const factory ListingFlagCategory.inaccurateInfo() = InaccurateInfo;
  const factory ListingFlagCategory.privacyConcern() = PrivacyConcern;
  const factory ListingFlagCategory.safetyHazard() = SafetyHazard;
  const factory ListingFlagCategory.other() = Other;

  const ListingFlagCategory._();

  String get title => when(
        pageMalfunction: () => 'Page has a glitch or malfunction',
        duplicateListing: () => "It's a duplicate listing",
        inaccurateInfo: () => 'The information is inaccurate or outdated',
        privacyConcern: () => 'Privacy concerns',
        safetyHazard: () => 'Safety Hazards',
        other: () => 'Others',
      );

  String get instructions => when(
        pageMalfunction: () => 'Provide your observations regarding the issue.',
        duplicateListing: () =>
            'Provide your observations regarding the issue.',
        inaccurateInfo: () =>
            'Provide more details about the inaccuracies or outdated information.',
        privacyConcern: () => 'Provide your observations regarding the issue.',
        safetyHazard: () => 'Provide your observations regarding the issue.',
        other: () => "Provide details about the issues you're experiencing.",
      );

  String get description => when(
        pageMalfunction: () =>
            'Please provide details about specific issues so that we can assist you better.',
        duplicateListing: () =>
            'Please provide details about specific issues so that we can assist you better.',
        inaccurateInfo: () =>
            'Please add details to help us address the issue. Thank you!',
        privacyConcern: () =>
            'Share your observations about the issue. For example, mention any issues with curtains, dividers, noise levels, etc.',
        safetyHazard: () =>
            'Share details about the safety concern, such as sharp edges, electrical outlets, or lighting issues.',
        other: () =>
            'Please provide details about specific issues so that we can assist you better.',
      );

  String get feedback => when(
        pageMalfunction: () =>
            "We will investigate and resolve the issue promptly. Thank you for bringing this to our attention and helping improve our platform's performance.",
        duplicateListing: () =>
            'Thank you for helping us maintain the integrity of our listings. We will review the information provided.',
        inaccurateInfo: () =>
            'We appreciate your help. We will promptly review and resolve the issue.',
        privacyConcern: () =>
            'Your input is crucial in maintaining a private and comfortable environment for all users. Thank you for your vigilance assistance and support!',
        safetyHazard: () =>
            'Your input helps us prioritise and address safety matters promptly. Thank you for contributing to a safe and secure environment for all users!',
        other: () =>
            'Thank you for sharing the issues that you are experiencing. We will review the information to better assist you.',
      );

  String get value => when(
        pageMalfunction: () => 'page_malfunction',
        duplicateListing: () => 'duplicate_listing',
        inaccurateInfo: () => 'inaccurate_info',
        privacyConcern: () => 'privacy_concern',
        safetyHazard: () => 'safety_hazard',
        other: () => 'other',
      );
}

final listingFlagCategories = [
  const ListingFlagCategory.pageMalfunction(),
  const ListingFlagCategory.duplicateListing(),
  const ListingFlagCategory.inaccurateInfo(),
  const ListingFlagCategory.privacyConcern(),
  const ListingFlagCategory.safetyHazard(),
  const ListingFlagCategory.other(),
];

enum ListingFlagAction { hideListing, noAction, other }

@freezed
class ListingFlag with _$ListingFlag {
  @CustomDateTimeConverter()
  const factory ListingFlag({
    required String id,
    @ListingFlagCategoryConverter() ListingFlagCategory? category,
    String? reason,
    List<String>? referenceImages,
    DateTime? reviewedAt,
    ListingFlagAction? action,
    String? actionReason,
    String? actionBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ListingFlag;

  factory ListingFlag.fromJson(Json json) => _$ListingFlagFromJson(json);
}

@freezed
class CreateListingFlagInput with _$CreateListingFlagInput {
  const factory CreateListingFlagInput({
    required String listingId,
    required String category,
    String? reason,
    @JsonKey(includeFromJson: false) List<File>? referenceImageFiles,
  }) = _CreateListingFlagInput;

  factory CreateListingFlagInput.fromJson(Json json) =>
      _$CreateListingFlagInputFromJson(json);
}
