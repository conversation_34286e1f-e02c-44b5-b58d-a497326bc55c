// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_ratings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListingRating _$ListingRatingFromJson(Map<String, dynamic> json) {
  return _ListingRating.fromJson(json);
}

/// @nodoc
mixin _$ListingRating {
  String get id => throw _privateConstructorUsedError;
  String? get listingId => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get username => throw _privateConstructorUsedError;
  double? get appRating => throw _privateConstructorUsedError;
  double? get experienceRating => throw _privateConstructorUsedError;
  double? get listingRating => throw _privateConstructorUsedError;
  String? get review => throw _privateConstructorUsedError;
  Session? get session => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingRatingCopyWith<ListingRating> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingCopyWith<$Res> {
  factory $ListingRatingCopyWith(
          ListingRating value, $Res Function(ListingRating) then) =
      _$ListingRatingCopyWithImpl<$Res, ListingRating>;
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? sessionId,
      String? username,
      double? appRating,
      double? experienceRating,
      double? listingRating,
      String? review,
      Session? session,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});

  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class _$ListingRatingCopyWithImpl<$Res, $Val extends ListingRating>
    implements $ListingRatingCopyWith<$Res> {
  _$ListingRatingCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? username = freezed,
    Object? appRating = freezed,
    Object? experienceRating = freezed,
    Object? listingRating = freezed,
    Object? review = freezed,
    Object? session = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      appRating: freezed == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as double?,
      experienceRating: freezed == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as double?,
      listingRating: freezed == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as double?,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SessionCopyWith<$Res>? get session {
    if (_value.session == null) {
      return null;
    }

    return $SessionCopyWith<$Res>(_value.session!, (value) {
      return _then(_value.copyWith(session: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingRatingImplCopyWith<$Res>
    implements $ListingRatingCopyWith<$Res> {
  factory _$$ListingRatingImplCopyWith(
          _$ListingRatingImpl value, $Res Function(_$ListingRatingImpl) then) =
      __$$ListingRatingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? sessionId,
      String? username,
      double? appRating,
      double? experienceRating,
      double? listingRating,
      String? review,
      Session? session,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class __$$ListingRatingImplCopyWithImpl<$Res>
    extends _$ListingRatingCopyWithImpl<$Res, _$ListingRatingImpl>
    implements _$$ListingRatingImplCopyWith<$Res> {
  __$$ListingRatingImplCopyWithImpl(
      _$ListingRatingImpl _value, $Res Function(_$ListingRatingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? username = freezed,
    Object? appRating = freezed,
    Object? experienceRating = freezed,
    Object? listingRating = freezed,
    Object? review = freezed,
    Object? session = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ListingRatingImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      appRating: freezed == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as double?,
      experienceRating: freezed == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as double?,
      listingRating: freezed == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as double?,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ListingRatingImpl implements _ListingRating {
  _$ListingRatingImpl(
      {required this.id,
      this.listingId,
      this.sessionId,
      this.username,
      this.appRating,
      this.experienceRating,
      this.listingRating,
      this.review,
      this.session,
      this.isHidden = false,
      this.createdAt,
      this.updatedAt});

  factory _$ListingRatingImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingRatingImplFromJson(json);

  @override
  final String id;
  @override
  final String? listingId;
  @override
  final String? sessionId;
  @override
  final String? username;
  @override
  final double? appRating;
  @override
  final double? experienceRating;
  @override
  final double? listingRating;
  @override
  final String? review;
  @override
  final Session? session;
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ListingRating(id: $id, listingId: $listingId, sessionId: $sessionId, username: $username, appRating: $appRating, experienceRating: $experienceRating, listingRating: $listingRating, review: $review, session: $session, isHidden: $isHidden, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.appRating, appRating) ||
                other.appRating == appRating) &&
            (identical(other.experienceRating, experienceRating) ||
                other.experienceRating == experienceRating) &&
            (identical(other.listingRating, listingRating) ||
                other.listingRating == listingRating) &&
            (identical(other.review, review) || other.review == review) &&
            (identical(other.session, session) || other.session == session) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      listingId,
      sessionId,
      username,
      appRating,
      experienceRating,
      listingRating,
      review,
      session,
      isHidden,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingImplCopyWith<_$ListingRatingImpl> get copyWith =>
      __$$ListingRatingImplCopyWithImpl<_$ListingRatingImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingRatingImplToJson(
      this,
    );
  }
}

abstract class _ListingRating implements ListingRating {
  factory _ListingRating(
      {required final String id,
      final String? listingId,
      final String? sessionId,
      final String? username,
      final double? appRating,
      final double? experienceRating,
      final double? listingRating,
      final String? review,
      final Session? session,
      final bool isHidden,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ListingRatingImpl;

  factory _ListingRating.fromJson(Map<String, dynamic> json) =
      _$ListingRatingImpl.fromJson;

  @override
  String get id;
  @override
  String? get listingId;
  @override
  String? get sessionId;
  @override
  String? get username;
  @override
  double? get appRating;
  @override
  double? get experienceRating;
  @override
  double? get listingRating;
  @override
  String? get review;
  @override
  Session? get session;
  @override
  bool get isHidden;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingImplCopyWith<_$ListingRatingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListingRatingSummary _$ListingRatingSummaryFromJson(Map<String, dynamic> json) {
  return _ListingRatingSummary.fromJson(json);
}

/// @nodoc
mixin _$ListingRatingSummary {
  int? get totalSessions => throw _privateConstructorUsedError;
  double? get averageExperienceRatings => throw _privateConstructorUsedError;
  int? get totalExperienceRatings => throw _privateConstructorUsedError;
  int? get fiveStarsCount => throw _privateConstructorUsedError;
  int? get fourStarsCount => throw _privateConstructorUsedError;
  int? get threeStarsCount => throw _privateConstructorUsedError;
  int? get twoStarsCount => throw _privateConstructorUsedError;
  int? get oneStarCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingRatingSummaryCopyWith<ListingRatingSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingSummaryCopyWith<$Res> {
  factory $ListingRatingSummaryCopyWith(ListingRatingSummary value,
          $Res Function(ListingRatingSummary) then) =
      _$ListingRatingSummaryCopyWithImpl<$Res, ListingRatingSummary>;
  @useResult
  $Res call(
      {int? totalSessions,
      double? averageExperienceRatings,
      int? totalExperienceRatings,
      int? fiveStarsCount,
      int? fourStarsCount,
      int? threeStarsCount,
      int? twoStarsCount,
      int? oneStarCount});
}

/// @nodoc
class _$ListingRatingSummaryCopyWithImpl<$Res,
        $Val extends ListingRatingSummary>
    implements $ListingRatingSummaryCopyWith<$Res> {
  _$ListingRatingSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalSessions = freezed,
    Object? averageExperienceRatings = freezed,
    Object? totalExperienceRatings = freezed,
    Object? fiveStarsCount = freezed,
    Object? fourStarsCount = freezed,
    Object? threeStarsCount = freezed,
    Object? twoStarsCount = freezed,
    Object? oneStarCount = freezed,
  }) {
    return _then(_value.copyWith(
      totalSessions: freezed == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int?,
      averageExperienceRatings: freezed == averageExperienceRatings
          ? _value.averageExperienceRatings
          : averageExperienceRatings // ignore: cast_nullable_to_non_nullable
              as double?,
      totalExperienceRatings: freezed == totalExperienceRatings
          ? _value.totalExperienceRatings
          : totalExperienceRatings // ignore: cast_nullable_to_non_nullable
              as int?,
      fiveStarsCount: freezed == fiveStarsCount
          ? _value.fiveStarsCount
          : fiveStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      fourStarsCount: freezed == fourStarsCount
          ? _value.fourStarsCount
          : fourStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      threeStarsCount: freezed == threeStarsCount
          ? _value.threeStarsCount
          : threeStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      twoStarsCount: freezed == twoStarsCount
          ? _value.twoStarsCount
          : twoStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      oneStarCount: freezed == oneStarCount
          ? _value.oneStarCount
          : oneStarCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingRatingSummaryImplCopyWith<$Res>
    implements $ListingRatingSummaryCopyWith<$Res> {
  factory _$$ListingRatingSummaryImplCopyWith(_$ListingRatingSummaryImpl value,
          $Res Function(_$ListingRatingSummaryImpl) then) =
      __$$ListingRatingSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? totalSessions,
      double? averageExperienceRatings,
      int? totalExperienceRatings,
      int? fiveStarsCount,
      int? fourStarsCount,
      int? threeStarsCount,
      int? twoStarsCount,
      int? oneStarCount});
}

/// @nodoc
class __$$ListingRatingSummaryImplCopyWithImpl<$Res>
    extends _$ListingRatingSummaryCopyWithImpl<$Res, _$ListingRatingSummaryImpl>
    implements _$$ListingRatingSummaryImplCopyWith<$Res> {
  __$$ListingRatingSummaryImplCopyWithImpl(_$ListingRatingSummaryImpl _value,
      $Res Function(_$ListingRatingSummaryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalSessions = freezed,
    Object? averageExperienceRatings = freezed,
    Object? totalExperienceRatings = freezed,
    Object? fiveStarsCount = freezed,
    Object? fourStarsCount = freezed,
    Object? threeStarsCount = freezed,
    Object? twoStarsCount = freezed,
    Object? oneStarCount = freezed,
  }) {
    return _then(_$ListingRatingSummaryImpl(
      totalSessions: freezed == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int?,
      averageExperienceRatings: freezed == averageExperienceRatings
          ? _value.averageExperienceRatings
          : averageExperienceRatings // ignore: cast_nullable_to_non_nullable
              as double?,
      totalExperienceRatings: freezed == totalExperienceRatings
          ? _value.totalExperienceRatings
          : totalExperienceRatings // ignore: cast_nullable_to_non_nullable
              as int?,
      fiveStarsCount: freezed == fiveStarsCount
          ? _value.fiveStarsCount
          : fiveStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      fourStarsCount: freezed == fourStarsCount
          ? _value.fourStarsCount
          : fourStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      threeStarsCount: freezed == threeStarsCount
          ? _value.threeStarsCount
          : threeStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      twoStarsCount: freezed == twoStarsCount
          ? _value.twoStarsCount
          : twoStarsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      oneStarCount: freezed == oneStarCount
          ? _value.oneStarCount
          : oneStarCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingRatingSummaryImpl implements _ListingRatingSummary {
  _$ListingRatingSummaryImpl(
      {this.totalSessions,
      this.averageExperienceRatings,
      this.totalExperienceRatings,
      this.fiveStarsCount,
      this.fourStarsCount,
      this.threeStarsCount,
      this.twoStarsCount,
      this.oneStarCount});

  factory _$ListingRatingSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingRatingSummaryImplFromJson(json);

  @override
  final int? totalSessions;
  @override
  final double? averageExperienceRatings;
  @override
  final int? totalExperienceRatings;
  @override
  final int? fiveStarsCount;
  @override
  final int? fourStarsCount;
  @override
  final int? threeStarsCount;
  @override
  final int? twoStarsCount;
  @override
  final int? oneStarCount;

  @override
  String toString() {
    return 'ListingRatingSummary(totalSessions: $totalSessions, averageExperienceRatings: $averageExperienceRatings, totalExperienceRatings: $totalExperienceRatings, fiveStarsCount: $fiveStarsCount, fourStarsCount: $fourStarsCount, threeStarsCount: $threeStarsCount, twoStarsCount: $twoStarsCount, oneStarCount: $oneStarCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingSummaryImpl &&
            (identical(other.totalSessions, totalSessions) ||
                other.totalSessions == totalSessions) &&
            (identical(
                    other.averageExperienceRatings, averageExperienceRatings) ||
                other.averageExperienceRatings == averageExperienceRatings) &&
            (identical(other.totalExperienceRatings, totalExperienceRatings) ||
                other.totalExperienceRatings == totalExperienceRatings) &&
            (identical(other.fiveStarsCount, fiveStarsCount) ||
                other.fiveStarsCount == fiveStarsCount) &&
            (identical(other.fourStarsCount, fourStarsCount) ||
                other.fourStarsCount == fourStarsCount) &&
            (identical(other.threeStarsCount, threeStarsCount) ||
                other.threeStarsCount == threeStarsCount) &&
            (identical(other.twoStarsCount, twoStarsCount) ||
                other.twoStarsCount == twoStarsCount) &&
            (identical(other.oneStarCount, oneStarCount) ||
                other.oneStarCount == oneStarCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalSessions,
      averageExperienceRatings,
      totalExperienceRatings,
      fiveStarsCount,
      fourStarsCount,
      threeStarsCount,
      twoStarsCount,
      oneStarCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingSummaryImplCopyWith<_$ListingRatingSummaryImpl>
      get copyWith =>
          __$$ListingRatingSummaryImplCopyWithImpl<_$ListingRatingSummaryImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingRatingSummaryImplToJson(
      this,
    );
  }
}

abstract class _ListingRatingSummary implements ListingRatingSummary {
  factory _ListingRatingSummary(
      {final int? totalSessions,
      final double? averageExperienceRatings,
      final int? totalExperienceRatings,
      final int? fiveStarsCount,
      final int? fourStarsCount,
      final int? threeStarsCount,
      final int? twoStarsCount,
      final int? oneStarCount}) = _$ListingRatingSummaryImpl;

  factory _ListingRatingSummary.fromJson(Map<String, dynamic> json) =
      _$ListingRatingSummaryImpl.fromJson;

  @override
  int? get totalSessions;
  @override
  double? get averageExperienceRatings;
  @override
  int? get totalExperienceRatings;
  @override
  int? get fiveStarsCount;
  @override
  int? get fourStarsCount;
  @override
  int? get threeStarsCount;
  @override
  int? get twoStarsCount;
  @override
  int? get oneStarCount;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingSummaryImplCopyWith<_$ListingRatingSummaryImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ListingRatingsResponse _$ListingRatingsResponseFromJson(
    Map<String, dynamic> json) {
  return _ListingRatingsResponse.fromJson(json);
}

/// @nodoc
mixin _$ListingRatingsResponse {
  List<ListingRating> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingRatingsResponseCopyWith<ListingRatingsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingsResponseCopyWith<$Res> {
  factory $ListingRatingsResponseCopyWith(ListingRatingsResponse value,
          $Res Function(ListingRatingsResponse) then) =
      _$ListingRatingsResponseCopyWithImpl<$Res, ListingRatingsResponse>;
  @useResult
  $Res call({List<ListingRating> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$ListingRatingsResponseCopyWithImpl<$Res,
        $Val extends ListingRatingsResponse>
    implements $ListingRatingsResponseCopyWith<$Res> {
  _$ListingRatingsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ListingRating>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingRatingsResponseImplCopyWith<$Res>
    implements $ListingRatingsResponseCopyWith<$Res> {
  factory _$$ListingRatingsResponseImplCopyWith(
          _$ListingRatingsResponseImpl value,
          $Res Function(_$ListingRatingsResponseImpl) then) =
      __$$ListingRatingsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ListingRating> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$ListingRatingsResponseImplCopyWithImpl<$Res>
    extends _$ListingRatingsResponseCopyWithImpl<$Res,
        _$ListingRatingsResponseImpl>
    implements _$$ListingRatingsResponseImplCopyWith<$Res> {
  __$$ListingRatingsResponseImplCopyWithImpl(
      _$ListingRatingsResponseImpl _value,
      $Res Function(_$ListingRatingsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$ListingRatingsResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ListingRating>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingRatingsResponseImpl implements _ListingRatingsResponse {
  const _$ListingRatingsResponseImpl(
      {required final List<ListingRating> data, required this.meta})
      : _data = data;

  factory _$ListingRatingsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingRatingsResponseImplFromJson(json);

  final List<ListingRating> _data;
  @override
  List<ListingRating> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'ListingRatingsResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingsResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingsResponseImplCopyWith<_$ListingRatingsResponseImpl>
      get copyWith => __$$ListingRatingsResponseImplCopyWithImpl<
          _$ListingRatingsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingRatingsResponseImplToJson(
      this,
    );
  }
}

abstract class _ListingRatingsResponse implements ListingRatingsResponse {
  const factory _ListingRatingsResponse(
      {required final List<ListingRating> data,
      required final Pagination meta}) = _$ListingRatingsResponseImpl;

  factory _ListingRatingsResponse.fromJson(Map<String, dynamic> json) =
      _$ListingRatingsResponseImpl.fromJson;

  @override
  List<ListingRating> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingsResponseImplCopyWith<_$ListingRatingsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ListingRatingsPagination {
  int get page => throw _privateConstructorUsedError;
  ListingRatingsQuery get query => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ListingRatingsPaginationCopyWith<ListingRatingsPagination> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingsPaginationCopyWith<$Res> {
  factory $ListingRatingsPaginationCopyWith(ListingRatingsPagination value,
          $Res Function(ListingRatingsPagination) then) =
      _$ListingRatingsPaginationCopyWithImpl<$Res, ListingRatingsPagination>;
  @useResult
  $Res call({int page, ListingRatingsQuery query});

  $ListingRatingsQueryCopyWith<$Res> get query;
}

/// @nodoc
class _$ListingRatingsPaginationCopyWithImpl<$Res,
        $Val extends ListingRatingsPagination>
    implements $ListingRatingsPaginationCopyWith<$Res> {
  _$ListingRatingsPaginationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? query = null,
  }) {
    return _then(_value.copyWith(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as ListingRatingsQuery,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingRatingsQueryCopyWith<$Res> get query {
    return $ListingRatingsQueryCopyWith<$Res>(_value.query, (value) {
      return _then(_value.copyWith(query: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingRatingsPaginationImplCopyWith<$Res>
    implements $ListingRatingsPaginationCopyWith<$Res> {
  factory _$$ListingRatingsPaginationImplCopyWith(
          _$ListingRatingsPaginationImpl value,
          $Res Function(_$ListingRatingsPaginationImpl) then) =
      __$$ListingRatingsPaginationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int page, ListingRatingsQuery query});

  @override
  $ListingRatingsQueryCopyWith<$Res> get query;
}

/// @nodoc
class __$$ListingRatingsPaginationImplCopyWithImpl<$Res>
    extends _$ListingRatingsPaginationCopyWithImpl<$Res,
        _$ListingRatingsPaginationImpl>
    implements _$$ListingRatingsPaginationImplCopyWith<$Res> {
  __$$ListingRatingsPaginationImplCopyWithImpl(
      _$ListingRatingsPaginationImpl _value,
      $Res Function(_$ListingRatingsPaginationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? query = null,
  }) {
    return _then(_$ListingRatingsPaginationImpl(
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as ListingRatingsQuery,
    ));
  }
}

/// @nodoc

class _$ListingRatingsPaginationImpl implements _ListingRatingsPagination {
  const _$ListingRatingsPaginationImpl(
      {required this.page, required this.query});

  @override
  final int page;
  @override
  final ListingRatingsQuery query;

  @override
  String toString() {
    return 'ListingRatingsPagination(page: $page, query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingsPaginationImpl &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, page, query);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingsPaginationImplCopyWith<_$ListingRatingsPaginationImpl>
      get copyWith => __$$ListingRatingsPaginationImplCopyWithImpl<
          _$ListingRatingsPaginationImpl>(this, _$identity);
}

abstract class _ListingRatingsPagination implements ListingRatingsPagination {
  const factory _ListingRatingsPagination(
          {required final int page, required final ListingRatingsQuery query}) =
      _$ListingRatingsPaginationImpl;

  @override
  int get page;
  @override
  ListingRatingsQuery get query;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingsPaginationImplCopyWith<_$ListingRatingsPaginationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ListingRatingsOffset {
  int get offset => throw _privateConstructorUsedError;
  ListingRatingsQuery get query => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ListingRatingsOffsetCopyWith<ListingRatingsOffset> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingsOffsetCopyWith<$Res> {
  factory $ListingRatingsOffsetCopyWith(ListingRatingsOffset value,
          $Res Function(ListingRatingsOffset) then) =
      _$ListingRatingsOffsetCopyWithImpl<$Res, ListingRatingsOffset>;
  @useResult
  $Res call({int offset, ListingRatingsQuery query});

  $ListingRatingsQueryCopyWith<$Res> get query;
}

/// @nodoc
class _$ListingRatingsOffsetCopyWithImpl<$Res,
        $Val extends ListingRatingsOffset>
    implements $ListingRatingsOffsetCopyWith<$Res> {
  _$ListingRatingsOffsetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? query = null,
  }) {
    return _then(_value.copyWith(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as ListingRatingsQuery,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingRatingsQueryCopyWith<$Res> get query {
    return $ListingRatingsQueryCopyWith<$Res>(_value.query, (value) {
      return _then(_value.copyWith(query: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ListingRatingsOffsetImplCopyWith<$Res>
    implements $ListingRatingsOffsetCopyWith<$Res> {
  factory _$$ListingRatingsOffsetImplCopyWith(_$ListingRatingsOffsetImpl value,
          $Res Function(_$ListingRatingsOffsetImpl) then) =
      __$$ListingRatingsOffsetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int offset, ListingRatingsQuery query});

  @override
  $ListingRatingsQueryCopyWith<$Res> get query;
}

/// @nodoc
class __$$ListingRatingsOffsetImplCopyWithImpl<$Res>
    extends _$ListingRatingsOffsetCopyWithImpl<$Res, _$ListingRatingsOffsetImpl>
    implements _$$ListingRatingsOffsetImplCopyWith<$Res> {
  __$$ListingRatingsOffsetImplCopyWithImpl(_$ListingRatingsOffsetImpl _value,
      $Res Function(_$ListingRatingsOffsetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = null,
    Object? query = null,
  }) {
    return _then(_$ListingRatingsOffsetImpl(
      offset: null == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as ListingRatingsQuery,
    ));
  }
}

/// @nodoc

class _$ListingRatingsOffsetImpl implements _ListingRatingsOffset {
  const _$ListingRatingsOffsetImpl({required this.offset, required this.query});

  @override
  final int offset;
  @override
  final ListingRatingsQuery query;

  @override
  String toString() {
    return 'ListingRatingsOffset(offset: $offset, query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingsOffsetImpl &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, offset, query);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingsOffsetImplCopyWith<_$ListingRatingsOffsetImpl>
      get copyWith =>
          __$$ListingRatingsOffsetImplCopyWithImpl<_$ListingRatingsOffsetImpl>(
              this, _$identity);
}

abstract class _ListingRatingsOffset implements ListingRatingsOffset {
  const factory _ListingRatingsOffset(
      {required final int offset,
      required final ListingRatingsQuery query}) = _$ListingRatingsOffsetImpl;

  @override
  int get offset;
  @override
  ListingRatingsQuery get query;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingsOffsetImplCopyWith<_$ListingRatingsOffsetImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ListingRatingsQuery {
  String get listingId => throw _privateConstructorUsedError;
  String? get sort => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ListingRatingsQueryCopyWith<ListingRatingsQuery> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingRatingsQueryCopyWith<$Res> {
  factory $ListingRatingsQueryCopyWith(
          ListingRatingsQuery value, $Res Function(ListingRatingsQuery) then) =
      _$ListingRatingsQueryCopyWithImpl<$Res, ListingRatingsQuery>;
  @useResult
  $Res call({String listingId, String? sort});
}

/// @nodoc
class _$ListingRatingsQueryCopyWithImpl<$Res, $Val extends ListingRatingsQuery>
    implements $ListingRatingsQueryCopyWith<$Res> {
  _$ListingRatingsQueryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingRatingsQueryImplCopyWith<$Res>
    implements $ListingRatingsQueryCopyWith<$Res> {
  factory _$$ListingRatingsQueryImplCopyWith(_$ListingRatingsQueryImpl value,
          $Res Function(_$ListingRatingsQueryImpl) then) =
      __$$ListingRatingsQueryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String listingId, String? sort});
}

/// @nodoc
class __$$ListingRatingsQueryImplCopyWithImpl<$Res>
    extends _$ListingRatingsQueryCopyWithImpl<$Res, _$ListingRatingsQueryImpl>
    implements _$$ListingRatingsQueryImplCopyWith<$Res> {
  __$$ListingRatingsQueryImplCopyWithImpl(_$ListingRatingsQueryImpl _value,
      $Res Function(_$ListingRatingsQueryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? sort = freezed,
  }) {
    return _then(_$ListingRatingsQueryImpl(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ListingRatingsQueryImpl implements _ListingRatingsQuery {
  const _$ListingRatingsQueryImpl({required this.listingId, this.sort});

  @override
  final String listingId;
  @override
  final String? sort;

  @override
  String toString() {
    return 'ListingRatingsQuery(listingId: $listingId, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingRatingsQueryImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @override
  int get hashCode => Object.hash(runtimeType, listingId, sort);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingRatingsQueryImplCopyWith<_$ListingRatingsQueryImpl> get copyWith =>
      __$$ListingRatingsQueryImplCopyWithImpl<_$ListingRatingsQueryImpl>(
          this, _$identity);
}

abstract class _ListingRatingsQuery implements ListingRatingsQuery {
  const factory _ListingRatingsQuery(
      {required final String listingId,
      final String? sort}) = _$ListingRatingsQueryImpl;

  @override
  String get listingId;
  @override
  String? get sort;
  @override
  @JsonKey(ignore: true)
  _$$ListingRatingsQueryImplCopyWith<_$ListingRatingsQueryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateListingRatingInput _$CreateListingRatingInputFromJson(
    Map<String, dynamic> json) {
  return _CreateListingRatingInput.fromJson(json);
}

/// @nodoc
mixin _$CreateListingRatingInput {
  String? get listingId => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  int get experienceRating => throw _privateConstructorUsedError;
  int get listingRating => throw _privateConstructorUsedError;
  int get appRating => throw _privateConstructorUsedError;
  String? get review => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateListingRatingInputCopyWith<CreateListingRatingInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateListingRatingInputCopyWith<$Res> {
  factory $CreateListingRatingInputCopyWith(CreateListingRatingInput value,
          $Res Function(CreateListingRatingInput) then) =
      _$CreateListingRatingInputCopyWithImpl<$Res, CreateListingRatingInput>;
  @useResult
  $Res call(
      {String? listingId,
      String? sessionId,
      int experienceRating,
      int listingRating,
      int appRating,
      String? review});
}

/// @nodoc
class _$CreateListingRatingInputCopyWithImpl<$Res,
        $Val extends CreateListingRatingInput>
    implements $CreateListingRatingInputCopyWith<$Res> {
  _$CreateListingRatingInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? experienceRating = null,
    Object? listingRating = null,
    Object? appRating = null,
    Object? review = freezed,
  }) {
    return _then(_value.copyWith(
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceRating: null == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as int,
      listingRating: null == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as int,
      appRating: null == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as int,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateListingRatingInputImplCopyWith<$Res>
    implements $CreateListingRatingInputCopyWith<$Res> {
  factory _$$CreateListingRatingInputImplCopyWith(
          _$CreateListingRatingInputImpl value,
          $Res Function(_$CreateListingRatingInputImpl) then) =
      __$$CreateListingRatingInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? listingId,
      String? sessionId,
      int experienceRating,
      int listingRating,
      int appRating,
      String? review});
}

/// @nodoc
class __$$CreateListingRatingInputImplCopyWithImpl<$Res>
    extends _$CreateListingRatingInputCopyWithImpl<$Res,
        _$CreateListingRatingInputImpl>
    implements _$$CreateListingRatingInputImplCopyWith<$Res> {
  __$$CreateListingRatingInputImplCopyWithImpl(
      _$CreateListingRatingInputImpl _value,
      $Res Function(_$CreateListingRatingInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? experienceRating = null,
    Object? listingRating = null,
    Object? appRating = null,
    Object? review = freezed,
  }) {
    return _then(_$CreateListingRatingInputImpl(
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceRating: null == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as int,
      listingRating: null == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as int,
      appRating: null == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as int,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateListingRatingInputImpl implements _CreateListingRatingInput {
  const _$CreateListingRatingInputImpl(
      {this.listingId,
      this.sessionId,
      required this.experienceRating,
      required this.listingRating,
      required this.appRating,
      this.review});

  factory _$CreateListingRatingInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateListingRatingInputImplFromJson(json);

  @override
  final String? listingId;
  @override
  final String? sessionId;
  @override
  final int experienceRating;
  @override
  final int listingRating;
  @override
  final int appRating;
  @override
  final String? review;

  @override
  String toString() {
    return 'CreateListingRatingInput(listingId: $listingId, sessionId: $sessionId, experienceRating: $experienceRating, listingRating: $listingRating, appRating: $appRating, review: $review)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateListingRatingInputImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.experienceRating, experienceRating) ||
                other.experienceRating == experienceRating) &&
            (identical(other.listingRating, listingRating) ||
                other.listingRating == listingRating) &&
            (identical(other.appRating, appRating) ||
                other.appRating == appRating) &&
            (identical(other.review, review) || other.review == review));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, listingId, sessionId,
      experienceRating, listingRating, appRating, review);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateListingRatingInputImplCopyWith<_$CreateListingRatingInputImpl>
      get copyWith => __$$CreateListingRatingInputImplCopyWithImpl<
          _$CreateListingRatingInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateListingRatingInputImplToJson(
      this,
    );
  }
}

abstract class _CreateListingRatingInput implements CreateListingRatingInput {
  const factory _CreateListingRatingInput(
      {final String? listingId,
      final String? sessionId,
      required final int experienceRating,
      required final int listingRating,
      required final int appRating,
      final String? review}) = _$CreateListingRatingInputImpl;

  factory _CreateListingRatingInput.fromJson(Map<String, dynamic> json) =
      _$CreateListingRatingInputImpl.fromJson;

  @override
  String? get listingId;
  @override
  String? get sessionId;
  @override
  int get experienceRating;
  @override
  int get listingRating;
  @override
  int get appRating;
  @override
  String? get review;
  @override
  @JsonKey(ignore: true)
  _$$CreateListingRatingInputImplCopyWith<_$CreateListingRatingInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpdateListingRatingInput _$UpdateListingRatingInputFromJson(
    Map<String, dynamic> json) {
  return _UpdateListingRatingInput.fromJson(json);
}

/// @nodoc
mixin _$UpdateListingRatingInput {
  String? get listingId => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  int get experienceRating => throw _privateConstructorUsedError;
  int get listingRating => throw _privateConstructorUsedError;
  int get appRating => throw _privateConstructorUsedError;
  String? get review => throw _privateConstructorUsedError;
  String get listingRatingId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UpdateListingRatingInputCopyWith<UpdateListingRatingInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateListingRatingInputCopyWith<$Res> {
  factory $UpdateListingRatingInputCopyWith(UpdateListingRatingInput value,
          $Res Function(UpdateListingRatingInput) then) =
      _$UpdateListingRatingInputCopyWithImpl<$Res, UpdateListingRatingInput>;
  @useResult
  $Res call(
      {String? listingId,
      String? sessionId,
      int experienceRating,
      int listingRating,
      int appRating,
      String? review,
      String listingRatingId});
}

/// @nodoc
class _$UpdateListingRatingInputCopyWithImpl<$Res,
        $Val extends UpdateListingRatingInput>
    implements $UpdateListingRatingInputCopyWith<$Res> {
  _$UpdateListingRatingInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? experienceRating = null,
    Object? listingRating = null,
    Object? appRating = null,
    Object? review = freezed,
    Object? listingRatingId = null,
  }) {
    return _then(_value.copyWith(
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceRating: null == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as int,
      listingRating: null == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as int,
      appRating: null == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as int,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
      listingRatingId: null == listingRatingId
          ? _value.listingRatingId
          : listingRatingId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateListingRatingInputImplCopyWith<$Res>
    implements $UpdateListingRatingInputCopyWith<$Res> {
  factory _$$UpdateListingRatingInputImplCopyWith(
          _$UpdateListingRatingInputImpl value,
          $Res Function(_$UpdateListingRatingInputImpl) then) =
      __$$UpdateListingRatingInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? listingId,
      String? sessionId,
      int experienceRating,
      int listingRating,
      int appRating,
      String? review,
      String listingRatingId});
}

/// @nodoc
class __$$UpdateListingRatingInputImplCopyWithImpl<$Res>
    extends _$UpdateListingRatingInputCopyWithImpl<$Res,
        _$UpdateListingRatingInputImpl>
    implements _$$UpdateListingRatingInputImplCopyWith<$Res> {
  __$$UpdateListingRatingInputImplCopyWithImpl(
      _$UpdateListingRatingInputImpl _value,
      $Res Function(_$UpdateListingRatingInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = freezed,
    Object? sessionId = freezed,
    Object? experienceRating = null,
    Object? listingRating = null,
    Object? appRating = null,
    Object? review = freezed,
    Object? listingRatingId = null,
  }) {
    return _then(_$UpdateListingRatingInputImpl(
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceRating: null == experienceRating
          ? _value.experienceRating
          : experienceRating // ignore: cast_nullable_to_non_nullable
              as int,
      listingRating: null == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as int,
      appRating: null == appRating
          ? _value.appRating
          : appRating // ignore: cast_nullable_to_non_nullable
              as int,
      review: freezed == review
          ? _value.review
          : review // ignore: cast_nullable_to_non_nullable
              as String?,
      listingRatingId: null == listingRatingId
          ? _value.listingRatingId
          : listingRatingId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateListingRatingInputImpl implements _UpdateListingRatingInput {
  const _$UpdateListingRatingInputImpl(
      {this.listingId,
      this.sessionId,
      required this.experienceRating,
      required this.listingRating,
      required this.appRating,
      this.review,
      required this.listingRatingId});

  factory _$UpdateListingRatingInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateListingRatingInputImplFromJson(json);

  @override
  final String? listingId;
  @override
  final String? sessionId;
  @override
  final int experienceRating;
  @override
  final int listingRating;
  @override
  final int appRating;
  @override
  final String? review;
  @override
  final String listingRatingId;

  @override
  String toString() {
    return 'UpdateListingRatingInput(listingId: $listingId, sessionId: $sessionId, experienceRating: $experienceRating, listingRating: $listingRating, appRating: $appRating, review: $review, listingRatingId: $listingRatingId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateListingRatingInputImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.experienceRating, experienceRating) ||
                other.experienceRating == experienceRating) &&
            (identical(other.listingRating, listingRating) ||
                other.listingRating == listingRating) &&
            (identical(other.appRating, appRating) ||
                other.appRating == appRating) &&
            (identical(other.review, review) || other.review == review) &&
            (identical(other.listingRatingId, listingRatingId) ||
                other.listingRatingId == listingRatingId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, listingId, sessionId,
      experienceRating, listingRating, appRating, review, listingRatingId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateListingRatingInputImplCopyWith<_$UpdateListingRatingInputImpl>
      get copyWith => __$$UpdateListingRatingInputImplCopyWithImpl<
          _$UpdateListingRatingInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateListingRatingInputImplToJson(
      this,
    );
  }
}

abstract class _UpdateListingRatingInput implements UpdateListingRatingInput {
  const factory _UpdateListingRatingInput(
      {final String? listingId,
      final String? sessionId,
      required final int experienceRating,
      required final int listingRating,
      required final int appRating,
      final String? review,
      required final String listingRatingId}) = _$UpdateListingRatingInputImpl;

  factory _UpdateListingRatingInput.fromJson(Map<String, dynamic> json) =
      _$UpdateListingRatingInputImpl.fromJson;

  @override
  String? get listingId;
  @override
  String? get sessionId;
  @override
  int get experienceRating;
  @override
  int get listingRating;
  @override
  int get appRating;
  @override
  String? get review;
  @override
  String get listingRatingId;
  @override
  @JsonKey(ignore: true)
  _$$UpdateListingRatingInputImplCopyWith<_$UpdateListingRatingInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
