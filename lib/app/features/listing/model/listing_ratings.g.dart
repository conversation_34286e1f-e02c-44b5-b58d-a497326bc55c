// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_ratings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingRatingImpl _$$ListingRatingImplFromJson(Map<String, dynamic> json) =>
    _$ListingRatingImpl(
      id: json['id'] as String,
      listingId: json['listing_id'] as String?,
      sessionId: json['session_id'] as String?,
      username: json['username'] as String?,
      appRating: (json['app_rating'] as num?)?.toDouble(),
      experienceRating: (json['experience_rating'] as num?)?.toDouble(),
      listingRating: (json['listing_rating'] as num?)?.toDouble(),
      review: json['review'] as String?,
      session: json['session'] == null
          ? null
          : Session.fromJson(json['session'] as Map<String, dynamic>),
      isHidden: json['is_hidden'] as bool? ?? false,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$ListingRatingImplToJson(_$ListingRatingImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'listing_id': instance.listingId,
      'session_id': instance.sessionId,
      'username': instance.username,
      'app_rating': instance.appRating,
      'experience_rating': instance.experienceRating,
      'listing_rating': instance.listingRating,
      'review': instance.review,
      'session': instance.session?.toJson(),
      'is_hidden': instance.isHidden,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

_$ListingRatingSummaryImpl _$$ListingRatingSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingRatingSummaryImpl(
      totalSessions: (json['total_sessions'] as num?)?.toInt(),
      averageExperienceRatings:
          (json['average_experience_ratings'] as num?)?.toDouble(),
      totalExperienceRatings:
          (json['total_experience_ratings'] as num?)?.toInt(),
      fiveStarsCount: (json['five_stars_count'] as num?)?.toInt(),
      fourStarsCount: (json['four_stars_count'] as num?)?.toInt(),
      threeStarsCount: (json['three_stars_count'] as num?)?.toInt(),
      twoStarsCount: (json['two_stars_count'] as num?)?.toInt(),
      oneStarCount: (json['one_star_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ListingRatingSummaryImplToJson(
        _$ListingRatingSummaryImpl instance) =>
    <String, dynamic>{
      'total_sessions': instance.totalSessions,
      'average_experience_ratings': instance.averageExperienceRatings,
      'total_experience_ratings': instance.totalExperienceRatings,
      'five_stars_count': instance.fiveStarsCount,
      'four_stars_count': instance.fourStarsCount,
      'three_stars_count': instance.threeStarsCount,
      'two_stars_count': instance.twoStarsCount,
      'one_star_count': instance.oneStarCount,
    };

_$ListingRatingsResponseImpl _$$ListingRatingsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingRatingsResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => ListingRating.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ListingRatingsResponseImplToJson(
        _$ListingRatingsResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };

_$CreateListingRatingInputImpl _$$CreateListingRatingInputImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateListingRatingInputImpl(
      listingId: json['listing_id'] as String?,
      sessionId: json['session_id'] as String?,
      experienceRating: (json['experience_rating'] as num).toInt(),
      listingRating: (json['listing_rating'] as num).toInt(),
      appRating: (json['app_rating'] as num).toInt(),
      review: json['review'] as String?,
    );

Map<String, dynamic> _$$CreateListingRatingInputImplToJson(
        _$CreateListingRatingInputImpl instance) =>
    <String, dynamic>{
      'listing_id': instance.listingId,
      'session_id': instance.sessionId,
      'experience_rating': instance.experienceRating,
      'listing_rating': instance.listingRating,
      'app_rating': instance.appRating,
      'review': instance.review,
    };

_$UpdateListingRatingInputImpl _$$UpdateListingRatingInputImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateListingRatingInputImpl(
      listingId: json['listing_id'] as String?,
      sessionId: json['session_id'] as String?,
      experienceRating: (json['experience_rating'] as num).toInt(),
      listingRating: (json['listing_rating'] as num).toInt(),
      appRating: (json['app_rating'] as num).toInt(),
      review: json['review'] as String?,
      listingRatingId: json['listing_rating_id'] as String,
    );

Map<String, dynamic> _$$UpdateListingRatingInputImplToJson(
        _$UpdateListingRatingInputImpl instance) =>
    <String, dynamic>{
      'listing_id': instance.listingId,
      'session_id': instance.sessionId,
      'experience_rating': instance.experienceRating,
      'listing_rating': instance.listingRating,
      'app_rating': instance.appRating,
      'review': instance.review,
      'listing_rating_id': instance.listingRatingId,
    };
