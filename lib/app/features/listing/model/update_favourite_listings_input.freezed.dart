// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_favourite_listings_input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UpdateFavouriteListingsInput _$UpdateFavouriteListingsInputFromJson(
    Map<String, dynamic> json) {
  return _UpdateFavouriteListingsInput.fromJson(json);
}

/// @nodoc
mixin _$UpdateFavouriteListingsInput {
  String get listingId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UpdateFavouriteListingsInputCopyWith<UpdateFavouriteListingsInput>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateFavouriteListingsInputCopyWith<$Res> {
  factory $UpdateFavouriteListingsInputCopyWith(
          UpdateFavouriteListingsInput value,
          $Res Function(UpdateFavouriteListingsInput) then) =
      _$UpdateFavouriteListingsInputCopyWithImpl<$Res,
          UpdateFavouriteListingsInput>;
  @useResult
  $Res call({String listingId});
}

/// @nodoc
class _$UpdateFavouriteListingsInputCopyWithImpl<$Res,
        $Val extends UpdateFavouriteListingsInput>
    implements $UpdateFavouriteListingsInputCopyWith<$Res> {
  _$UpdateFavouriteListingsInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
  }) {
    return _then(_value.copyWith(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateFavouriteListingsInputImplCopyWith<$Res>
    implements $UpdateFavouriteListingsInputCopyWith<$Res> {
  factory _$$UpdateFavouriteListingsInputImplCopyWith(
          _$UpdateFavouriteListingsInputImpl value,
          $Res Function(_$UpdateFavouriteListingsInputImpl) then) =
      __$$UpdateFavouriteListingsInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String listingId});
}

/// @nodoc
class __$$UpdateFavouriteListingsInputImplCopyWithImpl<$Res>
    extends _$UpdateFavouriteListingsInputCopyWithImpl<$Res,
        _$UpdateFavouriteListingsInputImpl>
    implements _$$UpdateFavouriteListingsInputImplCopyWith<$Res> {
  __$$UpdateFavouriteListingsInputImplCopyWithImpl(
      _$UpdateFavouriteListingsInputImpl _value,
      $Res Function(_$UpdateFavouriteListingsInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
  }) {
    return _then(_$UpdateFavouriteListingsInputImpl(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateFavouriteListingsInputImpl
    implements _UpdateFavouriteListingsInput {
  const _$UpdateFavouriteListingsInputImpl({required this.listingId});

  factory _$UpdateFavouriteListingsInputImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UpdateFavouriteListingsInputImplFromJson(json);

  @override
  final String listingId;

  @override
  String toString() {
    return 'UpdateFavouriteListingsInput(listingId: $listingId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateFavouriteListingsInputImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, listingId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateFavouriteListingsInputImplCopyWith<
          _$UpdateFavouriteListingsInputImpl>
      get copyWith => __$$UpdateFavouriteListingsInputImplCopyWithImpl<
          _$UpdateFavouriteListingsInputImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateFavouriteListingsInputImplToJson(
      this,
    );
  }
}

abstract class _UpdateFavouriteListingsInput
    implements UpdateFavouriteListingsInput {
  const factory _UpdateFavouriteListingsInput(
      {required final String listingId}) = _$UpdateFavouriteListingsInputImpl;

  factory _UpdateFavouriteListingsInput.fromJson(Map<String, dynamic> json) =
      _$UpdateFavouriteListingsInputImpl.fromJson;

  @override
  String get listingId;
  @override
  @JsonKey(ignore: true)
  _$$UpdateFavouriteListingsInputImplCopyWith<
          _$UpdateFavouriteListingsInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
