import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'listing_files.freezed.dart';
part 'listing_files.g.dart';

@freezed
class ListingFile with _$ListingFile {
  @CustomDateTimeConverter()
  factory ListingFile({
    required String id,
    String? listingId,
    String? imageUrl,
    @Default(false) bool isMain,
    @Default(false) bool isApproved,
    String? notApprovedReason,
    @Default(false) bool isHidden,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ListingFile;

  factory ListingFile.fromJson(Json json) => _$ListingFileFromJson(json);
}
