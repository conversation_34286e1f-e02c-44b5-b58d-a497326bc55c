import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'all_listings_input.freezed.dart';
part 'all_listings_input.g.dart';

@freezed
class AllListingsInput with _$AllListingsInput {
  const factory AllListingsInput({
    required double lat,
    required double lon,
    String? keywords,
  }) = _AllListingsInput;

  factory AllListingsInput.fromJson(Json json) =>
      _$AllListingsInputFromJson(json);
}
