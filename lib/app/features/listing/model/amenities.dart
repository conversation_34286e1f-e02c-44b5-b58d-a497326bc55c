import 'package:flutter/widgets.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';

export 'amenities.dart';

part 'amenities.freezed.dart';
part 'amenities.g.dart';

@freezed
class Amenity with _$Amenity {
  @CustomDateTimeConverter()
  factory Amenity({
    required String id,
    required String name,
    required String slug,
    String? description,
    required String fontIconName,
    @Default(false) bool isHidden,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Amenity;

  factory Amenity.fromJson(Json json) => _$AmenityFromJson(json);
}

@freezed
class AmenitiesResponse with _$AmenitiesResponse {
  factory AmenitiesResponse({
    required List<Amenity> data,
    required Pagination meta,
  }) = _AmenitiesResponse;

  factory AmenitiesResponse.fromJson(Json json) =>
      _$AmenitiesResponseFromJson(json);
}

final Map<String, IconData> amenityIconMap = {
  'automated-disinfection': CustomIcon.disinfect,
  'diaper-changing': CustomIcon.diaperChanging,
  'hot-and-cold-water-dispenser': CustomIcon.hotAndColdWaterDispenser,
  'mirror': CustomIcon.mirror,
  'paper-towel-dispenser': CustomIcon.paperTowelMachine,
  'power-socket': CustomIcon.powerSocket,
  'private-feeding-room': CustomIcon.lock,
  'private-feeding-room-with-curtain': CustomIcon.curtain,
  'seat': CustomIcon.chair,
  'sink': CustomIcon.sink,
  'table-top': CustomIcon.table,
  'waste-bin': CustomIcon.bin,
};
