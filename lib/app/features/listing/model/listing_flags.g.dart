// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_flags.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingFlagImpl _$$ListingFlagImplFromJson(Map<String, dynamic> json) =>
    _$ListingFlagImpl(
      id: json['id'] as String,
      category: _$JsonConverterFromJson<String, ListingFlagCategory>(
          json['category'], const ListingFlagCategoryConverter().fromJson),
      reason: json['reason'] as String?,
      referenceImages: (json['reference_images'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      reviewedAt: const CustomDateTimeConverter()
          .fromJson(json['reviewed_at'] as String?),
      action: $enumDecodeNullable(_$ListingFlagActionEnumMap, json['action']),
      actionReason: json['action_reason'] as String?,
      actionBy: json['action_by'] as String?,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$ListingFlagImplToJson(_$ListingFlagImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'category': _$JsonConverterToJson<String, ListingFlagCategory>(
          instance.category, const ListingFlagCategoryConverter().toJson),
      'reason': instance.reason,
      'reference_images': instance.referenceImages,
      'reviewed_at':
          const CustomDateTimeConverter().toJson(instance.reviewedAt),
      'action': _$ListingFlagActionEnumMap[instance.action],
      'action_reason': instance.actionReason,
      'action_by': instance.actionBy,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

const _$ListingFlagActionEnumMap = {
  ListingFlagAction.hideListing: 'hideListing',
  ListingFlagAction.noAction: 'noAction',
  ListingFlagAction.other: 'other',
};

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

_$CreateListingFlagInputImpl _$$CreateListingFlagInputImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateListingFlagInputImpl(
      listingId: json['listing_id'] as String,
      category: json['category'] as String,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$$CreateListingFlagInputImplToJson(
        _$CreateListingFlagInputImpl instance) =>
    <String, dynamic>{
      'listing_id': instance.listingId,
      'category': instance.category,
      'reason': instance.reason,
    };
