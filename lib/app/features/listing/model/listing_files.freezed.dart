// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'listing_files.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListingFile _$ListingFileFromJson(Map<String, dynamic> json) {
  return _ListingFile.fromJson(json);
}

/// @nodoc
mixin _$ListingFile {
  String get id => throw _privateConstructorUsedError;
  String? get listingId => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  bool get isMain => throw _privateConstructorUsedError;
  bool get isApproved => throw _privateConstructorUsedError;
  String? get notApprovedReason => throw _privateConstructorUsedError;
  bool get isHidden => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingFileCopyWith<ListingFile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingFileCopyWith<$Res> {
  factory $ListingFileCopyWith(
          ListingFile value, $Res Function(ListingFile) then) =
      _$ListingFileCopyWithImpl<$Res, ListingFile>;
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? imageUrl,
      bool isMain,
      bool isApproved,
      String? notApprovedReason,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$ListingFileCopyWithImpl<$Res, $Val extends ListingFile>
    implements $ListingFileCopyWith<$Res> {
  _$ListingFileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? imageUrl = freezed,
    Object? isMain = null,
    Object? isApproved = null,
    Object? notApprovedReason = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isMain: null == isMain
          ? _value.isMain
          : isMain // ignore: cast_nullable_to_non_nullable
              as bool,
      isApproved: null == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool,
      notApprovedReason: freezed == notApprovedReason
          ? _value.notApprovedReason
          : notApprovedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingFileImplCopyWith<$Res>
    implements $ListingFileCopyWith<$Res> {
  factory _$$ListingFileImplCopyWith(
          _$ListingFileImpl value, $Res Function(_$ListingFileImpl) then) =
      __$$ListingFileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? imageUrl,
      bool isMain,
      bool isApproved,
      String? notApprovedReason,
      bool isHidden,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$ListingFileImplCopyWithImpl<$Res>
    extends _$ListingFileCopyWithImpl<$Res, _$ListingFileImpl>
    implements _$$ListingFileImplCopyWith<$Res> {
  __$$ListingFileImplCopyWithImpl(
      _$ListingFileImpl _value, $Res Function(_$ListingFileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? imageUrl = freezed,
    Object? isMain = null,
    Object? isApproved = null,
    Object? notApprovedReason = freezed,
    Object? isHidden = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ListingFileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isMain: null == isMain
          ? _value.isMain
          : isMain // ignore: cast_nullable_to_non_nullable
              as bool,
      isApproved: null == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool,
      notApprovedReason: freezed == notApprovedReason
          ? _value.notApprovedReason
          : notApprovedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$ListingFileImpl implements _ListingFile {
  _$ListingFileImpl(
      {required this.id,
      this.listingId,
      this.imageUrl,
      this.isMain = false,
      this.isApproved = false,
      this.notApprovedReason,
      this.isHidden = false,
      this.createdAt,
      this.updatedAt});

  factory _$ListingFileImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingFileImplFromJson(json);

  @override
  final String id;
  @override
  final String? listingId;
  @override
  final String? imageUrl;
  @override
  @JsonKey()
  final bool isMain;
  @override
  @JsonKey()
  final bool isApproved;
  @override
  final String? notApprovedReason;
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ListingFile(id: $id, listingId: $listingId, imageUrl: $imageUrl, isMain: $isMain, isApproved: $isApproved, notApprovedReason: $notApprovedReason, isHidden: $isHidden, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingFileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.isMain, isMain) || other.isMain == isMain) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            (identical(other.notApprovedReason, notApprovedReason) ||
                other.notApprovedReason == notApprovedReason) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, listingId, imageUrl, isMain,
      isApproved, notApprovedReason, isHidden, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingFileImplCopyWith<_$ListingFileImpl> get copyWith =>
      __$$ListingFileImplCopyWithImpl<_$ListingFileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingFileImplToJson(
      this,
    );
  }
}

abstract class _ListingFile implements ListingFile {
  factory _ListingFile(
      {required final String id,
      final String? listingId,
      final String? imageUrl,
      final bool isMain,
      final bool isApproved,
      final String? notApprovedReason,
      final bool isHidden,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ListingFileImpl;

  factory _ListingFile.fromJson(Map<String, dynamic> json) =
      _$ListingFileImpl.fromJson;

  @override
  String get id;
  @override
  String? get listingId;
  @override
  String? get imageUrl;
  @override
  bool get isMain;
  @override
  bool get isApproved;
  @override
  String? get notApprovedReason;
  @override
  bool get isHidden;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ListingFileImplCopyWith<_$ListingFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
