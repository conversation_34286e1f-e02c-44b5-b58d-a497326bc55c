// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listing_files.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingFileImpl _$$ListingFileImplFromJson(Map<String, dynamic> json) =>
    _$ListingFileImpl(
      id: json['id'] as String,
      listingId: json['listing_id'] as String?,
      imageUrl: json['image_url'] as String?,
      isMain: json['is_main'] as bool? ?? false,
      isApproved: json['is_approved'] as bool? ?? false,
      notApprovedReason: json['not_approved_reason'] as String?,
      isHidden: json['is_hidden'] as bool? ?? false,
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
    );

Map<String, dynamic> _$$ListingFileImplToJson(_$ListingFileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'listing_id': instance.listingId,
      'image_url': instance.imageUrl,
      'is_main': instance.isMain,
      'is_approved': instance.isApproved,
      'not_approved_reason': instance.notApprovedReason,
      'is_hidden': instance.isHidden,
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
    };
