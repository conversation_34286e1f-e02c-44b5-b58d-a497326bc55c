import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/explore/model/nearby_listing_input.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listing_files.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';

part 'listings.freezed.dart';
part 'listings.g.dart';

enum ListingStatus {
  idle,
  occupied,
  disinfecting,
}

enum ListingType {
  gomama,
  care,
}

@freezed
class Listing with _$Listing {
  @CustomDateTimeConverter()
  const factory Listing({
    required String id,
    String? firestoreId,
    String? name,
    String? companyName,
    String? description,
    String? fullAddress,
    String? openingHours,
    String? postalCode,
    String? fullContactNumber,
    ListingPosition? position,
    // List<ListingRating>? listingRatings,
    double? averageExperienceRatings,
    int? totalExperienceRatings,
    int? totalSessions,
    int? maxNumberOfUsageExtensions,
    List<ListingFile>? listingFiles,
    List<ListingRating>? listingRatings,
    List<Amenity>? amenities,
    String? imageUrl,
    ListingStatus? status,
    ListingType? listingType,
    double? distance,
    @Default(false) bool isHidden,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Listing;

  const Listing._();

  factory Listing.fromJson(Json json) => _$ListingFromJson(json);

  String? get previewImage {
    if (listingFiles?.isNotEmpty ?? false) {
      return listingFiles!
              .firstWhereOrNull((element) => element.isMain)
              ?.imageUrl ??
          listingFiles!.first.imageUrl;
    }

    return null;
  }
}

@freezed
class ListingPosition with _$ListingPosition {
  const factory ListingPosition({
    required ListingCoordinate coordinate,
  }) = _ListingPosition;

  factory ListingPosition.fromJson(Json json) =>
      _$ListingPositionFromJson(json);
}

@freezed
class ListingCoordinate with _$ListingCoordinate {
  const factory ListingCoordinate({
    required double x,
    required double y,
  }) = _ListingCoordinate;

  factory ListingCoordinate.fromJson(Json json) =>
      _$ListingCoordinateFromJson(json);
}

@freezed
class ListingsResponse with _$ListingsResponse {
  const factory ListingsResponse({
    required List<Listing> data,
    required Pagination meta,
  }) = _ListingsResponse;

  factory ListingsResponse.fromJson(Json json) =>
      _$ListingsResponseFromJson(json);
}

@freezed
class AllListingsPagination with _$AllListingsPagination {
  const factory AllListingsPagination({
    required int page,
    AllListingsInput? input,
  }) = _AllListingsPagination;
}

@freezed
class AllListingsOffset with _$AllListingsOffset {
  const factory AllListingsOffset({
    required int offset,
    AllListingsInput? input,
  }) = _AllListingsOffset;
}

@freezed
class NearbyListingsPagination with _$NearbyListingsPagination {
  const factory NearbyListingsPagination({
    required int page,
    NearbyListingsInput? input,
  }) = _NearbyListingsPagination;
}

@freezed
class NearbyListingsOffset with _$NearbyListingsOffset {
  const factory NearbyListingsOffset({
    required int offset,
    NearbyListingsInput? input,
  }) = _NearbyListingsOffset;
}
