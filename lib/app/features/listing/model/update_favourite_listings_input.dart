import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/web_providers.dart';

part 'update_favourite_listings_input.freezed.dart';
part 'update_favourite_listings_input.g.dart';

@freezed
class UpdateFavouriteListingsInput with _$UpdateFavouriteListingsInput {
  const factory UpdateFavouriteListingsInput({
    required String listingId,
  }) = _UpdateFavouriteListingsInput;

  factory UpdateFavouriteListingsInput.fromJson(Json json) =>
      _$UpdateFavouriteListingsInputFromJson(json);
}
