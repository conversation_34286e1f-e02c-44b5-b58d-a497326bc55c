// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_listing_rating_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$submitReviewHash() => r'4f22e36159482a2a27bdc7c345ae4e102d38f3f6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [submitReview].
@ProviderFor(submitReview)
const submitReviewProvider = SubmitReviewFamily();

/// See also [submitReview].
class SubmitReviewFamily extends Family<AsyncValue<void>> {
  /// See also [submitReview].
  const SubmitReviewFamily();

  /// See also [submitReview].
  SubmitReviewProvider call(
    Map<String, dynamic> data,
  ) {
    return SubmitReviewProvider(
      data,
    );
  }

  @override
  SubmitReviewProvider getProviderOverride(
    covariant SubmitReviewProvider provider,
  ) {
    return call(
      provider.data,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'submitReviewProvider';
}

/// See also [submitReview].
class SubmitReviewProvider extends AutoDisposeFutureProvider<void> {
  /// See also [submitReview].
  SubmitReviewProvider(
    Map<String, dynamic> data,
  ) : this._internal(
          (ref) => submitReview(
            ref as SubmitReviewRef,
            data,
          ),
          from: submitReviewProvider,
          name: r'submitReviewProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$submitReviewHash,
          dependencies: SubmitReviewFamily._dependencies,
          allTransitiveDependencies:
              SubmitReviewFamily._allTransitiveDependencies,
          data: data,
        );

  SubmitReviewProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.data,
  }) : super.internal();

  final Map<String, dynamic> data;

  @override
  Override overrideWith(
    FutureOr<void> Function(SubmitReviewRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubmitReviewProvider._internal(
        (ref) => create(ref as SubmitReviewRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        data: data,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _SubmitReviewProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubmitReviewProvider && other.data == data;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, data.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SubmitReviewRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `data` of this provider.
  Map<String, dynamic> get data;
}

class _SubmitReviewProviderElement
    extends AutoDisposeFutureProviderElement<void> with SubmitReviewRef {
  _SubmitReviewProviderElement(super.provider);

  @override
  Map<String, dynamic> get data => (origin as SubmitReviewProvider).data;
}

String _$updateReviewHash() => r'30dbb1ef8da921c7e039a2674425567c87378a1e';

/// See also [updateReview].
@ProviderFor(updateReview)
const updateReviewProvider = UpdateReviewFamily();

/// See also [updateReview].
class UpdateReviewFamily extends Family<AsyncValue<void>> {
  /// See also [updateReview].
  const UpdateReviewFamily();

  /// See also [updateReview].
  UpdateReviewProvider call(
    Map<String, dynamic> data,
  ) {
    return UpdateReviewProvider(
      data,
    );
  }

  @override
  UpdateReviewProvider getProviderOverride(
    covariant UpdateReviewProvider provider,
  ) {
    return call(
      provider.data,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'updateReviewProvider';
}

/// See also [updateReview].
class UpdateReviewProvider extends AutoDisposeFutureProvider<void> {
  /// See also [updateReview].
  UpdateReviewProvider(
    Map<String, dynamic> data,
  ) : this._internal(
          (ref) => updateReview(
            ref as UpdateReviewRef,
            data,
          ),
          from: updateReviewProvider,
          name: r'updateReviewProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$updateReviewHash,
          dependencies: UpdateReviewFamily._dependencies,
          allTransitiveDependencies:
              UpdateReviewFamily._allTransitiveDependencies,
          data: data,
        );

  UpdateReviewProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.data,
  }) : super.internal();

  final Map<String, dynamic> data;

  @override
  Override overrideWith(
    FutureOr<void> Function(UpdateReviewRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateReviewProvider._internal(
        (ref) => create(ref as UpdateReviewRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        data: data,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _UpdateReviewProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateReviewProvider && other.data == data;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, data.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin UpdateReviewRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `data` of this provider.
  Map<String, dynamic> get data;
}

class _UpdateReviewProviderElement
    extends AutoDisposeFutureProviderElement<void> with UpdateReviewRef {
  _UpdateReviewProviderElement(super.provider);

  @override
  Map<String, dynamic> get data => (origin as UpdateReviewProvider).data;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
