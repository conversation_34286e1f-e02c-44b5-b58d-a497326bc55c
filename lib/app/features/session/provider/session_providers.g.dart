// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$createSessionHash() => r'56a8303ca358712a7125f80178c0fe86e57607c1';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [createSession].
@ProviderFor(createSession)
const createSessionProvider = CreateSessionFamily();

/// See also [createSession].
class CreateSessionFamily extends Family<AsyncValue<Session>> {
  /// See also [createSession].
  const CreateSessionFamily();

  /// See also [createSession].
  CreateSessionProvider call(
    CreateSessionInput input,
  ) {
    return CreateSessionProvider(
      input,
    );
  }

  @override
  CreateSessionProvider getProviderOverride(
    covariant CreateSessionProvider provider,
  ) {
    return call(
      provider.input,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'createSessionProvider';
}

/// See also [createSession].
class CreateSessionProvider extends AutoDisposeFutureProvider<Session> {
  /// See also [createSession].
  CreateSessionProvider(
    CreateSessionInput input,
  ) : this._internal(
          (ref) => createSession(
            ref as CreateSessionRef,
            input,
          ),
          from: createSessionProvider,
          name: r'createSessionProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$createSessionHash,
          dependencies: CreateSessionFamily._dependencies,
          allTransitiveDependencies:
              CreateSessionFamily._allTransitiveDependencies,
          input: input,
        );

  CreateSessionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.input,
  }) : super.internal();

  final CreateSessionInput input;

  @override
  Override overrideWith(
    FutureOr<Session> Function(CreateSessionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CreateSessionProvider._internal(
        (ref) => create(ref as CreateSessionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        input: input,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Session> createElement() {
    return _CreateSessionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CreateSessionProvider && other.input == input;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, input.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin CreateSessionRef on AutoDisposeFutureProviderRef<Session> {
  /// The parameter `input` of this provider.
  CreateSessionInput get input;
}

class _CreateSessionProviderElement
    extends AutoDisposeFutureProviderElement<Session> with CreateSessionRef {
  _CreateSessionProviderElement(super.provider);

  @override
  CreateSessionInput get input => (origin as CreateSessionProvider).input;
}

String _$sessionRegeneratePinHash() =>
    r'145c1b7c91a47891d72984b6b5b61135c9b99558';

/// See also [sessionRegeneratePin].
@ProviderFor(sessionRegeneratePin)
const sessionRegeneratePinProvider = SessionRegeneratePinFamily();

/// See also [sessionRegeneratePin].
class SessionRegeneratePinFamily extends Family<AsyncValue<Session>> {
  /// See also [sessionRegeneratePin].
  const SessionRegeneratePinFamily();

  /// See also [sessionRegeneratePin].
  SessionRegeneratePinProvider call(
    String sessionId,
  ) {
    return SessionRegeneratePinProvider(
      sessionId,
    );
  }

  @override
  SessionRegeneratePinProvider getProviderOverride(
    covariant SessionRegeneratePinProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sessionRegeneratePinProvider';
}

/// See also [sessionRegeneratePin].
class SessionRegeneratePinProvider extends AutoDisposeFutureProvider<Session> {
  /// See also [sessionRegeneratePin].
  SessionRegeneratePinProvider(
    String sessionId,
  ) : this._internal(
          (ref) => sessionRegeneratePin(
            ref as SessionRegeneratePinRef,
            sessionId,
          ),
          from: sessionRegeneratePinProvider,
          name: r'sessionRegeneratePinProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$sessionRegeneratePinHash,
          dependencies: SessionRegeneratePinFamily._dependencies,
          allTransitiveDependencies:
              SessionRegeneratePinFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  SessionRegeneratePinProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final String sessionId;

  @override
  Override overrideWith(
    FutureOr<Session> Function(SessionRegeneratePinRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SessionRegeneratePinProvider._internal(
        (ref) => create(ref as SessionRegeneratePinRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Session> createElement() {
    return _SessionRegeneratePinProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SessionRegeneratePinProvider &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SessionRegeneratePinRef on AutoDisposeFutureProviderRef<Session> {
  /// The parameter `sessionId` of this provider.
  String get sessionId;
}

class _SessionRegeneratePinProviderElement
    extends AutoDisposeFutureProviderElement<Session>
    with SessionRegeneratePinRef {
  _SessionRegeneratePinProviderElement(super.provider);

  @override
  String get sessionId => (origin as SessionRegeneratePinProvider).sessionId;
}

String _$extendSessionHash() => r'6b8c4f39494724635e1df203772310baf6ab2233';

/// See also [extendSession].
@ProviderFor(extendSession)
const extendSessionProvider = ExtendSessionFamily();

/// See also [extendSession].
class ExtendSessionFamily extends Family<AsyncValue<Session>> {
  /// See also [extendSession].
  const ExtendSessionFamily();

  /// See also [extendSession].
  ExtendSessionProvider call(
    String sessionId,
  ) {
    return ExtendSessionProvider(
      sessionId,
    );
  }

  @override
  ExtendSessionProvider getProviderOverride(
    covariant ExtendSessionProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'extendSessionProvider';
}

/// See also [extendSession].
class ExtendSessionProvider extends AutoDisposeFutureProvider<Session> {
  /// See also [extendSession].
  ExtendSessionProvider(
    String sessionId,
  ) : this._internal(
          (ref) => extendSession(
            ref as ExtendSessionRef,
            sessionId,
          ),
          from: extendSessionProvider,
          name: r'extendSessionProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$extendSessionHash,
          dependencies: ExtendSessionFamily._dependencies,
          allTransitiveDependencies:
              ExtendSessionFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  ExtendSessionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final String sessionId;

  @override
  Override overrideWith(
    FutureOr<Session> Function(ExtendSessionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ExtendSessionProvider._internal(
        (ref) => create(ref as ExtendSessionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Session> createElement() {
    return _ExtendSessionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ExtendSessionProvider && other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ExtendSessionRef on AutoDisposeFutureProviderRef<Session> {
  /// The parameter `sessionId` of this provider.
  String get sessionId;
}

class _ExtendSessionProviderElement
    extends AutoDisposeFutureProviderElement<Session> with ExtendSessionRef {
  _ExtendSessionProviderElement(super.provider);

  @override
  String get sessionId => (origin as ExtendSessionProvider).sessionId;
}

String _$endSessionHash() => r'c330a05bb0d6438d7a1ac0849db6d89461d619f8';

/// See also [endSession].
@ProviderFor(endSession)
const endSessionProvider = EndSessionFamily();

/// See also [endSession].
class EndSessionFamily extends Family<AsyncValue<Session>> {
  /// See also [endSession].
  const EndSessionFamily();

  /// See also [endSession].
  EndSessionProvider call({
    bool? isRestart,
  }) {
    return EndSessionProvider(
      isRestart: isRestart,
    );
  }

  @override
  EndSessionProvider getProviderOverride(
    covariant EndSessionProvider provider,
  ) {
    return call(
      isRestart: provider.isRestart,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'endSessionProvider';
}

/// See also [endSession].
class EndSessionProvider extends AutoDisposeFutureProvider<Session> {
  /// See also [endSession].
  EndSessionProvider({
    bool? isRestart,
  }) : this._internal(
          (ref) => endSession(
            ref as EndSessionRef,
            isRestart: isRestart,
          ),
          from: endSessionProvider,
          name: r'endSessionProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$endSessionHash,
          dependencies: EndSessionFamily._dependencies,
          allTransitiveDependencies:
              EndSessionFamily._allTransitiveDependencies,
          isRestart: isRestart,
        );

  EndSessionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.isRestart,
  }) : super.internal();

  final bool? isRestart;

  @override
  Override overrideWith(
    FutureOr<Session> Function(EndSessionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: EndSessionProvider._internal(
        (ref) => create(ref as EndSessionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        isRestart: isRestart,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Session> createElement() {
    return _EndSessionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is EndSessionProvider && other.isRestart == isRestart;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, isRestart.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin EndSessionRef on AutoDisposeFutureProviderRef<Session> {
  /// The parameter `isRestart` of this provider.
  bool? get isRestart;
}

class _EndSessionProviderElement
    extends AutoDisposeFutureProviderElement<Session> with EndSessionRef {
  _EndSessionProviderElement(super.provider);

  @override
  bool? get isRestart => (origin as EndSessionProvider).isRestart;
}

String _$allSessionsHash() => r'7a192980af328e52c4fc9278c51e05fa724c0a09';

/// See also [AllSessions].
@ProviderFor(AllSessions)
final allSessionsProvider =
    AutoDisposeAsyncNotifierProvider<AllSessions, SessionsResponse?>.internal(
  AllSessions.new,
  name: r'allSessionsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$allSessionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AllSessions = AutoDisposeAsyncNotifier<SessionsResponse?>;
String _$singleSessionHash() => r'e185e69c5e35caf7893cfefe4c0de9e140409007';

abstract class _$SingleSession
    extends BuildlessAutoDisposeAsyncNotifier<Session?> {
  late final String id;

  FutureOr<Session?> build(
    String id,
  );
}

/// See also [SingleSession].
@ProviderFor(SingleSession)
const singleSessionProvider = SingleSessionFamily();

/// See also [SingleSession].
class SingleSessionFamily extends Family<AsyncValue<Session?>> {
  /// See also [SingleSession].
  const SingleSessionFamily();

  /// See also [SingleSession].
  SingleSessionProvider call(
    String id,
  ) {
    return SingleSessionProvider(
      id,
    );
  }

  @override
  SingleSessionProvider getProviderOverride(
    covariant SingleSessionProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'singleSessionProvider';
}

/// See also [SingleSession].
class SingleSessionProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SingleSession, Session?> {
  /// See also [SingleSession].
  SingleSessionProvider(
    String id,
  ) : this._internal(
          () => SingleSession()..id = id,
          from: singleSessionProvider,
          name: r'singleSessionProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$singleSessionHash,
          dependencies: SingleSessionFamily._dependencies,
          allTransitiveDependencies:
              SingleSessionFamily._allTransitiveDependencies,
          id: id,
        );

  SingleSessionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  FutureOr<Session?> runNotifierBuild(
    covariant SingleSession notifier,
  ) {
    return notifier.build(
      id,
    );
  }

  @override
  Override overrideWith(SingleSession Function() create) {
    return ProviderOverride(
      origin: this,
      override: SingleSessionProvider._internal(
        () => create()..id = id,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SingleSession, Session?>
      createElement() {
    return _SingleSessionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SingleSessionProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin SingleSessionRef on AutoDisposeAsyncNotifierProviderRef<Session?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _SingleSessionProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SingleSession, Session?>
    with SingleSessionRef {
  _SingleSessionProviderElement(super.provider);

  @override
  String get id => (origin as SingleSessionProvider).id;
}

String _$activeSessionHash() => r'1bf1e4a234f989f533a8d4631f15c8bb317f3b5b';

/// See also [ActiveSession].
@ProviderFor(ActiveSession)
final activeSessionProvider =
    AsyncNotifierProvider<ActiveSession, SessionEvent?>.internal(
  ActiveSession.new,
  name: r'activeSessionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activeSessionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ActiveSession = AsyncNotifier<SessionEvent?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
