import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:gomama/app/features/listing/repository/listing_rating_repository.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_listing_rating_providers.g.dart';

@riverpod
Future<void> submitReview(SubmitReviewRef ref, Json data) {
  final input = CreateListingRatingInput.fromJson(data);
  Groveman.debug('submitReview', error: input);
  return ref.watch(listingRatingRepositoryProvider).submitRating(input);
}

@riverpod
Future<void> updateReview(UpdateReviewRef ref, Json data) {
  final input = UpdateListingRatingInput.fromJson(data);
  Groveman.debug('updateReview', error: input);
  return ref.watch(listingRatingRepositoryProvider).updateRating(input);
}
