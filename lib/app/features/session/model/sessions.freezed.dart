// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sessions.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Session _$SessionFromJson(Map<String, dynamic> json) {
  return _Session.fromJson(json);
}

/// @nodoc
mixin _$Session {
  String get id => throw _privateConstructorUsedError;
  String? get listingId => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  String? get lockBluetoothGuestKey => throw _privateConstructorUsedError;
  String? get lockCustomPin => throw _privateConstructorUsedError;
  String? get lockDailyPin => throw _privateConstructorUsedError;
  String? get lockHourlyPin => throw _privateConstructorUsedError;
  String? get lockOneTimePin => throw _privateConstructorUsedError;
  double? get expectedUsageDuration => throw _privateConstructorUsedError;
  double? get actualUsageDuration => throw _privateConstructorUsedError;
  int? get numberOfUsageExtensions =>
      throw _privateConstructorUsedError; // @Default(false) bool isEnded,
// @Default(false) bool isUsageExtended,
  bool get isHidden => throw _privateConstructorUsedError;
  Listing? get listing => throw _privateConstructorUsedError;
  PartialUser? get user => throw _privateConstructorUsedError;
  ListingRating? get listingRating => throw _privateConstructorUsedError;
  DateTime? get startedAt => throw _privateConstructorUsedError;
  DateTime? get expectedEndedAt => throw _privateConstructorUsedError;
  DateTime? get actualEndedAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  String? get firestoreId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionCopyWith<Session> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionCopyWith<$Res> {
  factory $SessionCopyWith(Session value, $Res Function(Session) then) =
      _$SessionCopyWithImpl<$Res, Session>;
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? userId,
      String? lockBluetoothGuestKey,
      String? lockCustomPin,
      String? lockDailyPin,
      String? lockHourlyPin,
      String? lockOneTimePin,
      double? expectedUsageDuration,
      double? actualUsageDuration,
      int? numberOfUsageExtensions,
      bool isHidden,
      Listing? listing,
      PartialUser? user,
      ListingRating? listingRating,
      DateTime? startedAt,
      DateTime? expectedEndedAt,
      DateTime? actualEndedAt,
      DateTime? createdAt,
      DateTime? updatedAt,
      String? firestoreId});

  $ListingCopyWith<$Res>? get listing;
  $ListingRatingCopyWith<$Res>? get listingRating;
}

/// @nodoc
class _$SessionCopyWithImpl<$Res, $Val extends Session>
    implements $SessionCopyWith<$Res> {
  _$SessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? userId = freezed,
    Object? lockBluetoothGuestKey = freezed,
    Object? lockCustomPin = freezed,
    Object? lockDailyPin = freezed,
    Object? lockHourlyPin = freezed,
    Object? lockOneTimePin = freezed,
    Object? expectedUsageDuration = freezed,
    Object? actualUsageDuration = freezed,
    Object? numberOfUsageExtensions = freezed,
    Object? isHidden = null,
    Object? listing = freezed,
    Object? user = freezed,
    Object? listingRating = freezed,
    Object? startedAt = freezed,
    Object? expectedEndedAt = freezed,
    Object? actualEndedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? firestoreId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      lockBluetoothGuestKey: freezed == lockBluetoothGuestKey
          ? _value.lockBluetoothGuestKey
          : lockBluetoothGuestKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lockCustomPin: freezed == lockCustomPin
          ? _value.lockCustomPin
          : lockCustomPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockDailyPin: freezed == lockDailyPin
          ? _value.lockDailyPin
          : lockDailyPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockHourlyPin: freezed == lockHourlyPin
          ? _value.lockHourlyPin
          : lockHourlyPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockOneTimePin: freezed == lockOneTimePin
          ? _value.lockOneTimePin
          : lockOneTimePin // ignore: cast_nullable_to_non_nullable
              as String?,
      expectedUsageDuration: freezed == expectedUsageDuration
          ? _value.expectedUsageDuration
          : expectedUsageDuration // ignore: cast_nullable_to_non_nullable
              as double?,
      actualUsageDuration: freezed == actualUsageDuration
          ? _value.actualUsageDuration
          : actualUsageDuration // ignore: cast_nullable_to_non_nullable
              as double?,
      numberOfUsageExtensions: freezed == numberOfUsageExtensions
          ? _value.numberOfUsageExtensions
          : numberOfUsageExtensions // ignore: cast_nullable_to_non_nullable
              as int?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      listing: freezed == listing
          ? _value.listing
          : listing // ignore: cast_nullable_to_non_nullable
              as Listing?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as PartialUser?,
      listingRating: freezed == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as ListingRating?,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedEndedAt: freezed == expectedEndedAt
          ? _value.expectedEndedAt
          : expectedEndedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualEndedAt: freezed == actualEndedAt
          ? _value.actualEndedAt
          : actualEndedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firestoreId: freezed == firestoreId
          ? _value.firestoreId
          : firestoreId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingCopyWith<$Res>? get listing {
    if (_value.listing == null) {
      return null;
    }

    return $ListingCopyWith<$Res>(_value.listing!, (value) {
      return _then(_value.copyWith(listing: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ListingRatingCopyWith<$Res>? get listingRating {
    if (_value.listingRating == null) {
      return null;
    }

    return $ListingRatingCopyWith<$Res>(_value.listingRating!, (value) {
      return _then(_value.copyWith(listingRating: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SessionImplCopyWith<$Res> implements $SessionCopyWith<$Res> {
  factory _$$SessionImplCopyWith(
          _$SessionImpl value, $Res Function(_$SessionImpl) then) =
      __$$SessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? listingId,
      String? userId,
      String? lockBluetoothGuestKey,
      String? lockCustomPin,
      String? lockDailyPin,
      String? lockHourlyPin,
      String? lockOneTimePin,
      double? expectedUsageDuration,
      double? actualUsageDuration,
      int? numberOfUsageExtensions,
      bool isHidden,
      Listing? listing,
      PartialUser? user,
      ListingRating? listingRating,
      DateTime? startedAt,
      DateTime? expectedEndedAt,
      DateTime? actualEndedAt,
      DateTime? createdAt,
      DateTime? updatedAt,
      String? firestoreId});

  @override
  $ListingCopyWith<$Res>? get listing;
  @override
  $ListingRatingCopyWith<$Res>? get listingRating;
}

/// @nodoc
class __$$SessionImplCopyWithImpl<$Res>
    extends _$SessionCopyWithImpl<$Res, _$SessionImpl>
    implements _$$SessionImplCopyWith<$Res> {
  __$$SessionImplCopyWithImpl(
      _$SessionImpl _value, $Res Function(_$SessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? listingId = freezed,
    Object? userId = freezed,
    Object? lockBluetoothGuestKey = freezed,
    Object? lockCustomPin = freezed,
    Object? lockDailyPin = freezed,
    Object? lockHourlyPin = freezed,
    Object? lockOneTimePin = freezed,
    Object? expectedUsageDuration = freezed,
    Object? actualUsageDuration = freezed,
    Object? numberOfUsageExtensions = freezed,
    Object? isHidden = null,
    Object? listing = freezed,
    Object? user = freezed,
    Object? listingRating = freezed,
    Object? startedAt = freezed,
    Object? expectedEndedAt = freezed,
    Object? actualEndedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? firestoreId = freezed,
  }) {
    return _then(_$SessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      listingId: freezed == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      lockBluetoothGuestKey: freezed == lockBluetoothGuestKey
          ? _value.lockBluetoothGuestKey
          : lockBluetoothGuestKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lockCustomPin: freezed == lockCustomPin
          ? _value.lockCustomPin
          : lockCustomPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockDailyPin: freezed == lockDailyPin
          ? _value.lockDailyPin
          : lockDailyPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockHourlyPin: freezed == lockHourlyPin
          ? _value.lockHourlyPin
          : lockHourlyPin // ignore: cast_nullable_to_non_nullable
              as String?,
      lockOneTimePin: freezed == lockOneTimePin
          ? _value.lockOneTimePin
          : lockOneTimePin // ignore: cast_nullable_to_non_nullable
              as String?,
      expectedUsageDuration: freezed == expectedUsageDuration
          ? _value.expectedUsageDuration
          : expectedUsageDuration // ignore: cast_nullable_to_non_nullable
              as double?,
      actualUsageDuration: freezed == actualUsageDuration
          ? _value.actualUsageDuration
          : actualUsageDuration // ignore: cast_nullable_to_non_nullable
              as double?,
      numberOfUsageExtensions: freezed == numberOfUsageExtensions
          ? _value.numberOfUsageExtensions
          : numberOfUsageExtensions // ignore: cast_nullable_to_non_nullable
              as int?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      listing: freezed == listing
          ? _value.listing
          : listing // ignore: cast_nullable_to_non_nullable
              as Listing?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as PartialUser?,
      listingRating: freezed == listingRating
          ? _value.listingRating
          : listingRating // ignore: cast_nullable_to_non_nullable
              as ListingRating?,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedEndedAt: freezed == expectedEndedAt
          ? _value.expectedEndedAt
          : expectedEndedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualEndedAt: freezed == actualEndedAt
          ? _value.actualEndedAt
          : actualEndedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firestoreId: freezed == firestoreId
          ? _value.firestoreId
          : firestoreId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
@CustomDateTimeConverter()
class _$SessionImpl extends _Session {
  _$SessionImpl(
      {required this.id,
      this.listingId,
      this.userId,
      this.lockBluetoothGuestKey,
      this.lockCustomPin,
      this.lockDailyPin,
      this.lockHourlyPin,
      this.lockOneTimePin,
      this.expectedUsageDuration,
      this.actualUsageDuration,
      this.numberOfUsageExtensions,
      this.isHidden = false,
      this.listing,
      this.user,
      this.listingRating,
      this.startedAt,
      this.expectedEndedAt,
      this.actualEndedAt,
      this.createdAt,
      this.updatedAt,
      this.firestoreId})
      : super._();

  factory _$SessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionImplFromJson(json);

  @override
  final String id;
  @override
  final String? listingId;
  @override
  final String? userId;
  @override
  final String? lockBluetoothGuestKey;
  @override
  final String? lockCustomPin;
  @override
  final String? lockDailyPin;
  @override
  final String? lockHourlyPin;
  @override
  final String? lockOneTimePin;
  @override
  final double? expectedUsageDuration;
  @override
  final double? actualUsageDuration;
  @override
  final int? numberOfUsageExtensions;
// @Default(false) bool isEnded,
// @Default(false) bool isUsageExtended,
  @override
  @JsonKey()
  final bool isHidden;
  @override
  final Listing? listing;
  @override
  final PartialUser? user;
  @override
  final ListingRating? listingRating;
  @override
  final DateTime? startedAt;
  @override
  final DateTime? expectedEndedAt;
  @override
  final DateTime? actualEndedAt;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final String? firestoreId;

  @override
  String toString() {
    return 'Session(id: $id, listingId: $listingId, userId: $userId, lockBluetoothGuestKey: $lockBluetoothGuestKey, lockCustomPin: $lockCustomPin, lockDailyPin: $lockDailyPin, lockHourlyPin: $lockHourlyPin, lockOneTimePin: $lockOneTimePin, expectedUsageDuration: $expectedUsageDuration, actualUsageDuration: $actualUsageDuration, numberOfUsageExtensions: $numberOfUsageExtensions, isHidden: $isHidden, listing: $listing, user: $user, listingRating: $listingRating, startedAt: $startedAt, expectedEndedAt: $expectedEndedAt, actualEndedAt: $actualEndedAt, createdAt: $createdAt, updatedAt: $updatedAt, firestoreId: $firestoreId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.lockBluetoothGuestKey, lockBluetoothGuestKey) ||
                other.lockBluetoothGuestKey == lockBluetoothGuestKey) &&
            (identical(other.lockCustomPin, lockCustomPin) ||
                other.lockCustomPin == lockCustomPin) &&
            (identical(other.lockDailyPin, lockDailyPin) ||
                other.lockDailyPin == lockDailyPin) &&
            (identical(other.lockHourlyPin, lockHourlyPin) ||
                other.lockHourlyPin == lockHourlyPin) &&
            (identical(other.lockOneTimePin, lockOneTimePin) ||
                other.lockOneTimePin == lockOneTimePin) &&
            (identical(other.expectedUsageDuration, expectedUsageDuration) ||
                other.expectedUsageDuration == expectedUsageDuration) &&
            (identical(other.actualUsageDuration, actualUsageDuration) ||
                other.actualUsageDuration == actualUsageDuration) &&
            (identical(
                    other.numberOfUsageExtensions, numberOfUsageExtensions) ||
                other.numberOfUsageExtensions == numberOfUsageExtensions) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.listing, listing) || other.listing == listing) &&
            const DeepCollectionEquality().equals(other.user, user) &&
            (identical(other.listingRating, listingRating) ||
                other.listingRating == listingRating) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.expectedEndedAt, expectedEndedAt) ||
                other.expectedEndedAt == expectedEndedAt) &&
            (identical(other.actualEndedAt, actualEndedAt) ||
                other.actualEndedAt == actualEndedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.firestoreId, firestoreId) ||
                other.firestoreId == firestoreId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        listingId,
        userId,
        lockBluetoothGuestKey,
        lockCustomPin,
        lockDailyPin,
        lockHourlyPin,
        lockOneTimePin,
        expectedUsageDuration,
        actualUsageDuration,
        numberOfUsageExtensions,
        isHidden,
        listing,
        const DeepCollectionEquality().hash(user),
        listingRating,
        startedAt,
        expectedEndedAt,
        actualEndedAt,
        createdAt,
        updatedAt,
        firestoreId
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionImplCopyWith<_$SessionImpl> get copyWith =>
      __$$SessionImplCopyWithImpl<_$SessionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionImplToJson(
      this,
    );
  }
}

abstract class _Session extends Session {
  factory _Session(
      {required final String id,
      final String? listingId,
      final String? userId,
      final String? lockBluetoothGuestKey,
      final String? lockCustomPin,
      final String? lockDailyPin,
      final String? lockHourlyPin,
      final String? lockOneTimePin,
      final double? expectedUsageDuration,
      final double? actualUsageDuration,
      final int? numberOfUsageExtensions,
      final bool isHidden,
      final Listing? listing,
      final PartialUser? user,
      final ListingRating? listingRating,
      final DateTime? startedAt,
      final DateTime? expectedEndedAt,
      final DateTime? actualEndedAt,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final String? firestoreId}) = _$SessionImpl;
  _Session._() : super._();

  factory _Session.fromJson(Map<String, dynamic> json) = _$SessionImpl.fromJson;

  @override
  String get id;
  @override
  String? get listingId;
  @override
  String? get userId;
  @override
  String? get lockBluetoothGuestKey;
  @override
  String? get lockCustomPin;
  @override
  String? get lockDailyPin;
  @override
  String? get lockHourlyPin;
  @override
  String? get lockOneTimePin;
  @override
  double? get expectedUsageDuration;
  @override
  double? get actualUsageDuration;
  @override
  int? get numberOfUsageExtensions;
  @override // @Default(false) bool isEnded,
// @Default(false) bool isUsageExtended,
  bool get isHidden;
  @override
  Listing? get listing;
  @override
  PartialUser? get user;
  @override
  ListingRating? get listingRating;
  @override
  DateTime? get startedAt;
  @override
  DateTime? get expectedEndedAt;
  @override
  DateTime? get actualEndedAt;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  String? get firestoreId;
  @override
  @JsonKey(ignore: true)
  _$$SessionImplCopyWith<_$SessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SessionsResponse _$SessionsResponseFromJson(Map<String, dynamic> json) {
  return _SessionsResponse.fromJson(json);
}

/// @nodoc
mixin _$SessionsResponse {
  List<Session> get data => throw _privateConstructorUsedError;
  Pagination get meta => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionsResponseCopyWith<SessionsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionsResponseCopyWith<$Res> {
  factory $SessionsResponseCopyWith(
          SessionsResponse value, $Res Function(SessionsResponse) then) =
      _$SessionsResponseCopyWithImpl<$Res, SessionsResponse>;
  @useResult
  $Res call({List<Session> data, Pagination meta});

  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class _$SessionsResponseCopyWithImpl<$Res, $Val extends SessionsResponse>
    implements $SessionsResponseCopyWith<$Res> {
  _$SessionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Session>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginationCopyWith<$Res> get meta {
    return $PaginationCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SessionsResponseImplCopyWith<$Res>
    implements $SessionsResponseCopyWith<$Res> {
  factory _$$SessionsResponseImplCopyWith(_$SessionsResponseImpl value,
          $Res Function(_$SessionsResponseImpl) then) =
      __$$SessionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Session> data, Pagination meta});

  @override
  $PaginationCopyWith<$Res> get meta;
}

/// @nodoc
class __$$SessionsResponseImplCopyWithImpl<$Res>
    extends _$SessionsResponseCopyWithImpl<$Res, _$SessionsResponseImpl>
    implements _$$SessionsResponseImplCopyWith<$Res> {
  __$$SessionsResponseImplCopyWithImpl(_$SessionsResponseImpl _value,
      $Res Function(_$SessionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$SessionsResponseImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<Session>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as Pagination,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionsResponseImpl implements _SessionsResponse {
  _$SessionsResponseImpl(
      {required final List<Session> data, required this.meta})
      : _data = data;

  factory _$SessionsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionsResponseImplFromJson(json);

  final List<Session> _data;
  @override
  List<Session> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final Pagination meta;

  @override
  String toString() {
    return 'SessionsResponse(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionsResponseImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionsResponseImplCopyWith<_$SessionsResponseImpl> get copyWith =>
      __$$SessionsResponseImplCopyWithImpl<_$SessionsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionsResponseImplToJson(
      this,
    );
  }
}

abstract class _SessionsResponse implements SessionsResponse {
  factory _SessionsResponse(
      {required final List<Session> data,
      required final Pagination meta}) = _$SessionsResponseImpl;

  factory _SessionsResponse.fromJson(Map<String, dynamic> json) =
      _$SessionsResponseImpl.fromJson;

  @override
  List<Session> get data;
  @override
  Pagination get meta;
  @override
  @JsonKey(ignore: true)
  _$$SessionsResponseImplCopyWith<_$SessionsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateSessionInput _$CreateSessionInputFromJson(Map<String, dynamic> json) {
  return _CreateSessionInput.fromJson(json);
}

/// @nodoc
mixin _$CreateSessionInput {
  String get listingId => throw _privateConstructorUsedError;
  String? get lockCustomPin => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  double get lat => throw _privateConstructorUsedError;
  double get lon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateSessionInputCopyWith<CreateSessionInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateSessionInputCopyWith<$Res> {
  factory $CreateSessionInputCopyWith(
          CreateSessionInput value, $Res Function(CreateSessionInput) then) =
      _$CreateSessionInputCopyWithImpl<$Res, CreateSessionInput>;
  @useResult
  $Res call(
      {String listingId,
      String? lockCustomPin,
      int duration,
      double lat,
      double lon});
}

/// @nodoc
class _$CreateSessionInputCopyWithImpl<$Res, $Val extends CreateSessionInput>
    implements $CreateSessionInputCopyWith<$Res> {
  _$CreateSessionInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? lockCustomPin = freezed,
    Object? duration = null,
    Object? lat = null,
    Object? lon = null,
  }) {
    return _then(_value.copyWith(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      lockCustomPin: freezed == lockCustomPin
          ? _value.lockCustomPin
          : lockCustomPin // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateSessionInputImplCopyWith<$Res>
    implements $CreateSessionInputCopyWith<$Res> {
  factory _$$CreateSessionInputImplCopyWith(_$CreateSessionInputImpl value,
          $Res Function(_$CreateSessionInputImpl) then) =
      __$$CreateSessionInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String listingId,
      String? lockCustomPin,
      int duration,
      double lat,
      double lon});
}

/// @nodoc
class __$$CreateSessionInputImplCopyWithImpl<$Res>
    extends _$CreateSessionInputCopyWithImpl<$Res, _$CreateSessionInputImpl>
    implements _$$CreateSessionInputImplCopyWith<$Res> {
  __$$CreateSessionInputImplCopyWithImpl(_$CreateSessionInputImpl _value,
      $Res Function(_$CreateSessionInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? lockCustomPin = freezed,
    Object? duration = null,
    Object? lat = null,
    Object? lon = null,
  }) {
    return _then(_$CreateSessionInputImpl(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      lockCustomPin: freezed == lockCustomPin
          ? _value.lockCustomPin
          : lockCustomPin // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      lat: null == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as double,
      lon: null == lon
          ? _value.lon
          : lon // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateSessionInputImpl implements _CreateSessionInput {
  _$CreateSessionInputImpl(
      {required this.listingId,
      this.lockCustomPin,
      required this.duration,
      required this.lat,
      required this.lon});

  factory _$CreateSessionInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateSessionInputImplFromJson(json);

  @override
  final String listingId;
  @override
  final String? lockCustomPin;
  @override
  final int duration;
  @override
  final double lat;
  @override
  final double lon;

  @override
  String toString() {
    return 'CreateSessionInput(listingId: $listingId, lockCustomPin: $lockCustomPin, duration: $duration, lat: $lat, lon: $lon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateSessionInputImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.lockCustomPin, lockCustomPin) ||
                other.lockCustomPin == lockCustomPin) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lon, lon) || other.lon == lon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, listingId, lockCustomPin, duration, lat, lon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateSessionInputImplCopyWith<_$CreateSessionInputImpl> get copyWith =>
      __$$CreateSessionInputImplCopyWithImpl<_$CreateSessionInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateSessionInputImplToJson(
      this,
    );
  }
}

abstract class _CreateSessionInput implements CreateSessionInput {
  factory _CreateSessionInput(
      {required final String listingId,
      final String? lockCustomPin,
      required final int duration,
      required final double lat,
      required final double lon}) = _$CreateSessionInputImpl;

  factory _CreateSessionInput.fromJson(Map<String, dynamic> json) =
      _$CreateSessionInputImpl.fromJson;

  @override
  String get listingId;
  @override
  String? get lockCustomPin;
  @override
  int get duration;
  @override
  double get lat;
  @override
  double get lon;
  @override
  @JsonKey(ignore: true)
  _$$CreateSessionInputImplCopyWith<_$CreateSessionInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SessionEvent _$SessionEventFromJson(Map<String, dynamic> json) {
  return _SessionEvent.fromJson(json);
}

/// @nodoc
mixin _$SessionEvent {
  String get type => throw _privateConstructorUsedError;
  Session? get session => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionEventCopyWith<SessionEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionEventCopyWith<$Res> {
  factory $SessionEventCopyWith(
          SessionEvent value, $Res Function(SessionEvent) then) =
      _$SessionEventCopyWithImpl<$Res, SessionEvent>;
  @useResult
  $Res call({String type, Session? session});

  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class _$SessionEventCopyWithImpl<$Res, $Val extends SessionEvent>
    implements $SessionEventCopyWith<$Res> {
  _$SessionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? session = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SessionCopyWith<$Res>? get session {
    if (_value.session == null) {
      return null;
    }

    return $SessionCopyWith<$Res>(_value.session!, (value) {
      return _then(_value.copyWith(session: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SessionEventImplCopyWith<$Res>
    implements $SessionEventCopyWith<$Res> {
  factory _$$SessionEventImplCopyWith(
          _$SessionEventImpl value, $Res Function(_$SessionEventImpl) then) =
      __$$SessionEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String type, Session? session});

  @override
  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class __$$SessionEventImplCopyWithImpl<$Res>
    extends _$SessionEventCopyWithImpl<$Res, _$SessionEventImpl>
    implements _$$SessionEventImplCopyWith<$Res> {
  __$$SessionEventImplCopyWithImpl(
      _$SessionEventImpl _value, $Res Function(_$SessionEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? session = freezed,
  }) {
    return _then(_$SessionEventImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionEventImpl implements _SessionEvent {
  _$SessionEventImpl({required this.type, this.session});

  factory _$SessionEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionEventImplFromJson(json);

  @override
  final String type;
  @override
  final Session? session;

  @override
  String toString() {
    return 'SessionEvent(type: $type, session: $session)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionEventImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.session, session) || other.session == session));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, session);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionEventImplCopyWith<_$SessionEventImpl> get copyWith =>
      __$$SessionEventImplCopyWithImpl<_$SessionEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionEventImplToJson(
      this,
    );
  }
}

abstract class _SessionEvent implements SessionEvent {
  factory _SessionEvent({required final String type, final Session? session}) =
      _$SessionEventImpl;

  factory _SessionEvent.fromJson(Map<String, dynamic> json) =
      _$SessionEventImpl.fromJson;

  @override
  String get type;
  @override
  Session? get session;
  @override
  @JsonKey(ignore: true)
  _$$SessionEventImplCopyWith<_$SessionEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
