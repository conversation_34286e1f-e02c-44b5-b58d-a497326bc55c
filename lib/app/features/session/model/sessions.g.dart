// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sessions.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SessionImpl _$$SessionImplFromJson(Map<String, dynamic> json) =>
    _$SessionImpl(
      id: json['id'] as String,
      listingId: json['listing_id'] as String?,
      userId: json['user_id'] as String?,
      lockBluetoothGuestKey: json['lock_bluetooth_guest_key'] as String?,
      lockCustomPin: json['lock_custom_pin'] as String?,
      lockDailyPin: json['lock_daily_pin'] as String?,
      lockHourlyPin: json['lock_hourly_pin'] as String?,
      lockOneTimePin: json['lock_one_time_pin'] as String?,
      expectedUsageDuration:
          (json['expected_usage_duration'] as num?)?.toDouble(),
      actualUsageDuration: (json['actual_usage_duration'] as num?)?.toDouble(),
      numberOfUsageExtensions:
          (json['number_of_usage_extensions'] as num?)?.toInt(),
      isHidden: json['is_hidden'] as bool? ?? false,
      listing: json['listing'] == null
          ? null
          : Listing.fromJson(json['listing'] as Map<String, dynamic>),
      user: json['user'] == null
          ? null
          : PartialUser.fromJson(json['user'] as Map<String, dynamic>),
      listingRating: json['listing_rating'] == null
          ? null
          : ListingRating.fromJson(
              json['listing_rating'] as Map<String, dynamic>),
      startedAt: const CustomDateTimeConverter()
          .fromJson(json['started_at'] as String?),
      expectedEndedAt: const CustomDateTimeConverter()
          .fromJson(json['expected_ended_at'] as String?),
      actualEndedAt: const CustomDateTimeConverter()
          .fromJson(json['actual_ended_at'] as String?),
      createdAt: const CustomDateTimeConverter()
          .fromJson(json['created_at'] as String?),
      updatedAt: const CustomDateTimeConverter()
          .fromJson(json['updated_at'] as String?),
      firestoreId: json['firestore_id'] as String?,
    );

Map<String, dynamic> _$$SessionImplToJson(_$SessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'listing_id': instance.listingId,
      'user_id': instance.userId,
      'lock_bluetooth_guest_key': instance.lockBluetoothGuestKey,
      'lock_custom_pin': instance.lockCustomPin,
      'lock_daily_pin': instance.lockDailyPin,
      'lock_hourly_pin': instance.lockHourlyPin,
      'lock_one_time_pin': instance.lockOneTimePin,
      'expected_usage_duration': instance.expectedUsageDuration,
      'actual_usage_duration': instance.actualUsageDuration,
      'number_of_usage_extensions': instance.numberOfUsageExtensions,
      'is_hidden': instance.isHidden,
      'listing': instance.listing?.toJson(),
      'user': instance.user?.toJson(),
      'listing_rating': instance.listingRating?.toJson(),
      'started_at': const CustomDateTimeConverter().toJson(instance.startedAt),
      'expected_ended_at':
          const CustomDateTimeConverter().toJson(instance.expectedEndedAt),
      'actual_ended_at':
          const CustomDateTimeConverter().toJson(instance.actualEndedAt),
      'created_at': const CustomDateTimeConverter().toJson(instance.createdAt),
      'updated_at': const CustomDateTimeConverter().toJson(instance.updatedAt),
      'firestore_id': instance.firestoreId,
    };

_$SessionsResponseImpl _$$SessionsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionsResponseImpl(
      data: (json['data'] as List<dynamic>)
          .map((e) => Session.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Pagination.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SessionsResponseImplToJson(
        _$SessionsResponseImpl instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'meta': instance.meta.toJson(),
    };

_$CreateSessionInputImpl _$$CreateSessionInputImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateSessionInputImpl(
      listingId: json['listing_id'] as String,
      lockCustomPin: json['lock_custom_pin'] as String?,
      duration: (json['duration'] as num).toInt(),
      lat: (json['lat'] as num).toDouble(),
      lon: (json['lon'] as num).toDouble(),
    );

Map<String, dynamic> _$$CreateSessionInputImplToJson(
        _$CreateSessionInputImpl instance) =>
    <String, dynamic>{
      'listing_id': instance.listingId,
      'lock_custom_pin': instance.lockCustomPin,
      'duration': instance.duration,
      'lat': instance.lat,
      'lon': instance.lon,
    };

_$SessionEventImpl _$$SessionEventImplFromJson(Map<String, dynamic> json) =>
    _$SessionEventImpl(
      type: json['type'] as String,
      session: json['session'] == null
          ? null
          : Session.fromJson(json['session'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SessionEventImplToJson(_$SessionEventImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'session': instance.session?.toJson(),
    };
