import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/custom_datetime_converter.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/network/web_response.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:intl/intl.dart';

part 'sessions.freezed.dart';
part 'sessions.g.dart';

@freezed
class Session with _$Session {
  @CustomDateTimeConverter()
  factory Session({
    required String id,
    String? listingId,
    String? userId,
    String? lockBluetoothGuestKey,
    String? lockCustomPin,
    String? lockDailyPin,
    String? lockHourlyPin,
    String? lockOneTimePin,
    double? expectedUsageDuration,
    double? actualUsageDuration,
    int? numberOfUsageExtensions,
    // @Default(false) bool isEnded,
    // @Default(false) bool isUsageExtended,
    @Default(false) bool isHidden,
    Listing? listing,
    PartialUser? user,
    ListingRating? listingRating,
    DateTime? startedAt,
    DateTime? expectedEndedAt,
    DateTime? actualEndedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? firestoreId,
  }) = _Session;

  const Session._();

  factory Session.fromJson(Json json) => _$SessionFromJson(json);

  // find remaining time in seconds
  int get remainingTime =>
      expectedEndedAt?.difference(DateTime.now()).inSeconds ?? 0;

  bool get hasExtended =>
      (numberOfUsageExtensions ?? 0) >=
      (listing?.maxNumberOfUsageExtensions ?? 1);

  bool get noEntry => startedAt == actualEndedAt;

  String get startDate =>
      startedAt != null ? DateFormat('dd MMM yyyy').format(startedAt!) : '';
  String get endDate => expectedEndedAt != null
      ? DateFormat('dd MMM yyyy').format(expectedEndedAt!)
      : '';
  String get actualEndDate => actualEndedAt != null
      ? DateFormat('dd MMM yyyy').format(actualEndedAt!)
      : '';
  String get startTime =>
      startedAt != null ? DateFormat('hh:mm aa').format(startedAt!) : '';
  String get endTime => expectedEndedAt != null
      ? DateFormat('hh:mm aa').format(expectedEndedAt!)
      : '';
  String get actualEndTime => actualEndedAt != null
      ? DateFormat('hh:mm aa').format(actualEndedAt!)
      : '';
}

@freezed
class SessionsResponse with _$SessionsResponse {
  factory SessionsResponse({
    required List<Session> data,
    required Pagination meta,
  }) = _SessionsResponse;

  factory SessionsResponse.fromJson(Json json) =>
      _$SessionsResponseFromJson(json);
}

@freezed
class CreateSessionInput with _$CreateSessionInput {
  factory CreateSessionInput({
    required String listingId,
    String? lockCustomPin,
    required int duration,
    required double lat,
    required double lon,
  }) = _CreateSessionInput;

  factory CreateSessionInput.fromJson(Json json) =>
      _$CreateSessionInputFromJson(json);
}

@freezed
class SessionEvent with _$SessionEvent {
  factory SessionEvent({
    required String type,
    Session? session,
  }) = _SessionEvent;

  factory SessionEvent.fromJson(Json json) => _$SessionEventFromJson(json);
}
