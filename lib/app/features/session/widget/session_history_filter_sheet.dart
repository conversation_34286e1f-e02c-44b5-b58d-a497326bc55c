import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/suggest/provider/listing_suggestion_providers.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_history_filter_sheet.g.dart';

@riverpod
class AmenityFilters extends _$AmenityFilters {
  // return amenity id
  @override
  List<String> build() {
    return [];
  }

  void setList(List<String> list) {
    state = list;
  }

  void clear() {
    state = [];
  }
}

@riverpod
class ListingSorts extends _$ListingSorts {
  @override
  String? build() {
    return null;
  }

  void set(String value) {
    state = value;
  }

  void clear() {
    state = null;
  }
}

class SessionHistoryFilterSheet extends HookConsumerWidget {
  const SessionHistoryFilterSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sort = ref.watch(listingSortsProvider);
    final amenities = ref.watch(amenityFiltersProvider);
    final filtering = useState<List<String>>(amenities);
    final sorting = useState<String>(sort ?? 'created_at:desc');

    return StarsBackground(
      isTransparent: true,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: ClipPath(
          clipper: FilterWaveClipper(),
          child: DecoratedBox(
            decoration: const BoxDecoration(
              color: CustomColors.secondaryExtraLight,
            ),
            child: SafeArea(
              bottom: false,
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      const SizedBox(
                        width: double.infinity,
                        height: kToolbarHeight,
                      ),
                      Text(
                        'Session History Filters',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium!
                            .copyWith(color: CustomColors.primary),
                      ),
                      const Positioned(
                        top: 4,
                        right: 8,
                        child: CloseButton(),
                      ),
                    ],
                  ),
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shrinkWrap: true,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: Text(
                            'Sort',
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall!
                                .copyWith(color: CustomColors.primary),
                          ),
                        ),
                        const SizedBox(height: 8),
                        _SortRow(sorting),
                        const SizedBox(height: 16),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(horizontal: 4),
                        //   child: Text(
                        //     'Amenities',
                        //     style: Theme.of(context)
                        //         .textTheme
                        //         .titleSmall!
                        //         .copyWith(color: CustomColors.primary),
                        //   ),
                        // ),
                        // const SizedBox(height: 8),
                        // _AmenitiesGrid(filtering),
                        const SizedBox(height: 16),
                        _CTA(
                          filtering: filtering.value,
                          sorting: sorting.value,
                          clear: () => filtering.value = [],
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Image.asset(
                            'assets/images/filter_goma.png',
                            width: 200,
                          ),
                        ),
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                  // const SizedBox(height: 400),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _SortRow extends ConsumerWidget {
  const _SortRow(this.sorting);
  final ValueNotifier<String> sorting;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Expanded(
          child: FilledButton(
            onPressed: () {
              sorting.value = 'created_at:desc';
            },
            style: FilledButton.styleFrom(
              backgroundColor: sorting.value == 'created_at:desc'
                  ? CustomColors.primaries.shade400
                  : CustomColors.primaries.shade50,
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
            child: Text(
              'Recent',
              style: textTheme(context).labelMedium!.copyWith(
                    color: sorting.value == 'created_at:desc'
                        ? CustomColors.primaries.shade50
                        : CustomColors.primaries.shade400,
                  ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: FilledButton(
            onPressed: () {
              sorting.value = 'created_at:asc';
            },
            style: FilledButton.styleFrom(
              backgroundColor: sorting.value == 'created_at:asc'
                  ? CustomColors.primaries.shade400
                  : CustomColors.primaries.shade50,
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
            child: Text(
              'Oldest',
              style: textTheme(context).labelMedium!.copyWith(
                    color: sorting.value == 'created_at:asc'
                        ? CustomColors.primaries.shade50
                        : CustomColors.primaries.shade400,
                  ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: FilledButton(
            onPressed: () {
              sorting.value = 'name:asc';
            },
            style: FilledButton.styleFrom(
              backgroundColor: sorting.value == 'name:asc'
                  ? CustomColors.primaries.shade400
                  : CustomColors.primaries.shade50,
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
            child: Text(
              'A-Z',
              style: textTheme(context).labelMedium!.copyWith(
                    color: sorting.value == 'name:asc'
                        ? CustomColors.primaries.shade50
                        : CustomColors.primaries.shade400,
                  ),
            ),
          ),
        ),
      ],
    );
  }
}

class _AmenitiesGrid extends HookConsumerWidget {
  const _AmenitiesGrid(this.filtering);
  final ValueNotifier<List<String>> filtering;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // TODO(kkcy): set default value from amenity list to states
    // TODO(kkcy): preload during app launch
    final amenitiesList = ref.watch(amenityListProvider);

    return Column(
      children: [
        amenitiesList.when(
          loading: () {
            return const SizedBox.shrink();
          },
          error: (error, stackTrace) {
            Groveman.error(
              'filter sheet',
              error: error,
              stackTrace: stackTrace,
            );
            return const SizedBox.shrink();
          },
          data: (response) {
            final amenities = response.data;

            return GridView.count(
              shrinkWrap: true,
              childAspectRatio: 30 / 12,
              crossAxisCount: 2,
              physics: const NeverScrollableScrollPhysics(),
              children: List.generate(
                amenities.length,
                (index) {
                  return Card(
                    color: filtering.value.contains(amenities[index].id)
                        ? CustomColors.primary
                        : CustomColors.secondaryLight,
                    child: InkWell(
                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                      onTap: () {
                        // ref
                        //     .read(amenityFiltersProvider.notifier)
                        //     .toggleAmenity(amenities[index].id);

                        if (filtering.value.contains(amenities[index].id)) {
                          filtering.value.remove(amenities[index].id);
                        } else {
                          filtering.value.add(amenities[index].id);
                        }

                        filtering.value = [...filtering.value];
                      },
                      child: Row(
                        children: [
                          const SizedBox(width: 16),
                          Icon(
                            amenityIconMap[amenities[index].fontIconName],
                            color: filtering.value.contains(amenities[index].id)
                                ? CustomColors.secondaryLight
                                : CustomColors.primary,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              amenities[index].name,
                              style: textTheme(context).labelMedium!.copyWith(
                                    height: 1.2,
                                    color: filtering.value
                                            .contains(amenities[index].id)
                                        ? CustomColors.secondaryLight
                                        : CustomColors.primary,
                                  ),
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                      ),
                    ),
                  );

                  // return FilterChip(
                  //   avatar: CircleAvatar(
                  //     child: CachedNetworkImage(
                  //       imageUrl: amenities[index].imageUrl,
                  //       errorWidget: (context, url, error) =>
                  //           const Icon(Icons.error, size: 10),
                  //     ),
                  //   ),
                  //   label: Text(amenities[index].name),
                  //   shape: RoundedRectangleBorder(
                  //     borderRadius: BorderRadius.circular(16),
                  //     side: const BorderSide(
                  //       color: Colors.transparent,
                  //     ),
                  //   ),
                  //   visualDensity: VisualDensity.compact,
                  //   selected: filtering.value
                  //       .contains(amenities[index].id),
                  //   backgroundColor: Colors.grey.shade200,
                  //   selectedColor: CustomColors.primary,
                  //   labelStyle: Theme.of(context)
                  //       .textTheme
                  //       .labelLarge!
                  //       .copyWith(
                  //         color: filtering.value
                  //                 .contains(amenities[index].id)
                  //             ? Colors.white
                  //             : Colors.black,
                  //       ),
                  //   onSelected: (value) {
                  //     // ref
                  //     //     .read(amenityFiltersProvider.notifier)
                  //     //     .toggleAmenity(amenities[index].id);
                  //     if (filtering.value
                  //         .contains(amenities[index].id)) {
                  //       filtering.value
                  //           .remove(amenities[index].id);
                  //     } else {
                  //       filtering.value
                  //           .add(amenities[index].id);
                  //     }

                  //     filtering.value = [...filtering.value];
                  //   },
                  // );
                },
                growable: false,
              ),
            );
          },
        ),
      ],
    );
  }
}

class _CTA extends ConsumerWidget {
  const _CTA({
    required this.filtering,
    required this.sorting,
    required this.clear,
  });
  final List<String> filtering;
  final String sorting;
  final Function() clear;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: TextButton(
            onPressed: clear,
            style: TextButton.styleFrom(
              shape: const StadiumBorder(
                side: BorderSide(
                  color: CustomColors.primary,
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24),
            ),
            child: const Text('Reset'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextButton(
            onPressed: () {
              ref.read(amenityFiltersProvider.notifier).setList(filtering);
              ref.read(listingSortsProvider.notifier).set(sorting);
              context.pop();
            },
            style: TextButton.styleFrom(
              shape: const StadiumBorder(
                side: BorderSide(
                  color: CustomColors.primary,
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24),
              backgroundColor: CustomColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply'),
          ),
        ),
      ],
    );
  }
}
