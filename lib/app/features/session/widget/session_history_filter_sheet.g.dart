// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_history_filter_sheet.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$amenityFiltersHash() => r'c5715ac1e3f8711f84d8ccc83e8297971bc58bf5';

/// See also [AmenityFilters].
@ProviderFor(AmenityFilters)
final amenityFiltersProvider =
    AutoDisposeNotifierProvider<AmenityFilters, List<String>>.internal(
  AmenityFilters.new,
  name: r'amenityFiltersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$amenityFiltersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AmenityFilters = AutoDisposeNotifier<List<String>>;
String _$listingSortsHash() => r'168b108017b79d1a6d367c69e0deb892aa0745e3';

/// See also [ListingSorts].
@ProviderFor(ListingSorts)
final listingSortsProvider =
    AutoDisposeNotifierProvider<ListingSorts, String?>.internal(
  ListingSorts.new,
  name: r'listingSortsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$listingSortsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListingSorts = AutoDisposeNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
