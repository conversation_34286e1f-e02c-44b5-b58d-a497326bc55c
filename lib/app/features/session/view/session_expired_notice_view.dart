import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/explore/provider/show_session_island_providers.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SessionExpiredNoticeView extends HookConsumerWidget {
  const SessionExpiredNoticeView(
      this.fromUnlockSessionView, this.fromUnlockSessionViewHasNotClickedLetsGo,
      {super.key});
  final bool fromUnlockSessionView;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (fromUnlockSessionView) {
      return _Content(220, fromUnlockSessionViewHasNotClickedLetsGo);
    }

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: _Body(fromUnlockSessionViewHasNotClickedLetsGo),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(this.fromUnlockSessionViewHasNotClickedLetsGo);
  double get maxExtent => 200 + kToolbarHeight;
  double get minExtent => kToolbarHeight;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CustomScrollView(
      slivers: [
        SliverPersistentHeader(
          delegate: BrandAppBar(
            maxHeight:
                kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
            minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
            title: const Text('Your session expired!'),
            child: Row(
              children: [
                SizedBox(
                  height: max(
                    mediaQuery(context).viewPadding.top,
                    kToolbarHeight * 10,
                  ),
                ),
              ],
            ),
          ),
          pinned: true,
        ),
        SliverList.list(
          children: [
            _Content(240, fromUnlockSessionViewHasNotClickedLetsGo),
          ],
        ),
      ],
    );
  }
}

class _Content extends ConsumerWidget {
  const _Content(this.logoSize, this.fromUnlockSessionViewHasNotClickedLetsGo);
  final double logoSize;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        32,
        fromUnlockSessionViewHasNotClickedLetsGo ? 12 : 32,
        32,
        32,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You did not enter the unlocked listing, the session has expired!',
            style: textTheme(context).headlineMedium,
          ),
          const SizedBox(height: 16),
          const Text(
            'Please try to unlock to create another new session.',
          ),
          if (!fromUnlockSessionViewHasNotClickedLetsGo) ...[
            const SizedBox(height: 32),
            Image.asset(
              'assets/images/goma_sad.png',
              height: logoSize,
            ),
          ],
          const SizedBox(height: 32),
          BrandButton.cta(
            onPressed: () {
              if (fromUnlockSessionViewHasNotClickedLetsGo) {
                context.pop();
              } else {
                // if clicked means there is current_session_view behind, hence pop twice.
                context
                  ..pop()
                  ..pop();
              }

              ref
                  .read(showSessionIslandProvider.notifier)
                  .hide(); // Since user don't need to submit rating, hence proceed to close session island.
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
