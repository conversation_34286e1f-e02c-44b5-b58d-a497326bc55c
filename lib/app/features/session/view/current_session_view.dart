import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/core/utils/use_interval.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/active_listing_status_providers.dart';
import 'package:gomama/app/features/listing/widget/flag_listing_expansion_tile.dart';
import 'package:gomama/app/features/listing/widget/help_support_expansion_tile.dart';
import 'package:gomama/app/features/listing/widget/listing_detail_expansion_tile.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/features/session/view/session_clean_notice_view.dart';
import 'package:gomama/app/features/session/view/session_expired_notice_view.dart';
import 'package:gomama/app/features/session/view/unlock_session_view.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:rive/rive.dart' as rive;

class CurrentSessionView extends HookConsumerWidget {
  const CurrentSessionView({super.key});

  static const routeName = 'current session';
  static const routePath = '/current-session';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Keep track of the last valid session to prevent flashing
    final lastValidSessionEvent = useState<SessionEvent?>(null);
    final sessionEventAsync = ref.watch(activeSessionProvider);
    final endingSessionError = useState<String?>(null);

    // Update lastValidSessionEvent when we get new data
    useEffect(
      () {
        if (sessionEventAsync.value != null &&
            sessionEventAsync.value?.session != null) {
          lastValidSessionEvent.value = sessionEventAsync.value;
        }

        return null;
      },
      [sessionEventAsync.value],
    );

    // Show error state
    if (sessionEventAsync.hasError) {
      Groveman.error(
        'CurrentSessionView',
        error: sessionEventAsync.error,
        stackTrace: sessionEventAsync.stackTrace,
      );

      // If we have a last valid session, show that instead of empty state
      if (lastValidSessionEvent.value?.session != null) {
        return Scaffold(
          body: _Body(
            lastValidSessionEvent.value!.session!,
            sessionEventAsync.value!.type,
            onError: (String message) {
              endingSessionError.value = message;
            },
          ),
        );
      }

      return const Scaffold(body: SizedBox.shrink());
    }

    // Show loading state only if we don't have a last valid session
    if (sessionEventAsync.isLoading && lastValidSessionEvent.value == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    // Use the current session if available, otherwise fall back to last valid session
    final sessionEvent = sessionEventAsync.value ?? lastValidSessionEvent.value;

    // something went wrong
    if (sessionEvent == null) {
      return const SizedBox.shrink();
    }

    if (sessionEvent.type == 'session_cleanup') {
      return SessionCleanNoticeView(
        lastValidSessionEvent.value!.session!.id,
        false,
        false,
      );
    }

    if (sessionEvent.type == 'entry_expired') {
      return const SessionExpiredNoticeView(
        false,
        false,
      );
    }

    // something went wrong
    if (sessionEvent.session == null) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      body: _Body(
        sessionEvent.session!,
        sessionEvent.type,
        onError: (String message) {
          endingSessionError.value = message;
        },
      ),
      bottomNavigationBar: AnimatedFooterWrapper(
        child: endingSessionError.value != null
            ? _FooterError(
                endingSessionError.value!,
                onTap: () {
                  endingSessionError.value = null;
                },
              )
            : sessionEvent.session!.hasExtended
                ? const SizedBox.shrink()
                : _Footer(sessionEvent.session!),
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(
    this.session,
    this.sessionType, {
    required this.onError,
  });
  final Session session;
  final String sessionType;
  final Function onError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final _countdownController = CountDownController();
    final _remainingTime = useState(session.remainingTime);
    // TODO(kkcy): show don't forgot your essentials
    final _isSessionEnded = useState(false);
    // final _isEndingSession = useState(false);
    final formatter = DateFormat('mm:ss');

    useInterval(
      () {
        if (_remainingTime.value > 0) {
          _remainingTime.value--;
        } else {
          _remainingTime.value = 0;
          _isSessionEnded.value = true;
        }
      },
      const Duration(seconds: 1),
    );

    // on session update
    useEffect(
      () {
        _remainingTime.value = session.remainingTime;

        return () {};
      },
      [session],
    );

    // return Stack(
    //   children: [
    //     // TODO(kkcy): need square aspect ratio
    //     const RiveAnimation.asset(
    //       'assets/rives/water_timer.riv',
    //       fit: BoxFit.cover,
    //       alignment: Alignment.topCenter,
    //     ),
    //     Positioned(
    //       top: 110,
    //       height: 240,
    //       left: 70,
    //       right: 70,
    //       child: CircularProgressIndicator(
    //         value: _remainingTime.value /
    //             // usageDuration is in minutes
    //             ((session.expectedUsageDuration ?? 30) * 60),
    //         backgroundColor: Colors.grey[300],
    //         strokeCap: StrokeCap.round,
    //         strokeWidth: 10,
    //       ),
    //     ),
    //     Positioned(
    //       top: 170,
    //       left: 0,
    //       right: 0,
    //       child: Column(
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         children: [
    //           const Text('Time Remaining'),
    //           const SizedBox(height: 16),
    //           Text(
    //             formatter.format(
    //               DateTime(0).add(
    //                 Duration(seconds: _remainingTime.value),
    //               ),
    //             ),
    //             style: Theme.of(context).textTheme.displayLarge!.copyWith(
    //                   color: CustomColors.primary,
    //                 ),
    //           ),
    //           const SizedBox(height: 24),
    //         ],
    //       ),
    //     ),
    //   ],
    // );

    return SingleChildScrollView(
      child: Column(
        children: [
          Stack(
            children: [
              Image.asset('assets/images/session_timer.png'),
              Positioned(
                top: mediaQuery(context).size.width * 0.24,
                left: 2,
                right: 0,
                height: mediaQuery(context).size.width * 0.6,
                child: const Center(
                  child: rive.RiveAnimation.asset(
                    'assets/rives/water_timer.riv',
                    fit: BoxFit.contain,
                    alignment: Alignment.topCenter,
                  ),
                ),
              ),
              Positioned(
                top: mediaQuery(context).size.width * 0.24,
                left: 2,
                right: 0,
                child: Center(
                  child: SizedBox(
                    width: mediaQuery(context).size.width * 0.6,
                    height: mediaQuery(context).size.width * 0.6,
                    child: CircularProgressIndicator(
                      value: _remainingTime.value /
                          // usageDuration is in minutes
                          ((session.expectedUsageDuration ?? 30) * 60),
                      backgroundColor: Colors.grey[300],
                      strokeCap: StrokeCap.round,
                      strokeWidth: 8,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: mediaQuery(context).size.width * 0.24,
                height: mediaQuery(context).size.width * 0.6,
                left: 0,
                right: 0,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Time Remaining'),
                    const SizedBox(height: 16),
                    Text(
                      _remainingTime.value >= 0
                          ? formatter.format(
                              DateTime(0).add(
                                Duration(seconds: _remainingTime.value),
                              ),
                            )
                          : '00:00',
                      style: Theme.of(context).textTheme.displayLarge,
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
              Positioned(
                top: mediaQuery(context).size.width * 1.04,
                left: 72,
                right: 72,
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'Start time',
                            style: textTheme(context).labelMedium,
                          ),
                          FittedBox(
                            fit: BoxFit.fitWidth,
                            child: Text(
                              session.startTime,
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                          ),
                          Text(session.startDate),
                        ],
                      ),
                    ),
                    const SizedBox(width: 32),
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'End time',
                            style: textTheme(context).labelMedium,
                          ),
                          FittedBox(
                            fit: BoxFit.fitWidth,
                            child: Text(
                              session.endTime,
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                          ),
                          Text(session.endDate),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                top: max(
                  mediaQuery(context).viewPadding.top,
                  kToolbarHeight / 1.5,
                ),
                left: 16,
                child: CircleAvatar(
                  foregroundColor: CustomColors.primary,
                  backgroundColor: Colors.white,
                  child: IconButton(
                    onPressed: () {
                      if (context.canPop()) {
                        context.pop();
                      } else {
                        const ExploreRoute().go(context);
                      }
                    },
                    icon: const Icon(CustomIcon.home),
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 64),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 48,
                  child: Image.asset(
                    session.listing?.listingType == ListingType.gomama
                        ? 'assets/maps/3.0x/purple_marker.png'
                        : 'assets/maps/3.0x/blue_marker.png',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        session.listing?.name ?? '',
                        style: textTheme(context).labelLarge!.copyWith(
                              color: CustomColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      if (session.listing?.companyName != null)
                        Text(
                          'by ${session.listing?.companyName}',
                          style: textTheme(context).labelMedium!.copyWith(
                                color: CustomColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 48),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: BrandButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (context) => const UnlockListingViewSheet(),
                );
              },
              debounced: true,
              child: const Text('Get Door Pin'),
            ),
          ),
          const SizedBox(height: 12),
          if (_remainingTime.value <= 0) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: BrandButton.cta(
                onPressed: () {
                  // times up but session is still active_session type means user still in pod but duration is over
                  if (sessionType == 'active_session') {
                    onError('Please exit the pod before submit the rating.');
                    return;
                  }

                  context.pop();

                  showCupertinoModalPopup(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) => ReviewSessionView(
                      session.id,
                      initialSession: session,
                    ),
                  );
                },
                debounced: true,
                child: const Text('Rate'),
              ),
            ),
            // ] else ...[
            //   Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 32),
            //   child: BrandButton.cta(
            //     onPressed: () {
            //       showAdaptiveDialog(
            //         context: context,
            //         builder: (context) {
            //           return AlertDialog.adaptive(
            //             title: const Text(
            //               'Are you sure you want\nto end the session?',
            //             ),
            //             actions: [
            //               AdaptiveTextButton(
            //                 onPressed: () {
            //                   if (_isEndingSession.value) {
            //                     return;
            //                   }

            //                   context.pop();
            //                 },
            //                 child: const Text('Cancel'),
            //               ),
            //               AdaptiveTextButton(
            //                 onPressed: () {
            //                   if (_isEndingSession.value) {
            //                     return;
            //                   }

            //                   ref.read(endSessionProvider().future).then(
            //                     (value) {
            //                       if (context.mounted) {
            //                         context.pop();

            //                         showCupertinoModalPopup(
            //                           barrierDismissible: false,
            //                           context: context,
            //                           builder: (context) => ReviewSessionView(
            //                             session.id,
            //                             initialSession: session,
            //                           ),
            //                         );
            //                       }
            //                     },
            //                   ).onError((error, stackTrace) {
            //                     _isEndingSession.value = false;

            //                     // close the confirmation dialog
            //                     if (context.mounted) {
            //                       context.pop();
            //                     }

            //                     // prompt user
            //                     if (error is AppNetworkResponseException) {
            //                       onError(
            //                         error.message ?? 'Something went wrong',
            //                       );

            //                       return;
            //                     }

            //                     onError('Something went wrong');
            //                   });
            //                 },
            //                 state: AdaptiveTextButtonState.danger,
            //                 child: const Text('Yes'),
            //               ),
            //             ],
            //           );
            //         },
            //       );
            //     },
            //     debounced: true,
            //     child: const Text('End Session'),
            //   ),
            // ),
          ],
          const SizedBox(height: 12),
          if (session.listing != null) ...[
            ListingDetail(session.listing!),
            HelpSupport(session.listing?.openingHours ?? ''),
            FlagListing(session.listing!),
            const SizedBox(height: 12),
          ],
        ],
      ),
    );
  }
}

class AnimatedFooterWrapper extends StatelessWidget {
  const AnimatedFooterWrapper({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  });

  final Widget child;
  final Duration duration;

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: duration,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            ),
          ),
          child: child,
        );
      },
      child: child,
    );
  }
}

// Modified _Footer widget
class _Footer extends HookConsumerWidget {
  const _Footer(this.session);
  final Session session;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isExtending = useState(false);
    final _remainingTime = useState(session.remainingTime);
    final _isSessionEnded = useState(false);

    useInterval(
      () {
        if (_remainingTime.value > 0) {
          _remainingTime.value--;
        } else {
          _remainingTime.value = 0;
          _isSessionEnded.value = true;
        }
      },
      const Duration(seconds: 1),
    );

    // on session update
    useEffect(
      () {
        _remainingTime.value = session.remainingTime;

        return () {};
      },
      [session],
    );

    // Times up, user should not be able to extend session.
    if (_remainingTime.value <= 0) {
      return const SizedBox.shrink();
    }

    return ColoredBox(
      color: CustomColors.primary,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Row(
            children: [
              const Text(
                'Require more time?\nTap for a 15 min extension.',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FilledButton(
                  onPressed: isExtending.value
                      ? null
                      : () async {
                          isExtending.value = true;
                          try {
                            await ref.read(
                              extendSessionProvider(session.id).future,
                            );
                          } finally {
                            isExtending.value = false;
                          }
                        },
                  style: FilledButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: CustomColors.primary,
                  ),
                  child: const Text('Extend'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FooterError extends ConsumerWidget {
  const _FooterError(
    this.message, {
    this.onTap,
  });

  final String message;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ColoredBox(
      color: CustomColors.red,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(width: 16),
              FilledButton(
                onPressed: onTap,
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: CustomColors.primary,
                ),
                child: const Text('OK'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
