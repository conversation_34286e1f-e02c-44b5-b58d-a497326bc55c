import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/explore/provider/show_session_island_providers.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_listing_rating_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:gomama/app/widgets/rating_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// NOTE: to submit session review
class ReviewSessionView extends HookConsumerWidget {
  const ReviewSessionView(
    this.id, {
    this.initialSession,
    super.key,
  });
  final String id;
  final Session? initialSession;

  static const routeName = 'review-session';
  static const routePath = '/sessions/:id/reviews';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(reviewSessionOngoingProvider);
    final sessionAsync = ref.watch(singleSessionProvider(id));

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ref
              .read(reviewSessionOngoingProvider.notifier)
              .update((state) => true);
        });

        // reset initial session id && session ongoing flags on dispost
        return () {
          if (context.mounted) {
            ref
                .read(reviewSessionOngoingProvider.notifier)
                .update((state) => false);
          }
        };
      },
      const [],
    );

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: sessionAsync.when(
        error: (error, stackTrace) {
          Groveman.error(
            'ReviewSessionView',
            error: error,
            stackTrace: stackTrace,
          );
          return const SizedBox.shrink();
        },
        loading: () {
          // while loading, we show what we have
          // if initialSession is not null, we show it
          if (initialSession != null) {
            return _Body(initialSession!);
          }

          return const Center(
            child: CircularProgressIndicator(),
          );
        },
        data: (session) {
          // on loaded, we show updated information
          return _Body(session!);
        },
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(this.session);
  final Session session;

  double get maxExtent => 200 + kToolbarHeight;
  double get minExtent => kToolbarHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reviewSuccess = useState(false);

    return CustomScrollView(
      slivers: [
        SliverPersistentHeader(
          delegate: BrandAppBar(
            maxHeight:
                kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
            minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
            title: const Text('Rate your experience, Mama!'),
            child: Column(
              children: [
                SizedBox(
                  height: max(
                    mediaQuery(context).viewPadding.top,
                    kToolbarHeight / 2,
                  ),
                ),
              ],
            ),
            automaticallyImplyLeading: false,
          ),
          pinned: true,
        ),
        SliverList.list(
          children: reviewSuccess.value
              ? [
                  const _ReviewSubmitted(),
                ]
              : [
                  Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 0, 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 16),
                              Text(
                                session.listing?.name ?? '',
                                style: textTheme(context).titleMedium!.copyWith(
                                      color: CustomColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${session.startTime} - ${session.actualEndTime} | ${session.actualEndDate}',
                                style: textTheme(context).bodySmall,
                              ),
                              const SizedBox(height: 6),
                              SizedBox(
                                width: mediaQuery(context).size.width * 0.6,
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Location: ',
                                          style: textTheme(context).bodySmall,
                                        ),
                                        Expanded(
                                          child: Text(
                                            session.listing?.fullAddress ?? '',
                                            style: textTheme(context).bodySmall,
                                            overflow: TextOverflow.fade,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 6),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Session ID: ',
                                          style: textTheme(context).bodySmall,
                                        ),
                                        Expanded(
                                          child: Text(
                                            session.id,
                                            style: textTheme(context).bodySmall,
                                            overflow: TextOverflow.fade,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 6),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Listing ID: ',
                                          style: textTheme(context).bodySmall,
                                        ),
                                        Expanded(
                                          child: Text(
                                            session.listing?.id ?? '',
                                            style: textTheme(context).bodySmall,
                                            overflow: TextOverflow.fade,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Image.asset(
                        'assets/images/goma_wink.png',
                        height: 160,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(),
                  ),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _ReviewForm(
                      session.listingId!,
                      session.id,
                      onSuccess: () {
                        reviewSuccess.value = true;
                        ref.read(showSessionIslandProvider.notifier).hide();
                        // reset initial session id
                        ref
                            .read(initialSessionIdProvider.notifier)
                            .update((state) => null);
                      },
                    ),
                  ),
                ],
        ),
      ],
    );
  }
}

class _ReviewForm extends HookConsumerWidget {
  const _ReviewForm(
    this.listingId,
    this.sessionId, {
    required this.onSuccess,
  });
  final String listingId;
  final String sessionId;
  final void Function() onSuccess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());

    return FormBuilder(
      key: _formKey.value,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your review',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mobile App',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Wayfinding, convenience'),
                  ],
                ),
              ),
              FormBuilderField<int>(
                name: 'app_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Lactation Pod',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Experience, cleanliness, etc'),
                  ],
                ),
              ),
              FormBuilderField<int>(
                name: 'listing_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Experience',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Does this service improve your experience?'),
                  ],
                ),
              ),
              FormBuilderField<int>(
                name: 'experience_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            'Any feedbacks or comments?',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          FormBuilderTextField(
            name: 'review',
            maxLines: 7,
            cursorColor: CustomColors.primary,
            decoration: InputDecoration(
              hintText:
                  'We’re here to improve your experience. Share your thoughts!',
              hintStyle: TextStyle(color: Colors.grey.shade500),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(16),
                gapPadding: 0,
              ),
              fillColor: CustomColors.secondaryLight,
              filled: true,
            ),
          ),
          const SizedBox(height: 16),
          BrandButton.cta(
            onPressed: () {
              // Validate and save the form values
              final success = _formKey.value.currentState?.saveAndValidate();

              if (success != true) {
                return;
              }

              final values =
                  Map<String, dynamic>.from(_formKey.value.currentState!.value)
                    ..addAll(
                      {'session_id': sessionId} as Json,
                    );

              ref.read(submitReviewProvider(values).future).then((value) {
                onSuccess();
                ref
                    .read(singleListingProvider(listingId).notifier)
                    .invalidate(listingId);
                ref
                    .read(allSessionsProvider.notifier)
                    .invalidate(); // Submit pending session review needs to immediately refresh
                ref.read(singleSessionProvider(sessionId).notifier).invalidate(
                    sessionId); // Submit pending session review needs to immediately refresh
              }).onError((error, stackTrace) {
                Groveman.error(
                  'submitReviewProvider',
                  error: error,
                  stackTrace: stackTrace,
                );

                // TODO(kkcy): show error message as toast
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        error is AppNetworkResponseException
                            ? '${error.message}'
                            : 'Something went wrong, please try again later',
                      ),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              });
            },
            child: const Text('Submit'),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _ReviewSubmitted extends ConsumerWidget {
  const _ReviewSubmitted();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          Text(
            'Thank you for your rating and feedback!',
            style: textTheme(context).headlineMedium,
          ),
          const SizedBox(height: 16),
          const Text(
            'Your feedback is valuable to us,\nhelping us to improve your experience.',
          ),
          const SizedBox(height: 32),
          Image.asset(
            'assets/images/goma_whiteboard.png',
            height: 240,
          ),
          const SizedBox(height: 32),
          BrandButton.cta(
            onPressed: () {
              if (context.canPop()) {
                context
                  ..pop()
                  ..pop();
              } else {
                const ExploreRoute().push(context);
              }
            },
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }
}
