import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/listing/widget/listing_gallery.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_listing_rating_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:gomama/app/widgets/rating_bar.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PastSessionView extends HookConsumerWidget {
  const PastSessionView({
    required this.initialSession,
    super.key,
  });

  final Session initialSession;

  static const routeName = 'past-session';
  static const routePath = '/sessions';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(singleSessionProvider(initialSession.id));

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: sessionAsync.when(
        error: (error, stackTrace) {
          Groveman.error(
            'PastSessionView',
            error: error,
            stackTrace: stackTrace,
          );
          return const SizedBox.shrink();
        },
        loading: () {
          // while loading, we show what we have
          return _Body(initialSession);
        },
        data: (session) {
          // on loaded, we show updated information
          return _Body(session!);
        },
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  _Body(this.session);
  final Session session;
  final isEdit = useState(false);

  double get maxExtent => (isEdit.value ? 0 : 200) + kToolbarHeight;
  double get minExtent => kToolbarHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// NOTE: listing should never be null
    if (session.listing == null) {
      return const SizedBox.shrink();
    }

    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) => [
        SliverOverlapAbsorber(
          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
            context,
          ),
          sliver: SliverSafeArea(
            top: false,
            sliver: SliverAppBar(
              backgroundColor: CustomColors.secondaryLight,
              expandedHeight: maxExtent,
              pinned: true,
              flexibleSpace: isEdit.value
                  ? null
                  : FlexibleSpaceBar(
                      collapseMode: CollapseMode.none,
                      background: ListingGallery(
                        session.listing!,
                        radius: BorderRadius.zero,
                        isReview: true,
                      ),
                    ),
            ),
          ),
        ),
      ],
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: isEdit.value
              ? _ReviewForm(
                  session,
                  isEdit,
                  onSuccess: () {
                    isEdit.value = false;
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Updated listing review successfully!'),
                          duration: Duration(seconds: 3),
                        ),
                      );
                    }
                  },
                )
              : _PastSessionInfoScrollView(session, isEdit),
        ),
      ),
    );
  }
}

class _PastSessionInfoScrollView extends ConsumerWidget {
  const _PastSessionInfoScrollView(this.session, this.isEdit);
  final Session session;
  final ValueNotifier<bool> isEdit;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (session.listing == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: mediaQuery(context).padding.bottom > 0 ? 0 : 20,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                session.listing!.name ?? '',
                style: textTheme(context).titleLarge!.copyWith(
                      color: CustomColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
            if (session.noEntry)
              Chip(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                label: const Text(
                  'No Entry',
                  style: TextStyle(
                    color: CustomColors.secondaryExtraLight,
                  ),
                ),
                side: const BorderSide(
                  color: CustomColors.secondaryLight,
                ),
                backgroundColor: CustomColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(32),
                ),
              )
            else if (session.listingRating !=
                null) // Can update rating only when there is one. We expect backend cronjob to auto review all unreviewed session last 2 days.
              IconButton(
                style: isEdit.value
                    ? IconButton.styleFrom(
                        backgroundColor: CustomColors.primaries,
                        foregroundColor: CustomColors.backgroundForm,
                      )
                    : IconButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: CustomColors.primaries,
                      ),
                onPressed: () async {
                  isEdit.value = true;
                },
                icon: const Icon(Icons.edit, size: 30),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          '${session.startTime} - ${session.actualEndTime} | ${session.actualEndDate}',
          style: textTheme(context).bodySmall,
        ),
        const SizedBox(height: 6),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location: ',
              style: textTheme(context).bodySmall,
            ),
            Expanded(
              child: Text(
                session.listing!.fullAddress ?? '',
                style: textTheme(context).bodySmall,
                overflow: TextOverflow.fade,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Session ID: ',
              style: textTheme(context).bodySmall,
            ),
            Expanded(
              child: Text(
                session.id,
                style: textTheme(context).bodySmall,
                overflow: TextOverflow.fade,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Listing ID: ',
              style: textTheme(context).bodySmall,
            ),
            Expanded(
              child: Text(
                session.listing!.id,
                style: textTheme(context).bodySmall,
                overflow: TextOverflow.fade,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        const Divider(),
        const SizedBox(height: 12),
        Text(
          'Your review',
          style: textTheme(context).titleMedium!.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mobile App',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('Wayfinding, convenience'),
                ],
              ),
            ),
            RatingBarIndicator(
              rating: session.listingRating?.appRating ?? 0,
              itemBuilder: (context, index) => const Icon(
                CustomIcon.star,
                color: CustomColors.primary,
              ),
              itemSize: 24,
            ),
          ],
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Lactation Pod',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('Experience, cleanliness, etc'),
                ],
              ),
            ),
            RatingBarIndicator(
              rating: session.listingRating?.listingRating ?? 0,
              itemBuilder: (context, index) => const Icon(
                CustomIcon.star,
                color: CustomColors.primary,
              ),
              itemSize: 24,
            ),
          ],
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Experience',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('Does this service improve your experience?'),
                ],
              ),
            ),
            RatingBarIndicator(
              rating: session.listingRating?.experienceRating ?? 0,
              itemBuilder: (context, index) => const Icon(
                CustomIcon.star,
                color: CustomColors.primary,
              ),
              itemSize: 24,
            ),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          'Your feedbacks',
          style: textTheme(context).titleMedium!.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          maxLines: 7,
          cursorColor: CustomColors.primary,
          decoration: InputDecoration(
            hintStyle: TextStyle(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(16),
              gapPadding: 0,
            ),
            fillColor: CustomColors.secondaryLight,
            filled: true,
          ),
          enabled: false,
          initialValue: session.listingRating?.review ?? '',
        ),
      ],
    );
  }
}

class _ReviewForm extends HookConsumerWidget {
  const _ReviewForm(
    this.session,
    this.isEdit, {
    required this.onSuccess,
  });
  final Session session;
  final ValueNotifier<bool> isEdit;
  final void Function() onSuccess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormBuilderState>());

    return FormBuilder(
      key: _formKey.value,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: mediaQuery(context).padding.bottom > 0 ? 0 : 20,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  session.listing!.name ?? '',
                  style: textTheme(context).titleLarge!.copyWith(
                        color: CustomColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              IconButton(
                style: isEdit.value
                    ? IconButton.styleFrom(
                        backgroundColor: CustomColors.primaries,
                        foregroundColor: CustomColors.backgroundForm,
                      )
                    : IconButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: CustomColors.primaries,
                      ),
                onPressed: () async {
                  isEdit.value = false;
                },
                icon: const Icon(Icons.edit, size: 30),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${session.startTime} - ${session.actualEndTime} | ${session.actualEndDate}',
            style: textTheme(context).bodySmall,
          ),
          const SizedBox(height: 6),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Location: ',
                style: textTheme(context).bodySmall,
              ),
              Expanded(
                child: Text(
                  session.listing!.fullAddress ?? '',
                  style: textTheme(context).bodySmall,
                  overflow: TextOverflow.fade,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Session ID: ',
                style: textTheme(context).bodySmall,
              ),
              Expanded(
                child: Text(
                  session.id,
                  style: textTheme(context).bodySmall,
                  overflow: TextOverflow.fade,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Listing ID: ',
                style: textTheme(context).bodySmall,
              ),
              Expanded(
                child: Text(
                  session.listing!.id,
                  style: textTheme(context).bodySmall,
                  overflow: TextOverflow.fade,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Divider(),
          const SizedBox(height: 12),
          Text(
            'Your review',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mobile App',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Wayfinding, convenience'),
                  ],
                ),
              ),
              Column(
                children: [
                  FormBuilderField<int>(
                    name: 'app_rating',
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(),
                    ]),
                    initialValue: session.listingRating?.appRating?.ceil() ?? 5,
                    builder: (FormFieldState<dynamic> field) {
                      return RatingBar.builder(
                        initialRating: session.listingRating?.appRating ?? 5,
                        itemBuilder: (context, index) => Icon(
                          CustomIcon.star,
                          color: index <= (field.value as int)
                              ? CustomColors.primary
                              : Colors.grey.shade300,
                        ),
                        itemSize: 24,
                        onRatingUpdate: (value) {
                          field.didChange(value.toInt());
                        },
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Lactation Pod',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Experience, cleanliness, etc'),
                  ],
                ),
              ),
              FormBuilderField<int>(
                name: 'listing_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue: session.listingRating?.listingRating?.ceil() ?? 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: session.listingRating?.listingRating ?? 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Experience',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Does this service improve your experience?'),
                  ],
                ),
              ),
              FormBuilderField<int>(
                name: 'experience_rating',
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                initialValue:
                    session.listingRating?.experienceRating?.ceil() ?? 5,
                builder: (FormFieldState<dynamic> field) {
                  return RatingBar.builder(
                    initialRating: session.listingRating?.experienceRating ?? 5,
                    itemBuilder: (context, index) => Icon(
                      CustomIcon.star,
                      color: index <= (field.value as int)
                          ? CustomColors.primary
                          : Colors.grey.shade300,
                    ),
                    itemSize: 24,
                    onRatingUpdate: (value) {
                      field.didChange(value.toInt());
                    },
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            'Your feedbacks',
            style: textTheme(context).titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          FormBuilderTextField(
            initialValue: session.listingRating?.review ?? '',
            name: 'review',
            maxLines: 7,
            cursorColor: CustomColors.primary,
            decoration: InputDecoration(
              hintText:
                  'We’re here to improve your experience. Share your thoughts!',
              hintStyle: TextStyle(color: Colors.grey.shade500),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(16),
                gapPadding: 0,
              ),
              fillColor: CustomColors.secondaryLight,
              filled: true,
            ),
          ),
          const SizedBox(height: 16),
          BrandButton.cta(
            onPressed: () {
              // Validate and save the form values
              final success = _formKey.value.currentState?.saveAndValidate();

              if (success != true) {
                return;
              }

              final values = Map<String, dynamic>.from(
                _formKey.value.currentState!.value,
              )..addAll(
                  {'listing_rating_id': session.listingRating!.id} as Json,
                );

              ref.read(updateReviewProvider(values).future).then((value) {
                onSuccess();
                ref.read(allSessionsProvider.notifier).invalidate();
                ref
                    .read(singleSessionProvider(session.id).notifier)
                    .invalidate(session.id);
                if (context.mounted) {
                  Navigator.pop(context);
                }
              }).onError((error, stackTrace) {
                Groveman.error(
                  'updateReviewProvider',
                  error: error,
                  stackTrace: stackTrace,
                );

                // TODO(kkcy): show error message as toast
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        error is AppNetworkResponseException
                            ? '${error.message}'
                            : 'Something went wrong, please try again later',
                      ),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              });
            },
            child: const Text('Submit'),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}
