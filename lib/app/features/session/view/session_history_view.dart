import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/past_session_view.dart';
import 'package:gomama/app/features/session/widget/session_history_filter_sheet.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SessionHistoryView extends HookConsumerWidget {
  const SessionHistoryView({super.key});

  static const routeName = 'history';
  static const routePath = '/history';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionAsync = ref.watch(allSessionsProvider);

    return BrandScaffold(
      title: const Text('Session History'),
      actions: [
        IconButton(
          icon: const Icon(CustomIcon.filter),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: CustomColors.primary,
          ),
          onPressed: () {
            showCupertinoModalPopup(
              context: context,
              builder: (context) {
                return const SessionHistoryFilterSheet();
              },
            );
          },
        ),
        const SizedBox(width: 16),
      ],
      physics: const NeverScrollableScrollPhysics(),
      child: BrandBottomSheet(
        maxHeight: max(
              mediaQuery(context).viewPadding.top,
              kToolbarHeight / 2,
            ) +
            100,
        minHeight: mediaQuery(context).size.height / 2,
        slivers: [
          const SliverPadding(padding: EdgeInsets.only(top: 12)),
          sessionAsync.when(
            error: (error, stackTrace) {
              Groveman.error(
                'SessionHistoryView',
                error: error,
                stackTrace: stackTrace,
              );

              return const SliverToBoxAdapter(
                child: SizedBox.shrink(),
              );
            },
            loading: () => const SliverFillRemaining(
              child: Center(
                child: SizedBox(
                  width: 160,
                  height: 160,
                  child: LoadingView(),
                ),
              ),
            ),
            data: (session) {
              if (session?.data.isEmpty ?? true) {
                return SliverToBoxAdapter(
                  child: Column(
                    children: [
                      const SizedBox(height: 64),
                      Image.asset(
                        'assets/images/goma_sad.png',
                        height: 160,
                      ),
                      const Text('There are no sessions.'),
                    ],
                  ),
                );
              }

              return SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                sliver: SliverList.builder(
                  itemCount: session!.data.length,
                  itemBuilder: (context, index) {
                    return _HistoryCard(session.data[index]);
                  },
                ),
              );
            },
          ),
          const SliverPadding(padding: EdgeInsets.only(top: 16)),
          const SliverPadding(padding: EdgeInsets.only(top: 8)),
        ],
      ),
    );
  }
}

class _HistoryCard extends ConsumerWidget {
  const _HistoryCard(this.initialSession);
  final Session initialSession;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        color: CustomColors.secondaryLight,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: CustomColors.secondary),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            if (initialSession.listingRating == null &&
                DateTime.now().difference(initialSession.createdAt!) <
                    const Duration(days: 2) &&
                !initialSession.noEntry) {
              ReviewSessionRoute(initialSession.id, $extra: initialSession)
                  .push(context);
              return;
            }
            PastSessionRoute(initialSession).push(context);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        'Session: ${initialSession.id}',
                        style: Theme.of(context).textTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      initialSession.endDate,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              const Divider(
                color: CustomColors.secondary,
                height: 1,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: Row(
                  children: [
                    Column(
                      children: [
                        if (initialSession.listingRating == null &&
                            initialSession.noEntry)
                          Chip(
                            padding: const EdgeInsets.symmetric(horizontal: 6),
                            label: const Text(
                              'No Entry',
                              style: TextStyle(
                                color: CustomColors.secondaryExtraLight,
                              ),
                            ),
                            side: const BorderSide(
                              color: CustomColors.secondaryLight,
                            ),
                            backgroundColor: CustomColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(32),
                            ),
                          )
                        else if (initialSession.listingRating == null &&
                            DateTime.now().difference(
                                  initialSession.createdAt!,
                                ) <
                                const Duration(days: 2) &&
                            !initialSession.noEntry)
                          // Note:
                          // Only session that are within last 2 days because 2 days after are expect to auto review by backend.
                          // Only session that are not no-entry (because session could be no-entry and within 2 days)
                          // Only when listing rating is null (because 2 days and null rating means user still can submit their review before auto review hence show Pending chip)
                          Chip(
                            padding: const EdgeInsets.symmetric(horizontal: 6),
                            label: const Text(
                              'Pending',
                              style: TextStyle(
                                color: CustomColors.secondaryExtraLight,
                              ),
                            ),
                            side: const BorderSide(
                              color: CustomColors.secondaryLight,
                            ),
                            backgroundColor: CustomColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(32),
                            ),
                          ),
                        CachedNetworkImage(
                          imageUrl: initialSession.listing?.previewImage ?? '',
                          height: 100,
                          width: 80,
                          imageBuilder: (context, imageProvider) {
                            return DecoratedBox(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                ),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(12),
                                ),
                              ),
                            );
                          },
                          errorWidget: (context, url, error) =>
                              const Icon(CustomIcon.error),
                        ),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            initialSession.listing?.name ?? 'Go!Mama Pod',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: CustomColors.primary,
                                  height: 1.2,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Go!Mama Access',
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: CustomColors.placeholder,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                      'Experience Rating: ${initialSession.listingRating?.experienceRating != null ? initialSession.listingRating?.experienceRating?.toStringAsFixed(2) : '-'}'),
                                  const SizedBox(width: 4),
                                  if (initialSession
                                          .listingRating?.experienceRating !=
                                      null)
                                    const Icon(
                                      CustomIcon.star,
                                      size: 16,
                                    ),
                                ],
                              ),
                              Row(
                                children: [
                                  Text(
                                      'App Rating: ${initialSession.listingRating?.appRating != null ? initialSession.listingRating?.appRating?.toStringAsFixed(2) : '-'}'),
                                  const SizedBox(width: 4),
                                  if (initialSession
                                          .listingRating?.experienceRating !=
                                      null)
                                    const Icon(
                                      CustomIcon.star,
                                      size: 16,
                                    ),
                                ],
                              ),
                              Row(
                                children: [
                                  Text(
                                    'Listing Rating: ${initialSession.listingRating?.listingRating != null ? initialSession.listingRating?.listingRating?.toStringAsFixed(2) : '-'}',
                                  ),
                                  const SizedBox(width: 4),
                                  if (initialSession
                                          .listingRating?.experienceRating !=
                                      null)
                                    const Icon(
                                      CustomIcon.star,
                                      size: 16,
                                    ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 6,
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Start time',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelSmall,
                                        ),
                                        Text(initialSession.startTime),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 6,
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'End time',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelSmall,
                                        ),
                                        Text(initialSession.actualEndTime),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
