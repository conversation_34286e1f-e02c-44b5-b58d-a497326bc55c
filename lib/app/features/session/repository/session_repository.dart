import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/session/model/sessions.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'session_repository.g.dart';

@Riverpod(keepAlive: true)
SessionRepository sessionRepository(SessionRepositoryRef ref) =>
    SessionRepository(ref);

class SessionRepository {
  SessionRepository(this.ref);
  final SessionRepositoryRef ref;
  final _sessionCache = <String, Session>{};

  void clearCache(String sessionId) {
    _sessionCache.remove(sessionId);
  }

  Future<SessionsResponse> fetchSessions(
    List<String>? amenities,
    String? sort,
  ) async {
    try {
      final response = await ref.read(repositoryProvider).get<Json>(
        '/sessions',
        queryParameters: {
          if (amenities?.isNotEmpty ?? false) 'amenities': amenities,
          if (sort?.isNotEmpty ?? false) 'sort': sort,
        },
      );

      final result = SessionsResponse.fromJson(response.data!);

      for (final session in result.data) {
        _sessionCache[session.id] = session;
      }

      return SessionsResponse.fromJson(response.data!);
    } catch (error) {
      Groveman.warning('fetchSessions', error: error);
      rethrow;
    }
  }

  /// NOTE: can be use to find active session by passing 'active'
  /// should only have one active session at a time
  Future<Session?> fetchSession(
    String id, {
    CancelToken? cancelToken,
  }) async {
    // Commented out to be able to fetch the listing ratings for the Session History - ReviewSessionView
    // if (id != 'active') {
    //   if (_sessionCache.containsKey(id)) {
    //     return _sessionCache[id]!;
    //   }
    // }

    try {
      final response = await ref.read(repositoryProvider).get<Json>(
            '/sessions/$id',
            cancelToken: cancelToken,
          );

      if (id == 'active') {
        if (response.data?['success'] != true) {
          // no active session right now
          return null;
        }
      }

      final result = Session.fromJson(response.data?['data'] as Json);

      if (id != 'active') {
        _sessionCache[result.id] = result;
      }

      return result;
    } catch (error) {
      Groveman.warning('fetchSession', error: error);
      rethrow;
    }
  }

  Future<Session> extendSession({
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/sessions/extend',
            cancelToken: cancelToken,
          );

      final result = Session.fromJson(response.data?['data'] as Json);

      // _sessionCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('extendSession', error: error);
      rethrow;
    }
  }

  Future<Session> createSession(
    CreateSessionInput input, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/sessions',
            cancelToken: cancelToken,
            data: input.toJson(),
          );

      final result = Session.fromJson(response.data?['data'] as Json);

      // _sessionCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('createSession', error: error);
      rethrow;
    }
  }

  Future<Session> endSession({
    bool? isRestart,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/sessions/end${isRestart == true ? '?restart=true' : ''}',
            cancelToken: cancelToken,
          );

      final result = Session.fromJson(response.data?['data'] as Json);

      // _sessionCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('endSession', error: error);
      rethrow;
    }
  }

  Future<Session> regeneratePin(
    String sessionId, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/sessions/$sessionId/pins',
            cancelToken: cancelToken,
          );

      final result = Session.fromJson(response.data?['data'] as Json);

      // _sessionCache[result.id] = result;

      return result;
    } catch (error) {
      Groveman.warning('regeneratePin', error: error);
      rethrow;
    }
  }
}
