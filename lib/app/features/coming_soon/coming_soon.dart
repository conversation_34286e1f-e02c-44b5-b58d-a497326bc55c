import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/info_content_management_providers.dart';
import 'package:gomama/app/widgets/background_shapes.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ComingSoonView extends HookConsumerWidget {
  const ComingSoonView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StarsBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 48),
          const MilkBackground(),
          Expanded(
            child: DecoratedBox(
              decoration: const BoxDecoration(
                color: CustomColors.secondaryLight,
              ),
              child: Column(
                children: [
                  Image.asset(
                    'assets/images/goma_planet.png',
                    width: 180,
                    height: 180,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'Coming Soon!',
                    style: textTheme(context).headlineMedium?.copyWith(
                          color: CustomColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Our commerce experience is not ready yet. Stay tuned for exciting updates!',
                    style: textTheme(context).bodyLarge?.copyWith(
                          color: CustomColors.text,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
