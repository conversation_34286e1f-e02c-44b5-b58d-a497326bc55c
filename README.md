# `gomama_flutter_v2` - Customer Mobile App

## Overview

This repository contains the source code for the Gomama customer-facing mobile application, built with **Flutter (Dart)**. The app is designed for end-users to book and manage services provided by the Gomama platform.

## Key Features

-   **User Authentication:** Secure login and registration.
-   **Service Booking:** Browse and book available services.
-   **Real-time Updates:** Receives live updates (e.g., booking status, driver location) via a WebSocket connection to `gomama_realtime`.
-   **API Communication:** Interacts with both the `gomama_adonis` (core) and `gomama_cms` (content) APIs to fetch and display data.
-   **CMS-Driven Content:** Renders dynamic content managed in `gomama_cms`.

## Tech Stack

-   **Framework:** Flutter (Dart)
-   **State Management:** (As per project implementation, e.g., BLoC, Provider)
-   **Networking:** `http` for REST API calls, `web_socket_channel` for real-time communication.

## Getting Started

This project uses different configurations for development, staging, and production environments.

1.  **Install Dependencies:**
    ```bash
    flutter pub get
    ```

2.  **Run the App (Development Flavor):**
    ```bash
    flutter run --flavor development --target lib/main_development.dart
    ```

3.  **Run the App (Staging Flavor):**
    ```bash
    flutter run --flavor staging --target lib/main_staging.dart
    ```

4.  **Run the App (Production Flavor):**
    ```bash
    flutter run --flavor production --target lib/main_production.dart
    ```

## Building the App

To generate models and other generated files, run the following command:

```bash
fvm flutter pub run build_runner build --delete-conflicting-outputs
```