name: gomama
description: Go!Mama
version: 1.0.4+55
publish_to: none

environment:
  sdk: ">=3.0.1 <4.0.0"
  flutter: 3.22.1

dependencies:
  app_settings: ^5.1.1
  app_tracking_transparency: ^2.0.6
  cached_network_image: ^3.4.0
  cupertino_icons: ^1.0.8
  defer_pointer: ^0.0.2
  device_info_plus: ^10.1.0
  dio: ^5.4.3+1
  dio_http_formatter: ^3.2.1
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  flutter:
    sdk: flutter
  flutter_branch_sdk: ^8.3.1
  flutter_dotenv: ^5.0.2
  flutter_facebook_auth: ^7.1.0
  flutter_form_builder: ^9.2.1
  flutter_hooks: ^0.20.3
  flutter_inappwebview: ^6.1.5
  flutter_local_notifications: ^18.0.1
  flutter_localizations:
    sdk: flutter
  flutter_secure_storage: ^9.2.2
  # form_builder_cupertino_fields: ^0.2.1
  flutter_svg: ^2.0.10+1
  flutter_widget_from_html_core: ^0.15.2
  font_awesome_flutter: ^10.7.0
  form_builder_phone_field: ^2.0.1
  form_builder_validators: ^11.0.0
  # form_builder_validators:
  #   git:
  #     url: **************:flutter-form-builder-ecosystem/form_builder_validators.git
  freezed_annotation: ^2.4.1
  geolocator: ^12.0.0
  go_router: ^14.1.3
  google_sign_in: ^6.2.1
  groveman: ^1.2.2
  hive_flutter: ^1.1.0
  hooks_riverpod: ^2.5.1
  http: ^1.2.1
  image_picker: ^1.1.1
  intl: ^0.19.0
  jovial_svg: ^1.1.21
  json_annotation: ^4.9.0
  lottie: ^3.1.2
  # mapbox_maps_flutter: ^2.2.0
  mapbox_maps_flutter: ^2.6.1
  package_info_plus: ^8.0.2
  permission_handler: ^11.3.1
  photo_view: ^0.15.0
  pinput: ^5.0.0
  pub_semver: ^2.1.3
  rive: ^0.13.12
  riverpod_annotation: ^2.3.5
  share_plus: ^10.0.0
  shopify_checkout_sheet_kit:
    git:
      url: **************:gaincue/shopify_checkout_sheet_kit_flutter.git
  shopify_flutter: ^2.2.5
  sign_in_with_apple: ^6.1.0
  url_launcher: ^6.2.10
  uuid: ^4.4.0
  video_player: ^2.9.2
  web_socket_channel: ^3.0.1
  mqtt_client: ^10.0.0

dev_dependencies:
  build_runner: ^2.4.10
  custom_lint: ^0.6.4
  flutter_native_splash: ^2.4.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.2
  go_router_builder: ^2.7.0
  icons_launcher: ^2.1.7
  json_serializable: ^6.8.0
  mocktail: ^1.0.3
  riverpod_generator: ^2.4.0
  riverpod_lint: ^2.3.10
  very_good_analysis: ^6.0.0

dependency_overrides:
  # flutter_inappwebview_android:
  #   git:
  #     url: https://github.com/holzgeist/flutter_inappwebview
  #     path: flutter_inappwebview_android
  #     ref: d89b1d32638b49dfc58c4b7c84153be0c269d057
  # TODO(kkcy): Recheck once form_builder_phone_field version >2.0.1 is released
  form_builder_phone_field:
    git:
      url: https://github.com/flutter-form-builder-ecosystem/form_builder_phone_field.git
      ref: 4bd3c9d1a3d69af87dc7d218191c0e8f07e315c7
  graphql:
    git:
      url: https://github.com/hagen00/graphql-flutter.git
      ref: main
      path: packages/graphql
  graphql_flutter:
    git:
      url: https://github.com/hagen00/graphql-flutter.git
      ref: main
      path: packages/graphql_flutter
  # TODO(kkcy): Recheck once mapbox_maps_flutter version >2.2.0rc is released
  # mapbox_maps_flutter:
  #   git:
  #     url: https://github.com/ThomasAunvik/mapbox-maps-flutter.git
  #     ref: c9cf8a0c117e2a96e35f8ddbb0edb4291473448a

flutter:
  uses-material-design: true
  generate: true

  fonts:
    - family: CustomIcon
      fonts:
        - asset: fonts/CustomIcon.ttf
    - family: SimplySerif
      fonts:
        - asset: fonts/SimplySerif-Bold.ttf
        - asset: fonts/SimplySerif-BoldItalic.ttf
        - asset: fonts/SimplySerif-Book.ttf
        - asset: fonts/SimplySerif-BookItalic.ttf
    - family: AveriaSansLibre
      fonts:
        - asset: fonts/AveriaSansLibre-Bold.ttf
        - asset: fonts/AveriaSansLibre-BoldItalic.ttf
        - asset: fonts/AveriaSansLibre-Italic.ttf
        - asset: fonts/AveriaSansLibre-Light.ttf
        - asset: fonts/AveriaSansLibre-LightItalic.ttf
        - asset: fonts/AveriaSansLibre-Regular.ttf
    - family: QuickSand
      fonts:
        - asset: fonts/QuickSand-Bold.ttf
        - asset: fonts/QuickSand-SemiBold.ttf
        - asset: fonts/QuickSand-Regular.ttf
        - asset: fonts/QuickSand-Medium.ttf
        - asset: fonts/QuickSand-Light.ttf

  assets:
    - assets/
    - assets/maps/
    - assets/images/
    - assets/backgrounds/
    - assets/rives/
    - assets/launchers/
    - assets/videos/
    - .env.development
    - .env.staging
    - .env.production
